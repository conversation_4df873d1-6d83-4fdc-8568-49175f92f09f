(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d229411"],{dd50:function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticStyle:{"padding-left":"20px"}},[e("el-form",{attrs:{inline:!0,"label-width":"100px"}},[e("el-form-item",{attrs:{label:"监测间隔(ms)"}},[e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值范围500-10000毫秒",placement:"top"}},[e("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测间隔",type:"number",clearable:"",size:"small"},model:{value:t.monitorInterval,callback:function(i){t.monitorInterval=i},expression:"monitorInterval"}})],1)],1),e("el-form-item",{attrs:{label:"监测次数"}},[e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值方位1-300",placement:"top"}},[e("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测次数",type:"number",clearable:"",size:"small"},model:{value:t.monitorNumber,callback:function(i){t.monitorNumber=i},expression:"monitorNumber"}})],1)],1),e("el-form-item",[e("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"success",icon:"el-icon-video-play",size:"mini"},on:{click:function(i){return t.beginMonitor()}}},[t._v("开始监测")]),e("el-button",{attrs:{type:"danger",icon:"el-icon-video-pause",size:"mini"},on:{click:function(i){return t.stopMonitor()}}},[t._v("停止监测")])],1)],1),e("el-row",{directives:[{name:"loading",rawName:"v-loading",value:t.chartLoading,expression:"chartLoading"}],attrs:{gutter:20,"element-loading-text":"正在接收设备数据，请耐心等待......","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},t._l(t.monitorThings,(function(t,i){return e("el-col",{key:i,staticStyle:{"margin-bottom":"20px"},attrs:{span:12}},[e("el-card",{attrs:{shadow:"hover","body-style":{paddingTop:"10px",marginBottom:"-20px"}}},[e("div",{ref:"monitor",refInFor:!0,staticStyle:{height:"210px",padding:"0"}})])],1)})),1)],1)},o=[],a=(e("14d9"),e("b0c0"),e("d3b7"),e("25f0"),{name:"DeviceMonitor",props:{device:{type:Object,default:null}},watch:{device:function(t,i){if(this.deviceInfo=t,this.deviceInfo&&0!=this.deviceInfo.deviceId){this.monitorThings=this.deviceInfo.monitorList,this.dataList=[];for(var e=0;e<this.monitorThings.length;e++)this.dataList.push({id:this.monitorThings[e].id,name:this.monitorThings[e].name,data:[]});this.$nextTick((function(){this.getMonitorChart()})),this.mqttCallback()}}},data:function(){return{monitorInterval:1e3,monitorNumber:60,chart:[],dataList:[],monitorThings:[],chartLoading:!1,deviceInfo:{}}},created:function(){},methods:{mqttPublish:function(t,i){var e=this,n="",o="";4==i.type&&(n="/"+t.productId+"/"+t.serialNumber+"/monitor/get",o='{"count":'+i.value+',"interval":'+this.monitorInterval+"}",""!=n&&this.$mqttTool.publish(n,o,i.name).then((function(t){e.$modal.notifySuccess(t)})).catch((function(t){e.$modal.notifyError(t)})))},mqttCallback:function(){var t=this;this.$mqttTool.client.on("message",(function(i,e,n){var o=i.split("/"),a=(o[1],o[2]);if(e=JSON.parse(e.toString()),e&&("status"==o[3]&&(console.log("接收到【设备状态】主题：",i),console.log("接收到【设备状态】内容：",e),t.deviceInfo.serialNumber==a&&(t.deviceInfo.status=e.status,t.deviceInfo.isShadow=e.isShadow,t.deviceInfo.rssi=e.rssi)),"monitor"==o[3])){console.log("接收到【实时监测】主题：",i),console.log("接收到【实时监测】内容：",e),t.chartLoading=!1;for(var s=0;s<e.length;s++)for(var r=e[s].value,l=e[s].id,c=(e[s].remark,0);c<t.dataList.length;c++){if(l==t.dataList[c].id){t.dataList[c].length>50&&t.dataList[c].shift(),t.dataList[c].data.push([t.getTime(),r]),t.chart[c].setOption({series:[{data:t.dataList[c].data}]});break}if(0==t.dataList[c].id.indexOf("array_")){var d=t.dataList[c].id.substring(6,8),h=t.dataList[c].id.substring(9);if(h==l){var m=r.split(",");t.dataList[c].length>50&&t.dataList[c].shift(),t.dataList[c].data.push([t.getTime(),m[d]]),t.chart[c].setOption({series:[{data:t.dataList[c].data}]});break}}}}}))},beginMonitor:function(){if(3==this.deviceInfo.status){for(var t=0;t<this.dataList.length;t++)this.dataList[t].data=[];(this.monitorInterval<500||this.monitorInterval>1e4)&&this.$modal.alertError("实时监测的间隔范围500-10000毫秒"),(0==this.monitorNumber||this.monitorNumber>300)&&this.$modal.alertError("实时监测数量范围1-300");var i={name:"更新实时监测"};i.value=this.monitorNumber,i.type=4,this.mqttPublish(this.deviceInfo,i),this.chartLoading=!0}else this.$modal.alertError("设备不在线，下发指令失败")},stopMonitor:function(){if(3==this.deviceInfo.status){this.chartLoading=!1;var t={name:"关闭实时监测",value:0,type:4};this.mqttPublish(this.deviceInfo,t)}else this.$modal.alertError("设备不在线，下发指令失败")},getMonitorChart:function(){for(var t=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],i=0;i<this.monitorThings.length;i++){var e;this.$refs.monitor[i].style.width=document.documentElement.clientWidth/2-255+"px",this.chart[i]=this.$echarts.init(this.$refs.monitor[i]),e={title:{left:"center",text:this.monitorThings[i].name+" （单位 "+(void 0!=this.monitorThings[i].datatype.unit?this.monitorThings[i].datatype.unit:"无")+"）",textStyle:{fontSize:14}},grid:{top:"50px",left:"20px",right:"20px",bottom:"10px",containLabel:!0},tooltip:{trigger:"axis",axisPointer:{animation:!0}},xAxis:{type:"time",show:!1,splitLine:{show:!1}},yAxis:{type:"value",boundaryGap:[0,"100%"],splitLine:{show:!0}},series:[{name:this.monitorThings[i].name,type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:i>9?t[0]:t[i]},areaStyle:{},data:[]}]},e&&this.chart[i].setOption(e)}},getTime:function(){var t=new Date,i=t.getFullYear(),e=t.getMonth()+1,n=t.getDate(),o=t.getHours(),a=t.getMinutes(),s=t.getSeconds();return e=e<10?"0"+e:e,n=n<10?"0"+n:n,o=o<10?"0"+o:o,i+"-"+e+"-"+n+" "+o+":"+a+":"+s}}}),s=a,r=e("2877"),l=Object(r["a"])(s,n,o,!1,null,null,null);i["default"]=l.exports}}]);