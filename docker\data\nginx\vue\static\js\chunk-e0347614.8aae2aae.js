(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e0347614"],{"67dd":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticStyle:{"padding-left":"20px"}},[n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.channelList,size:"mini"}},[n("el-table-column",{attrs:{label:"设备ID",align:"center",prop:"deviceSipId"}}),n("el-table-column",{attrs:{label:"通道ID",align:"center",prop:"channelSipId"}}),n("el-table-column",{attrs:{label:"快照","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isVideoChannel(t.row)?n("el-image",{staticStyle:{width:"60px"},attrs:{src:e.getSnap(t.row),"preview-src-list":e.getBigSnap(t.row),fit:"contain"}},[n("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[n("i",{staticClass:"el-icon-picture-outline"})])]):e._e()]}}])}),n("el-table-column",{attrs:{label:"通道名称",align:"center",prop:"channelName"}}),n("el-table-column",{attrs:{label:"产品型号",align:"center",prop:"model"}}),n("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.sip_gen_status,value:t.row.status,size:"mini"}})]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",type:"success",icon:"el-icon-video-play",disabled:2!==t.row.status},on:{click:function(n){return e.sendDevicePush(t.row)}}},[e._v("查看直播")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},i=[],l=n("e2de"),r={name:"Channel",dicts:["sip_gen_status","video_type","channel_type"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber)}},data:function(){return{loadSnap:{},deviceInfo:{},loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,channelList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deviceSipId:null},form:{}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.getList()},methods:{sendDevicePush:function(e){var t={tabName:"sipPlayer",channelId:e.channelSipId};this.$emit("playerEvent",t),console.log("通知设备推流："+e.deviceSipId+" : "+e.channelSipId)},getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){console.log(t),e.channelList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={channelId:null,channelSipId:null,deviceSipId:null,channelName:null,manufacture:null,model:null,owner:null,civilcode:null,block:null,address:null,parentid:null,ipaddress:null,port:null,password:null,ptztype:null,ptztypetext:null,status:0,longitude:null,latitude:null,streamid:null,subcount:null,parental:1,hasaudio:1},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset();var n=e.channelId||this.ids;Object(l["d"])(n).then((function(e){t.form=e.data,t.open=!0,t.title="修改监控设备通道信息"}))},handleDelete:function(e){var t=this,n=e.channelId||this.ids;this.$modal.confirm('是否确认删除监控设备通道信息编号为"'+n+'"的数据项？').then((function(){return Object(l["c"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getSnap:function(e){return console.log("getSnap:/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"),"/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"},getBigSnap:function(e){return[this.getSnap(e)]},isVideoChannel:function(e){var t=e.channelSipId.substring(10,13);return!("111"!==t&&"112"!==t&&"118"!==t&&"131"!==t&&"132"!==t)}}},s=r,o=n("2877"),c=Object(o["a"])(s,a,i,!1,null,null,null);t["default"]=c.exports},e2de:function(e,t,n){"use strict";n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return l})),n.d(t,"a",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"h",(function(){return o})),n.d(t,"f",(function(){return c})),n.d(t,"b",(function(){return u})),n.d(t,"g",(function(){return d}));var a=n("b775");function i(e){return Object(a["a"])({url:"/sip/channel/list",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/sip/channel/"+e,method:"get"})}function r(e,t){return Object(a["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function s(e){return Object(a["a"])({url:"/sip/channel/"+e,method:"delete"})}function o(e,t){return Object(a["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function c(e,t,n){return Object(a["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:n})}function u(e,t){return Object(a["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function d(e,t,n,i){return Object(a["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+n,method:"get",params:i})}}}]);