(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e143e342"],{"38da":function(e,t,a){"use strict";a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return i})),a.d(t,"g",(function(){return r})),a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return m})),a.d(t,"e",(function(){return u}));var n=a("b775");function o(e){return Object(n["a"])({url:"/iot/temp/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/iot/temp/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/iot/temp",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/iot/temp",method:"put",data:e})}function s(e){return Object(n["a"])({url:"/iot/temp/"+e,method:"delete"})}function m(e){return Object(n["a"])({url:"/iot/temp/getTemp",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/iot/temp/getTempByPid",method:"get",params:e})}},f398:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"6px"}},[a("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"6px"}},[a("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-20px"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"模板名称",prop:"templateName"}},[a("el-input",{attrs:{placeholder:"请输入模板名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.templateName,callback:function(t){e.$set(e.queryParams,"templateName",t)},expression:"queryParams.templateName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:add"],expression:"['iot:temp:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1)],1),a("el-card",{staticStyle:{"padding-bottom":"100px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.tempList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"模板名称",align:"center",prop:"templateName"}}),a("el-table-column",{attrs:{label:"采集方式",align:"center",prop:"pollingMethod"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.data_collect_type,value:t.row.pollingMethod}})]}}])}),a("el-table-column",{attrs:{label:"从机/变量",align:"center",prop:"slaveTotal,pointTotal"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.slaveTotal)+"/"+e._s(t.row.pointTotal)+" ")]}}])}),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:edit"],expression:"['iot:temp:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:edit"],expression:"['iot:temp:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-cpu"},on:{click:function(a){return e.editForm(t.row)}}},[e._v("从机 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:remove"],expression:"['iot:temp:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{staticStyle:{"font-size":"medium"},attrs:{title:e.title,visible:e.open,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"模板名称",prop:"templateName",size:"small"}},[a("el-input",{attrs:{placeholder:"请输入模板名称"},model:{value:e.form.templateName,callback:function(t){e.$set(e.form,"templateName",t)},expression:"form.templateName"}})],1),a("el-form-item",{attrs:{label:"采集方式",prop:"pollingMethod"}},[a("el-radio-group",{on:{change:function(t){return e.typeChange(e.form.pollingMethod)}},model:{value:e.form.pollingMethod,callback:function(t){e.$set(e.form,"pollingMethod",t)},expression:"form.pollingMethod"}},e._l(e.dict.type.data_collect_type,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1),e.form.isAdd?a("div",{staticClass:"dialog-footer",attrs:{slot:"footer",align:"center"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("下一步,配置从机和变量")])],1):a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")])],1)],1)],1)],1)},o=[],l=a("5530"),i=(a("d81d"),a("14d9"),a("38da")),r={name:"Temp",dicts:["data_collect_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,tempList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,templateName:null,type:null,pollingMethod:null,slaveTotal:null,pointTotal:null,share:null,userId:null},form:{pollingMethod:"0",isAdd:!0},rules:{templateName:[{required:!0,message:"模板名称不能为空",trigger:"blur"}],pollingMethod:[{required:!0,message:"采集方式",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(i["f"])(this.queryParams).then((function(t){e.tempList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={templateId:null,templateName:null,type:null,pollingMethod:"0",slaveTotal:null,pointTotal:null,share:null,createTime:null,createBy:null,updateTime:null,updateBy:null,userId:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.templateId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加变量模板"},handleUpdate:function(e){var t=this;this.reset();var a=e.templateId||this.ids;Object(i["d"])(a).then((function(e){t.form=e.data,t.form.pollingMethod=t.form.pollingMethod+"",t.open=!0,t.form.isAdd=!1,t.title="修改变量模板"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.templateId?Object(i["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.$router.push({path:0==e.form.pollingMethod?"/iot/varTemp-edit/point/":"/iot/varTemp-edit/mjpoint/",query:{templateId:t.data,templateName:e.form.templateName,pollingMethod:e.form.pollingMethod,share:e.form.share,isOpen:1}})})))}))},editForm:function(e){this.$router.push({path:0===e.pollingMethod?"/iot/varTemp-edit/point/":"/iot/varTemp-edit/mjpoint/",query:{templateId:e.templateId,templateName:e.templateName,pollingMethod:e.pollingMethod,share:e.share,isOpen:2}})},handleDelete:function(e){var t=this,a=e.templateId||this.ids;this.$modal.confirm('是否确认删除变量模板编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/temp/export",Object(l["a"])({},this.queryParams),"temp_".concat((new Date).getTime(),".xlsx"))},typeChange:function(e){this.form.pollingMethod=e}}},s=r,m=a("2877"),u=Object(m["a"])(s,n,o,!1,null,null,null);t["default"]=u.exports}}]);