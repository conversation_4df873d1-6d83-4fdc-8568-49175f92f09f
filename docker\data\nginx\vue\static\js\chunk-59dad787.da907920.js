(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-59dad787"],{5911:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"monitor-cache"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{staticStyle:{"margin-bottom":"19px"},attrs:{span:24}},[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.232015-0")))])]),a("div",{staticClass:"el-table el-table--enable-row-hover el-table--medium"},[a("table",{staticStyle:{width:"100%"},attrs:{cellspacing:"0"}},[a("tbody",[a("tr",[a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-1")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.redis_version))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-2")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s("standalone"==e.cache.info.redis_mode?"单机":"集群"))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-3")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.tcp_port))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-4")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.connected_clients))]):e._e()])]),a("tr",[a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-5")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.uptime_in_days))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-6")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.used_memory_human))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-7")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(parseFloat(e.cache.info.used_cpu_user_children).toFixed(2)))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-8")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.maxmemory_human))]):e._e()])]),a("tr",[a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-9")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s("0"==e.cache.info.aof_enabled?"否":"是"))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-10")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.rdb_last_bgsave_status))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-11")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.dbSize?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.dbSize))]):e._e()]),a("td",{staticClass:"el-table__cell is-leaf"},[a("div",{staticClass:"cell"},[e._v(e._s(e.$t("system.cache.232015-12")))])]),a("td",{staticClass:"el-table__cell is-leaf"},[e.cache.info?a("div",{staticClass:"cell"},[e._v(e._s(e.cache.info.instantaneous_input_kbps)+"kps/"+e._s(e.cache.info.instantaneous_output_kbps)+"kps")]):e._e()])])])])])])],1),a("el-col",{attrs:{span:12}},[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.232015-13")))])]),a("div",{ref:"commandstats",staticStyle:{height:"420px"}})])],1),a("el-col",{attrs:{span:12}},[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.232015-14")))])]),a("div",{ref:"usedmemory",staticStyle:{height:"420px"}})])],1)],1)],1)},c=[],l=a("ceee"),i=a("313e"),n={name:"Cache",data:function(){return{commandstats:null,usedmemory:null,cache:[]}},created:function(){this.getList(),this.openLoading()},methods:{getList:function(){var e=this;Object(l["d"])().then((function(t){e.cache=t.data,e.$modal.closeLoading(),console.log(t.data.commandStats,"hhhh"),e.commandstats=i["init"](e.$refs.commandstats,"macarons"),e.commandstats.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},series:[{name:e.$t("system.cache.232015-15"),type:"pie",roseType:"radius",radius:[15,95],center:["50%","38%"],data:t.data.commandStats,animationEasing:"cubicInOut",animationDuration:1e3}]}),e.usedmemory=i["init"](e.$refs.usedmemory,"macarons"),e.usedmemory.setOption({tooltip:{formatter:"{b} <br/>{a} : "+e.cache.info.used_memory_human},series:[{name:e.$t("system.cache.232015-16"),type:"gauge",min:0,max:1e3,detail:{formatter:e.cache.info.used_memory_human},data:[{value:parseFloat(e.cache.info.used_memory_human),name:e.$t("system.cache.232015-17")}]}]})}))},openLoading:function(){this.$modal.loading(this.$t("system.server.890786-32"))}}},o=n,_=(a("87cf"),a("2877")),d=Object(_["a"])(o,s,c,!1,null,"99a74e6c",null);t["default"]=d.exports},"741e":function(e,t,a){},"87cf":function(e,t,a){"use strict";a("741e")},ceee:function(e,t,a){"use strict";a.d(t,"d",(function(){return c})),a.d(t,"g",(function(){return l})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"b",(function(){return _})),a.d(t,"a",(function(){return d}));var s=a("b775");function c(){return Object(s["a"])({url:"/monitor/cache",method:"get"})}function l(){return Object(s["a"])({url:"/monitor/cache/getNames",method:"get"})}function i(e){return Object(s["a"])({url:"/monitor/cache/getKeys/"+e,method:"get"})}function n(e,t){return Object(s["a"])({url:"/monitor/cache/getValue/"+e+"/"+t,method:"get"})}function o(e){return Object(s["a"])({url:"/monitor/cache/clearCacheName/"+e,method:"delete"})}function _(e){return Object(s["a"])({url:"/monitor/cache/clearCacheKey/"+e,method:"delete"})}function d(){return Object(s["a"])({url:"/monitor/cache/clearCacheAll",method:"delete"})}}}]);