(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54c59123"],{"330c":function(e,t,a){},"9f66":function(e,t,a){"use strict";a.r(t);var c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"monitor-cache-list"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-card",{staticStyle:{height:"calc(100vh - 90px)"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.list.093478-0")))]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-refresh-right"},on:{click:function(t){return e.refreshCacheNames()}}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"cacheTable",staticStyle:{width:"100%"},attrs:{data:e.cacheNames,border:!1,height:e.tableHeight,"highlight-current-row":""},on:{"row-click":e.getCacheKeys}},[a("el-table-column",{attrs:{label:e.$t("system.cache.list.093478-1"),width:"55",align:"center",type:"index"}}),a("el-table-column",{attrs:{label:e.$t("system.cache.list.093478-2"),align:"left",prop:"cacheName","show-overflow-tooltip":!0,formatter:e.nameFormatter}}),a("el-table-column",{attrs:{label:e.$t("remark"),align:"left",prop:"remark","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:e.$t("opation"),width:"65",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:cache:remove"],expression:"['monitor:cache:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(a){return a.stopPropagation(),e.handleClearCacheName(t.row)}}},[e._v(e._s(e.$t("del")))])]}}])})],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-card",{staticStyle:{height:"calc(100vh - 90px)"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.list.093478-3")))]),a("el-button",{staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-refresh-right"},on:{click:function(t){return e.refreshCacheKeys()}}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.subLoading,expression:"subLoading"}],ref:"keyTable",staticStyle:{width:"100%"},attrs:{data:e.cacheKeys,border:!1,height:e.tableHeight,"highlight-current-row":""},on:{"row-click":e.handleCacheValue}},[a("el-table-column",{attrs:{label:e.$t("system.cache.list.093478-1"),width:"60",align:"center",type:"index"}}),a("el-table-column",{attrs:{label:e.$t("system.cache.list.093478-4"),align:"left","show-overflow-tooltip":!0,formatter:e.keyFormatter}}),a("el-table-column",{attrs:{label:e.$t("opation"),width:"65",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:cache:remove"],expression:"['monitor:cache:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(a){return a.stopPropagation(),e.handleClearCacheKey(t.row)}}},[e._v(e._s(e.$t("del")))])]}}])})],1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-card",{staticStyle:{height:"calc(100vh - 90px)"},attrs:{bordered:!1}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[e._v(e._s(e.$t("system.cache.list.093478-5")))]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:cache:remove"],expression:"['monitor:cache:remove']"}],staticStyle:{float:"right",padding:"3px 0"},attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(t){return e.handleClearCacheAll()}}},[e._v(" "+e._s(e.$t("system.cache.list.093478-6"))+" ")])],1),a("el-form",{attrs:{model:e.cacheForm}},[a("el-row",{attrs:{gutter:32}},[a("el-col",{attrs:{offset:1,span:22}},[a("el-form-item",{attrs:{label:e.$t("system.cache.list.093478-7"),prop:"cacheName"}},[a("el-input",{attrs:{readOnly:!0},model:{value:e.cacheForm.cacheName,callback:function(t){e.$set(e.cacheForm,"cacheName",t)},expression:"cacheForm.cacheName"}})],1)],1),a("el-col",{attrs:{offset:1,span:22}},[a("el-form-item",{attrs:{label:e.$t("system.cache.list.093478-8"),prop:"cacheKey"}},[a("el-input",{attrs:{readOnly:!0},model:{value:e.cacheForm.cacheKey,callback:function(t){e.$set(e.cacheForm,"cacheKey",t)},expression:"cacheForm.cacheKey"}})],1)],1),a("el-col",{attrs:{offset:1,span:22}},[a("el-form-item",{attrs:{label:e.$t("system.cache.list.093478-9"),prop:"cacheValue"}},[a("el-input",{attrs:{type:"textarea",rows:8,readOnly:!0},model:{value:e.cacheForm.cacheValue,callback:function(t){e.$set(e.cacheForm,"cacheValue",t)},expression:"cacheForm.cacheValue"}})],1)],1)],1)],1)],1)],1)],1)],1)},r=[],n=(a("ac1f"),a("5319"),a("ceee")),s={name:"CacheList",data:function(){return{cacheNames:[],cacheKeys:[],cacheForm:{},loading:!0,subLoading:!1,nowCacheName:"",tableHeight:window.innerHeight-200}},created:function(){this.getCacheNames()},methods:{getCacheNames:function(){var e=this;this.loading=!0,Object(n["g"])().then((function(t){200===t.code&&(e.cacheNames=t.data,e.cacheNames&&e.cacheNames.length>0&&(e.$refs.cacheTable.setCurrentRow(e.cacheNames[0]),e.getCacheKeys(e.cacheNames[0]))),e.loading=!1}))},refreshCacheNames:function(){this.getCacheNames(),this.$modal.msgSuccess(this.$t("system.cache.list.093478-10"))},handleClearCacheName:function(e){var t=this,a=e.cacheName;this.$modal.confirm(this.$t("system.cache.list.093478-16",[a.replace(/:/g,"")])).then((function(){return Object(n["c"])(a)})).then((function(e){200===e.code?(t.getCacheKeys(),t.$modal.msgSuccess(t.$t("system.cache.list.093478-12"))):t.$modal.msgError(t.$t("system.cache.list.093478-13"))})).catch((function(){}))},getCacheKeys:function(e){var t=this;this.cacheKeys=[],this.cacheForm={};var a=void 0!==e?e.cacheName:this.nowCacheName;""!==a&&(this.subLoading=!0,Object(n["f"])(a).then((function(e){200===e.code&&(t.cacheKeys=e.data,t.nowCacheName=a,t.cacheKeys&&t.cacheKeys.length>0&&(t.$refs.keyTable.setCurrentRow(t.cacheKeys[0]),t.handleCacheValue(t.cacheKeys[0]))),t.subLoading=!1})))},refreshCacheKeys:function(){this.getCacheKeys(),this.$modal.msgSuccess(this.$t("system.cache.list.093478-10"))},handleClearCacheKey:function(e){var t=this;this.$modal.confirm(this.$t("system.cache.list.093478-16",[e])).then((function(){return Object(n["b"])(e)})).then((function(e){200===e.code?(t.getCacheKeys(),t.$modal.msgSuccess(t.$t("system.cache.list.093478-12"))):t.$modal.msgError(t.$t("system.cache.list.093478-13"))})).catch((function(){}))},nameFormatter:function(e){return e.cacheName.replace(":","")},keyFormatter:function(e){return e.replace(this.nowCacheName,"")},handleCacheValue:function(e){var t=this;Object(n["e"])(this.nowCacheName,e).then((function(e){t.cacheForm=e.data}))},handleClearCacheAll:function(){var e=this;this.$modal.confirm(this.$t("system.cache.list.093478-17")).then((function(){return Object(n["a"])()})).then((function(t){200===t.code?e.$modal.msgSuccess(e.$t("system.cache.list.093478-14")):e.$modal.msgError(e.$t("system.cache.list.093478-15"))})).catch((function(){}))}}},o=s,l=(a("c774"),a("2877")),i=Object(l["a"])(o,c,r,!1,null,"e95cace0",null);t["default"]=i.exports},c774:function(e,t,a){"use strict";a("330c")},ceee:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"g",(function(){return n})),a.d(t,"f",(function(){return s})),a.d(t,"e",(function(){return o})),a.d(t,"c",(function(){return l})),a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return h}));var c=a("b775");function r(){return Object(c["a"])({url:"/monitor/cache",method:"get"})}function n(){return Object(c["a"])({url:"/monitor/cache/getNames",method:"get"})}function s(e){return Object(c["a"])({url:"/monitor/cache/getKeys/"+e,method:"get"})}function o(e,t){return Object(c["a"])({url:"/monitor/cache/getValue/"+e+"/"+t,method:"get"})}function l(e){return Object(c["a"])({url:"/monitor/cache/clearCacheName/"+e,method:"delete"})}function i(e){return Object(c["a"])({url:"/monitor/cache/clearCacheKey/"+e,method:"delete"})}function h(){return Object(c["a"])({url:"/monitor/cache/clearCacheAll",method:"delete"})}}}]);