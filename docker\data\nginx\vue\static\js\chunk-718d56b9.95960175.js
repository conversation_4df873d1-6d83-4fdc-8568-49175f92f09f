(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-718d56b9"],{"75cd":function(e,t,r){},"7fbd":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"center_bottom"},[r("div",[r("dv-scroll-board",{staticStyle:{width:"360px",height:"175px"},attrs:{config:e.config}})],1),r("div",{staticStyle:{display:"flex",height:"115px","margin-top":"30px"}},[r("div",[r("dv-water-level-pond",{staticStyle:{width:"115px",height:"100%"},attrs:{config:e.configCpu}}),r("div",{staticStyle:{"text-align":"center","margin-top":"10px",color:"#23cdd8","font-weight":"600"}},[e._v("CPU")])],1),r("div",{staticStyle:{margin:"0 20px"}},[r("dv-water-level-pond",{staticStyle:{width:"115px",height:"100%"},attrs:{config:e.configMemery}}),r("div",{staticStyle:{"text-align":"center","margin-top":"10px",color:"#23cdd8","font-weight":"600"}},[e._v("内存")])],1),r("div",{},[r("dv-water-level-pond",{staticStyle:{width:"115px",height:"100%"},attrs:{config:e.configDisk}}),r("div",{staticStyle:{"text-align":"center","margin-top":"10px",color:"#23cdd8","font-weight":"600"}},[e._v("系统盘")])],1)])])},i=[],s=(r("b0c0"),r("b680"),r("a9e3"),r("ac1f"),r("5319"),r("cc0b")),c={data:function(){return{timer:null,config:{},data:[["服务器名称",""],["服务器IP",""],["操作系统",""],["系统架构",""],["CPU核心数",""],["服务器内存",""],["Java名称",""],["Java版本",""],["Java启动时间",""],["Java运行时长",""],["Java占用内存",""],["Java总内存",""]],server:{jvm:{name:"",version:"",startTime:"",runTime:"",used:"",total:100},sys:{computerName:"",osName:"",computerIp:"",osArch:""},cpu:{cpuNum:1},mem:{total:2}},configCpu:{data:[50],shape:"roundRect",formatter:"{value}%",waveHeight:10},configMemery:{data:[50],shape:"roundRect",formatter:"{value}%",waveHeight:10},configDisk:{data:[50],shape:"roundRect",formatter:"{value}%",waveHeight:10}}},props:{},mounted:function(){this.getServer()},beforeDestroy:function(){this.clearData()},methods:{getServer:function(){var e=this;Object(s["a"])().then((function(t){e.server=t.data,e.config={rowNum:6,oddRowBGC:"",evenRowBGC:"",columnWidth:[105,230],data:[["服务器名：",e.server.sys.computerName],["服务器IP：",e.server.sys.computerIp],["操作系统：",e.server.sys.osName],["系统架构：",e.server.sys.osArch],["CPU核心：",e.server.cpu.cpuNum],["系统内存：",e.server.mem.total],["Java名称：",e.server.jvm.name],["Java版本：",e.server.jvm.version],["启动时间：",e.server.jvm.startTime],["运行时长：",e.server.jvm.runTime],["运行内存：",e.server.jvm.used],["JVM总内存：",e.server.jvm.total]]};var r=(e.server.cpu.used+e.server.cpu.sys)/(e.server.cpu.used+e.server.cpu.sys+e.server.cpu.free)*100;e.configCpu={data:[r.toFixed(1),r.toFixed(1)-10],shape:"roundRect",formatter:"{value}%",waveHeight:10};var a=e.server.mem.used/(e.server.mem.used+e.server.mem.free)*100;e.configMemery={data:[a.toFixed(1),a.toFixed(1)-10],shape:"roundRect",formatter:"{value}%",waveHeight:10};var i=Number(e.server.sysFiles[0].used.replace("GB",""))/(Number(e.server.sysFiles[0].used.replace("GB",""))+Number(e.server.sysFiles[0].free.replace("GB","")))*100;e.configDisk={data:[i.toFixed(1),i.toFixed(1)-10],shape:"roundRect",formatter:"{value}%",waveHeight:10},e.switper()}))},clearData:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},switper:function(){var e=this;if(!this.timer){var t=function(t){e.getServer()};this.timer=setInterval(t,6e4)}}}},o=c,n=(r("b926"),r("2877")),v=Object(n["a"])(o,a,i,!1,null,"0c97b1ca",null);t["default"]=v.exports},b926:function(e,t,r){"use strict";r("75cd")},cc0b:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));var a=r("b775");function i(){return Object(a["a"])({url:"/monitor/server",method:"get"})}}}]);