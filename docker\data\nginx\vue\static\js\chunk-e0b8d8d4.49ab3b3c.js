(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e0b8d8d4"],{"07ac":function(t,e,a){var i=a("23e7"),n=a("6f53").values;i({target:"Object",stat:!0},{values:function(t){return n(t)}})},"0bc2":function(t,e,a){"use strict";a.d(e,"b",(function(){return n})),a.d(e,"a",(function(){return o}));var i=a("b775");function n(t){return Object(i["a"])({url:"/iot/runtime/service/invoke",method:"post",data:t})}function o(t){return Object(i["a"])({url:"/iot/runtime/funcLog",method:"get",params:t})}},"2fab":function(t,e,a){"use strict";a("d8d5")},"466d":function(t,e,a){"use strict";var i=a("c65b"),n=a("d784"),o=a("825a"),r=a("7234"),s=a("50c4"),l=a("577e"),c=a("1d80"),u=a("dc4a"),d=a("8aa5"),m=a("14c3");n("match",(function(t,e,a){return[function(e){var a=c(this),n=r(e)?void 0:u(e,t);return n?i(n,e,a):new RegExp(e)[t](l(a))},function(t){var i=o(this),n=l(t),r=a(e,i,n);if(r.done)return r.value;if(!i.global)return m(i,n);var c=i.unicode;i.lastIndex=0;var u,p=[],f=0;while(null!==(u=m(i,n))){var h=l(u[0]);p[f]=h,""===h&&(i.lastIndex=d(n,s(i.lastIndex),c)),f++}return 0===f?null:p}]}))},"584f":function(t,e,a){"use strict";a.d(e,"l",(function(){return n})),a.d(e,"q",(function(){return o})),a.d(e,"m",(function(){return r})),a.d(e,"n",(function(){return s})),a.d(e,"k",(function(){return l})),a.d(e,"f",(function(){return c})),a.d(e,"c",(function(){return u})),a.d(e,"g",(function(){return d})),a.d(e,"i",(function(){return m})),a.d(e,"d",(function(){return p})),a.d(e,"r",(function(){return f})),a.d(e,"o",(function(){return h})),a.d(e,"p",(function(){return v})),a.d(e,"h",(function(){return b})),a.d(e,"a",(function(){return g})),a.d(e,"s",(function(){return y})),a.d(e,"b",(function(){return x})),a.d(e,"e",(function(){return _})),a.d(e,"j",(function(){return w}));var i=a("b775");function n(t){return Object(i["a"])({url:"/iot/device/list",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:t})}function l(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function c(t){return Object(i["a"])({url:"/iot/device/"+t,method:"get"})}function u(t){return Object(i["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function d(t){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function m(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function p(t,e){return Object(i["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function f(t){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+t,method:"post"})}function h(){return Object(i["a"])({url:"",method:"get"})}function v(){return Object(i["a"])({url:"",method:"get"})}function b(t){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function g(t){return Object(i["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/iot/device",method:"put",data:t})}function x(t){return Object(i["a"])({url:"/iot/device/"+t,method:"delete"})}function _(t){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:t})}function w(t){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}},"6f53":function(t,e,a){var i=a("83ab"),n=a("e330"),o=a("df75"),r=a("fc6a"),s=a("d1e7").f,l=n(s),c=n([].push),u=function(t){return function(e){var a,n=r(e),s=o(n),u=s.length,d=0,m=[];while(u>d)a=s[d++],i&&!l(n,a)||c(m,t?[a,n[a]]:n[a]);return m}};t.exports={entries:u(!0),values:u(!1)}},8448:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"6px"}},[a("el-row",{attrs:{gutter:5}},[a("el-col",{attrs:{xs:24,sm:12,md:12,lg:7,xl:6}},[a("el-card",{staticStyle:{"min-height":"800px","margin-bottom":"5px"}},[a("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-10px"},attrs:{model:t.queryParams,inline:!0,size:"mini"}},[a("el-form-item",{attrs:{prop:"deviceName"}},[a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入设备名称",clearable:"",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.deviceName,callback:function(e){t.$set(t.queryParams,"deviceName",e)},expression:"queryParams.deviceName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.deviceList,"highlight-current-row":"",size:"mini"},on:{"row-click":t.rowClick}},[a("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("input",{attrs:{type:"radio"},domProps:{checked:t.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:"设备名称(编号)",align:"center","header-align":"center",prop:"deviceName,serialNumber"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.deviceName)+" "),a("br"),t._v(" ("+t._s(e.row.serialNumber)+") ")]}}])}),a("el-table-column",{attrs:{label:"子设备",align:"center",prop:"subDeviceCount",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.subDeviceCount)+" ")]}}])}),a("el-table-column",{attrs:{label:"轮询状态",align:"center",prop:"status",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":3,"inactive-value":4,disabled:!t.editPermission},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 10px"},attrs:{small:"",layout:"total,prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1)],1),a("el-col",{attrs:{xs:24,sm:12,md:12,lg:9,xl:8}},[a("el-card",{staticStyle:{"min-height":"800px","margin-bottom":"5px",padding:"0 10px"}},[a("div",{staticClass:"phone"},[a("div",{staticClass:"phone-container"},[a("div",{staticClass:"phone-title"},[t._v("Modbus设备模拟器")]),a("div",{ref:"messageContent",staticClass:"messageContent"},[t._l(t.messageList,(function(e,i){return a("div",{key:i},[a("div",{class:"receive"==e.direction?"messageReceive":"messageSend"},[a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[a("div",{staticStyle:{width:"190px"}},[a("span",{staticStyle:{"font-weight":"600","line-height":"24px"}},[t._v("时间:")]),t._v(" "+t._s(e.ts)+" ")]),a("div",{staticStyle:{width:"50px"}},[t._v("Qos: "+t._s(e.qos))])]),a("div",[a("span",{staticStyle:{"font-weight":"600","line-height":"24px"}},[t._v("主题:")]),t._v(" "+t._s(e.topic)+" ")]),a("div",[a("span",{staticStyle:{"font-weight":"600","line-height":"24px"}},[t._v("内容:")]),t._v(" "+t._s(e.data)+" ")]),a("div")])])})),a("div",{staticStyle:{height:"200px",display:"flex",width:"100%"}})],2),a("div",{staticClass:"messageBottom"},[a("el-form",{attrs:{inline:!0,size:"mini",form:"simulateForm"}},[a("el-form-item",{staticClass:"adaptWidth",staticStyle:{width:"100%"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择Payload"},on:{change:t.payloadTypeChange},model:{value:t.simulateForm.payloadType,callback:function(e){t.$set(t.simulateForm,"payloadType",e)},expression:"simulateForm.payloadType"}},[a("el-option",{attrs:{label:"JSON",value:"json"}}),a("el-option",{attrs:{label:"Hex",value:"hex"}}),a("el-option",{attrs:{label:"Base64",value:"base64"}}),a("el-option",{attrs:{label:"Plaintext",value:"plaintext"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择QoS"},on:{change:t.qosChange},model:{value:t.simulateForm.qos,callback:function(e){t.$set(t.simulateForm,"qos",e)},expression:"simulateForm.qos"}},[a("el-option",{attrs:{label:"0 (最多一次)",value:"0"}}),a("el-option",{attrs:{label:"1 (最少一次)",value:"1"}}),a("el-option",{attrs:{label:"2 (有且仅有一次)",value:"2"}})],1)],1)],1)],1),a("el-form-item",{staticClass:"adaptWidth",staticStyle:{"margin-top":"-10px",width:"100%"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择主题"},on:{change:t.topicChange},model:{value:t.simulateForm.topicSuffix,callback:function(e){t.$set(t.simulateForm,"topicSuffix",e)},expression:"simulateForm.topicSuffix"}},t._l(t.topics,(function(t){return a("el-option",{key:t.topicName,attrs:{label:t.desc,value:t.topicName}})})),1)],1),a("el-col",{attrs:{span:7}},[a("el-button",{attrs:{type:"primary"},on:{click:t.enDecode}},[t._v("报文编辑器")])],1)],1)],1),a("el-form-item",{staticClass:"adaptWidth",staticStyle:{"margin-top":"-10px",width:"100%"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:16}},[a("el-input",{attrs:{disabled:"",type:"textarea",rows:"3",placeholder:"请使用报文编辑器生成",resize:"none"},model:{value:t.simulateForm.data,callback:function(e){t.$set(t.simulateForm,"data",e)},expression:"simulateForm.data"}})],1),a("el-col",{attrs:{span:7}},[a("el-button",{staticStyle:{"margin-top":"2px",width:"92px",height:"62px"},attrs:{type:"success"},on:{click:t.simulateSend}},[t._v("发送")])],1)],1)],1)],1)],1)])])])],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:8,xl:10}},[a("el-tabs",{staticStyle:{flex:"1",height:"800px","margin-bottom":"5px"},attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.thingsType,callback:function(e){t.thingsType=e},expression:"thingsType"}},[a("el-tab-pane",{attrs:{label:"属性上报",name:"prop"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},t._l(t.runningData,(function(e,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{xs:24,sm:12,md:12,lg:12,xl:8}},[a("el-card",{staticClass:"elcard",staticStyle:{padding:"0px",height:"auto"},attrs:{shadow:"hover"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[t._v(" "+t._s(e.name)+" ")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[t._v(t._s(e.value))]),e.value?a("span",[t._v(t._s(e.datatype.unit||e.datatype.unitName))]):t._e()])]),a("div",[t._v(t._s(e.ts))]),null==e.name?a("div",{staticStyle:{"background-color":"#0f73ee",height:"10px",width:"10px"}}):t._e(),null==e.value||""==e.value?a("div",{staticStyle:{height:"20px",width:"10px"}}):t._e(),null==e.ts?a("div",{staticStyle:{height:"10px",width:"10px"}}):t._e()])],1)})),1)],1)],1),a("el-tab-pane",{attrs:{label:"服务下发",name:"function"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},[t._l(t.runningData,(function(e,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{":xs":24,sm:12,md:12,lg:12,xl:8}},[a("el-card",{staticClass:"elcard",staticStyle:{height:"auto"},attrs:{shadow:"hover"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[t._v(" "+t._s(e.name)+" ")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[t._v(t._s(e.value))]),e.value?a("span",[t._v(t._s(e.datatype.unit))]):t._e(),a("el-button",{staticStyle:{float:"right","margin-right":"-5px",padding:"3px 5px"},attrs:{type:"primary",plain:"",icon:"el-icon-s-promotion",size:"mini"},on:{click:function(a){return a.stopPropagation(),t.editFunc(e)}}},[t._v("发送")])],1)]),a("div",{staticClass:"card-bottom"},[a("span",[t._v(t._s(e.ts))])]),null==e.name?a("div",{staticStyle:{"background-color":"#0f73ee",height:"10px",width:"10px"}}):t._e(),null==e.value||""==e.value?a("div",{staticStyle:{height:"20px",width:"10px"}}):t._e(),null==e.ts?a("div",{staticStyle:{height:"10px",width:"10px"}}):t._e()])],1)})),a("el-empty",{directives:[{name:"show",rawName:"v-show",value:0==t.runningData.length,expression:"runningData.length == 0"}],attrs:{description:"暂无数据"}})],2)],1)],1),a("el-tab-pane",{attrs:{disabled:"",name:"slave"}},[a("span",{staticStyle:{"margin-left":"0px"},attrs:{slot:"label"},slot:"label"},[a("el-select",{attrs:{placeholder:"请选择设备从机",size:"mini"},on:{change:t.selectSlave},model:{value:t.params.slaveId,callback:function(e){t.$set(t.params,"slaveId",e)},expression:"params.slaveId"}},t._l(t.slaveList,(function(t){return a("el-option",{key:t.slaveId,attrs:{label:t.deviceName+"   ("+t.slaveId+")",value:t.slaveId}})})),1)],1)])],1)],1)],1),a("el-dialog",{staticStyle:{float:"right"},attrs:{title:"服务调用",visible:t.dialogValue,width:"30%"},on:{"update:visible":function(e){t.dialogValue=e}}},[a("el-form",{staticStyle:{height:"100%",padding:"0 20px"},attrs:{size:"mini"},model:{value:t.from,callback:function(e){t.from=e},expression:"from"}},[a("el-form-item",{attrs:{label:t.from.name,"label-width":"180px"}},["integer"==t.from.datatype.type||"decimal"==t.from.datatype.type?a("el-input",{staticStyle:{width:"50%"},attrs:{type:"number"},on:{input:function(e){return t.justicNumber()}},model:{value:t.from.value,callback:function(e){t.$set(t.from,"value",e)},expression:"from.value"}}):t._e(),"enum"==t.from.datatype.type?a("el-select",{on:{change:function(e){return t.changeSelect()}},model:{value:t.from.value,callback:function(e){t.$set(t.from,"value",e)},expression:"from.value"}},t._l(t.from.datatype.enumList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.text,value:t.value}})})),1):t._e(),"bool"===t.from.datatype.type?a("el-switch",{attrs:{"inline-prompt":""},model:{value:t.from.value,callback:function(e){t.$set(t.from,"value",e)},expression:"from.value"}}):t._e(),"integer"!=t.from.datatype.type&&"decimal"!=t.from.datatype.type||!t.from.datatype.type.unit||"un"==t.from.datatype.type.unit||"/"==t.from.datatype.type.unit?t._e():a("span",[t._v("（"+t._s(t.from.unit)+"）")]),"integer"==t.from.datatype.type||"decimal"==t.from.datatype.type?a("div",{staticClass:"range"},[t._v(" (数据范围:"+t._s("null"==t.from.datatype.max?"bool"==t.from.datatype.type?0:"":t.from.datatype.min)+" ~ "+t._s("null"==t.from.datatype.max?"bool"==t.from.datatype.type?1:"":t.from.datatype.max)+") ")]):t._e()],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogValue=!1}}},[t._v("取消")]),a("el-button",{attrs:{type:"primary",loading:t.btnLoading,disabled:!t.canSend},on:{click:t.sendService}},[t._v("确认")])],1)],1),a("el-dialog",{attrs:{title:"报文编辑器",visible:t.encodeDiaLog,width:"50%"},on:{"update:visible":function(e){t.encodeDiaLog=e}}},[a("el-form",[a("el-form-item",{attrs:{label:"请选择协议: "}},[a("el-select",{staticStyle:{width:"150px"},attrs:{disabled:"",placeholder:"请选择协议"},on:{change:t.changeProductCode},model:{value:t.codeFrom.protocolCode,callback:function(e){t.$set(t.codeFrom,"protocolCode",e)},expression:"codeFrom.protocolCode"}},t._l(t.protocolList,(function(t){return a("el-option",{key:t.protocolCode,attrs:{label:t.protocolName,value:t.protocolCode}})})),1),a("span",{staticStyle:{color:"#ffba00","margin-left":"20px"}},[t._v("暂时只支持modbus协议")])],1)],1),a("el-tabs",{attrs:{type:"border-card",value:"first"},on:{"tab-click":t.encodeTagClick}},[a("el-tab-pane",{attrs:{label:"报文解码",name:"first"}},[a("el-form",{attrs:{inline:!0,model:t.codeFrom,"label-width":"70px"}},[a("el-form-item",[a("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",rows:"5",placeholder:"输入待解析报文"},model:{value:t.codeFrom.payload,callback:function(e){t.$set(t.codeFrom,"payload",e)},expression:"codeFrom.payload"}})],1),a("el-form-item",{staticStyle:{display:"block"}},[a("el-button",{staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:function(e){return t.onSubmit(1,!1)}}},[t._v("解析")])],1),t.codeFrom.result?a("el-form-item",[a("div",{staticStyle:{width:"700px",color:"#00bb00"},domProps:{innerHTML:t._s(t.codeFrom.result)}})]):t._e()],1)],1),a("el-tab-pane",{attrs:{label:"读指令生成",name:"second"}},[a("el-form",{attrs:{"label-width":"85px"},model:{value:t.codeFrom,callback:function(e){t.codeFrom=e},expression:"codeFrom"}},[a("el-form-item",{attrs:{label:"从机地址",prop:"slaveId",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"从机地址,如:1"},model:{value:t.codeFrom.slaveId,callback:function(e){t.$set(t.codeFrom,"slaveId",e)},expression:"codeFrom.slaveId"}})],1),a("el-form-item",{attrs:{label:"寄存器地址",prop:"address",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"寄存器地址"},model:{value:t.codeFrom.address,callback:function(e){t.$set(t.codeFrom,"address",e)},expression:"codeFrom.address"}})],1),a("el-form-item",{attrs:{label:"功能码",prop:"code",size:"small"}},[a("el-select",{staticStyle:{width:"250px"},attrs:{placeholder:"请选择功能码"},model:{value:t.codeFrom.code,callback:function(e){t.$set(t.codeFrom,"code",e)},expression:"codeFrom.code"}},[a("el-option",{attrs:{label:"03-读保持寄存器",value:"3"}}),a("el-option",{attrs:{label:"04-读输入寄存器",value:"4"}})],1)],1),a("el-form-item",{attrs:{label:"读取个数",prop:"count",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"读取个数"},model:{value:t.codeFrom.count,callback:function(e){t.$set(t.codeFrom,"count",e)},expression:"codeFrom.count"}})],1),a("el-form-item",{staticStyle:{display:"block"}},[a("el-button",{staticStyle:{width:"100px"},attrs:{type:"primary"},on:{click:function(e){return t.onSubmit(2,!1)}}},[t._v("生成")]),a("el-button",{staticStyle:{width:"200px","margin-left":"20px"},attrs:{type:"success"},on:{click:function(e){return t.onSubmit(2,!0)}}},[t._v("生成并复制至发送框")])],1),t.codeFrom.result?a("el-form-item",[a("div",[t._v(" 报文: "),a("span",{staticStyle:{color:"#00bb00"}},[t._v(t._s(t.codeFrom.result))])])]):t._e()],1)],1),a("el-tab-pane",{attrs:{label:"写指令生成",name:"third"}},[a("el-form",{attrs:{"label-width":"85px"},model:{value:t.codeFrom,callback:function(e){t.codeFrom=e},expression:"codeFrom"}},[a("el-form-item",{attrs:{label:"从机地址",prop:"slaveId",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"从机地址,如:1"},model:{value:t.codeFrom.slaveId,callback:function(e){t.$set(t.codeFrom,"slaveId",e)},expression:"codeFrom.slaveId"}})],1),a("el-form-item",{attrs:{label:"寄存器地址",prop:"address",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"寄存器地址"},model:{value:t.codeFrom.address,callback:function(e){t.$set(t.codeFrom,"address",e)},expression:"codeFrom.address"}})],1),a("el-form-item",{attrs:{label:"写入值",prop:"value",size:"small"}},[a("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"输入值"},model:{value:t.codeFrom.writeData,callback:function(e){t.$set(t.codeFrom,"writeData",e)},expression:"codeFrom.writeData"}})],1),a("el-form-item",{attrs:{label:"功能码",prop:"code",size:"small"}},[a("div",{staticStyle:{color:"#0f73ee"}},[t._v("06")])]),a("el-form-item",{staticStyle:{display:"block"}},[a("el-button",{staticStyle:{width:"100px"},attrs:{type:"primary"},on:{click:function(e){return t.onSubmit(3,!1)}}},[t._v("生成")]),a("el-button",{staticStyle:{width:"200px","margin-left":"20px"},attrs:{type:"success"},on:{click:function(e){return t.onSubmit(3,!0)}}},[t._v("生成并复制至发送框")])],1),t.codeFrom.result?a("el-form-item",[a("div",{staticStyle:{width:"200px",height:"40px"}},[t._v(" 报文: "),a("span",{staticStyle:{color:"#00bb00"}},[t._v(t._s(t.codeFrom.result))])])]):t._e()],1)],1),a("el-tab-pane",{attrs:{label:"CRC生成/校验",name:"four"}},[a("el-form",{attrs:{inline:!0,model:t.codeFrom,"label-width":"70px"}},[a("el-form-item",[a("el-input",{staticStyle:{width:"600px"},attrs:{type:"textarea",rows:"5",placeholder:"待校验报文"},model:{value:t.codeFrom.payload,callback:function(e){t.$set(t.codeFrom,"payload",e)},expression:"codeFrom.payload"}})],1),a("el-form-item",{staticStyle:{display:"block"}},[a("el-button",{staticStyle:{width:"120px"},attrs:{type:"primary"},on:{click:function(e){return t.onSubmit(4,!1)}}},[t._v("生成")]),a("el-button",{staticStyle:{width:"120px","margin-left":"20px"},attrs:{type:"success"},on:{click:function(e){return t.onSubmit(5,!1)}}},[t._v("校验")])],1),t.codeFrom.result?a("el-form-item",[a("div",[t._v(" 结果: "),a("span",{staticStyle:{color:"#0f73ee"},domProps:{innerHTML:t._s(t.codeFrom.result)}})])]):t._e(),a("el-form-item",{staticStyle:{width:"700px"}})],1)],1)],1)],1)],1)},n=[],o=a("2909"),r=a("c7eb"),s=a("1da1"),l=(a("14d9"),a("a434"),a("e9c4"),a("b64b"),a("d3b7"),a("07ac"),a("25f0"),a("8a79"),a("159b"),a("584f")),c=(a("0bc2"),a("ed08"),a("a824"),a("ba95")),u=a("b213"),d=a("bb86"),m=a("e350"),p="integer",f="decimal",h="bool",v="enum",b={name:"Simulate",dicts:["iot_device_status","iot_is_enable"],components:{},data:function(){return{loading:!1,total:0,deviceList:[],queryParams:{pageNum:1,pageSize:12,deviceName:null,productId:null,groupId:null,productName:null,isSimulate:1},editPermission:!1,messageList:[],simulateForm:{payloadType:"hex",qos:"0"},selectDevice:{},dialogValue:!1,encodeDiaLog:!1,gridData:[],groupId:1,treeData:[],runningData:[],debounceGetRuntime:"",runtimeName:"",serialNumber:"",params:{serialNumber:void 0,type:1,slaveId:""},slaveList:[],thingsType:"prop",opationList:[],funVal:{},canSend:!1,functionName:{},btnLoading:!1,logList:[],showValue:"",from:{datatype:{type:""}},topics:[],codeFrom:{protocolCode:"MODBUS"},protocolList:[],device:{}}},created:function(){this.connectMqtt();var t=Object(m["a"])(["iot:device:edit"]);t&&(this.editPermission=!0)},methods:{connectMqtt:function(){var t=this;return Object(s["a"])(Object(r["a"])().mark((function e(){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(null!=t.$mqttTool.client){e.next=3;break}return e.next=3,t.$mqttTool.connect();case 3:t.mqttCallback(),t.getList(),t.getDownTopics(),t.getProtocol();case 7:case"end":return e.stop()}}),e)})))()},mqttCallback:function(){var t=this;this.$mqttTool.client.on("message",(function(e,a,i){var n,r=e.split("/");r[1],r[2];(a=JSON.parse(a.toString()),a)&&(e.endsWith("ws/service")&&(console.log("接收到【设备数据上报】主题：",a),t.updateData(a)),e.endsWith("ws/post/simulate")&&((n=t.messageList).push.apply(n,Object(o["a"])(a)),t.messageList.splice(0,a.length-1),t.messageList.length>30&&t.messageList.splice(0,10),t.scrollBottom()))}))},mqttSubscribe:function(t){var e="/"+t.productId+"/"+t.serialNumber+"/ws/post/simulate",a=[];t.subDeviceList.length>0&&t.subDeviceList.forEach((function(e){var i="/"+t.productId+"/"+e.serialNumber+"/ws/service";a.push(i)})),a.push(e),this.$mqttTool.subscribe(a)},getList:function(){var t=this;this.loading=!0,this.queryParams.params={},Object(l["n"])(this.queryParams).then((function(e){for(var a=0;a<e.rows.length;a++)e.rows[a].isSelect=!1;t.deviceList=e.rows,t.total=e.total,t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList(),this.runningData.length=0,this.slaveList=null,this.params.slaveId=""},getDownTopics:function(){var t=this,e={isSimulate:!0};Object(d["b"])(e).then((function(e){t.topics=e.data}))},rowClick:function(t){var e=this;return Object(s["a"])(Object(r["a"])().mark((function a(){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(null==t){a.next=15;break}return e.setRadioSelected(t.deviceId),e.selectDevice=t,e.serialNumber=e.selectDevice.serialNumber,e.params.productId=e.selectDevice.productId,e.params.deviceId=e.selectDevice.deviceId,a.next=8,e.getDeviceDetail();case 8:e.device=a.sent,e.slaveList=e.device.subDeviceList,e.params.serialNumber=e.selectDevice.serialNumber+"_"+e.device.slaveId,e.params.slaveId=e.device.slaveId,e.getRuntimeStatus(),e.getSimulateLog(),e.mqttSubscribe(e.device);case 15:case"end":return a.stop()}}),a)})))()},setRadioSelected:function(t){for(var e=0;e<this.deviceList.length;e++)this.deviceList[e].deviceId==t?this.deviceList[e].isSelect=!0:this.deviceList[e].isSelect=!1},getSimulateLog:function(){var t=this,e={serialNumber:this.params.serialNumber};Object(c["a"])(e).then((function(e){t.messageList=e.rows,t.$nextTick((function(){this.scrollBottom()}))}))},simulateSend:function(){this.params.serialNumber&&this.params.productId?this.simulateForm.topicSuffix?this.simulateForm.data?(this.simulateForm.topic="/"+this.params.productId+"/"+this.params.serialNumber+this.simulateForm.topicSuffix,this.simulateForm.direction="send",this.simulateForm.ts=this.getTime(),this.messageList.push(JSON.parse(JSON.stringify(this.simulateForm))),this.scrollBottom(),this.pushSimulateDown()):this.$modal.alert("设备模拟的消息内容不能为空"):this.$modal.alert("设备模拟的主题不能为空"):this.$modal.alert("请先选择设备")},pushSimulateDown:function(){var t=this,e={topic:this.simulateForm.topic,message:this.simulateForm.data,qos:this.simulateForm.qos};Object(d["e"])(e).then((function(e){t.$modal.success("指令下发成功")}))},scrollBottom:function(){var t=this.$refs.messageContent;t.scroll({top:t.scrollHeight,behavior:"smooth"})},qosChange:function(t){},topicChange:function(t){},enDecode:function(){this.encodeDiaLog=!0,this.reset()},payloadTypeChange:function(t){},getTime:function(){var t=new Date,e=t.getFullYear(),a=t.getMonth()+1,i=t.getDate(),n=t.getHours(),o=t.getMinutes(),r=t.getSeconds();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,e+"-"+a+"-"+i+" "+n+":"+o+":"+r},getRuntimeStatus:function(){var t=this;Object(l["h"])(this.params).then((function(e){t.runningData=e.data.thingsModels}))},getDeviceDetail:function(){var t=this;return new Promise((function(e,a){Object(l["f"])(t.params.deviceId).then((function(t){e(t.data)})).catch((function(t){a(t)}))}))},getSlaveList:function(){this.getRuntimeStatus()},selectSlave:function(){this.params.serialNumber=this.serialNumber+"_"+this.params.slaveId,this.getRuntimeStatus()},handleClick:function(){this.selectDevice&&("prop"===this.thingsType?this.params.type=1:"function"===this.thingsType&&(this.params.type=2))},updateParam:function(t){},editFunc:function(t){console.log("item",t),this.dialogValue=!0,this.canSend=!0,this.funVal={},this.getValueName(t),t.id=parseInt(t.id),this.from=t},getValueName:function(t){var e=this;switch(console.log("item",t),t.datatype.type){case v:var a=t.datatype.enumList;a.forEach((function(a){a.text===t.logValue&&(e.funVal[t.id]=a.text)}));break;case p:case f:case h:this.funVal[t.id]=t.logValue;break}},sendService:function(){var t=this;console.log("service",this.from);try{this.getShowValue(Object.values(this.funVal)[0]);var e={topic:"/"+this.params.productId+"/"+this.params.serialNumber+"/property/set/simulate",slaveId:this.params.slaveId,value:this.from.value,address:this.from.id};Object(d["e"])(e).then((function(e){200==e.code&&t.$message({type:"success",message:"服务调用成功!"})}))}finally{this.dialogValue=!1}},getShowValue:function(t){var e=this;switch(this.from.datatype.type){case v:var a=this.from.datatype.enumList;a.forEach((function(a){a.value===t&&(e.showValue=a.text)}));break;case p:case f:this.showValue=t;case h:this.showValue=1==t?this.from.datatype.trueText:this.from.datatype.falseText;break}},changeSelect:function(){this.$forceUpdate()},justicNumber:function(){if(this.canSend=!0,this.from.datatype.max<this.funVal[this.from.identity]||this.from.datatype.min>this.funVal[this.from.identity])return this.canSend=!1,!0;this.$forceUpdate()},updateData:function(t){var e=this;t&&t.forEach((function(t){e.runningData.some((function(a,i){if(t.slaveId===a.slaveId&&t.id==a.id){var n=e.runningData[i];return n.value=t.value,n.ts=t.ts,e.$set(e.runningData,i,n),!0}}))}))},handleStatusChange:function(t){var e=this,a=3===t.status?"启用":"停用",i={deviceId:t.deviceId,status:t.status,serialNumber:t.serialNumber,productId:t.productId};Object(l["s"])(i).then((function(t){e.$modal.msgSuccess(a+"成功")})).catch((function(){}))},encodeTagClick:function(){this.reset()},onSubmit:function(t,e){var a=this;this.codeFrom.type=t,2==t?this.codeFrom.writeData=0:3==t&&(this.codeFrom.code=6,this.codeFrom.count=0),Object(d["a"])(this.codeFrom).then((function(t){a.codeFrom.result=t.msg,e&&(a.simulateForm.data=a.codeFrom.result)}))},getProtocol:function(){var t=this,e={status:1};Object(u["d"])(e).then((function(e){t.protocolList=e.rows}))},changeProductCode:function(){},reset:function(){this.codeFrom={payload:null,slaveId:void 0,address:null,code:null,count:null,writeData:null,type:null,result:null,protocolCode:"MODBUS"},this.resetForm("codeFrom")}}},g=b,y=(a("2fab"),a("eb8e"),a("2877")),x=Object(y["a"])(g,i,n,!1,null,"e777c758",null);e["default"]=x.exports},"879e7":function(t,e,a){},"8a79":function(t,e,a){"use strict";var i=a("23e7"),n=a("e330"),o=a("06cf").f,r=a("50c4"),s=a("577e"),l=a("5a34"),c=a("1d80"),u=a("ab13"),d=a("c430"),m=n("".endsWith),p=n("".slice),f=Math.min,h=u("endsWith"),v=!d&&!h&&!!function(){var t=o(String.prototype,"endsWith");return t&&!t.writable}();i({target:"String",proto:!0,forced:!v&&!h},{endsWith:function(t){var e=s(c(this));l(t);var a=arguments.length>1?arguments[1]:void 0,i=e.length,n=void 0===a?i:f(r(a),i),o=s(t);return m?m(e,o,n):p(e,n-o.length,n)===o}})},a824:function(t,e,a){"use strict";a.d(e,"e",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r})),a.d(e,"f",(function(){return s})),a.d(e,"b",(function(){return l})),a.d(e,"d",(function(){return c}));var i=a("b775");function n(t){return Object(i["a"])({url:"/iot/salve/list",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/iot/salve/"+t,method:"get"})}function r(t){return Object(i["a"])({url:"/iot/salve",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/iot/salve",method:"put",data:t})}function l(t){return Object(i["a"])({url:"/iot/salve/"+t,method:"delete"})}function c(t){return Object(i["a"])({url:"/iot/salve/listByPId",method:"get",params:t})}},b213:function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r})),a.d(e,"e",(function(){return s})),a.d(e,"b",(function(){return l}));var i=a("b775");function n(t){return Object(i["a"])({url:"/iot/protocol/list",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/iot/protocol/"+t,method:"get"})}function r(t){return Object(i["a"])({url:"/iot/protocol",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/iot/protocol",method:"put",data:t})}function l(t){return Object(i["a"])({url:"/iot/protocol/"+t,method:"delete"})}},ba95:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var i=a("b775");function n(t){return Object(i["a"])({url:"/iot/simulate/list",method:"get",params:t})}},bb86:function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"b",(function(){return r})),a.d(e,"a",(function(){return s})),a.d(e,"e",(function(){return l}));var i=a("b775");a("bc3a"),a("5c96"),a("21a6"),a("5f87"),a("c38a");function n(t){return Object(i["a"])({url:"/iot/tool/register",headers:{isToken:!1},method:"post",data:t})}function o(t){return Object(i["a"])({url:"/iot/tool/userList",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/iot/tool/getTopics",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/iot/tool/decode",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/iot/tool/simulate",method:"get",params:t})}},d8d5:function(t,e,a){},e350:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));a("caad"),a("d3b7"),a("2532");var i=a("4360");function n(t){if(t&&t instanceof Array&&t.length>0){var e=i["a"].getters&&i["a"].getters.permissions,a=t,n="*:*:*",o=e.some((function(t){return n===t||a.includes(t)}));return!!o}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}},eb8e:function(t,e,a){"use strict";a("879e7")},ed08:function(t,e,a){"use strict";a.d(e,"b",(function(){return i})),a.d(e,"e",(function(){return n})),a.d(e,"c",(function(){return o})),a.d(e,"a",(function(){return r})),a.d(e,"f",(function(){return s})),a.d(e,"d",(function(){return l}));a("53ca"),a("d9e2"),a("a630"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("6062"),a("3ca3"),a("466d"),a("5319"),a("159b"),a("ddb0"),a("c38a");function i(t,e,a){var i,n,o,r,s,l=function l(){var c=+new Date-r;c<e&&c>0?i=setTimeout(l,e-c):(i=null,a||(s=t.apply(o,n),i||(o=n=null)))};return function(){for(var n=arguments.length,c=new Array(n),u=0;u<n;u++)c[u]=arguments[u];o=this,r=+new Date;var d=a&&!i;return i||(i=setTimeout(l,e)),d&&(s=t.apply(o,c),o=c=null),s}}function n(t,e){for(var a=Object.create(null),i=t.split(","),n=0;n<i.length;n++)a[i[n]]=!0;return e?function(t){return a[t.toLowerCase()]}:function(t){return a[t]}}var o="export default ",r={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function s(t){return t.replace(/( |^)[a-z]/g,(function(t){return t.toUpperCase()}))}function l(t){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(t)}}}]);