(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a990e6d8"],{"41d7":function(t,i,e){"use strict";e.r(i);var o=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{title:t.upload.title,visible:t.upload.importDeviceDialog,width:"500px","append-to-body":""},on:{"update:visible":function(i){return t.$set(t.upload,"importDeviceDialog",i)}}},[e("el-form",{ref:"importForm",attrs:{"label-position":"top",model:t.importForm,rules:t.importRules}},[e("el-form-item",{attrs:{label:t.$t("uploadFile"),prop:"fileList"}},[e("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:t.upload.headers,action:t.upload.url+"?productId="+t.productId+"&type="+("isSelectData"==t.justiceSelect?2:1),disabled:t.upload.isUploading,"on-progress":t.handleFileUploadProgress,"on-error":t.handleError,"on-success":t.handleFileSuccess,"auto-upload":!1,"on-change":t.handleChange,"on-remove":t.handleRemove,drag:""},model:{value:t.importForm.fileList,callback:function(i){t.$set(t.importForm,"fileList",i)},expression:"importForm.fileList"}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v(" "+t._s(t.$t("dragFileTips"))+" "),e("em",[t._v(t._s(t.$t("clickFileTips")))])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[e("div",{staticStyle:{"margin-top":"10px"}},[e("span",[t._v(t._s(t.$t("device.batch-import-dialog.850870-5")))])])])]),e("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:t.importTemplate}},[e("i",{staticClass:"el-icon-download"}),t._v(" "+t._s(t.$t("device.batch-import-dialog.850870-6"))+" ")])],1)],1),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:t.submitFileForm}},[t._v(t._s(t.$t("confirm")))]),e("el-button",{on:{click:function(i){t.upload.importDeviceDialog=!1}}},[t._v(t._s(t.$t("cancel")))])],1)],1)},a=[],l=(e("99af"),e("a9e3"),e("5f87")),s=e("7aa2"),r={name:"batchImport",props:{productId:{type:Number,default:0},justiceSelect:{type:String,default:"isSelectData"}},data:function(){return{type:1,importForm:{productId:null,fileList:[]},file:null,configList:[],upload:{importDeviceDialog:!1,title:this.$t("batchImport"),isUploading:!1,headers:{Authorization:"Bearer "+Object(l["a"])()},url:"/prod-api/modbus/config/importModbus"},importRules:{fileList:[{required:!0,message:this.$t("plzUploadFile"),trigger:"change"}]},loading:!1}},methods:{importTemplate:function(){var t="isSelectData"==this.justiceSelect?2:1,i="isSelectData"==this.justiceSelect?this.$t("product.components.batchImportModbus.745343-1"):this.$t("product.components.batchImportModbus.745343-0");this.download("/modbus/config/modbusTemplate?type="+t,{},"".concat(i,"_").concat((new Date).getTime(),".xlsx"))},handleChange:function(t,i){this.importForm.fileList=i,this.importForm.fileList&&this.$refs.importForm.clearValidate("fileList")},handleRemove:function(t,i){this.importForm.fileList=i,this.$refs.importForm.validateField("fileList")},handleFileUploadProgress:function(t,i,e){this.upload.isUploading=!0},handleError:function(t,i,e){this.upload.importDeviceDialog=!1,this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0})},handleFileSuccess:function(t,i,e){this.upload.importDeviceDialog=!1,this.upload.isUploading=!1,this.loading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0}),this.$parent.getIOList(),this.$parent.getDataList()},submitFileForm:function(){var t=this;this.$refs["importForm"].validate((function(i){i&&(t.upload.isUploading=!0,t.$refs.upload.submit(),t.getIOList(),setTimeout((function(){t.$emit("data-imported",t.configList)}),500))}))},getIOList:function(){var t=this,i={pageNum:1,pageSize:10,type:1,productId:this.productId};Object(s["b"])(i).then((function(i){t.configList=i.rows,t.total=i.total}))}}},n=r,d=e("2877"),p=Object(d["a"])(n,o,a,!1,null,null,null);i["default"]=p.exports},"7aa2":function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"a",(function(){return l}));var o=e("b775");function a(t){return Object(o["a"])({url:"/modbus/config/list",method:"get",params:t})}function l(t){return Object(o["a"])({url:"/modbus/config/addBatch",method:"post",data:t})}}}]);