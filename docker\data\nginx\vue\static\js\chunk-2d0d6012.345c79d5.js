(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6012"],{7168:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{prop:"logType"}},[i("el-select",{attrs:{placeholder:e.$t("device.device-log.798283-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.dict.type.iot_event_type,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{attrs:{prop:"identify"}},[i("el-input",{attrs:{placeholder:e.$t("device.device-log.798283-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identify,callback:function(t){e.$set(e.queryParams,"identify",t)},expression:"queryParams.identify"}})],1),i("el-form-item",[i("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":e.$t("device.device-log.798283-5"),"end-placeholder":e.$t("device.device-log.798283-6")},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.device-log.798283-7")))]),i("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.device-log.798283-8")))])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceLogList,border:!1}},[i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-9"),align:"center",prop:"logType","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_event_type,value:t.row.logType}})]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-10"),align:"center",prop:"logType","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.mode?i("el-tag",{attrs:{type:"primary"}},[e._v(e._s(e.$t("device.device-log.798283-11")))]):2==t.row.mode?i("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("device.device-log.798283-12")))]):i("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("device.device-log.798283-13")))])]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-14"),align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(t.row.createTime))])]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-2"),align:"left",prop:"identify","min-width":"120"}}),i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-15"),align:"left",prop:"logValue","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("div",{domProps:{innerHTML:e._s(e.formatValueDisplay(t.row))}})]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-log.798283-16"),align:"left",prop:"remark","min-width":"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null==t.row.remark?e.$t("device.device-log.798283-17"):t.row.remark)+" ")]}}])})],1),i("div",{staticStyle:{height:"60px"}},[i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1)},n=[],r=i("5530"),s=(i("b0c0"),i("a9e3"),i("b775"));function o(e){return Object(s["a"])({url:"/iot/event/list",method:"get",params:e})}var l={name:"DeviceLog",dicts:["iot_event_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList(),this.thingsModel=this.deviceInfo.cacheThingsModel)}},mounted:function(){this.deviceInfo=this.device,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList(),this.thingsModel=this.deviceInfo.cacheThingsModel)},data:function(){return{thingsModel:{},loading:!0,showSearch:!0,total:0,deviceLogList:[],queryParams:{pageNum:1,pageSize:10,logType:null,logValue:null,deviceId:null,serialNumber:null,deviceName:null,identify:null,isMonitor:null},daterangeTime:[]}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.addDateRange(this.queryParams,this.daterangeTime)).then((function(t){e.deviceLogList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.daterangeTime=[],this.handleQuery()},handleExport:function(){this.download("iot/event/export",Object(r["a"])({},this.queryParams),"eventLog_".concat((new Date).getTime(),".xlsx"))},formatValueDisplay:function(e){if(1==e.logType){var t=this.getThingsModelItem(1,e.identify);if(""!=t)return(t.parentName?"["+t.parentName+(t.arrayIndex?t.arrayIndex:"")+"] ":"")+t.name+'： <span style="color:#486FF2;">'+this.getThingsModelItemValue(t,e.logValue)+" "+(void 0!=t.datatype.unit?t.datatype.unit:"")+"</span>"}else if(2==e.logType){var i=this.getThingsModelItem(2,e.identify);if(""!=i)return(i.parentName?"["+i.parentName+(i.arrayIndex?i.arrayIndex:"")+"] ":"")+i.name+'： <span style="color:#486FF2">'+this.getThingsModelItemValue(i,e.logValue)+" "+(void 0!=i.datatype.unit?i.datatype.unit:"")+"</span>"}else{if(3==e.logType){var a=this.getThingsModelItem(3,e.identify);return""!=a?(a.parentName?"["+a.parentName+(a.arrayIndex?a.arrayIndex:"")+"] ":"")+a.name+'： <span style="color:#486FF2">'+this.getThingsModelItemValue(a,e.logValue)+" "+(void 0!=a.datatype.unit?a.datatype.unit:"")+"</span>":e.logValue}if(4==e.logType)return'<span style="font-weight:bold">设备升级</span>';if(5==e.logType)return'<span style="font-weight:bold">设备上线</span>';if(6==e.logType)return'<span style="font-weight:bold">设备离线</span>'}return""},getThingsModelItemValue:function(e,t){if("bool"==e.datatype.type){if("0"==t)return e.datatype.falseText;if("1"==t)return e.datatype.trueText}else if("enum"==e.datatype.type)for(var i=0;i<e.datatype.enumList.length;i++)if(t==e.datatype.enumList[i].value)return e.datatype.enumList[i].text;return t},getThingsModelItem:function(e,t){if(1==e&&this.thingsModel.properties)for(var i=0;i<this.thingsModel.properties.length;i++){if(this.thingsModel.properties[i].id==t)return this.thingsModel.properties[i];if("object"==this.thingsModel.properties[i].datatype.type)for(var a=0;a<this.thingsModel.properties[i].datatype.params.length;a++)if(this.thingsModel.properties[i].datatype.params[a].id==t)return this.thingsModel.properties[i].datatype.params[a].parentName=this.thingsModel.properties[i].name,this.thingsModel.properties[i].datatype.params[a];if("array"==this.thingsModel.properties[i].datatype.type&&this.thingsModel.properties[i].datatype.arrayType)if("object"==this.thingsModel.properties[i].datatype.arrayType){var n=t,r=0;t.indexOf("array_")>-1&&(r=t.substring(6,8),n=t.substring(9));for(var s=0;s<this.thingsModel.properties[i].datatype.params.length;s++)if(this.thingsModel.properties[i].datatype.params[s].id==n)return this.thingsModel.properties[i].datatype.params[s].arrayIndex=Number(r)+1,this.thingsModel.properties[i].datatype.params[s].parentName=this.thingsModel.properties[i].name,this.thingsModel.properties[i].datatype.params[s]}else for(var o=0;o<this.thingsModel.properties[i].datatype.arrayCount.length;o++)if(this.thingsModel.properties[i].id==realIdentity)return this.thingsModel.properties[i].arrayIndex=Number(arrayIndex)+1,this.thingsModel.properties[i].parentName=this.$t("device.device-log.798283-21"),this.thingsModel.properties[i]}else if(2==e&&this.thingsModel.functions)for(var l=0;l<this.thingsModel.functions.length;l++){if(this.thingsModel.functions[l].id==t)return this.thingsModel.functions[l];if("object"==this.thingsModel.functions[l].datatype.type)for(var d=0;d<this.thingsModel.functions[l].datatype.params.length;d++)if(this.thingsModel.functions[l].datatype.params[d].id==t)return this.thingsModel.functions[l].datatype.params[d].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[d];if("array"==this.thingsModel.functions[l].datatype.type&&this.thingsModel.functions[l].datatype.arrayType){var p=t,u=0;if(t.indexOf("array_")>-1&&(u=t.substring(6,8),p=t.substring(9)),"object"==this.thingsModel.functions[l].datatype.arrayType){for(var h=0;h<this.thingsModel.functions[l].datatype.params.length;h++)if(this.thingsModel.functions[l].datatype.params[h].id==p)return this.thingsModel.functions[l].datatype.params[h].arrayIndex=Number(u)+1,this.thingsModel.functions[l].datatype.params[h].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[h]}else for(var c=0;c<this.thingsModel.functions[l].datatype.arrayCount.length;c++)if(this.thingsModel.functions[l].id==p)return this.thingsModel.functions[l].arrayIndex=Number(u)+1,this.thingsModel.functions[l].parentName=this.$t("device.device-log.798283-21"),this.thingsModel.functions[l]}}else if(3==e&&this.thingsModel.events)for(var g=0;g<this.thingsModel.events.length;g++)if(this.thingsModel.events[g].id==t)return this.thingsModel.events[g];return""}}},d=l,p=i("2877"),u=Object(p["a"])(d,a,n,!1,null,null,null);t["default"]=u.exports}}]);