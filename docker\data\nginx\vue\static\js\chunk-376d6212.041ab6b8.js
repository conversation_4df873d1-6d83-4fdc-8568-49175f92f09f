(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-376d6212"],{"1e8b":function(t,e,n){"use strict";n.r(e);var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"user-info"},[n("el-descriptions",{attrs:{column:3}},[n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-1")}},[t._v(t._s(t.userInfo.userName))]),n("el-descriptions-item",{attrs:{label:t.$t("user.index.098976-13")}},[t._v(t._s(t.userInfo.phonenumber))]),n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-2")}},[t._v(t._s(t.userInfo.email))]),n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-3")}},[t._v(" "+t._s(t.posts?(t.userInfo.dept?t.userInfo.dept.deptName:"")+" / "+t.posts:""+(t.userInfo.dept?t.userInfo.dept.deptName:""))+" ")]),n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-4")}},[t._v(t._s(t.roles))]),n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-5")}},[t._v(t._s(t.userInfo.createTime))]),n("el-descriptions-item",{attrs:{label:t.$t("user.profile.index.894502-6")}},[t.wxBind?n("el-button",{attrs:{type:"warning",size:"mini"},on:{click:t.handleUnBindWeChat}},[t._v(t._s(t.$t("user.profile.index.894502-7")))]):n("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.handleBindWeChat}},[t._v(t._s(t.$t("user.profile.index.894502-8")))])],1)],1),n("el-dialog",{attrs:{title:"绑定微信",visible:t.isBindWeChat,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.isBindWeChat=e}}},[n("div",{staticClass:"bindWeChatDialog"},[n("div",{staticClass:"dec"},[t._v("请通过微信扫一扫，进行微信绑定。")]),n("div",{staticClass:"weChat"},[n("div",{staticStyle:{height:"200px"},attrs:{id:"weChatLogin"}})])]),n("div",{attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){t.isBindWeChat=!1}}},[t._v(t._s(t.$t("close")))])],1)]),n("el-dialog",{attrs:{title:t.$t("user.profile.index.894502-11"),visible:t.isUnBindWeChat,width:"600px","append-to-body":""},on:{"update:visible":function(e){t.isUnBindWeChat=e}}},[n("div",{staticClass:"unBindWeChatDialog"},[n("el-form",{attrs:{"label-width":"150px"}},[n("el-form-item",{attrs:{label:t.$t("user.profile.index.894502-12"),prop:"pasaward"}},[n("el-input",{staticStyle:{width:"80%"},attrs:{type:t.pwdtype},model:{value:t.unBindWeChat.password,callback:function(e){t.$set(t.unBindWeChat,"password",e)},expression:"unBindWeChat.password"}},[n("template",{slot:"suffix"},[n("span",{staticClass:"el-icon-view",staticStyle:{"margin-right":"8px"},on:{click:function(e){return t.pwdTypeChange()}}})])],2)],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{size:"small"},on:{click:function(e){t.isUnBindWeChat=!1}}},[t._v(t._s(t.$t("close")))]),n("el-button",{attrs:{size:"small",type:"primary"},on:{click:t.confirmUnBindWeChat}},[t._v(t._s(t.$t("confirm")))])],1)])],1)},r=[],i=n("5530"),o=n("c0c7"),a=n("7ded"),u={props:{user:{type:Object,default:{}},postGroup:{type:String,default:""},roleGroup:{type:String,default:""},wxbind:{type:Boolean,default:!1}},data:function(){return{isBindWeChat:!1,isUnBindWeChat:!1,pwdtype:"password",wxBind:this.wxbind,userInfo:this.user,posts:this.postGroup,roles:this.roleGroup,unBindWeChat:{password:"",verifyType:1}}},mounted:function(){this.getUser();var t=this.$route.query.wxBindMsgId;t?this.getWeChatBindMsg():console.log("此时没有进行绑定操作！");var e=document.createElement("script");e.type="text/javascript",e.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js",document.body.appendChild(e)},methods:{handleBindWeChat:function(){Object(o["h"])().then((function(t){var e=document.createElement("script");e.type="text/javascript",e.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js";var n=document.body.appendChild(e);n.onload=function(){new WxLogin({self_redirect:!1,id:"weChatLogin",appid:t.data.appid,scope:t.data.scope,redirect_uri:t.data.redirectUri,state:t.data.state,style:"black",href:"data:text/css;base64,LmltcG93ZXJCb3ggLnRpdGxlIHsKIGRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciB7CiBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGUgewogYm9yZGVyOm5vbmU7CiB3aWR0aDogMjAwcHg7CiBoZWlnaHQ6IDIwMHB4OwogbWFyZ2luOjAgYXV0bzsKfQouaW1wb3dlckJveCAuc3RhdHVzewogZGlzcGxheTogbm9uZQp9"})}})),this.isBindWeChat=!0},handleUnBindWeChat:function(){this.isUnBindWeChat=!0},pwdTypeChange:function(){"password"==this.pwdtype?this.pwdtype="text":this.pwdtype="password"},confirmUnBindWeChat:function(){var t=this,e=Object(i["a"])({},this.unBindWeChat);Object(o["n"])(e).then((function(e){200===e.code?(t.getUser(),t.$modal.msgSuccess(e.msg)):t.$modal.msgError(e.msg),t.isUnBindWeChat=!1}))},getUser:function(){var t=this;Object(o["k"])().then((function(e){200===e.code&&(t.userInfo=e.data,t.wxBind=e.wxBind,t.roles=e.roleGroup,t.posts=e.postGroup)}))},getWeChatBindMsg:function(){var t=this,e=this.$route.query.wxBindMsgId;Object(a["l"])(e).then((function(e){200===e.code?t.$modal.msgSuccess(e.msg):t.$modal.msgError(e.msg)}))}}},d=u,c=(n("d960"),n("2877")),l=Object(c["a"])(d,s,r,!1,null,"3c69c49e",null);e["default"]=l.exports},"343e":function(t,e,n){},c0c7:function(t,e,n){"use strict";n.d(e,"l",(function(){return i})),n.d(e,"o",(function(){return o})),n.d(e,"j",(function(){return a})),n.d(e,"i",(function(){return u})),n.d(e,"a",(function(){return d})),n.d(e,"q",(function(){return c})),n.d(e,"c",(function(){return l})),n.d(e,"m",(function(){return p})),n.d(e,"b",(function(){return f})),n.d(e,"h",(function(){return m})),n.d(e,"n",(function(){return h})),n.d(e,"k",(function(){return b})),n.d(e,"r",(function(){return g})),n.d(e,"s",(function(){return w})),n.d(e,"t",(function(){return y})),n.d(e,"f",(function(){return C})),n.d(e,"p",(function(){return v})),n.d(e,"d",(function(){return B})),n.d(e,"e",(function(){return x})),n.d(e,"g",(function(){return j}));var s=n("b775"),r=n("c38a");function i(t){return Object(s["a"])({url:"/system/user/list",method:"get",params:t})}function o(t){return Object(s["a"])({url:"/system/user/listTerminal",method:"get",params:t})}function a(t){return Object(s["a"])({url:"/system/user/"+Object(r["f"])(t),method:"get"})}function u(t){return Object(s["a"])({url:"/system/dept/getRole?deptId="+t,method:"get"})}function d(t){return Object(s["a"])({url:"/system/user",method:"post",data:t})}function c(t){return Object(s["a"])({url:"/system/user",method:"put",data:t})}function l(t){return Object(s["a"])({url:"/system/user/"+t,method:"delete"})}function p(t,e){var n={userId:t,password:e};return Object(s["a"])({url:"/system/user/resetPwd",method:"put",data:n})}function f(t,e){var n={userId:t,status:e};return Object(s["a"])({url:"/system/user/changeStatus",method:"put",data:n})}function m(){return Object(s["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(t){return Object(s["a"])({url:"/wechat/cancelBind",method:"post",data:t})}function b(){return Object(s["a"])({url:"/system/user/profile",method:"get"})}function g(t){return Object(s["a"])({url:"/system/user/profile",method:"put",data:t})}function w(t,e){var n={oldPassword:t,newPassword:e};return Object(s["a"])({url:"/system/user/profile/updatePwd",method:"put",params:n})}function y(t){return Object(s["a"])({url:"/system/user/profile/avatar",method:"post",data:t})}function C(t){return Object(s["a"])({url:"/system/user/authRole/"+t,method:"get"})}function v(t){return Object(s["a"])({url:"/system/user/authRole",method:"put",params:t})}function B(){return Object(s["a"])({url:"/system/user/deptTree",method:"get"})}function x(t){return Object(s["a"])({url:"/system/user/deptTree?showOwner="+t,method:"get"})}function j(t){return Object(s["a"])({url:"/system/user/getByDeptId",method:"get",params:t})}},d960:function(t,e,n){"use strict";n("343e")}}]);