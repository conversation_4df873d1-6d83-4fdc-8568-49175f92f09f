(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4fdc3fc4"],{"01a2":function(t,e,n){},"3c52":function(t,e,n){"use strict";n("01a2")},8076:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"player-webrtc"},[n("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-select",{attrs:{placeholder:t.$t("views.components.player.webrtc.08878-1"),clearable:""},on:{change:function(e){return t.changeChannel()}},model:{value:t.channelId,callback:function(e){t.channelId=e},expression:"channelId"}},t._l(t.channelList,(function(t){return n("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),n("div",{staticClass:"trank-wrap"},[n("el-button",{staticClass:"btn",attrs:{type:t.getBroadcastStatus(),disabled:-2===t.broadcastStatus||""===t.channelId,circle:"",icon:"el-icon-microphone"},on:{click:function(e){return t.broadcastStatusClick()}}}),n("div",{staticClass:"title-wrap"},[-2===t.broadcastStatus?n("span",[t._v(t._s(t.$t("views.components.player.webrtc.08878-2")))]):t._e(),-1===t.broadcastStatus?n("span",[t._v(t._s(t.$t("views.components.player.webrtc.08878-3")))]):t._e(),0===t.broadcastStatus?n("span",[t._v(t._s(t.$t("views.components.player.webrtc.08878-4")))]):t._e(),1===t.broadcastStatus?n("span",[t._v(t._s(t.$t("views.components.player.webrtc.08878-5")))]):t._e()])],1)],1)},s=[],c=(n("caad"),n("d81d"),n("2532"),n("e2de")),r={name:"webrtc",components:{},props:{device:{type:Object,default:null}},watch:{device:function(t,e){this.deviceInfo=t,this.deviceInfo.channelId&&(this.channelId=this.deviceInfo.channelId),this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.deviceId=this.device.serialNumber)}},data:function(){return{deviceInfo:null,deviceId:"",channelId:"",broadcastMode:!0,broadcastRtc:null,broadcastStatus:-1,channelList:[],queryParams:{pageNum:1,pageSize:10,deviceSipId:null,channelSipId:null}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.deviceId=this.device.serialNumber,this.getList()},destroyed:function(){},methods:{getList:function(){var t=this;Object(c["g"])(this.queryParams).then((function(e){t.channelList=e.rows.map((function(t){return{value:t.channelSipId,label:t.channelName}})),console.log(t.channelList)}))},changeChannel:function(){},getBroadcastStatus:function(){return-2==this.broadcastStatus||-1==this.broadcastStatus?"primary":0==this.broadcastStatus?"warning":1==this.broadcastStatus?"danger":void 0},broadcastStatusClick:function(){var t=this;-1==this.broadcastStatus?(this.broadcastStatus=0,Object(c["f"])(this.deviceId,this.channelId).then((function(e){if(console.log(e),200===e.code){var n=e.data;document.location.protocol.includes("https")?t.startwebrtc(n.rtcs):t.startwebrtc(n.rtc)}else t.$message({showClose:!0,message:e.msg,type:"error"})}))):1===this.broadcastStatus&&(this.broadcastStatus=-1,this.broadcastRtc.close())},startwebrtc:function(t){var e=this;this.broadcastRtc=new ZLMRTCClient.Endpoint({debug:!0,zlmsdpUrl:t,simulecast:!1,useCamera:!1,audioEnable:!0,videoEnable:!1,recvOnly:!1}),this.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_NOT_SUPPORT,(function(t){console.error("不支持webrtc",t),e.$message({showClose:!0,message:e.$t("views.components.player.webrtc.08878-6"),type:"error"}),e.broadcastStatus=-1})),this.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ICE_CANDIDATE_ERROR,(function(t){console.error("ICE 协商出错"),e.$message({showClose:!0,message:e.$t("views.components.player.webrtc.08878-7"),type:"error"}),e.broadcastStatus=-1})),this.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_OFFER_ANWSER_EXCHANGE_FAILED,(function(t){console.error("offer anwser 交换失败",t),e.$message({showClose:!0,message:e.$t("views.components.player.webrtc.08878-8")+t,type:"error"}),e.broadcastStatus=-1})),this.broadcastRtc.on(ZLMRTCClient.Events.WEBRTC_ON_CONNECTION_STATE_CHANGE,(function(t){console.log("状态改变",t),"connecting"===t?e.broadcastStatus=0:"connected"===t?(e.broadcastStatus=1,Object(c["j"])(e.deviceId,e.channelId).then((function(t){200===t.code&&console.log("开始推流！")}))):"disconnected"===t&&(e.broadcastStatus=-1)})),this.broadcastRtc.on(ZLMRTCClient.Events.CAPTURE_STREAM_FAILED,(function(t){console.log("捕获流失败",t),e.$message({showClose:!0,message:e.$t("views.components.player.webrtc.08878-9")+t,type:"error"}),e.broadcastStatus=-1}))},stopBroadcast:function(){var t=this;this.broadcastRtc.close(),this.broadcastStatus=-1,Object(c["l"])(this.deviceId,this.channelId).then((function(e){200==e.code?(t.broadcastStatus=-1,t.broadcastRtc.close()):t.$message({showClose:!0,message:e.msg,type:"error"})}))}}},o=r,i=(n("3c52"),n("2877")),l=Object(i["a"])(o,a,s,!1,null,"d3a31448",null);e["default"]=l.exports},e2de:function(t,e,n){"use strict";n.d(e,"g",(function(){return s})),n.d(e,"e",(function(){return c})),n.d(e,"a",(function(){return r})),n.d(e,"d",(function(){return o})),n.d(e,"k",(function(){return i})),n.d(e,"h",(function(){return l})),n.d(e,"c",(function(){return u})),n.d(e,"i",(function(){return d})),n.d(e,"b",(function(){return h})),n.d(e,"f",(function(){return b})),n.d(e,"j",(function(){return p})),n.d(e,"l",(function(){return f}));var a=n("b775");function s(t){return Object(a["a"])({url:"/sip/channel/list",method:"get",params:t})}function c(t){return Object(a["a"])({url:"/sip/channel/"+t,method:"get"})}function r(t,e){return Object(a["a"])({url:"/sip/channel/"+t,method:"post",data:e})}function o(t){return Object(a["a"])({url:"/sip/channel/"+t,method:"delete"})}function i(t,e){return Object(a["a"])({url:"/sip/player/play/"+t+"/"+e,method:"get"})}function l(t,e,n){return Object(a["a"])({url:"/sip/player/playback/"+t+"/"+e,method:"get",params:n})}function u(t,e,n){return Object(a["a"])({url:"/sip/player/closeStream/"+t+"/"+e+"/"+n,method:"get"})}function d(t,e,n,s){return Object(a["a"])({url:"/sip/player/playbackSeek/"+t+"/"+e+"/"+n,method:"get",params:s})}function h(t){return Object(a["a"])({url:"/iot/relation/addOrUp",method:"post",data:t})}function b(t,e){return Object(a["a"])({url:"/sip/talk/getPushUrl/"+t+"/"+e,method:"get"})}function p(t,e){return Object(a["a"])({url:"/sip/talk/broadcast/"+t+"/"+e,method:"get"})}function f(t,e){return Object(a["a"])({url:"/sip/talk/broadcast/stop/"+t+"/"+e,method:"get"})}}}]);