(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-662afafa"],{"0767":function(t,e,n){},"584f":function(t,e,n){"use strict";n.d(e,"l",(function(){return a})),n.d(e,"q",(function(){return o})),n.d(e,"m",(function(){return c})),n.d(e,"n",(function(){return r})),n.d(e,"k",(function(){return d})),n.d(e,"f",(function(){return s})),n.d(e,"c",(function(){return l})),n.d(e,"g",(function(){return u})),n.d(e,"i",(function(){return f})),n.d(e,"d",(function(){return p})),n.d(e,"r",(function(){return h})),n.d(e,"o",(function(){return v})),n.d(e,"p",(function(){return b})),n.d(e,"h",(function(){return g})),n.d(e,"a",(function(){return m})),n.d(e,"s",(function(){return y})),n.d(e,"b",(function(){return j})),n.d(e,"e",(function(){return O})),n.d(e,"j",(function(){return D}));var i=n("b775");function a(t){return Object(i["a"])({url:"/iot/device/list",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:t})}function d(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function s(t){return Object(i["a"])({url:"/iot/device/"+t,method:"get"})}function l(t){return Object(i["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function u(t){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function f(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function p(t,e){return Object(i["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function h(t){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+t,method:"post"})}function v(){return Object(i["a"])({url:"",method:"get"})}function b(){return Object(i["a"])({url:"",method:"get"})}function g(t){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function m(t){return Object(i["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/iot/device",method:"put",data:t})}function j(t){return Object(i["a"])({url:"/iot/device/"+t,method:"delete"})}function O(t){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:t})}function D(t){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}},"58ce":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{width:"100%",height:"100%","background-color":"#ffffff",overflow:"auto"},attrs:{id:"DeviceTree"}},[n("div",{staticStyle:{"line-height":"3vh","margin-bottom":"10px","font-size":"17px"}},[t._v("设备列表")]),n("el-tree",{ref:"tree",staticStyle:{"min-width":"100%",display:"inline-block !important"},attrs:{props:t.defaultProps,"current-node-key":t.selectchannelId,"default-expanded-keys":t.expandIds,"highlight-current":!0,load:t.loadNode,lazy:"","node-key":"id"},on:{"node-click":t.handleNodeClick},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node;e.data;return n("span",{staticClass:"custom-tree-node",staticStyle:{width:"100%"}},[0===i.data.type&&i.data.online?n("span",{staticClass:"device-online iconfont icon-jiedianleizhukongzhongxin2",attrs:{title:"在线设备"}}):t._e(),0!==i.data.type||i.data.online?t._e():n("span",{staticClass:"device-offline iconfont icon-jiedianleizhukongzhongxin2",attrs:{title:"离线设备"}}),3===i.data.type&&i.data.online?n("span",{staticClass:"device-online iconfont icon-shebeileijiankongdian",attrs:{title:"在线通道"}}):t._e(),3!==i.data.type||i.data.online?t._e():n("span",{staticClass:"device-offline iconfont icon-shebeileijiankongdian",attrs:{title:"离线通道"}}),4===i.data.type&&i.data.online?n("span",{staticClass:"device-online iconfont icon-shebeileiqiuji",attrs:{title:"在线通道-球机"}}):t._e(),4!==i.data.type||i.data.online?t._e():n("span",{staticClass:"device-offline iconfont icon-shebeileiqiuji",attrs:{title:"离线通道-球机"}}),5===i.data.type&&i.data.online?n("span",{staticClass:"device-online iconfont icon-shebeileibanqiu",attrs:{title:"在线通道-半球"}}):t._e(),5!==i.data.type||i.data.online?t._e():n("span",{staticClass:"device-offline iconfont icon-shebeileibanqiu",attrs:{title:"离线通道-半球"}}),6===i.data.type&&i.data.online?n("span",{staticClass:"device-online iconfont icon-shebeileiqiangjitongdao",attrs:{title:"在线通道-枪机"}}):t._e(),6!==i.data.type||i.data.online?t._e():n("span",{staticClass:"device-offline iconfont icon-shebeileiqiangjitongdao",attrs:{title:"离线通道-枪机"}}),i.data.online?n("span",{staticClass:"device-online",staticStyle:{"padding-left":"1px"}},[t._v(t._s(i.label))]):t._e(),i.data.online?t._e():n("span",{staticClass:"device-offline",staticStyle:{"padding-left":"1px"}},[t._v(t._s(i.label))])])}}])})],1)},a=[],o=(n("99af"),n("14d9"),n("b0c0"),n("f5a7")),c=n("584f"),r={name:"DeviceTree",data:function(){return{total:0,channelList:[],DeviceData:[],expandIds:[],selectData:{},selectchannelId:"",defaultProps:{children:"children",label:"name",isLeaf:"isLeaf"},queryParams:{pageNum:1,pageSize:100,status:3,deviceType:3}}},props:["onlyCatalog","clickEvent"],mounted:function(){this.selectchannelId="",this.expandIds=["0"]},methods:{handleNodeClick:function(t,e,n){if(this.selectData=e.data,this.selectchannelId=e.data.value,0!==e.level){var i=this.$refs.tree.getNode(t.userData.channelSipId);"function"==typeof this.clickEvent&&e.level>1&&this.clickEvent(i.data.userData)}},loadNode:function(t,e){var n=this;if(0===t.level)Object(c["n"])(this.queryParams).then((function(t){var n=t.rows;if(n.length>0){for(var i=[],a=0;a<n.length;a++){var o={name:n[a].deviceName,isLeaf:!1,id:n[a].serialNumber,type:0,online:3===n[a].status,userData:n[a]};i.push(o)}e(i)}else e([])}));else{var i=[];Object(o["b"])(t.data.userData.serialNumber).then((function(t){null!=t.data?(i=i.concat(t.data),n.channelDataHandler(i,e)):e([])}))}},channelDataHandler:function(t,e){if(t.length>0){for(var n=[],i=0;i<t.length;i++){var a=t[i],o=a.id.substring(10,13);console.log("channelType: "+o);var c=3;if(a.id.length<=10)c=2;else if(a.id.length>14){var r=a.id.substring(10,13);"111"!==r&&"112"!==r&&"118"!==r&&"131"!==r&&"132"!==r?c=-1:1===a.basicData.ptztype?c=4:2===a.basicData.ptztype?c=5:3!==a.basicData.ptztype&&4!==a.basicData.ptztype||(c=6)}else(a.basicData.subCount>0||1===a.basicData.parental)&&(c=2);var d={name:a.name||a.id,isLeaf:!0,id:a.id,deviceId:a.deviceId,type:c,online:2===a.status,userData:a.basicData};"111"!==o&&"112"!==o&&"118"!==o&&"131"!==o&&"132"!==o||n.push(d)}e(n)}else e([])},reset:function(){this.$forceUpdate()}},destroyed:function(){}},d=r,s=(n("8ed9"),n("2877")),l=Object(s["a"])(d,i,a,!1,null,null,null);e["default"]=l.exports},"8ed9":function(t,e,n){"use strict";n("0767")},f5a7:function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return o})),n.d(e,"c",(function(){return c}));var i=n("b775");function a(t){return Object(i["a"])({url:"/sip/device/listchannel/"+t,method:"get"})}function o(t){return Object(i["a"])({url:"/sip/device/sipid/"+t,method:"delete"})}function c(t,e,n){return Object(i["a"])({url:"/sip/ptz/direction/"+t+"/"+e,method:"post",data:n})}}}]);