html, body {
    width: 100%;
    height: 100%;
}

html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    line-height: 1.6;
    position: relative;
    font-family: "Microsoft Yahei", tahoma, arial, "Hiragino Sans GB";
}


.root {
    display: flex;
    place-content: center;
    margin-top: 3rem;
}

.container-shell {
	background: hsla(0, 0%, 50%, 0.5);
	width: 100%;
	position: relative;
	border-radius: 5px;  
}

.container-shell:before {
    content: "jessibuca demo player";
    position: absolute;
    color: darkgray;
    left: 10px;
    text-shadow: 1px 1px black;
}

#container {
    background: rgba(13, 14, 27, 0.7);
    width: 960px;
    height: 597px;
}

.container {
    background: rgba(13, 14, 27, 0.7);
    width: 320px;
    height: 199px;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
}

.input {
    display: flex;
    margin-top: 10px;
    max-width: 960px;
    color: white;
    place-content: stretch;
}

.input2 {
    bottom: 0px;
}

.input input {
    flex: auto;
}

.err {
    position: absolute;
    top: 40px;
    left: 10px;
    color: red;
}

.option {
    position: absolute;
    top: 4px;
    right: 10px;
    display: flex;
    place-content: center;
    font-size: 12px;
}

.option span {
    color: white;
}

.page {
    background: url('./bg.jpg');
    background-repeat: no-repeat;
    background-position: top;
}

button{
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
}

.container-multi{
    text-align: center;
}


.audio-container{
    width: 960px;
}

@media (max-width: 720px) {
    input {
        outline: 0;
    }

    * {
        margin: 0;
        padding: 0;
    }

    * {
        -webkit-tap-highlight-color: transparent
    }

    a img {
        border: 0;
    }

    a {
        text-decoration: none;
    }


    li {
        list-style: none;
    }

    ol,
    ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }


    #container {
        width: 100%;
        height: 52.7vw;
        margin: 0 auto;
    }
    
    .input{
        max-width: 95vw;
    }

    .audio-container{
        width: 95vw;
    }

    .container {
        width: 95vw;
        height: 52.7vw;
        margin: 0 auto;
        margin-bottom: 10px;
        display: block;
    }
}
