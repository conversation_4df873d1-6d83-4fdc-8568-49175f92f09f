(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4261f672"],{"09a1":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{staticClass:"template-parameter-dialog",attrs:{title:t.$t("template.paramter.038405-0"),visible:t.openEdit,width:"900px","append-to-body":""},on:{"update:visible":function(e){t.openEdit=e}}},[a("el-row",[a("el-col",{staticClass:"model-card",attrs:{span:11}},[a("el-form",{staticClass:"search-form",attrs:{model:t.queryParams,inline:!0,"label-width":"48px",size:"small"}},[a("el-form-item",{attrs:{label:"",prop:"templateName"}},[a("el-input",{staticStyle:{width:"200px"},attrs:{placeholder:t.$t("template.paramter.038405-1"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.templateName,callback:function(e){t.$set(t.queryParams,"templateName",e)},expression:"queryParams.templateName"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.handleQuery},slot:"append"})],1)],1)],1),a("div",{staticClass:"tip-wrap"},[a("i",{staticClass:"el-icon-warning"}),t._v(" "+t._s(t.$t("template.paramter.038405-3"))+" ")]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.templateList,size:"small","highlight-current-row":"",border:!1,"show-header":!1,"row-style":{backgroundColor:"#eee"}},on:{"row-click":t.rowClick}},[a("el-table-column",{attrs:{label:t.$t("template.paramter.038405-4"),width:"30",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[a("input",{attrs:{type:"radio",disabled:"array"==t.row.datatype||"object"==t.row.datatype,name:"template"},domProps:{checked:t.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:t.$t("template.paramter.038405-5"),align:"left",prop:"templateName"}}),a("el-table-column",{attrs:{label:t.$t("template.paramter.038405-6"),align:"left",prop:"identifier"}}),a("el-table-column",{attrs:{label:t.$t("template.paramter.038405-7"),align:"center",prop:"datatype",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("dict-tag",{attrs:{options:t.dict.type.iot_data_type,value:e.row.datatype}})]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 10px","background-color":"#eee"},attrs:{small:"",layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-8"),prop:"name"}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-9")},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-10"),prop:"id"}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-11")},model:{value:t.form.id,callback:function(e){t.$set(t.form,"id",e)},expression:"form.id"}})],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-12"),prop:"order"}},[a("el-input-number",{staticStyle:{width:"290px"},attrs:{"controls-position":"right",placeholder:t.$t("template.paramter.038405-13"),type:"number"},model:{value:t.form.order,callback:function(e){t.$set(t.form,"order",e)},expression:"form.order"}})],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-14"),prop:"property"}},[a("el-checkbox",{attrs:{name:"isChart",label:t.$t("template.paramter.038405-15"),"true-label":1,"false-label":0},on:{change:t.isChartChange},model:{value:t.form.isChart,callback:function(e){t.$set(t.form,"isChart",e)},expression:"form.isChart"}}),a("el-checkbox",{attrs:{name:"isMonitor",label:t.$t("template.paramter.038405-16"),"true-label":1,"false-label":0},on:{change:t.isMonitorChange},model:{value:t.form.isMonitor,callback:function(e){t.$set(t.form,"isMonitor",e)},expression:"form.isMonitor"}}),a("el-checkbox",{attrs:{name:"isReadonly",label:t.$t("template.paramter.038405-17"),"true-label":1,"false-label":0},on:{change:t.isReadonlyChange},model:{value:t.form.isReadonly,callback:function(e){t.$set(t.form,"isReadonly",e)},expression:"form.isReadonly"}}),a("el-checkbox",{attrs:{name:"isHistory",label:t.$t("template.paramter.038405-18"),"true-label":1,"false-label":0},model:{value:t.form.isHistory,callback:function(e){t.$set(t.form,"isHistory",e)},expression:"form.isHistory"}}),a("el-checkbox",{attrs:{name:"isSharePerm",label:t.$t("template.paramter.038405-19"),"true-label":1,"false-label":0},model:{value:t.form.isSharePerm,callback:function(e){t.$set(t.form,"isSharePerm",e)},expression:"form.isSharePerm"}})],1),a("div",{staticStyle:{"margin-bottom":"20px","background-color":"#ddd",height:"1px"}}),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-20"),prop:"datatype"}},[a("el-select",{staticStyle:{width:"132.9px"},attrs:{placeholder:t.$t("template.paramter.038405-21")},model:{value:t.form.datatype,callback:function(e){t.$set(t.form,"datatype",e)},expression:"form.datatype"}},[a("el-option",{key:"integer",attrs:{label:t.$t("template.paramter.038405-22"),value:"integer"}}),a("el-option",{key:"decimal",attrs:{label:t.$t("template.paramter.038405-23"),value:"decimal"}}),a("el-option",{key:"bool",attrs:{label:t.$t("template.paramter.038405-24"),value:"bool",disabled:1==t.form.isChart}}),a("el-option",{key:"enum",attrs:{label:t.$t("template.paramter.038405-25"),value:"enum",disabled:1==t.form.isChart}}),a("el-option",{key:"string",attrs:{label:t.$t("template.paramter.038405-26"),value:"string",disabled:1==t.form.isChart}})],1)],1),"integer"==t.form.datatype||"decimal"==t.form.datatype?a("div",[a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-27")}},[a("el-row",{staticStyle:{width:"290px"}},[a("el-col",{attrs:{span:11}},[a("el-input",{attrs:{placeholder:t.$t("template.paramter.038405-28"),type:"number"},model:{value:t.form.specs.min,callback:function(e){t.$set(t.form.specs,"min",e)},expression:"form.specs.min"}})],1),a("el-col",{attrs:{span:2,align:"center"}},[t._v(t._s(t.$t("template.paramter.038405-29")))]),a("el-col",{attrs:{span:11}},[a("el-input",{attrs:{placeholder:t.$t("template.paramter.038405-30"),type:"number"},model:{value:t.form.specs.max,callback:function(e){t.$set(t.form.specs,"max",e)},expression:"form.specs.max"}})],1)],1)],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-31")}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-32")},model:{value:t.form.specs.unit,callback:function(e){t.$set(t.form.specs,"unit",e)},expression:"form.specs.unit"}})],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-33")}},[a("el-input-number",{staticStyle:{width:"290px"},attrs:{"controls-position":"right",placeholder:t.$t("template.paramter.038405-34"),type:"number"},model:{value:t.form.specs.step,callback:function(e){t.$set(t.form.specs,"step",e)},expression:"form.specs.step"}})],1)],1):t._e(),"bool"==t.form.datatype?a("div",[a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-35"),prop:""}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:10}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-36")},model:{value:t.form.specs.falseText,callback:function(e){t.$set(t.form.specs,"falseText",e)},expression:"form.specs.falseText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[t._v(t._s(t.$t("template.paramter.038405-37")))])],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-38")},model:{value:t.form.specs.trueText,callback:function(e){t.$set(t.form.specs,"trueText",e)},expression:"form.specs.trueText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[t._v(t._s(t.$t("template.paramter.038405-39")))])],1)],1)],1):t._e(),"enum"==t.form.datatype?a("div",[a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-40")}},[a("el-select",{staticStyle:{width:"132.9px"},attrs:{placeholder:t.$t("template.paramter.038405-41")},model:{value:t.form.specs.showWay,callback:function(e){t.$set(t.form.specs,"showWay",e)},expression:"form.specs.showWay"}},[a("el-option",{key:"select",attrs:{label:t.$t("template.paramter.038405-42"),value:"select"}}),a("el-option",{key:"button",attrs:{label:t.$t("template.paramter.038405-43"),value:"button"}})],1)],1),a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-44"),prop:""}},[t._l(t.form.specs.enumList,(function(e,r){return a("el-row",{key:"enum"+r,staticStyle:{width:"290px","margin-bottom":"10px"}},[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:t.$t("template.paramter.038405-45")},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-input",{attrs:{placeholder:t.$t("template.paramter.038405-46")},model:{value:e.text,callback:function(a){t.$set(e,"text",a)},expression:"item.text"}})],1),0!=r?a("el-col",{attrs:{span:3,offset:1}},[a("a",{staticStyle:{color:"#f56c6c"},on:{click:function(e){return t.removeEnumItem(r)}}},[t._v(t._s(t.$t("template.paramter.038405-47")))])]):t._e()],1)})),a("div",[t._v(" + "),a("a",{staticStyle:{color:"#486ff2"},on:{click:function(e){return t.addEnumItem()}}},[t._v(t._s(t.$t("template.paramter.038405-48")))])])],2)],1):t._e(),"string"==t.form.datatype?a("div",[a("el-form-item",{attrs:{label:t.$t("template.paramter.038405-49"),prop:""}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:t.$t("template.paramter.038405-50"),type:"number"},model:{value:t.form.specs.maxLength,callback:function(e){t.$set(t.form.specs,"maxLength",e)},expression:"form.specs.maxLength"}})],1)],1)],1)],1):t._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.$t("template.paramter.038405-51")))]),a("el-button",{on:{click:t.cancel}},[t._v(t._s(t.$t("template.paramter.038405-52")))])],1)],1)},s=[],l=(a("14d9"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("cec4")),o={name:"things_parameter",dicts:["iot_things_type","iot_data_type","iot_yes_no"],props:{data:{type:Object,default:null}},watch:{data:function(t,e){this.index=t.index,t&&t.parameter.name&&""!=t.parameter.name&&(this.form.name=t.parameter.name,this.form.id=t.parameter.id,this.form.order=t.parameter.order,this.form.isChart=t.parameter.isChart?t.parameter.isChart:0,this.form.isHistory=t.parameter.isHistory?t.parameter.isHistory:1,this.form.isSharePerm=t.parameter.isSharePerm?t.parameter.isSharePerm:0,this.form.isMonitor=t.parameter.isMonitor?t.parameter.isMonitor:0,this.form.isReadonly=t.parameter.isReadonly?t.parameter.isReadonly:0,this.form.specs=t.parameter.datatype,this.form.datatype=this.form.specs.type,this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer")),this.openEdit=!0,this.getList()}},data:function(){return{loading:!0,total:0,templateList:[],openEdit:!1,queryParams:{pageNum:1,pageSize:10,name:null,type:null},index:-1,form:{},rules:{name:[{required:!0,message:this.$t("template.paramter.038405-53"),trigger:"blur"}],id:[{required:!0,message:this.$t("template.paramter.038405-54"),trigger:"blur"}],order:[{required:!0,message:this.$t("template.paramter.038405-55"),trigger:"blur"}],datatype:[{required:!0,message:this.$t("template.paramter.038405-56"),trigger:"change"}]}}},created:function(){this.getList(),this.reset()},methods:{getList:function(){var t=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(e){for(var a=0;a<e.rows.length;a++)e.rows[a].isSelect=!1;t.templateList=e.rows,t.total=e.total,t.setRadioSelected(t.productId),t.loading=!1}))},rowClick:function(t){null!=t&&"array"!=t.datatype&&"object"!=t.datatype&&(this.form.name=t.templateName,this.form.id=t.identifier,this.form.order=t.modelOrder,this.form.isChart=t.isChart?t.isChart:0,this.form.isHistory=t.isHistory?t.isHistory:1,this.form.isSharePerm=t.isSharePerm?t.isSharePerm:0,this.form.isReadonly=t.isReadonly?t.isReadonly:0,this.form.isMonitor=t.isMonitor?t.isMonitor:0,this.form.datatype=t.datatype,this.form.specs=JSON.parse(t.specs),this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer"),this.setRadioSelected(t.templateId))},setRadioSelected:function(t){for(var e=0;e<this.templateList.length;e++)this.templateList[e].templateId==t?this.templateList[e].isSelect=!0:this.templateList[e].isSelect=!1},cancel:function(){this.openEdit=!1,this.reset()},reset:function(){this.index=-1,this.form={name:null,id:null,order:0,datatype:"integer",isChart:0,isHistory:1,isSharePerm:0,isMonitor:0,isReadonly:0,specs:{enumList:[{value:"",text:""}],showWay:"select"}},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){if(e){t.form.datatype=t.formatThingsSpecs(),delete t.form.specs,t.openEdit=!1;var a={parameter:JSON.parse(JSON.stringify(t.form)),index:t.index};console.log("data",a),t.$emit("dataEvent",a),t.reset()}}))},isChartChange:function(){1==this.form.isChart?this.form.isReadonly=1:this.form.isMonitor=0},isMonitorChange:function(){1==this.form.isMonitor&&(this.form.isReadonly=1,this.form.isChart=1)},isReadonlyChange:function(){0==this.form.isReadonly&&(this.form.isMonitor=0,this.form.isChart=0)},formatThingsSpecs:function(){var t={};return t.type=this.form.datatype,"integer"==this.form.datatype||"decimal"==this.form.datatype?(t.min=Number(this.form.specs.min?this.form.specs.min:0),t.max=Number(this.form.specs.max?this.form.specs.max:100),t.unit=this.form.specs.unit?this.form.specs.unit:"",t.step=Number(this.form.specs.step?this.form.specs.step:1)):"string"==this.form.datatype?t.maxLength=Number(this.form.specs.maxLength?this.form.specs.maxLength:1024):"bool"==this.form.datatype?(t.falseText=this.form.specs.falseText?this.form.specs.falseText:this.$t("template.paramter.038405-57"),t.trueText=this.form.specs.trueText?this.form.specs.trueText:this.$t("template.paramter.038405-58")):"array"==this.form.datatype?t.arrayType=this.form.specs.arrayType:"enum"==this.form.datatype&&(t.showWay=this.form.specs.showWay,this.form.specs.enumList&&""!=this.form.specs.enumList[0].text?t.enumList=this.form.specs.enumList:(t.showWay="select",t.enumList=[{value:"0",text:this.$t("template.paramter.038405-59")},{value:"1",text:this.$t("template.paramter.038405-60")}])),t},addEnumItem:function(){this.form.specs.enumList.push({value:"",text:""})},removeEnumItem:function(t){this.form.specs.enumList.splice(t,1)}}},i=o,m=(a("272f"),a("a1b0"),a("2877")),p=Object(m["a"])(i,r,s,!1,null,"24460646",null);e["default"]=p.exports},"272f":function(t,e,a){"use strict";a("35c0")},"35c0":function(t,e,a){},"3e84":function(t,e,a){},a1b0:function(t,e,a){"use strict";a("3e84")},cec4:function(t,e,a){"use strict";a.d(e,"e",(function(){return s})),a.d(e,"d",(function(){return l})),a.d(e,"a",(function(){return o})),a.d(e,"f",(function(){return i})),a.d(e,"b",(function(){return m})),a.d(e,"c",(function(){return p}));var r=a("b775");function s(t){return Object(r["a"])({url:"/iot/template/list",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/iot/template/"+t,method:"get"})}function o(t){return Object(r["a"])({url:"/iot/template",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/iot/template",method:"put",data:t})}function m(t){return Object(r["a"])({url:"/iot/template/"+t,method:"delete"})}function p(t){return Object(r["a"])({url:"/iot/template/getPoints",method:"get",params:t})}}}]);