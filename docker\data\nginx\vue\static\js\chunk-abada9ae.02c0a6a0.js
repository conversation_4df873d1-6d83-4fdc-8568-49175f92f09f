(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-abada9ae","chunk-28b7a9aa"],{"10f3":function(e,t,i){"use strict";i.d(t,"e",(function(){return n})),i.d(t,"d",(function(){return o})),i.d(t,"c",(function(){return a})),i.d(t,"a",(function(){return u})),i.d(t,"g",(function(){return s})),i.d(t,"f",(function(){return l})),i.d(t,"b",(function(){return c}));var r=i("b775");function n(e){return Object(r["a"])({url:"/iot/group/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/iot/group/"+e,method:"get"})}function a(e){return Object(r["a"])({url:"/iot/group/getDeviceIds/"+e,method:"get"})}function u(e){return Object(r["a"])({url:"/iot/group",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/iot/group",method:"put",data:e})}function l(e){return Object(r["a"])({url:"/iot/group/updateDeviceGroups",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/iot/group/"+e,method:"delete"})}},1967:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"iot-group"},[i("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"15px","border-radius":"8px",width:"100%"}},[i("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-18px",padding:"3px 0 0 0"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[i("el-form-item",{attrs:{prop:"groupName"}},[i("el-input",{attrs:{placeholder:e.$t("iot.group.index.637432-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.groupName,callback:function(t){e.$set(e.queryParams,"groupName",t)},expression:"queryParams.groupName"}})],1),i("div",{staticStyle:{float:"right"}},[i("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),i("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1)],1),i("el-card",[i("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:add"],expression:"['iot:group:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:e.handleAdd}},[e._v(e._s(e.$t("iot.group.index.637432-5")))])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:remove"],expression:"['iot:group:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(e._s(e.$t("del")))])],1),i("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.groupList,border:!1},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",align:"center",width:"55"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.index.637432-0"),align:"left",prop:"groupName","min-width":"200"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.index.637432-6"),align:"center",prop:"groupOrder",width:"100"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.index.637432-7"),align:"center",prop:"createTime",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),i("el-table-column",{attrs:{label:e.$t("iot.group.index.637432-8"),align:"center",prop:"userName",width:"100"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.index.637432-9"),align:"left",prop:"remark","min-width":"150"}}),i("el-table-column",{attrs:{fixed:"right",label:e.$t("iot.group.index.637432-10"),align:"center","class-name":"small-padding fixed-width",width:"290"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:list"],expression:"['iot:device:list']"}],attrs:{size:"small",type:"text",icon:"el-icon-search"},on:{click:function(i){return e.handleViewDevice(t.row.groupId)}}},[e._v(" "+e._s(e.$t("iot.group.index.637432-11"))+" ")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:add"],expression:"['iot:group:add']"}],attrs:{size:"small",type:"text",icon:"el-icon-plus"},on:{click:function(i){return e.selectDevice(t.row)}}},[e._v(e._s(e.$t("iot.group.index.637432-12")))]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:query"],expression:"['iot:group:query']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("update")))]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:remove"],expression:"['iot:group:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v(" "+e._s(e.$t("iot.group.index.637432-14"))+" ")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),i("deviceList",{ref:"groupDeviceList",attrs:{group:e.group}}),i("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[i("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[i("el-form-item",{attrs:{label:e.$t("iot.group.index.637432-0"),prop:"groupName"}},[i("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("iot.group.index.637432-1")},model:{value:e.form.groupName,callback:function(t){e.$set(e.form,"groupName",t)},expression:"form.groupName"}})],1),i("el-form-item",{attrs:{label:e.$t("iot.group.index.637432-6"),prop:"groupOrder"}},[i("el-input",{staticStyle:{width:"400px"},attrs:{type:"number",placeholder:e.$t("iot.group.index.637432-15")},model:{value:e.form.groupOrder,callback:function(t){e.$set(e.form,"groupOrder",t)},expression:"form.groupOrder"}})],1),i("el-form-item",{attrs:{label:e.$t("iot.group.index.637432-9"),prop:"remark"}},[i("el-input",{staticStyle:{width:"400px"},attrs:{type:"textarea",placeholder:e.$t("iot.group.index.637432-16"),autosize:{minRows:3,maxRows:5}},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:e.form.groupId,expression:"form.groupId"},{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:edit"],expression:"['iot:group:edit']"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("update")))]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:group:add"],expression:"['iot:group:add']"},{name:"show",rawName:"v-show",value:!e.form.groupId,expression:"!form.groupId"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("add")))]),i("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("iot.group.index.637432-19")))])],1)],1)],1)},n=[],o=i("5530"),a=(i("d81d"),i("14d9"),i("d4b0")),u=i("10f3"),s=i("5f87"),l={name:"Group",components:{deviceList:a["default"]},data:function(){return{isAdmin:!1,myGroup:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,groupList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,groupName:null,userId:null},group:{},form:{},rules:{groupName:[{required:!0,message:this.$t("iot.group.index.637432-20"),trigger:"blur"}],groupOrder:[{required:!0,message:this.$t("iot.group.index.637432-21"),trigger:"blur"}]}}},created:function(){this.getList(),this.init()},methods:{init:function(){-1===this.$store.state.user.roles.indexOf("tenant")&&-1===this.$store.state.user.roles.indexOf("general")&&(this.isAdmin=!0)},myGroupChange:function(){this.queryParams.userId=this.myGroup?Object(s["b"])():null},handleViewDevice:function(e){this.$router.push({path:"/iot/device",query:{t:Date.now(),groupId:e}})},getList:function(){var e=this;this.loading=!0,Object(u["e"])(this.queryParams).then((function(t){e.groupList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={groupId:null,groupName:null,groupOrder:null,userId:null,userName:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.groupId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=this.$t("iot.group.index.637432-22")},handleUpdate:function(e){var t=this;this.reset();var i=e.groupId||this.ids;Object(u["d"])(i).then((function(e){t.form=e.data,t.open=!0,t.title=t.$t("iot.group.index.637432-23")}))},selectDevice:function(e){this.group=e,this.$refs.groupDeviceList.openDeviceList=!0},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.groupId?Object(u["g"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("iot.group.index.637432-24")),e.open=!1,e.getList()})):Object(u["a"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("iot.group.index.637432-25")),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,i=e.groupId||this.ids;this.$modal.confirm(this.$t("iot.group.index.637432-26",[i])).then((function(){return Object(u["b"])(i)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("iot.group.index.637432-27"))})).catch((function(){}))},handleExport:function(){this.download("iot/group/export",Object(o["a"])({},this.queryParams),"group_".concat((new Date).getTime(),".xlsx"))}}},c=l,d=(i("5b8b"),i("2877")),p=Object(d["a"])(c,r,n,!1,null,"e3aefd0c",null);t["default"]=p.exports},"584f":function(e,t,i){"use strict";i.d(t,"n",(function(){return n})),i.d(t,"t",(function(){return o})),i.d(t,"o",(function(){return a})),i.d(t,"p",(function(){return u})),i.d(t,"m",(function(){return s})),i.d(t,"f",(function(){return l})),i.d(t,"c",(function(){return c})),i.d(t,"g",(function(){return d})),i.d(t,"i",(function(){return p})),i.d(t,"d",(function(){return m})),i.d(t,"u",(function(){return g})),i.d(t,"q",(function(){return h})),i.d(t,"r",(function(){return f})),i.d(t,"h",(function(){return v})),i.d(t,"a",(function(){return b})),i.d(t,"v",(function(){return y})),i.d(t,"b",(function(){return w})),i.d(t,"e",(function(){return x})),i.d(t,"k",(function(){return $})),i.d(t,"l",(function(){return O})),i.d(t,"j",(function(){return N})),i.d(t,"s",(function(){return _}));var r=i("b775");function n(e){return Object(r["a"])({url:"/iot/device/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function a(e){return Object(r["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function u(e){return Object(r["a"])({url:"/iot/device/shortList",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/iot/device/all",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function p(){return Object(r["a"])({url:"/iot/device/statistic",method:"get"})}function m(e,t){return Object(r["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function g(e,t){return Object(r["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function h(e){return Object(r["a"])({url:"/iot/record/list",method:"get",params:e})}function f(e){return Object(r["a"])({url:"/iot/record/list",method:"get",params:e})}function v(e){return Object(r["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function b(e){return Object(r["a"])({url:"/iot/device",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/iot/device",method:"put",data:e})}function w(e){return Object(r["a"])({url:"/iot/device/"+e,method:"delete"})}function x(e){return Object(r["a"])({url:"/iot/device/generator",method:"get",params:e})}function $(e){return Object(r["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function O(e){return Object(r["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function N(e){return Object(r["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function _(e){return Object(r["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},"5b8b":function(e,t,i){"use strict";i("f299")},d4b0:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.$t("iot.group.device-list.849593-0"),visible:e.openDeviceList,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.openDeviceList=t}}},[i("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{prop:"deviceName"}},[i("el-input",{attrs:{placeholder:e.$t("iot.group.device-list.849593-2"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("iot.group.device-list.849593-3")))])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleTable",attrs:{data:e.deviceList,size:"small",border:!1},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.device-list.849593-4"),align:"left",prop:"deviceName","min-width":"130"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.device-list.849593-5"),align:"left",prop:"serialNumber","min-width":"130"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.device-list.849593-6"),align:"left",prop:"productName","min-width":"130"}}),i("el-table-column",{attrs:{label:e.$t("iot.group.device-list.849593-7"),align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?i("el-tag",{attrs:{type:"success",size:"small"}},[e._v(e._s(e.$t("iot.group.device-list.849593-8")))]):i("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(e._s(e.$t("iot.group.device-list.849593-9")))])]}}])}),i("el-table-column",{attrs:{label:e.$t("iot.group.device-list.849593-10"),align:"center",prop:"status","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_device_status,size:"small",value:t.row.status}})]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.handleDeviceSelected}},[e._v(e._s(e.$t("iot.group.device-list.849593-11")))]),i("el-button",{on:{click:e.closeSelectDeviceList}},[e._v(e._s(e.$t("iot.group.device-list.849593-12")))])],1)],1)},n=[],o=(i("14d9"),i("a434"),i("d3b7"),i("159b"),i("10f3")),a=i("584f"),u={name:"device-list",dicts:["iot_device_status"],props:{group:{type:Object,default:null}},data:function(){return{deviceGroup:{},loading:!0,ids:[],openDeviceList:!1,total:0,deviceList:[],queryParams:{pageNum:1,pageSize:10,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,status:null,networkAddress:null,activeTime:null}}},watch:{group:{handler:function(e,t){e.groupId&&(this.deviceGroup=e,this.queryParams.userId=this.deviceGroup.userId,this.queryParams.pageNum=1,this.getDeviceIdsByGroupId(this.deviceGroup.groupId))},immediate:!0}},created:function(){},methods:{getDeviceIdsByGroupId:function(e){var t=this;Object(o["c"])(e).then((function(e){t.ids=e.data,t.getList()}))},getList:function(){var e=this;this.loading=!0,this.queryParams.params={},null!=this.daterangeActiveTime&&""!=this.daterangeActiveTime&&(this.queryParams.params["beginActiveTime"]=this.daterangeActiveTime[0],this.queryParams.params["endActiveTime"]=this.daterangeActiveTime[1]),Object(a["o"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.forEach((function(t){e.$nextTick((function(){e.ids.some((function(e){return e===t.deviceId}))&&e.$refs.multipleTable.toggleRowSelection(t,!0)}))}))}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeActiveTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e,t){var i=this.ids.indexOf(t.deviceId),r=e.indexOf(t);-1==i&&-1!=r?this.ids.push(t.deviceId):-1!=i&&-1==r&&this.ids.splice(i,1)},handleSelectionAll:function(e){for(var t=0;t<this.deviceList.length;t++){var i=this.ids.indexOf(this.deviceList[t].deviceId),r=e.indexOf(this.deviceList[t]);-1==i&&-1!=r?this.ids.push(this.deviceList[t].deviceId):-1!=i&&-1==r&&this.ids.splice(i,1)}},closeSelectDeviceList:function(){this.openDeviceList=!1},handleDeviceSelected:function(){var e=this;this.group.deviceIds=this.ids,Object(o["f"])(this.group).then((function(t){e.$modal.msgSuccess(e.$t("iot.group.device-list.849593-13")),e.openDeviceList=!1}))}}},s=u,l=i("2877"),c=Object(l["a"])(s,r,n,!1,null,null,null);t["default"]=c.exports},f299:function(e,t,i){}}]);