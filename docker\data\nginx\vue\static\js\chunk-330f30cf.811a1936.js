(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-330f30cf"],{"06d9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:"设备导入记录",visible:t.open,width:"800px"},on:{"update:visible":function(e){t.open=e}}},[r("div",{staticStyle:{"margin-top":"-55px"}},[r("el-divider",{staticStyle:{"margin-top":"-30px"}}),r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{prop:"productId"}},[r("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"产品名称",filterable:""},model:{value:t.queryParams.productId,callback:function(e){t.$set(t.queryParams,"productId",e)},expression:"queryParams.productId"}},t._l(t.productList,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),r("el-form-item",{attrs:{prop:"status"}},[r("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"批次任务状态",filterable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.statusList,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),r("el-form-item",[r("el-date-picker",{staticStyle:{width:"180px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.daterangeTime,callback:function(e){t.daterangeTime=e},expression:"daterangeTime"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.dataList,size:"mini"}},[r("el-table-column",{attrs:{label:"批次号",align:"left"}}),r("el-table-column",{attrs:{label:"设备总数",align:"center"}}),r("el-table-column",{attrs:{label:"成功数量",align:"center"}}),r("el-table-column",{attrs:{label:"失败数量",align:"center"}}),r("el-table-column",{attrs:{label:"批次任务数量",align:"center"}}),r("el-table-column",{attrs:{label:"完成时间",align:"center"}}),r("el-table-column",{attrs:{label:"创建时间",align:"center"}})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)}}})],1)])},a=[],u=(r("d81d"),r("9b9c")),i=r("584f"),o={name:"importRecord",dicts:[],data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],queryParams:{pageNum:1,pageSize:10,productName:null}}},created:function(){this.getProductList()},methods:{getProductList:function(){var t=this;this.loading=!0;var e={pageSize:999};Object(u["f"])(e).then((function(e){t.productList=e.rows.map((function(t){return{value:t.productId,label:t.productName}})),t.loading=!1}))},getList:function(){var t=this;this.loading=!0,Object(i["o"])().then((function(e){t.dataList=e.rows,t.toltal=e.total,t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}},c=o,l=r("2877"),d=Object(l["a"])(c,n,a,!1,null,null,null);e["default"]=d.exports},"584f":function(t,e,r){"use strict";r.d(e,"l",(function(){return a})),r.d(e,"q",(function(){return u})),r.d(e,"m",(function(){return i})),r.d(e,"n",(function(){return o})),r.d(e,"k",(function(){return c})),r.d(e,"f",(function(){return l})),r.d(e,"c",(function(){return d})),r.d(e,"g",(function(){return s})),r.d(e,"i",(function(){return m})),r.d(e,"d",(function(){return f})),r.d(e,"r",(function(){return p})),r.d(e,"o",(function(){return b})),r.d(e,"p",(function(){return g})),r.d(e,"h",(function(){return h})),r.d(e,"a",(function(){return v})),r.d(e,"s",(function(){return y})),r.d(e,"b",(function(){return j})),r.d(e,"e",(function(){return O})),r.d(e,"j",(function(){return q}));var n=r("b775");function a(t){return Object(n["a"])({url:"/iot/device/list",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:t})}function c(){return Object(n["a"])({url:"/iot/device/all",method:"get"})}function l(t){return Object(n["a"])({url:"/iot/device/"+t,method:"get"})}function d(t){return Object(n["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function m(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function f(t,e){return Object(n["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function p(t){return Object(n["a"])({url:"/iot/device/recovery?deviceIds="+t,method:"post"})}function b(){return Object(n["a"])({url:"",method:"get"})}function g(){return Object(n["a"])({url:"",method:"get"})}function h(t){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function v(t){return Object(n["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/iot/device",method:"put",data:t})}function j(t){return Object(n["a"])({url:"/iot/device/"+t,method:"delete"})}function O(t){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:t})}function q(t){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}},"9b9c":function(t,e,r){"use strict";r.d(e,"f",(function(){return a})),r.d(e,"g",(function(){return u})),r.d(e,"e",(function(){return i})),r.d(e,"a",(function(){return o})),r.d(e,"i",(function(){return c})),r.d(e,"d",(function(){return l})),r.d(e,"b",(function(){return d})),r.d(e,"c",(function(){return s})),r.d(e,"h",(function(){return m}));var n=r("b775");function a(t){return Object(n["a"])({url:"/iot/product/list",method:"get",params:t})}function u(){return Object(n["a"])({url:"/iot/product/shortList",method:"get"})}function i(t){return Object(n["a"])({url:"/iot/product/"+t,method:"get"})}function o(t){return Object(n["a"])({url:"/iot/product",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/iot/product",method:"put",data:t})}function l(t){return Object(n["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function d(t){return Object(n["a"])({url:"/iot/product/status/",method:"put",data:t})}function s(t){return Object(n["a"])({url:"/iot/product/"+t,method:"delete"})}function m(t){return Object(n["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:t})}}}]);