(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c995c22"],{"170c":function(t,e,i){},3021:function(t,e,i){"use strict";i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){return r})),i.d(e,"d",(function(){return a})),i.d(e,"b",(function(){return n}));var o=i("b775");function s(t){return Object(o["a"])({url:"/sip/sipconfig/"+t,method:"get"})}function r(t){return Object(o["a"])({url:"/sip/sipconfig",method:"post",data:t})}function a(t){return Object(o["a"])({url:"/sip/sipconfig",method:"put",data:t})}function n(t){return Object(o["a"])({url:"/sip/sipconfig/product/"+t,method:"delete"})}},"5c8b":function(t,e,i){"use strict";i("170c")},f9ef:function(t,e,i){"use strict";i.r(e);var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"sip-config"},[i("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px"}},[i("el-row",{attrs:{gutter:100}},[i("el-col",{attrs:{xs:24,sm:24,md:12,lg:12,xl:8}},[i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-1"),prop:"ip"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{disabled:""},model:{value:t.form.ip,callback:function(e){t.$set(t.form,"ip",e)},expression:"form.ip"}})],1),i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-2"),prop:"domainAlias"}},[i("el-input",{staticStyle:{width:"100%"},model:{value:t.form.domainAlias,callback:function(e){t.$set(t.form,"domainAlias",e)},expression:"form.domainAlias"}})],1),i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-3"),prop:"password"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:t.$t("sip.sipConfig.998537-4")},model:{value:t.form.password,callback:function(e){t.$set(t.form,"password",e)},expression:"form.password"}})],1),i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-0"),prop:"isdefault"}},[i("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:t.form.isdefault,callback:function(e){t.$set(t.form,"isdefault",e)},expression:"form.isdefault"}})],1)],1),i("el-col",{attrs:{xs:24,sm:24,md:12,lg:12,xl:8}},[i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-6"),prop:"port"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"number",disabled:""},model:{value:t.form.port,callback:function(e){t.$set(t.form,"port",e)},expression:"form.port"}})],1),i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-7"),prop:"serverSipid"}},[i("el-input",{staticStyle:{width:"100%"},model:{value:t.form.serverSipid,callback:function(e){t.$set(t.form,"serverSipid",e)},expression:"form.serverSipid"}})],1),i("el-form-item",{attrs:{label:t.$t("sip.sipConfig.998537-5")}},[i("el-input",{staticStyle:{width:"100%"},attrs:{disabled:""},model:{value:t.accessWay,callback:function(e){t.accessWay=e},expression:"accessWay"}})],1)],1),i("el-col",{attrs:{xs:23,sm:23,md:23,lg:23,xl:15}},[i("el-form-item",{staticStyle:{"text-align":"center","margin-top":"20px"}},[i("el-button",{directives:[{name:"show",rawName:"v-show",value:t.form.id&&1===t.productInfo.status,expression:"form.id && productInfo.status === 1"},{name:"hasPermi",rawName:"v-hasPermi",value:["iot:video:edit"],expression:"['iot:video:edit']"}],attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.$t("update")))]),i("el-button",{directives:[{name:"show",rawName:"v-show",value:!t.form.id&&1===t.productInfo.status,expression:"!form.id && productInfo.status === 1"},{name:"hasPermi",rawName:"v-hasPermi",value:["iot:video:add"],expression:"['iot:video:add']"}],attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.$t("add")))])],1)],1)],1)],1)],1)},s=[],r=i("5530"),a=i("3021"),n={name:"ConfigSip",props:{product:{type:Object,default:null}},data:function(){return{accessWay:"国标GB28181",loading:!0,ids:[],total:0,sipconfigList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,productId:null},form:{},rules:{domainAlias:[{required:!0,message:this.$t("sip.sipConfig.998537-8"),trigger:"blur"}],serverSipid:[{required:!0,message:this.$t("sip.sipConfig.998537-9"),trigger:"blur"}],password:[{required:!0,message:this.$t("sip.sipConfig.998537-10"),trigger:"blur"}]}}},watch:{product:function(t,e){this.productInfo=Object(r["a"])({},t),this.productInfo&&this.productInfo.productId&&(this.form.id||this.getSipconfig()),this.$forceUpdate()}},created:function(){this.productInfo=this.product,this.productInfo&&this.productInfo.productId&&this.getSipconfig()},methods:{getSipconfig:function(){var t=this;Object(a["c"])(this.productInfo.productId).then((function(e){t.form=e.data}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(t.form.productId=t.product.productId,null==t.form.isdefault&&(t.form.isdefault=0),null!=t.form.id?Object(a["d"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功")})):Object(a["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.getSipconfig(!1)})))}))}}},l=n,c=(i("5c8b"),i("2877")),p=Object(c["a"])(l,o,s,!1,null,"5107e122",null);e["default"]=p.exports}}]);