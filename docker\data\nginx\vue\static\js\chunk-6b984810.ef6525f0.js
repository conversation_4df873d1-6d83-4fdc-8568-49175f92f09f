(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b984810","chunk-2d0a3715"],{"01ca":function(e,t,a){"use strict";a.d(t,"f",(function(){return l})),a.d(t,"d",(function(){return r})),a.d(t,"g",(function(){return i})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return u})),a.d(t,"i",(function(){return o})),a.d(t,"c",(function(){return c})),a.d(t,"b",(function(){return d})),a.d(t,"h",(function(){return m}));var n=a("b775");function l(e){return Object(n["a"])({url:"/iot/model/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/iot/model/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/iot/model/permList/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/iot/model",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/iot/model/import",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/iot/model",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/iot/model/"+e,method:"delete"})}function d(e){return Object(n["a"])({url:"/iot/model/cache/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/iot/model/synchron",method:"post",data:e})}},"1c4f":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[e.isSubDev?a("el-form-item",{attrs:{label:"请选择设备从机:","label-width":"120px"}},[a("el-select",{attrs:{placeholder:"请选择设备从机"},on:{change:e.selectSlave},model:{value:e.queryParams.slaveId,callback:function(t){e.$set(e.queryParams,"slaveId",t)},expression:"queryParams.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+"   (从机地址:"+e.slaveId+")",value:e.slaveId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"日志类型",prop:"funType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.funType,callback:function(t){e.$set(e.queryParams,"funType",t)},expression:"queryParams.funType"}},e._l(e.dict.type.iot_function_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identify"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identify,callback:function(t){e.$set(e.queryParams,"identify",t)},expression:"queryParams.identify"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:e.showName,align:"center",prop:"identify"}}),a("el-table-column",{attrs:{label:"指令类型",align:"center",prop:"funType",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_function_type,value:t.row.funType}})]}}])}),a("el-table-column",{attrs:{label:"设置值",align:"center",prop:"funValue"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),a("el-table-column",{attrs:{label:"下发时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"下发结果描述",align:"center",prop:"resultMsg"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},l=[],r=a("5530"),i=(a("d81d"),a("dc9c")),s=(a("01ca"),{name:"device-func",dicts:["iot_function_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.isSubDev=this.deviceInfo.subDeviceList&&this.deviceInfo.subDeviceList.length>0,this.showName=this.isSubDev?"寄存器地址":"标识符",this.queryParams.deviceId=this.deviceInfo.deviceId,this.queryParams.slaveId=this.deviceInfo.slaveId,this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.slaveList=e.subDeviceList,this.getList())}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,logList:[],title:"",open:!1,deviceInfo:{},daterangeTime:[],queryParams:{pageNum:1,pageSize:10,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,slaveId:null},form:{},isSubDev:!1,showName:null,slaveList:[],rules:{identify:[{required:!0,message:"标识符不能为空",trigger:"blur"}],funType:[{required:!0,message:"1==服务下发，2=属性获取，3.OTA升级不能为空",trigger:"change"}],funValue:[{required:!0,message:"日志值不能为空",trigger:"blur"}],serialNumber:[{required:!0,message:"设备编号不能为空",trigger:"blur"}]}}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]),this.queryParams.slaveId&&(this.queryParams.serialNumber=this.queryParams.serialNumber+"_"+this.queryParams.slaveId),Object(i["a"])(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,createBy:null,createTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleExport:function(){this.download("iot/log/export",Object(r["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))},selectSlave:function(){}}}),u=s,o=a("2877"),c=Object(o["a"])(u,n,l,!1,null,null,null);t["default"]=c.exports},dc9c:function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));var n=a("b775");function l(e){return Object(n["a"])({url:"/iot/log/list",method:"get",params:e})}}}]);