(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5d005370"],{"0d20":function(t,e,r){},9429:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"user-info-head",on:{click:function(e){return t.editCropper()}}},[r("img",{staticClass:"img-circle img-lg",attrs:{src:t.options.img,title:t.$t("user.resetPwd.450986-11")}})]),r("el-dialog",{attrs:{title:t.title,visible:t.open,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.open=e},opened:t.modalOpened,close:t.closeDialog}},[r("el-row",[r("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[t.visible?r("vue-cropper",{ref:"cropper",attrs:{img:t.options.img,info:!0,autoCrop:t.options.autoCrop,autoCropWidth:t.options.autoCropWidth,autoCropHeight:t.options.autoCropHeight,fixedBox:t.options.fixedBox,outputType:t.options.outputType},on:{realTime:t.realTime}}):t._e()],1),r("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[r("div",{staticClass:"avatar-upload-preview"},[r("img",{style:t.previews.img,attrs:{src:t.previews.url}})])])],1),r("br"),r("el-row",[r("el-col",{attrs:{lg:2,sm:3,xs:3}},[r("el-upload",{attrs:{action:"#","http-request":t.requestUpload,"show-file-list":!1,"before-upload":t.beforeUpload}},[r("el-button",{attrs:{size:"small"}},[t._v(" "+t._s(t.$t("select"))+" "),r("i",{staticClass:"el-icon-upload el-icon--right"})])],1)],1),r("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[r("el-button",{attrs:{icon:"el-icon-plus",size:"small"},on:{click:function(e){return t.changeScale(1)}}})],1),r("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[r("el-button",{attrs:{icon:"el-icon-minus",size:"small"},on:{click:function(e){return t.changeScale(-1)}}})],1),r("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[r("el-button",{attrs:{icon:"el-icon-refresh-left",size:"small"},on:{click:function(e){return t.rotateLeft()}}})],1),r("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[r("el-button",{attrs:{icon:"el-icon-refresh-right",size:"small"},on:{click:function(e){return t.rotateRight()}}})],1),r("el-col",{attrs:{lg:{span:2,offset:6},sm:2,xs:2}},[r("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.uploadImg()}}},[t._v(t._s(t.$t("submit")))])],1)],1)],1)],1)},o=[],s=r("4360"),i=r("7e79"),u=r("c0c7"),a=r("ed08"),c={components:{VueCropper:i["VueCropper"]},props:{user:{type:Object}},data:function(){return{open:!1,visible:!1,title:this.$t("user.resetPwd.450986-12"),options:{img:s["a"].getters.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png"},previews:{},resizeHandler:null}},methods:{editCropper:function(){this.open=!0},modalOpened:function(){var t=this;this.visible=!0,this.resizeHandler||(this.resizeHandler=Object(a["b"])((function(){t.refresh()}),100)),window.addEventListener("resize",this.resizeHandler)},refresh:function(){this.$refs.cropper.refresh()},requestUpload:function(){},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},changeScale:function(t){t=t||1,this.$refs.cropper.changeScale(t)},beforeUpload:function(t){var e=this;if(-1==t.type.indexOf("image/"))this.$modal.msgError(this.$t("user.resetPwd.450986-13"));else{var r=new FileReader;r.readAsDataURL(t),r.onload=function(){e.options.img=r.result}}},uploadImg:function(){var t=this;this.$refs.cropper.getCropBlob((function(e){var r=new FormData;r.append("avatarfile",e),Object(u["t"])(r).then((function(e){t.open=!1,t.options.img="/prod-api"+e.imgUrl,s["a"].commit("SET_AVATAR",t.options.img),t.$modal.msgSuccess(t.$t("updateSuccess")),t.visible=!1}))}))},realTime:function(t){this.previews=t},closeDialog:function(){this.options.img=s["a"].getters.avatar,this.visible=!1,window.removeEventListener("resize",this.resizeHandler)}}},l=c,d=(r("bd54"),r("2877")),p=Object(d["a"])(l,n,o,!1,null,"dd6cc878",null);e["default"]=p.exports},bd54:function(t,e,r){"use strict";r("0d20")},c0c7:function(t,e,r){"use strict";r.d(e,"l",(function(){return s})),r.d(e,"o",(function(){return i})),r.d(e,"j",(function(){return u})),r.d(e,"i",(function(){return a})),r.d(e,"a",(function(){return c})),r.d(e,"q",(function(){return l})),r.d(e,"c",(function(){return d})),r.d(e,"m",(function(){return p})),r.d(e,"b",(function(){return f})),r.d(e,"h",(function(){return m})),r.d(e,"n",(function(){return h})),r.d(e,"k",(function(){return g})),r.d(e,"r",(function(){return b})),r.d(e,"s",(function(){return v})),r.d(e,"t",(function(){return w})),r.d(e,"f",(function(){return y})),r.d(e,"p",(function(){return O})),r.d(e,"d",(function(){return j})),r.d(e,"e",(function(){return x})),r.d(e,"g",(function(){return C}));var n=r("b775"),o=r("c38a");function s(t){return Object(n["a"])({url:"/system/user/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/system/user/listTerminal",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/system/user/"+Object(o["f"])(t),method:"get"})}function a(t){return Object(n["a"])({url:"/system/dept/getRole?deptId="+t,method:"get"})}function c(t){return Object(n["a"])({url:"/system/user",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/system/user",method:"put",data:t})}function d(t){return Object(n["a"])({url:"/system/user/"+t,method:"delete"})}function p(t,e){var r={userId:t,password:e};return Object(n["a"])({url:"/system/user/resetPwd",method:"put",data:r})}function f(t,e){var r={userId:t,status:e};return Object(n["a"])({url:"/system/user/changeStatus",method:"put",data:r})}function m(){return Object(n["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(t){return Object(n["a"])({url:"/wechat/cancelBind",method:"post",data:t})}function g(){return Object(n["a"])({url:"/system/user/profile",method:"get"})}function b(t){return Object(n["a"])({url:"/system/user/profile",method:"put",data:t})}function v(t,e){var r={oldPassword:t,newPassword:e};return Object(n["a"])({url:"/system/user/profile/updatePwd",method:"put",params:r})}function w(t){return Object(n["a"])({url:"/system/user/profile/avatar",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/system/user/authRole/"+t,method:"get"})}function O(t){return Object(n["a"])({url:"/system/user/authRole",method:"put",params:t})}function j(){return Object(n["a"])({url:"/system/user/deptTree",method:"get"})}function x(t){return Object(n["a"])({url:"/system/user/deptTree?showOwner="+t,method:"get"})}function C(t){return Object(n["a"])({url:"/system/user/getByDeptId",method:"get",params:t})}}}]);