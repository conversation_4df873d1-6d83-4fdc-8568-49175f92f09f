(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7cb79408","chunk-05c05b4c"],{"1ab6":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:"选择设备",visible:e.openDeviceList,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.openDeviceList=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[r("el-input",{attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),r("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[r("el-input",{attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.firmwareVersion,callback:function(t){e.$set(e.queryParams,"firmwareVersion",t)},expression:"queryParams.firmwareVersion"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleTable",attrs:{data:e.deviceList,size:"mini",border:""},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),r("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),r("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"firmwareVersion"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v("Version ")]),e._v(" "+e._s(t.row.firmwareVersion)+" ")]}}])}),r("el-table-column",{attrs:{label:"设备类型",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?r("el-tag",{attrs:{type:"success"}},[e._v("分享")]):r("el-tag",{attrs:{type:"primary"}},[e._v("拥有")])]}}])}),r("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleDeviceSelected}},[e._v("确 定")]),r("el-button",{on:{click:e.closeSelectDeviceList}},[e._v("取 消")])],1)],1)},a=[],n=(r("d3b7"),r("159b"),r("14d9"),r("a434"),r("584f")),o={name:"device-list",dicts:["iot_device_status"],props:{upGrade:{type:Object,default:null}},data:function(){return{formUpGrade:{},loading:!0,ids:[],openDeviceList:!1,total:0,deviceList:[],queryParams:{pageNum:1,pageSize:10,deviceName:null,productId:null,productName:null,firmwareVersion:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,status:null,networkAddress:null,activeTime:null}}},watch:{upGrade:{handler:function(e,t){e.flag&&(this.formUpGrade=e,this.queryParams.productId=this.formUpGrade.productId,this.queryParams.firmwareVersion=this.formUpGrade.firmwareVersion,this.ids=this.formUpGrade.deviceList,this.queryParams.pageNum=1,this.getList())},immediate:!0,deep:!0}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,Object(n["l"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.forEach((function(t){e.$nextTick((function(){e.ids.some((function(e){return e===t.serialNumber}))&&e.$refs.multipleTable.toggleRowSelection(t,!0)}))}))}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleSelectionChange:function(e,t){var r=this.ids.indexOf(t.serialNumber),i=e.indexOf(t);-1==r&&-1!=i?this.ids.push(t.serialNumber):-1!=r&&-1==i&&this.ids.splice(r,1)},handleSelectionAll:function(e){for(var t=0;t<this.deviceList.length;t++){var r=this.ids.indexOf(this.deviceList[t].serialNumber),i=e.indexOf(this.deviceList[t]);-1==r&&-1!=i?this.ids.push(this.deviceList[t].serialNumber):-1!=r&&-1==i&&this.ids.splice(r,1)}},closeSelectDeviceList:function(){this.openDeviceList=!1,this.formUpGrade.flag=!1},handleDeviceSelected:function(){this.formUpGrade.deviceList=this.ids,this.formUpGrade.deviceAmount=this.ids.length,this.formUpGrade.flag=!1,this.$modal.msgSuccess("设备选择成功"),this.openDeviceList=!1}}},l=o,s=r("2877"),u=Object(s["a"])(l,i,a,!1,null,null,null);t["default"]=u.exports},2065:function(e,t,r){"use strict";r.d(t,"d",(function(){return a})),r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return l}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/firmware/task/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/task",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/firmware/task/deviceList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/firmware/task/deviceStatistic",method:"get",params:e})}},"584f":function(e,t,r){"use strict";r.d(t,"k",(function(){return a})),r.d(t,"n",(function(){return n})),r.d(t,"l",(function(){return o})),r.d(t,"m",(function(){return l})),r.d(t,"j",(function(){return s})),r.d(t,"e",(function(){return u})),r.d(t,"c",(function(){return c})),r.d(t,"f",(function(){return d})),r.d(t,"h",(function(){return m})),r.d(t,"g",(function(){return p})),r.d(t,"a",(function(){return f})),r.d(t,"o",(function(){return h})),r.d(t,"b",(function(){return v})),r.d(t,"d",(function(){return b})),r.d(t,"i",(function(){return g}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function s(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function u(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function p(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function f(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function h(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function v(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function b(){return Object(i["a"])({url:"/iot/device/generator",method:"get"})}function g(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},"5af9":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{padding:"6px"}},[r("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"6px"}},[r("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-20px"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"固件名称",prop:"firmwareName"}},[r("el-input",{attrs:{placeholder:"请输入固件名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.firmwareName,callback:function(t){e.$set(e.queryParams,"firmwareName",t)},expression:"queryParams.firmwareName"}})],1),r("el-form-item",{attrs:{label:"所属产品"}},[r("el-select",{attrs:{placeholder:"所属产品",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productShortList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("el-form-item",[e._v("· "),r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),r("el-form-item",{staticStyle:{float:"right"}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:add"],expression:"['iot:firmware:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("添加固件 ")])],1)],1)],1),r("el-card",{staticStyle:{"padding-bottom":"100px"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{label:"固件名称",align:"center",prop:"firmwareName"}}),r("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v("Version ")]),e._v(" "+e._s(t.row.version)+" ")]}}])}),r("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{underline:!1,type:"primary"},on:{click:function(r){return e.handleViewProduct(t.row.productId)}}},[e._v(" "+e._s(t.row.productName)+" ")])]}}])}),r("el-table-column",{attrs:{label:"下载地址",align:"center",prop:"filePath","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{href:e.getDownloadUrl(t.row.filePath),underline:!1,type:"success"}},[e._v(" "+e._s(e.getDownloadUrl(t.row.filePath))+" ")])]}}])}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"isLatest",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isLatest?r("el-tag",{attrs:{type:"success"}},[e._v("最新")]):r("el-tag",{attrs:{type:"info"}},[e._v("默认")])]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"固件描述",align:"center",prop:"remark","min-width":"150"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"280"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-upload"},on:{click:function(r){return e.otaUpGrade(t.row)}}},[e._v("固件升级 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-edit"},on:{click:function(r){return e.handleEdit(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-info"},on:{click:function(r){return e.handleInfo(t.row)}}},[e._v("详情 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:remove"],expression:"['iot:firmware:remove']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"固件名称",prop:"firmwareName"}},[r("el-input",{attrs:{placeholder:"请输入固件名称"},model:{value:e.form.firmwareName,callback:function(t){e.$set(e.form,"firmwareName",t)},expression:"form.firmwareName"}}),r("span",{staticStyle:{"font-size":"10px"}},[e._v("支持中文，英文大小写、数字、部分常用符号")])],1),r("el-form-item",{attrs:{label:"所属产品",prop:"productId"}},[r("el-select",{attrs:{placeholder:"请选择产品"},on:{change:e.selectProduct},model:{value:e.form.productId,callback:function(t){e.$set(e.form,"productId",t)},expression:"form.productId"}},e._l(e.productShortList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("el-form-item",{attrs:{label:"固件版本号",prop:"version"}},[r("el-input",{attrs:{placeholder:"请输入固件版本",type:"number",step:"0.1"},model:{value:e.form.version,callback:function(t){e.$set(e.form,"version",t)},expression:"form.version"}})],1),r("el-form-item",{attrs:{label:"最新固件",prop:"isLatest"}},[r("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.isLatest,callback:function(t){e.$set(e.form,"isLatest",t)},expression:"form.isLatest"}}),r("el-link",{staticStyle:{"font-size":"12px","margin-left":"15px"},attrs:{type:"info",underline:!1}},[e._v("提示：产品中只能有一个最新固件")])],1),r("el-form-item",{attrs:{label:"选择固件",prop:"filePath"}},[r("fileUpload",{ref:"file-upload",attrs:{value:e.form.filePath,limit:1,fileSize:10,fileType:["bin","zip","pdf"]},on:{input:function(t){return e.getFilePath(t)}}})],1),r("el-form-item",{attrs:{label:"固件描述",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"对本次上次的固件进行描述和记录,请输入0-100个字符"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保 存")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),r("deviceList",{ref:"deviceList",attrs:{upGrade:e.formUpGrade}}),r("el-dialog",{attrs:{title:e.title,visible:e.openUpGrade,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openUpGrade=t}}},[r("el-form",{ref:"formUpGrade",attrs:{model:e.formUpGrade,rules:e.rulesUpGrade,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"固件名称",prop:"firmwareName"}},[e._v(" "+e._s(e.form.firmwareName)+" ")]),r("el-form-item",{attrs:{label:"所属产品",prop:"productId"}},[e._v(" "+e._s(e.form.productName)+" ")]),r("el-form-item",{attrs:{label:"固件版本号",prop:"version"}},[e._v(" Version "+e._s(e.form.version)+" ")]),r("el-form-item",{attrs:{label:"任务名称",prop:"taskName"}},[r("el-input",{attrs:{placeholder:"请输入任务名称(支持中文，英文大小写、数字、部分常用符号，最大长度20)",maxlength:"20"},model:{value:e.formUpGrade.taskName,callback:function(t){e.$set(e.formUpGrade,"taskName",t)},expression:"formUpGrade.taskName"}})],1),r("el-form-item",{attrs:{label:"升级方式",prop:"upgradeType"}},[r("el-radio-group",{attrs:{size:"small"},on:{change:e.changeUpgradeType},model:{value:e.formUpGrade.upgradeType,callback:function(t){e.$set(e.formUpGrade,"upgradeType",t)},expression:"formUpGrade.upgradeType"}},[r("el-radio-button",{attrs:{label:"1",active:""}},[e._v("按固件版本")]),r("el-radio-button",{attrs:{label:"2"}},[e._v("按设备名称")])],1)],1),1==e.formUpGrade.upgradeType?r("el-form-item",{attrs:{label:"待升级版本号",prop:"upVersion"}},[r("el-select",{attrs:{placeholder:"请选择升级版本号"},on:{change:e.selectUpGradeVersion},model:{value:e.formUpGrade.upVersion,callback:function(t){e.$set(e.formUpGrade,"upVersion",t)},expression:"formUpGrade.upVersion"}},e._l(e.upGradeVersionList,(function(t){return r("el-option",{key:t.firmwareId,attrs:{label:t.firmwareName,value:t.firmwareId}},[r("span",{staticStyle:{float:"left"}},[e._v(e._s(t.firmwareName))]),r("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v("V."+e._s(t.version))])])})),1)],1):e._e(),1==e.formUpGrade.upgradeType?r("el-form-item",{attrs:{label:"升级范围",prop:"upType"}},[r("el-select",{attrs:{placeholder:"请选择升级范围",clearable:""},on:{change:e.changeUpType},model:{value:e.formUpGrade.upType,callback:function(t){e.$set(e.formUpGrade,"upType",t)},expression:"formUpGrade.upType"}},e._l(e.dict.type.oat_update_limit,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),r("el-form-item",{attrs:{label:"选中设备"}},[r("el-badge",{staticClass:"item",attrs:{value:e.formUpGrade.deviceAmount}},[r("el-button",{staticClass:"mr5",attrs:{type:"primary",size:"mini",plain:""},on:{click:e.selectDeviceList}},[e._v("选择设备")])],1),e._l(e.formUpGrade.deviceList,(function(t){return r("el-tag",{key:t,attrs:{closable:"",size:"small","disable-transitions":!1},on:{close:function(r){return e.handleClose(t)}}},[e._v(" "+e._s(t)+" ")])}))],2),r("el-form-item",{attrs:{label:"预定升级时间",prop:"bookTime"}},[r("el-date-picker",{attrs:{type:"datetime",placeholder:"选择日期时间"},model:{value:e.formUpGrade.bookTime,callback:function(t){e.$set(e.formUpGrade,"bookTime",t)},expression:"formUpGrade.bookTime"}})],1),r("el-form-item",{attrs:{label:"升级描述",prop:"taskDesc"}},[r("el-input",{attrs:{type:"textarea",placeholder:"对本次升级任务进行描述和记录,请输入0-200个字符"},model:{value:e.formUpGrade.taskDesc,callback:function(t){e.$set(e.formUpGrade,"taskDesc",t)},expression:"formUpGrade.taskDesc"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitFormUpGrade}},[e._v("保 存")]),r("el-button",{on:{click:e.canceUpGrade}},[e._v("取 消")])],1)],1)],1)],1)},a=[],n=r("5530"),o=(r("14d9"),r("d81d"),r("e9c4"),r("b0c0"),r("d3b7"),r("159b"),r("a434"),r("2a75")),l=r("1ab6"),s=r("9b9c"),u=r("814a"),c=r("2065"),d=r("584f"),m=r("5f87"),p={name:"Firmware",dicts:["iot_yes_no","oat_update_limit"],components:{fileUpload:o["a"],deviceList:l["default"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,firmwareList:[],productShortList:[],upGradeVersionList:[],group:{},title:"",open:!1,openUpGrade:!1,queryUpGradeVersion:{productId:null,version:0},queryDeviceByVersion:{productId:null,firmwareVersion:0},queryParams:{pageNum:1,pageSize:10,firmwareName:null,productName:null,tenantName:null,isSys:null},form:{version:1},rules:{firmwareName:[{required:!0,message:"固件名称不能为空",trigger:"blur"}],productId:[{required:!0,message:"产品ID不能为空",trigger:"blur"}],productName:[{required:!0,message:"产品名称不能为空",trigger:"blur"}],version:[{required:!0,message:"固件版本不能为空",trigger:"blur"}],filePath:[{required:!0,message:"文件路径不能为空",trigger:"blur"}]},formUpGrade:{taskName:null,firmwareId:0,deviceAmount:0,bookTime:null,upgradeType:"1",upType:null,deviceList:[],version:null,flag:!1},rulesUpGrade:{taskName:[{required:!0,message:"任务名称不能为空",trigger:"blur"}]},upload:{isUploading:!1,headers:{Authorization:"Bearer "+Object(m["a"])()},url:"/prod-api/iot/tool/upload",fileList:[]}}},created:function(){this.getList(),this.getProductShortList()},methods:{handleViewProduct:function(e){this.$router.push({path:"/iot/product-edit",query:{t:Date.now(),productId:e}})},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},getList:function(){var e=this;this.loading=!0,Object(u["e"])(this.queryParams).then((function(t){e.firmwareList=t.rows,e.total=t.total,e.loading=!1}))},getProductShortList:function(){var e=this;Object(s["g"])().then((function(t){e.productShortList=t.data}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={firmwareId:null,firmwareName:null,productId:null,productName:null,tenantId:null,tenantName:null,isLatest:0,isSys:null,version:1,filePath:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.formUpGrade={taskName:null,firmwareId:0,deviceAmount:0,bookTime:null,upgradeType:"1",upType:null,deviceList:[],version:null,flag:!1},this.resetForm("form"),this.resetForm("formUpGrade")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.firmwareId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加新固件",this.upload.fileList=[]},handleEdit:function(e){var t=this;this.reset();var r=e.firmwareId||this.ids;Object(u["c"])(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改产品固件",t.upload.fileList=[{name:t.form.firmwareName,url:t.form.filePath}]}))},handleInfo:function(e){e&&(sessionStorage.setItem("firmwareTaskInfo",JSON.stringify(e)),this.$router.push({name:"FirmwareTask"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.firmwareId?Object(u["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(u["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.firmwareId||this.ids;this.$modal.confirm('是否确认删除产品固件编号为"'+r+'"的数据项？').then((function(){return Object(u["b"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/firmware/export",Object(n["a"])({},this.queryParams),"firmware_".concat((new Date).getTime(),".xlsx"))},selectProduct:function(e){for(var t=0;t<this.productShortList.length;t++)if(this.productShortList[t].id==e)return void(this.form.productName=this.productShortList[t].name)},changeUpgradeType:function(e){this.resetDeviceList(),2==e&&(this.formUpGrade.version=null)},changeUpType:function(e){var t=this;this.resetDeviceList(),"1"==e&&(this.queryDeviceByVersion.productId=this.formUpGrade.productId,this.queryDeviceByVersion.firmwareVersion=this.formUpGrade.version,Object(d["l"])(this.queryDeviceByVersion).then((function(e){e.rows.forEach((function(e){t.formUpGrade.deviceList.push(e.serialNumber)})),t.formUpGrade.deviceAmount=e.total})))},selectUpGradeVersion:function(e){this.resetDeviceList();for(var t=0;t<this.upGradeVersionList.length;t++)if(this.upGradeVersionList[t].firmwareId==e)return void(this.formUpGrade.version=this.upGradeVersionList[t].version)},resetDeviceList:function(){this.formUpGrade.upType=this.formUpGrade.upType,this.formUpGrade.deviceList=[],this.formUpGrade.deviceAmount=0},selectDeviceList:function(){this.formUpGrade.flag=!0,this.$refs.deviceList.openDeviceList=!0},handleClose:function(e){this.formUpGrade.deviceList.splice(this.formUpGrade.deviceList.indexOf(e),1),this.formUpGrade.deviceAmount=this.formUpGrade.deviceList.length},getFilePath:function(e){this.form.filePath=e},submitUpload:function(){this.$refs.upload.submit()},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.isUploading=!1,this.form.filePath=e.url,this.$modal.msgSuccess(e.msg)},handleDownload:function(e){window.open("/prod-api"+e.filePath)},otaUpGrade:function(e){var t=this;this.reset();var r=e.firmwareId||this.ids;Object(u["c"])(r).then((function(e){t.form=e.data,t.openUpGrade=!0,t.title="固件升级",t.getUpGradeVersionList(),t.formUpGrade.productId=t.form.productId,t.formUpGrade.firmwareId=r}))},submitFormUpGrade:function(){var e=this;this.$refs["formUpGrade"].validate((function(t){t&&(e.formUpGrade.deviceAmount>0?Object(c["a"])(e.formUpGrade).then((function(t){200==t.code?(e.$modal.msgSuccess("修改成功"),e.openUpGrade=!1,e.reset()):e.$modal.msgError(res.data.message)})):e.$modal.msgError("必须有选中设备才能提交固件升级任务！"))}))},canceUpGrade:function(){this.openUpGrade=!1,this.reset()},getUpGradeVersionList:function(){var e=this;this.queryUpGradeVersion.productId=this.form.productId,this.queryUpGradeVersion.version=this.form.version,Object(u["f"])(this.queryUpGradeVersion).then((function(t){e.upGradeVersionList=t.data}))}}},f=p,h=(r("b9ca"),r("2877")),v=Object(h["a"])(f,i,a,!1,null,"88aeeb2c",null);t["default"]=v.exports},"814a":function(e,t,r){"use strict";r.d(t,"e",(function(){return a})),r.d(t,"f",(function(){return n})),r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return l})),r.d(t,"a",(function(){return s})),r.d(t,"g",(function(){return u})),r.d(t,"b",(function(){return c}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/firmware/getLatest/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},"9b9c":function(e,t,r){"use strict";r.d(t,"f",(function(){return a})),r.d(t,"g",(function(){return n})),r.d(t,"e",(function(){return o})),r.d(t,"a",(function(){return l})),r.d(t,"i",(function(){return s})),r.d(t,"d",(function(){return u})),r.d(t,"b",(function(){return c})),r.d(t,"c",(function(){return d})),r.d(t,"h",(function(){return m}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/product/list",method:"get",params:e})}function n(){return Object(i["a"])({url:"/iot/product/shortList",method:"get"})}function o(e){return Object(i["a"])({url:"/iot/product/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/product",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/iot/product",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/product/status/",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/product/"+e,method:"delete"})}function m(e){return Object(i["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:e})}},ae16:function(e,t,r){},b9ca:function(e,t,r){"use strict";r("ae16")}}]);