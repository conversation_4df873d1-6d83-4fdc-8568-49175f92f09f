(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2e1194f8","chunk-0181411d","chunk-085c5162","chunk-40553a81","chunk-330f30cf","chunk-1127e5d0"],{"06d9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"设备导入记录",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{prop:"productId"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"产品名称",filterable:""},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"批次任务状态",filterable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-date-picker",{staticStyle:{width:"180px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"mini"}},[a("el-table-column",{attrs:{label:"批次号",align:"left"}}),a("el-table-column",{attrs:{label:"设备总数",align:"center"}}),a("el-table-column",{attrs:{label:"成功数量",align:"center"}}),a("el-table-column",{attrs:{label:"失败数量",align:"center"}}),a("el-table-column",{attrs:{label:"批次任务数量",align:"center"}}),a("el-table-column",{attrs:{label:"完成时间",align:"center"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)}}})],1)])},i=[],l=(a("d81d"),a("9b9c")),o=a("584f"),n={name:"importRecord",dicts:[],data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],queryParams:{pageNum:1,pageSize:10,productName:null}}},created:function(){this.getProductList()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999};Object(l["f"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(o["o"])().then((function(t){e.dataList=t.rows,e.toltal=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}},s=n,u=a("2877"),c=Object(u["a"])(s,r,i,!1,null,null,null);t["default"]=c.exports},"10f3":function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"g",(function(){return s})),a.d(t,"f",(function(){return u})),a.d(t,"b",(function(){return c}));var r=a("b775");function i(e){return Object(r["a"])({url:"/iot/group/list",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/iot/group/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/iot/group/getDeviceIds/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/iot/group",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/iot/group",method:"put",data:e})}function u(e){return Object(r["a"])({url:"/iot/group/updateDeviceGroups",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/iot/group/"+e,method:"delete"})}},1803:function(e,t,a){"use strict";a("bbc4")},"3aec":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.importDeviceDialog,width:"550px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"importDeviceDialog",t)}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"importForm",attrs:{"label-position":"top",model:e.importForm,rules:e.importRules}},[a("el-form-item",{attrs:{label:"产品",prop:"productId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择产品",filterable:""},model:{value:e.importForm.productId,callback:function(t){e.$set(e.importForm,"productId",t)},expression:"importForm.productId"}},e._l(e.productList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"上传文件",prop:"fileList"}},[a("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?productId="+e.importForm.productId,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,"on-change":e.handleChange,"on-remove":e.handleRemove,drag:""},model:{value:e.importForm.fileList,callback:function(t){e.$set(e.importForm,"fileList",t)},expression:"importForm.fileList"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticStyle:{"margin-top":"10px"}},[a("span",[e._v("提示:仅允许导入xls、xlsx格式文件。")])])])]),a("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[a("i",{staticClass:"el-icon-download"}),e._v("下载设备导入模板")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.importDeviceDialog=!1}}},[e._v("取 消")])],1)])},i=[],l=(a("d81d"),a("9b9c")),o=a("5f87"),n={name:"batchImport",data:function(){return{type:1,importForm:{productId:null,fileList:[]},productList:[],file:null,upload:{importDeviceDialog:!1,title:"批量导入",isUploading:!1,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/iot/device/importData"},importRules:{productId:[{required:!0,message:"产品不能为空",trigger:"change"}],fileList:[{required:!0,message:"请上传文件",trigger:"change"}]}}},created:function(){this.getProductList()},methods:{importTemplate:function(){this.download("/iot/device/uploadTemplate?type="+this.type,{},"device_template_".concat((new Date).getTime(),".xlsx"))},handleChange:function(e,t){this.importForm.fileList=t,this.importForm.fileList&&this.$refs.importForm.clearValidate("fileList")},handleRemove:function(e,t){this.importForm.fileList=t,this.$refs.importForm.validateField("fileList")},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.importDeviceDialog=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0})},getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999};Object(l["f"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.total=t.total,e.loading=!1}))},submitFileForm:function(){var e=this;this.$refs["importForm"].validate((function(t){t&&(e.$refs.upload.submit(),e.upload.importDeviceDialog=!1)}))}}},s=n,u=a("2877"),c=Object(u["a"])(s,r,i,!1,null,null,null);t["default"]=c.exports},"4efc":function(e,t,a){e.exports=a.p+"static/img/gateway.5a51e30f.png"},"52bb":function(e,t,a){e.exports=a.p+"static/img/product.66c3c4d5.png"},"584f":function(e,t,a){"use strict";a.d(t,"l",(function(){return i})),a.d(t,"q",(function(){return l})),a.d(t,"m",(function(){return o})),a.d(t,"n",(function(){return n})),a.d(t,"k",(function(){return s})),a.d(t,"f",(function(){return u})),a.d(t,"c",(function(){return c})),a.d(t,"g",(function(){return d})),a.d(t,"i",(function(){return p})),a.d(t,"d",(function(){return m})),a.d(t,"r",(function(){return h})),a.d(t,"o",(function(){return f})),a.d(t,"p",(function(){return v})),a.d(t,"h",(function(){return g})),a.d(t,"a",(function(){return b})),a.d(t,"s",(function(){return y})),a.d(t,"b",(function(){return w})),a.d(t,"e",(function(){return x})),a.d(t,"j",(function(){return q}));var r=a("b775");function i(e){return Object(r["a"])({url:"/iot/device/list",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/iot/device/shortList",method:"get",params:e})}function s(){return Object(r["a"])({url:"/iot/device/all",method:"get"})}function u(e){return Object(r["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function p(){return Object(r["a"])({url:"/iot/device/statistic",method:"get"})}function m(e,t){return Object(r["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function h(e){return Object(r["a"])({url:"/iot/device/recovery?deviceIds="+e,method:"post"})}function f(){return Object(r["a"])({url:"",method:"get"})}function v(){return Object(r["a"])({url:"",method:"get"})}function g(e){return Object(r["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function b(e){return Object(r["a"])({url:"/iot/device",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/iot/device",method:"put",data:e})}function w(e){return Object(r["a"])({url:"/iot/device/"+e,method:"delete"})}function x(e){return Object(r["a"])({url:"/iot/device/generator",method:"get",params:e})}function q(e){return Object(r["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},"9b9c":function(e,t,a){"use strict";a.d(t,"f",(function(){return i})),a.d(t,"g",(function(){return l})),a.d(t,"e",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"i",(function(){return s})),a.d(t,"d",(function(){return u})),a.d(t,"b",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"h",(function(){return p}));var r=a("b775");function i(e){return Object(r["a"])({url:"/iot/product/list",method:"get",params:e})}function l(){return Object(r["a"])({url:"/iot/product/shortList",method:"get"})}function o(e){return Object(r["a"])({url:"/iot/product/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/iot/product",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/iot/product",method:"put",data:e})}function u(e){return Object(r["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/iot/product/status/",method:"put",data:e})}function d(e){return Object(r["a"])({url:"/iot/product/"+e,method:"delete"})}function p(e){return Object(r["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:e})}},bbc4:function(e,t,a){},bed0:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"设备分配记录",visible:e.open,width:"70%"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{staticStyle:{width:"240px"},attrs:{prop:"deptId"}},[a("treeselect",{attrs:{options:e.deptOptions,"show-count":!0,placeholder:"归属机构"},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}})],1),a("el-form-item",{attrs:{prop:"productId"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"产品名称",filterable:""},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"mini"}},[a("el-table-column",{attrs:{label:"产品名称",align:"left"}}),a("el-table-column",{attrs:{align:"left"}},[a("template",{slot:"header"},[a("span",[e._v("归属机构")]),a("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:"设备被分配前的归属机构",placement:"top"}},[a("i",{staticClass:"el-icon-warning-outline"})])],1)],2),a("el-table-column",{attrs:{align:"left"}},[a("template",{slot:"header"},[a("span",[e._v("目标机构")]),a("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:"设备被分配后的归属机构",placement:"top"}},[a("i",{staticClass:"el-icon-warning-outline"})])],1)],2),a("el-table-column",{attrs:{label:"分配总数",align:"left"}}),a("el-table-column",{attrs:{label:"分配成功",align:"left"}}),a("el-table-column",{attrs:{label:"分配失败",align:"left"}}),a("el-table-column",{attrs:{label:"分配状态",align:"left"}}),a("el-table-column",{attrs:{label:"分配方式",align:"left"}}),a("el-table-column",{attrs:{label:"分配时间",align:"left"}}),a("el-table-column",{attrs:{label:"操作",align:"center"}},[[a("el-button",{staticStyle:{padding:"5px"},attrs:{type:"danger",size:"small",icon:"el-icon-delete"}},[e._v("下载明细")])]],2)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)}}})],1)])},i=[],l=(a("d81d"),a("9b9c")),o=a("584f"),n=a("c0c7"),s=a("ca17"),u=a.n(s),c=(a("542c"),{name:"allotRecord",dicts:[],components:{Treeselect:u.a},data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],deptOptions:[],queryParams:{pageNum:1,pageSize:10,productName:null,serialNumber:""}}},created:function(){this.getProductList(),this.getDeptTree()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999};Object(l["f"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(o["p"])().then((function(t){e.dataList=t.rows,e.toltal=t.total,e.loading=!1}))},getDeptTree:function(){var e=this;Object(n["d"])().then((function(t){e.deptOptions=t.data}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}}),d=c,p=a("2877"),m=Object(p["a"])(d,r,i,!1,null,null,null);t["default"]=m.exports},c0c7:function(e,t,a){"use strict";a.d(t,"k",(function(){return l})),a.d(t,"i",(function(){return o})),a.d(t,"h",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"o",(function(){return u})),a.d(t,"c",(function(){return c})),a.d(t,"l",(function(){return d})),a.d(t,"b",(function(){return p})),a.d(t,"g",(function(){return m})),a.d(t,"m",(function(){return h})),a.d(t,"j",(function(){return f})),a.d(t,"p",(function(){return v})),a.d(t,"q",(function(){return g})),a.d(t,"r",(function(){return b})),a.d(t,"f",(function(){return y})),a.d(t,"n",(function(){return w})),a.d(t,"d",(function(){return x})),a.d(t,"e",(function(){return q}));var r=a("b775"),i=a("c38a");function l(e){return Object(r["a"])({url:"/system/user/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/user/"+Object(i["e"])(e),method:"get"})}function n(e){return Object(r["a"])({url:"/system/dept/getRole?deptId="+e,method:"get"})}function s(e){return Object(r["a"])({url:"/system/user",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/user",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/user/"+e,method:"delete"})}function d(e,t){var a={userId:e,password:t};return Object(r["a"])({url:"/system/user/resetPwd",method:"put",data:a})}function p(e,t){var a={userId:e,status:t};return Object(r["a"])({url:"/system/user/changeStatus",method:"put",data:a})}function m(){return Object(r["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(e){return Object(r["a"])({url:"/wechat/cancelBind",method:"post",data:e})}function f(){return Object(r["a"])({url:"/system/user/profile",method:"get"})}function v(e){return Object(r["a"])({url:"/system/user/profile",method:"put",data:e})}function g(e,t){var a={oldPassword:e,newPassword:t};return Object(r["a"])({url:"/system/user/profile/updatePwd",method:"put",params:a})}function b(e){return Object(r["a"])({url:"/system/user/profile/avatar",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/system/user/authRole/"+e,method:"get"})}function w(e){return Object(r["a"])({url:"/system/user/authRole",method:"put",params:e})}function x(){return Object(r["a"])({url:"/system/user/deptTree",method:"get"})}function q(e){return Object(r["a"])({url:"/system/user/deptTree?showOwner="+e,method:"get"})}},c572:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{padding:"6px"}},[r("el-card",{staticStyle:{"margin-bottom":"6px"}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticStyle:{"margin-bottom":"-20px"},attrs:{model:e.queryParams,inline:!0,"label-width":"75px"}},[r("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[r("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),r("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[r("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),r("el-form-item",{attrs:{label:"设备状态",prop:"status"}},[r("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择设备状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_device_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"我的分组"}},[r("el-select",{staticStyle:{width:"150px"},attrs:{placeholder:"请选择我的分组",clearable:"",size:"small"},model:{value:e.queryParams.groupId,callback:function(t){e.$set(e.queryParams,"groupId",t)},expression:"queryParams.groupId"}},e._l(e.myGroupList,(function(e){return r("el-option",{key:e.groupId,attrs:{label:e.groupName,value:e.groupId}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],on:{command:function(t){return e.handleCommand(t)}}},[r("el-button",{attrs:{size:"mini",type:"primary",plain:""}},[e._v("新增设备"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{command:"handleEditDevice"}},[e._v("手动添加")]),r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{command:"handleBatchImport"}},[e._v("批量导入")])],1)],1)],1),r("el-col",{attrs:{span:1.5}},[r("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],on:{command:function(t){return e.handleCommand1(t)}}},[r("el-button",{attrs:{size:"mini",type:"primary",plain:""}},[e._v("分配设备"),r("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],attrs:{command:"handleSelectAllot"}},[e._v("选择分配")]),r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],attrs:{command:"handleImportAllot"}},[e._v("导入分配")])],1)],1)],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:recovery"],expression:"['iot:device:recovery']"}],attrs:{size:"mini",type:"primary",plain:""},on:{click:e.recycleDevice}},[e._v("回收设备")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-s-grid",size:"mini"},on:{click:e.handleChangeShowType}},[e._v("切换展示")])],1),r("el-col",{attrs:{span:1.5}},[r("el-checkbox",{staticStyle:{margin:"5px 0 0"},on:{change:e.handleQuery},model:{value:e.queryParams.showChild,callback:function(t){e.$set(e.queryParams,"showChild",t)},expression:"queryParams.showChild"}},[e._v("显示下级机构数据")]),r("el-tooltip",{attrs:{content:"选中后，本级可以看下级的数据",placement:"top"}},[r("i",{staticClass:"el-icon-question"})])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1)],1)],1),"list"==e.showType?r("el-card",{staticStyle:{"padding-bottom":"100px"}},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList,border:""}},[r("el-table-column",{attrs:{label:"编号",align:"center","header-align":"center",prop:"deviceId",width:"50"}}),r("el-table-column",{attrs:{label:"设备名称",align:"center","header-align":"center",prop:"deviceName","min-width":"120"}}),r("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber","min-width":"130"}}),r("el-table-column",{attrs:{label:"所属产品",align:"center",prop:"productName","min-width":"120"}}),r("el-table-column",{attrs:{label:"协议",align:"center",prop:"transport","min-width":"50"}}),r("el-table-column",{attrs:{label:"通讯协议",align:"center",prop:"protocolCode","min-width":"100"}}),r("el-table-column",{attrs:{label:"子设备数",align:"center",prop:"subDeviceCount",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.subDeviceCount)+" ")]}}],null,!1,1050122242)}),r("el-table-column",{attrs:{label:"设备影子",align:"center",prop:"isShadow",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isShadow?r("el-tag",{attrs:{type:"success",size:"small"}},[e._v("启用")]):r("el-tag",{attrs:{type:"info",size:"small"}},[e._v("禁用")])]}}],null,!1,982151954)}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status,size:"small"}})]}}],null,!1,3188204005)}),r("el-table-column",{attrs:{label:"信号",align:"center",prop:"rssi",width:"60"},scopedSlots:e._u([{key:"default",fn:function(e){return[3==e.row.status&&e.row.rssi>="-55"?r("svg-icon",{attrs:{"icon-class":"wifi_4"}}):3==e.row.status&&e.row.rssi>="-70"&&e.row.rssi<"-55"?r("svg-icon",{attrs:{"icon-class":"wifi_3"}}):3==e.row.status&&e.row.rssi>="-85"&&e.row.rssi<"-70"?r("svg-icon",{attrs:{"icon-class":"wifi_2"}}):3==e.row.status&&e.row.rssi>="-100"&&e.row.rssi<"-85"?r("svg-icon",{attrs:{"icon-class":"wifi_1"}}):r("svg-icon",{attrs:{"icon-class":"wifi_0"}})]}}],null,!1,3608481156)}),r("el-table-column",{attrs:{label:"定位方式",align:"center",prop:"locationWay"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_location_way,value:t.row.locationWay,size:"small"}})]}}],null,!1,1502323453)}),r("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"firmwareVersion"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("Ver "+e._s(t.row.firmwareVersion))])]}}],null,!1,1749219447)}),r("el-table-column",{attrs:{label:"激活时间",align:"center",prop:"activeTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.activeTime,"{y}-{m}-{d}")))])]}}],null,!1,3799299972)}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}],null,!1,3484357996)}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],staticStyle:{padding:"5px"},attrs:{type:"danger",size:"small",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticStyle:{padding:"5px"},attrs:{type:"primary",size:"small",icon:"el-icon-view"},on:{click:function(a){return e.handleEditDevice(t.row)}}},[e._v("查看")]),0!=e.form.deviceId?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticStyle:{padding:"5px"},attrs:{type:"primary",size:"small"},on:{click:function(a){return e.openSummaryDialog(t.row)}}},[e._v("二维码")]):e._e()]}}],null,!1,3997248863)})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize,pageSizes:[12,24,36,60]},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1):e._e(),"card"==e.showType?r("el-card",{staticStyle:{"padding-bottom":"100px"}},[r("el-row",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{gutter:30}},e._l(e.deviceList,(function(t,i){return r("el-col",{key:i,staticStyle:{"margin-bottom":"30px","text-align":"center"},attrs:{xs:24,sm:12,md:12,lg:8,xl:6}},[r("el-card",{staticClass:"card-item",attrs:{"body-style":{padding:"20px"},shadow:"always"}},[r("el-row",{attrs:{type:"flex",gutter:10,justify:"space-between"}},[r("el-col",{staticStyle:{"text-align":"left"},attrs:{span:20}},[r("el-link",{staticStyle:{"font-weight":"bold","font-size":"16px","line-height":"32px"},attrs:{type:"",underline:!1},on:{click:function(a){return e.handleDeviceDetail(t)}}},[r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"分享的设备",placement:"top-start"}},[1!=t.isOwner?r("svg-icon",{staticStyle:{"font-size":"20px"},attrs:{"icon-class":"share"}}):e._e()],1),1==t.isOwner?r("svg-icon",{attrs:{"icon-class":"device"}}):e._e(),r("span",{staticStyle:{margin:"0 5px"}},[e._v(e._s(t.deviceName))])],1)],1),r("el-col",{staticStyle:{"font-size":"20px","padding-top":"5px",cursor:"pointer"},attrs:{span:1.5}},[r("svg-icon",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{"icon-class":"qrcode"},on:{click:function(a){return e.openSummaryDialog(t)}}})],1),r("el-col",{attrs:{span:3}},[r("div",{staticStyle:{"font-size":"28px",color:"#ccc"}},[3==t.status&&t.rssi>="-55"?r("svg-icon",{attrs:{"icon-class":"wifi_4"}}):3==t.status&&t.rssi>="-70"&&t.rssi<"-55"?r("svg-icon",{attrs:{"icon-class":"wifi_3"}}):3==t.status&&t.rssi>="-85"&&t.rssi<"-70"?r("svg-icon",{attrs:{"icon-class":"wifi_2"}}):3==t.status&&t.rssi>="-100"&&t.rssi<"-85"?r("svg-icon",{attrs:{"icon-class":"wifi_1"}}):r("svg-icon",{attrs:{"icon-class":"wifi_0"}})],1)])],1),r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:17}},[r("div",{staticStyle:{"text-align":"left","line-height":"40px","white-space":"nowrap"}},[r("dict-tag",{staticStyle:{display:"inline-block"},attrs:{options:e.dict.type.iot_device_status,value:t.status,size:"small"}}),r("span",{staticStyle:{display:"inline-block",margin:"0 10px"}},[t.protocolCode?r("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(e._s(t.protocolCode))]):e._e()],1),t.transport?r("el-tag",{attrs:{type:"primary",size:"small"}},[e._v(e._s(t.transport))]):e._e()],1),r("el-descriptions",{staticStyle:{"white-space":"nowrap"},attrs:{column:1,size:"mini"}},[r("el-descriptions-item",{attrs:{label:"编号"}},[e._v(" "+e._s(t.serialNumber)+" ")]),r("el-descriptions-item",{attrs:{label:"产品"}},[e._v(" "+e._s(t.productName)+" ")]),r("el-descriptions-item",{attrs:{label:"激活时间"}},[e._v(" "+e._s(e.parseTime(t.activeTime,"{y}-{m}-{d}"))+" ")])],1)],1),r("el-col",{attrs:{span:7}},[r("div",{staticStyle:{"margin-top":"10px"}},[null!=t.imgUrl&&""!=t.imgUrl?r("el-image",{staticStyle:{width:"100%",height:"100px","border-radius":"10px"},attrs:{lazy:"","preview-src-list":[e.baseUrl+t.imgUrl],src:e.baseUrl+t.imgUrl,fit:"cover"}}):2==t.deviceType?r("el-image",{staticStyle:{width:"100%",height:"100px","border-radius":"10px"},attrs:{"preview-src-list":[a("4efc")],src:a("4efc"),fit:"cover"}}):3==t.deviceType?r("el-image",{staticStyle:{width:"100%",height:"100px","border-radius":"10px"},attrs:{"preview-src-list":[a("c59e")],src:a("c59e"),fit:"cover"}}):r("el-image",{staticStyle:{width:"100%",height:"100px","border-radius":"10px"},attrs:{"preview-src-list":[a("52bb")],src:a("52bb"),fit:"cover"}})],1)])],1),r("el-button-group",{staticStyle:{"margin-top":"15px"}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],staticStyle:{padding:"5px 10px"},attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t)}}},[e._v("删除")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticStyle:{padding:"5px 15px"},attrs:{type:"primary",size:"mini",icon:"el-icon-view"},on:{click:function(a){return e.handleEditDevice(t,"basic")}}},[e._v("查看")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticStyle:{padding:"5px 15px"},attrs:{type:"success",size:"mini",icon:"el-icon-odometer"},on:{click:function(a){return e.handleRunDevice(t)}}},[e._v("运行状态")])],1)],1)],1)})),1),0==e.total?r("el-empty",{attrs:{description:"暂无数据，请添加设备"}}):e._e(),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize,pageSizes:[12,24,36,60]},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1):e._e(),r("el-dialog",{attrs:{visible:e.openSummary,width:"300px","append-to-body":""},on:{"update:visible":function(t){e.openSummary=t}}},[r("div",{staticStyle:{border:"1px solid #ccc",width:"220px","text-align":"center",margin:"0 auto","margin-top":"-15px"}},[r("vue-qr",{attrs:{text:e.qrText,size:200}}),r("div",{staticStyle:{"padding-bottom":"10px"}},[e._v("设备二维码")])],1)]),r("batchImport",{ref:"batchImport",on:{save:e.saveDialog}}),r("allotImport",{ref:"allotImport",on:{save:e.saveAllotDialog}}),r("importRecord",{ref:"importRecord"}),r("recycleRecord",{ref:"recycleRecord"}),r("allotRecord",{ref:"allotRecord"})],1)},i=[],l=a("c7eb"),o=a("1da1"),n=(a("14d9"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("658f5")),s=a.n(n),u=a("584f"),c=a("10f3"),d=a("f5a7"),p=a("dce4"),m=a("ca17"),h=a.n(m),f=(a("542c"),a("3aec")),v=a("f4c2"),g=a("06d9"),b=a("f1cb"),y=a("bed0"),w={name:"Device",dicts:["iot_device_status","iot_is_enable","iot_location_way","iot_transport_type"],components:{vueQr:s.a,Treeselect:h.a,batchImport:f["default"],allotImport:v["default"],importRecord:g["default"],recycleRecord:b["default"],allotRecord:y["default"]},data:function(){return{qrText:"fastbee",openSummary:!1,showSearch:!0,showType:"card",loading:!0,total:0,deviceList:[],myGroupList:[],baseUrl:"/prod-api",queryParams:{pageNum:1,pageSize:12,showChild:!0,deviceName:null,productId:null,groupId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,status:null,networkAddress:null,activeTime:null},form:{productId:0,status:1,locationWay:1,firmwareVersion:1,serialNumber:"",deviceType:1,isSimulate:0},productList:[],isSubDev:!1}},created:function(){var e=this.$route.query.productId;null!=e&&(this.queryParams.productId=Number(e),this.queryParams.groupId=null,this.queryParams.serialNumber=null);var t=this.$route.query.groupId;null!=t&&(this.queryParams.groupId=Number(t),this.queryParams.productId=null,this.queryParams.serialNumber=null);var a=this.$route.query.sn;null!=a&&(this.queryParams.serialNumber=a,this.queryParams.productId=null,this.queryParams.groupId=null),this.connectMqtt()},activated:function(){var e=this.$route.query.t;if(null!=e&&e!=this.uniqueId){this.uniqueId=e;var t=this.$route.query.pageNum;null!=t&&(this.queryParams.pageNum=Number(t));var a=this.$route.query.productId;null!=a&&(this.queryParams.productId=Number(a),this.queryParams.groupId=null,this.queryParams.serialNumber=null);var r=this.$route.query.groupId;null!=r&&(this.queryParams.groupId=Number(r),this.queryParams.productId=null,this.queryParams.serialNumber=null);var i=this.$route.query.sn;null!=i&&(this.queryParams.serialNumber=i,this.queryParams.productId=null,this.queryParams.groupId=null),this.getList()}},methods:{connectMqtt:function(){var e=this;return Object(o["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.$mqttTool.client){t.next=3;break}return t.next=3,e.$mqttTool.connect();case 3:e.mqttCallback(),e.getList();case 5:case"end":return t.stop()}}),t)})))()},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,r){var i=t.split("/"),l=(i[1],i[2]);if(a=JSON.parse(a.toString()),a&&"status"==i[3]){console.log("接收到【设备状态】主题：",t),console.log("接收到【设备状态】内容：",a);for(var o=0;o<e.deviceList.length;o++)if(e.deviceList[o].serialNumber==l)return e.deviceList[o].status=a.status,e.deviceList[o].isShadow=a.isShadow,void(e.deviceList[o].rssi=a.rssi)}}))},handleCommand:function(e){switch(e){case"handleEditDevice":this.handleEditDevice(0);break;case"handleBatchImport":this.handleBatchImport();break;default:break}},handleBatchImport:function(){this.$refs.batchImport.upload.importDeviceDialog=!0,this.$refs.batchImport.importForm.productId=null},handleImportAllot:function(){this.$refs.allotImport.upload.importAllotDialog=!0,this.$refs.allotImport.allotForm.productId=null,this.$refs.allotImport.allotForm.deptId=null},saveDialog:function(){this.getList()},saveAllotDialog:function(){this.getList()},handleCommand1:function(e){switch(e){case"handleSelectAllot":this.handleSelectAllot();break;case"handleImportAllot":this.handleImportAllot();break;default:break}},handleSelectAllot:function(){this.$router.push({path:"/iot/device-select-allot"})},recycleDevice:function(){this.$router.push({path:"/iot/device-recycle"})},handleCommandMore:function(e){switch(e){case"importRecord":this.handleImportRecord();break;case"exportDevice":this.handleexportDevice();break;case"recycleRecord":this.handleRecycleRecord();break;case"allotRecord":this.handleAllotRecord();break;default:break}},handleImportRecord:function(){this.$refs.importRecord.open=!0},handleRecycleRecord:function(){this.$refs.recycleRecord.open=!0},handleAllotRecord:function(){this.$refs.allotRecord.open=!0},openSummaryDialog:function(e){var t={type:1,deviceNumber:e.serialNumber,productId:e.productId,productName:e.productName};this.qrText=JSON.stringify(t),this.openSummary=!0},mqttSubscribe:function(e){for(var t=[],a=0;a<e.length;a++){var r="/+/"+e[a].serialNumber+"/status/post";t.push(r)}this.$mqttTool.subscribe(t)},getGroupList:function(){var e=this;this.loading=!0;var t={pageSize:30,pageNum:1,userId:this.$store.state.user.userId};Object(c["e"])(t).then((function(t){e.myGroupList=t.rows}))},getList:function(){var e=this;this.loading=!0,this.queryParams.params={},this.getGroupList(),Object(u["n"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.deviceList&&e.deviceList.length>0&&e.mqttSubscribe(e.deviceList),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.productId=null,this.queryParams.groupId=null,this.queryParams.serialNumber=null,this.resetForm("queryForm"),this.handleQuery()},handleChangeShowType:function(){this.showType="card"==this.showType?"list":"card"},handleDeviceDetail:function(e){p["a"].hasPermi("iot:device:query")&&this.handleEditDevice(e)},handleEditDevice:function(e,t){var a=0,r=0;0!=e&&(a=e.deviceId||this.ids,r=e.subDeviceCount>0?1:0),this.$router.push({path:"/iot/device-edit",query:{deviceId:a,isSubDev:r,pageNum:this.queryParams.pageNum,activeName:t}})},handleRunDevice:function(e){var t=0,a=0;0!=e&&(t=e.deviceId||this.ids,a=e.subDeviceCount>0?1:0),3===e.deviceType?this.$router.push({path:"/iot/device-edit",query:{deviceId:t,isSubDev:a,pageNum:this.queryParams.pageNum,activeName:"sipChannel"}}):this.$router.push({path:"/iot/device-edit",query:{deviceId:t,isSubDev:a,pageNum:this.queryParams.pageNum,activeName:"runningStatus"}})},handleDelete:function(e){var t=this,a=e.deviceId||this.ids;this.$modal.confirm('是否确认删除设备编号为"'+a+'"的数据项？').then((function(){return 3===e.deviceType&&Object(d["a"])(e.serialNumber),Object(u["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},shadowUnEnable:function(e,t){return 3!=e.status&&0==e.isShadow||!!t.isReadonly}}},x=w,q=(a("1803"),a("2877")),S=Object(q["a"])(x,r,i,!1,null,"498e9f1a",null);t["default"]=S.exports},c59e:function(e,t,a){e.exports=a.p+"static/img/video.fb1e1b71.png"},f1cb:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"设备回收记录",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{prop:"deptId"}},[a("treeselect",{staticStyle:{width:"180px"},attrs:{options:e.deptOptions,"show-count":!0,placeholder:"机构名称"},model:{value:e.queryParams.deptId,callback:function(t){e.$set(e.queryParams,"deptId",t)},expression:"queryParams.deptId"}})],1),a("el-form-item",{attrs:{prop:"productId"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"产品名称",filterable:""},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{prop:"serialNumber"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"mini"}},[a("el-table-column",{attrs:{label:"机构名称",align:"left"}}),a("el-table-column",{attrs:{label:"被回收机构名称",align:"left","min-width":"120"}}),a("el-table-column",{attrs:{label:"设备名称",align:"left"}}),a("el-table-column",{attrs:{label:"DeviceKey",align:"left"}}),a("el-table-column",{attrs:{label:"设备编号",align:"left"}}),a("el-table-column",{attrs:{label:"产品名称",align:"left"}}),a("el-table-column",{attrs:{label:"回收时间",align:"left"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)}}})],1)])},i=[],l=(a("d81d"),a("9b9c")),o=a("584f"),n=a("c0c7"),s=a("ca17"),u=a.n(s),c=(a("542c"),{name:"recycleRecord",dicts:[],components:{Treeselect:u.a},data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],deptOptions:[],queryParams:{pageNum:1,pageSize:10,productName:null,serialNumber:""}}},created:function(){this.getProductList(),this.getDeptTree()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999};Object(l["f"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(o["p"])().then((function(t){e.dataList=t.rows,e.toltal=t.total,e.loading=!1}))},getDeptTree:function(){var e=this;Object(n["d"])().then((function(t){e.deptOptions=t.data}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}}),d=c,p=a("2877"),m=Object(p["a"])(d,r,i,!1,null,null,null);t["default"]=m.exports},f4c2:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.importAllotDialog,width:"550px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"importAllotDialog",t)}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"allotForm",attrs:{"label-position":"top",model:e.allotForm,rules:e.allotRules}},[a("el-form-item",{attrs:{label:"产品",prop:"productId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择产品",filterable:""},model:{value:e.allotForm.productId,callback:function(t){e.$set(e.allotForm,"productId",t)},expression:"allotForm.productId"}},e._l(e.productList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"目标机构",prop:"deptId"}},[a("treeselect",{attrs:{options:e.deptOptions,"show-count":!0,placeholder:"请选择目标机构"},model:{value:e.allotForm.deptId,callback:function(t){e.$set(e.allotForm,"deptId",t)},expression:"allotForm.deptId"}})],1),a("el-form-item",{attrs:{label:"上传文件",prop:"fileList"}},[a("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?productId="+e.allotForm.productId+"&deptId="+e.allotForm.deptId,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,"on-change":e.handleChange,"on-remove":e.handleRemove,drag:""},model:{value:e.allotForm.fileList,callback:function(t){e.$set(e.allotForm,"fileList",t)},expression:"allotForm.fileList"}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticStyle:{"margin-top":"-5px"}},[a("span",[e._v("1.仅允许导入xls、xlsx格式文件。")]),a("div",{staticStyle:{"margin-top":"-10px"}},[a("span",[e._v("2.单次最多分配1000个设备,单次设备较多时需要较长的校验、导入时间。")])]),a("div",{staticStyle:{"margin-top":"-10px"}},[a("span",[e._v("3.上传文件并分配后，可到设备列表-更多操作-设备导入记录中查看上传失败的设备详情信息。")])])])])]),e._v(" "),a("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importAllotTemplate}},[a("i",{staticClass:"el-icon-download"}),e._v("设备分配模板")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.upload.importAllotDialog=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitImportDevice}},[e._v("确认分配")])],1)])},i=[],l=(a("d81d"),a("9b9c")),o=a("5f87"),n=a("c0c7"),s=a("ca17"),u=a.n(s),c=(a("542c"),{name:"allotImport",components:{Treeselect:u.a},data:function(){return{type:1,allotForm:{productId:0,deptId:0,fileList:[]},productList:[],deptOptions:[],upload:{title:"导入分配",importAllotDialog:!1,isUploading:!1,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/iot/device/importAssignmentData"},isSubDev:!1,allotRules:{productId:[{required:!0,message:"产品不能为空",trigger:"change"}],deptId:[{required:!0,message:"目标机构不能为空",trigger:"change"}],fileList:[{required:!0,message:"请上传文件",trigger:"change"}]}}},created:function(){this.getDeptTree(),this.getProductList()},methods:{getDeptTree:function(){var e=this;Object(n["d"])().then((function(t){e.deptOptions=t.data}))},importAllotTemplate:function(){this.type=2,this.download("/iot/device/uploadTemplate?type="+this.type,{},"allot_device_".concat((new Date).getTime(),".xlsx"))},handleChange:function(e,t){this.allotForm.fileList=t,this.allotForm.fileList&&this.$refs.allotForm.clearValidate("fileList")},handleRemove:function(e,t){this.allotForm.fileList=t,this.$refs.allotForm.validateField("fileList")},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0})},getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999};Object(l["f"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.total=t.total,e.loading=!1}))},submitImportDevice:function(){var e=this;this.$refs["allotForm"].validate((function(t){t&&(e.$refs.upload.submit(),e.upload.importAllotDialog=!1)}))}}}),d=c,p=a("2877"),m=Object(p["a"])(d,r,i,!1,null,null,null);t["default"]=m.exports},f5a7:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"c",(function(){return o}));var r=a("b775");function i(e){return Object(r["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function o(e,t,a){return Object(r["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:a})}}}]);