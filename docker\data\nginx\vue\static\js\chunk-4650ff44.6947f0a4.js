(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4650ff44"],{1138:function(e,t,r){"use strict";r.r(t);var s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"system-terminal-user"},[r("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-card"},[r("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{prop:"userName"}},[r("el-input",{attrs:{placeholder:e.$t("user.index.098976-2"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),r("el-form-item",{attrs:{prop:"phonenumber"}},[r("el-input",{attrs:{placeholder:e.$t("user.index.098976-18"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phonenumber,callback:function(t){e.$set(e.queryParams,"phonenumber",t)},expression:"queryParams.phonenumber"}})],1),r("el-form-item",{attrs:{prop:"status"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:e.$t("user.index.098976-6"),clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("div",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),r("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1)],1),r("el-card",[r("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(e._s(e.$t("del")))])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch,columns:e.columns},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList,border:!1},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",align:"center",width:"55"}}),e.columns[0].visible?r("el-table-column",{key:"userId",attrs:{label:e.$t("user.index.098976-30"),align:"left",prop:"userId","show-overflow-tooltip":!0,"min-width":"100"}}):e._e(),e.columns[1].visible?r("el-table-column",{key:"userName",attrs:{label:e.$t("user.index.098976-10"),align:"left",prop:"userName","show-overflow-tooltip":!0,"min-width":"220"}}):e._e(),e.columns[2].visible?r("el-table-column",{key:"nickName",attrs:{label:e.$t("user.index.098976-11"),align:"left",prop:"nickName","show-overflow-tooltip":!0,"min-width":"220"}}):e._e(),e.columns[3].visible?r("el-table-column",{key:"phonenumber",attrs:{label:e.$t("user.index.098976-13"),align:"center",prop:"phonenumber",width:"120"}}):e._e(),e.columns[4].visible?r("el-table-column",{key:"status",attrs:{label:e.$t("status"),align:"center",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":0,"inactive-value":1},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}],null,!1,828910814)}):e._e(),e.columns[6].visible?r("el-table-column",{attrs:{label:e.$t("creatTime"),align:"center",prop:"createTime",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}):e._e(),r("el-table-column",{attrs:{fixed:"right",label:e.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"215"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!==t.row.userId?[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("update")))]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("del")))]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd"],expression:"['system:user:resetPwd']"}],attrs:{size:"small",type:"text",icon:"el-icon-key"},on:{click:function(r){return e.handleResetPwd(t.row)}}},[e._v(e._s(e.$t("user.index.098976-15")))])]:void 0}}],null,!0)})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:e.$t("user.index.098976-11"),prop:"nickName"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("user.index.098976-16"),maxlength:"30"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1),r("el-form-item",{attrs:{label:e.$t("user.index.098976-13"),prop:"phonenumber"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("user.index.098976-18"),maxlength:"11"},model:{value:e.form.phonenumber,callback:function(t){e.$set(e.form,"phonenumber",t)},expression:"form.phonenumber"}})],1),r("el-form-item",{attrs:{label:e.$t("user.index.098976-19"),prop:"email"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("user.index.098976-20"),maxlength:"50"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1),void 0==e.form.userId?r("el-form-item",{attrs:{label:e.$t("user.index.098976-10"),prop:"userName"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("user.index.098976-2"),maxlength:"30"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1):e._e(),void 0==e.form.userId?r("el-form-item",{attrs:{label:e.$t("role.selectUser.093468-2"),prop:"password"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("user.index.098976-22"),type:"password",maxlength:"20","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1):e._e(),r("el-form-item",{attrs:{label:e.$t("status"),prop:"status"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:Number(t.value)}},[e._v(e._s(t.label))])})),1)],1),r("el-form-item",{attrs:{label:e.$t("role.selectUser.093468-3"),prop:"roleIds"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{multiple:"",placeholder:e.$t("role.selectUser.093468-4"),disabled:void 0!=this.form.userId},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}},e._l(e.roleOptions,(function(e){return r("el-option",{key:e.roleId,attrs:{label:e.roleName,value:e.roleId,disabled:1==e.status}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("remark")}},[r("el-input",{staticStyle:{width:"400px"},attrs:{type:"textarea",placeholder:e.$t("plzInput")},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("confirm")))]),r("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1)],1)},n=[],a=(r("4de4"),r("d81d"),r("d3b7"),r("c0c7")),i=(r("5f87"),r("ca17")),o=r.n(i),l=(r("542c"),{name:"User",dicts:["sys_normal_disable","sys_user_sex"],components:{Treeselect:o.a},data:function(){return{loading:!0,ids:[],showSearch:!0,total:0,userList:null,title:"",open:!1,initPassword:void 0,roleOptions:[],form:{},multiple:!0,queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},options:[{value:!0,label:this.$t("user.index.098976-28")},{value:!1,label:this.$t("user.index.098976-29")}],columns:[{key:0,label:this.$t("user.index.098976-30"),visible:!0},{key:1,label:this.$t("user.index.098976-10"),visible:!0},{key:2,label:this.$t("user.index.098976-11"),visible:!0},{key:3,label:this.$t("user.index.098976-29"),visible:!0},{key:4,label:this.$t("user.index.098976-13"),visible:!0},{key:5,label:this.$t("status"),visible:!0},{key:6,label:this.$t("creatTime"),visible:!0}],rules:{userName:[{required:!0,message:this.$t("user.index.098976-31"),trigger:"blur"},{min:2,max:20,message:this.$t("user.index.098976-32"),trigger:"blur"}],nickName:[{required:!0,message:this.$t("user.index.098976-33"),trigger:"blur"}],password:[{required:!0,message:this.$t("user.index.098976-34"),trigger:"blur"},{min:5,max:20,message:this.$t("user.index.098976-35"),trigger:"blur"}],roleIds:[{required:!0,message:this.$t("user.index.098976-36"),trigger:"change"}],status:[{required:!0}],email:[{type:"email",message:this.$t("user.index.098976-37"),trigger:["blur","change"]}],phonenumber:[{required:!0,message:this.$t("user.index.098976-38"),trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:this.$t("user.index.098976-39"),trigger:"blur"}]}}},watch:{deptName:function(e){this.$refs.tree.filter(e)}},created:function(){var e=this,t=this.$route.params&&this.$route.params.deptId;t?(this.queryParams.deptId=t,this.getList()):this.getList(),this.getConfigKey("sys.user.initPassword").then((function(t){e.initPassword=t.msg}))},methods:{getList:function(){var e=this;this.loading=!0,this.form.deptId=this.queryParams.deptId,Object(a["o"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.userList=t.rows,e.total=t.total,e.loading=!1}))},handleStatusChange:function(e){var t=this,r="0"===e.status?this.$t("simulate.index.111543-54"):this.$t("simulate.index.111543-55");this.$modal.confirm(this.$t("user.index.098976-40")+r+'""'+e.userName+this.$t("user.index.098976-41")).then((function(){return Object(a["b"])(e.userId,e.status)})).then((function(){t.$modal.msgSuccess(r+t.$t("success"))})).catch((function(){e.status=0===e.status?1:0}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.userId})),this.single=1!==e.length,this.multiple=!e.length},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:0,remark:void 0,postIds:[],roleIds:[]},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.deptId=void 0,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleAdd:function(){var e=this;this.reset(),Object(a["j"])().then((function(t){e.roleOptions=t.roles,e.open=!0,e.title=e.$t("user.index.098976-42"),e.form.deptId=e.queryParams.deptId,e.form.password=e.initPassword}))},handleUpdate:function(e){var t=this;this.reset();var r=e.userId||this.ids;Object(a["j"])(r).then((function(r){t.idEditDept=e.deptId,t.form=r.data,t.roleOptions=r.roles,t.$set(t.form,"postIds",r.postIds),t.$set(t.form,"roleIds",r.roleIds),t.open=!0,t.title=t.$t("user.index.098976-43"),t.form.password=""}))},getRoleList:function(e){var t=this;if(this.form.deptId=e.id,void 0!=this.form.deptId&&null!=this.form.deptId){var r=this.form.deptId;Object(a["i"])(r).then((function(e){t.roleOptions=e.roles}))}},handleResetPwd:function(e){var t=this;this.$prompt(this.$t("user.index.098976-44")+e.userName+this.$t("user.index.098976-45"),this.$t("user.index.098976-46"),{confirmButtonText:this.$t("confirm"),cancelButtonText:this.$t("cancel"),closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:this.$t("user.index.098976-35")}).then((function(r){var s=r.value;Object(a["m"])(e.userId,s).then((function(e){t.$modal.msgSuccess(t.$t("user.index.098976-47")+s)}))})).catch((function(){}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.userId?Object(a["q"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess")),e.open=!1,e.getList()})):Object(a["a"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("addSuccess")),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.userId||this.ids;this.$modal.confirm(this.$t("user.index.098976-48",[r])).then((function(){return Object(a["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("delSuccess"))})).catch((function(){}))}}}),u=l,d=(r("e960"),r("2877")),c=Object(d["a"])(u,s,n,!1,null,"b76468be",null);t["default"]=c.exports},c0c7:function(e,t,r){"use strict";r.d(t,"l",(function(){return a})),r.d(t,"o",(function(){return i})),r.d(t,"j",(function(){return o})),r.d(t,"i",(function(){return l})),r.d(t,"a",(function(){return u})),r.d(t,"q",(function(){return d})),r.d(t,"c",(function(){return c})),r.d(t,"m",(function(){return m})),r.d(t,"b",(function(){return h})),r.d(t,"h",(function(){return p})),r.d(t,"n",(function(){return f})),r.d(t,"k",(function(){return b})),r.d(t,"r",(function(){return v})),r.d(t,"s",(function(){return y})),r.d(t,"t",(function(){return g})),r.d(t,"f",(function(){return $})),r.d(t,"p",(function(){return x})),r.d(t,"d",(function(){return w})),r.d(t,"e",(function(){return k})),r.d(t,"g",(function(){return _}));var s=r("b775"),n=r("c38a");function a(e){return Object(s["a"])({url:"/system/user/list",method:"get",params:e})}function i(e){return Object(s["a"])({url:"/system/user/listTerminal",method:"get",params:e})}function o(e){return Object(s["a"])({url:"/system/user/"+Object(n["f"])(e),method:"get"})}function l(e){return Object(s["a"])({url:"/system/dept/getRole?deptId="+e,method:"get"})}function u(e){return Object(s["a"])({url:"/system/user",method:"post",data:e})}function d(e){return Object(s["a"])({url:"/system/user",method:"put",data:e})}function c(e){return Object(s["a"])({url:"/system/user/"+e,method:"delete"})}function m(e,t){var r={userId:e,password:t};return Object(s["a"])({url:"/system/user/resetPwd",method:"put",data:r})}function h(e,t){var r={userId:e,status:t};return Object(s["a"])({url:"/system/user/changeStatus",method:"put",data:r})}function p(){return Object(s["a"])({url:"/wechat/getWxBindQr",method:"get"})}function f(e){return Object(s["a"])({url:"/wechat/cancelBind",method:"post",data:e})}function b(){return Object(s["a"])({url:"/system/user/profile",method:"get"})}function v(e){return Object(s["a"])({url:"/system/user/profile",method:"put",data:e})}function y(e,t){var r={oldPassword:e,newPassword:t};return Object(s["a"])({url:"/system/user/profile/updatePwd",method:"put",params:r})}function g(e){return Object(s["a"])({url:"/system/user/profile/avatar",method:"post",data:e})}function $(e){return Object(s["a"])({url:"/system/user/authRole/"+e,method:"get"})}function x(e){return Object(s["a"])({url:"/system/user/authRole",method:"put",params:e})}function w(){return Object(s["a"])({url:"/system/user/deptTree",method:"get"})}function k(e){return Object(s["a"])({url:"/system/user/deptTree?showOwner="+e,method:"get"})}function _(e){return Object(s["a"])({url:"/system/user/getByDeptId",method:"get",params:e})}},c6cb:function(e,t,r){},e960:function(e,t,r){"use strict";r("c6cb")}}]);