.topCenter .el-button+.el-button{margin-left:0;margin-top:10px}.vue-treeselect__label{font-weight:400;font-size:14px}.general{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.topLeft{width:45%;height:373px}.topCenter{width:10%;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.topRight{width:45%;height:373px}.footer{position:fixed;left:0;right:0;bottom:0;z-index:999;padding:20px 50px 20px 360px;margin-bottom:0!important;color:#fff;text-align:center;-webkit-box-shadow:0 0 10px rgba(0,0,0,.08);box-shadow:0 0 10px rgba(0,0,0,.08)}.pagination-container{text-align:left;width:45%;margin-left:200px;margin-top:20px}