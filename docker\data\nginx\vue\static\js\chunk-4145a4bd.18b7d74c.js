(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4145a4bd"],{"584f":function(e,t,n){"use strict";n.d(t,"l",(function(){return i})),n.d(t,"q",(function(){return a})),n.d(t,"m",(function(){return l})),n.d(t,"n",(function(){return o})),n.d(t,"k",(function(){return u})),n.d(t,"f",(function(){return s})),n.d(t,"c",(function(){return c})),n.d(t,"g",(function(){return d})),n.d(t,"i",(function(){return m})),n.d(t,"d",(function(){return f})),n.d(t,"r",(function(){return p})),n.d(t,"o",(function(){return h})),n.d(t,"p",(function(){return v})),n.d(t,"h",(function(){return b})),n.d(t,"a",(function(){return g})),n.d(t,"s",(function(){return y})),n.d(t,"b",(function(){return w})),n.d(t,"e",(function(){return O})),n.d(t,"j",(function(){return j}));var r=n("b775");function i(e){return Object(r["a"])({url:"/iot/device/list",method:"get",params:e})}function a(e){return Object(r["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/iot/device/shortList",method:"get",params:e})}function u(){return Object(r["a"])({url:"/iot/device/all",method:"get"})}function s(e){return Object(r["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(r["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(r["a"])({url:"/iot/device/statistic",method:"get"})}function f(e,t){return Object(r["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function p(e){return Object(r["a"])({url:"/iot/device/recovery?deviceIds="+e,method:"post"})}function h(){return Object(r["a"])({url:"",method:"get"})}function v(){return Object(r["a"])({url:"",method:"get"})}function b(e){return Object(r["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function g(e){return Object(r["a"])({url:"/iot/device",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/iot/device",method:"put",data:e})}function w(e){return Object(r["a"])({url:"/iot/device/"+e,method:"delete"})}function O(e){return Object(r["a"])({url:"/iot/device/generator",method:"get",params:e})}function j(e){return Object(r["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},9626:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[n("el-input",{attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),n("el-form-item",{attrs:{label:"设备状态",prop:"status"}},[n("el-select",{attrs:{placeholder:"请选择设备状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_device_status,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),n("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),n("el-table-column",{attrs:{label:"网关编码",align:"center",prop:"gwDevCode"}}),n("el-table-column",{attrs:{label:"从机地址",align:"center",prop:"addr"}}),n("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"firmwareVersion"}}),n("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])}),n("el-table-column",{attrs:{label:"激活时间",align:"center",prop:"activeTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.activeTime,"{y}-{m}-{d}")))])]}}])}),n("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:edit"],expression:"['iot:device:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"设备名",prop:"deviceName"}},[n("el-input",{attrs:{placeholder:"请输入设备名"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1),n("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[n("el-input",{attrs:{placeholder:"请输入固件版本"},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],a=(n("d81d"),n("584f")),l={name:"device-sub",props:{device:{type:Object,default:null}},dicts:["iot_device_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,deviceList:[],title:"",open:!1,daterangeActiveTime:[],queryParams:{pageNum:1,pageSize:10,gwDevCode:""},form:{},rules:{deviceName:[{required:!0,message:"设备名不能为空",trigger:"blur"}],firmwareVersion:[{required:!0,message:"固件版本不能为空",trigger:"blur"}]}}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.gwDevCode=this.deviceInfo.serialNumber,this.getList())}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeActiveTime&&""!=this.daterangeActiveTime&&(this.queryParams.params["beginActiveTime"]=this.daterangeActiveTime[0],this.queryParams.params["endActiveTime"]=this.daterangeActiveTime[1]),Object(a["l"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.map((function(e){var t=e.serialNumber.split("_");e.addr=t[1]}))}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deviceId:null,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,firmwareVersion:null,status:0,isShadow:null,rssi:null,isCustomerLocation:null,networkAddress:null,networkIp:null,thingsModelValue:null,longitude:null,latitude:null,activeTime:null,delFlag:null,createBy:null,imgUrl:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeActiveTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.deviceId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加设备"},handleUpdate:function(e){var t=this;this.reset();var n=e.deviceId||this.ids;Object(a["f"])(n).then((function(e){t.form=e.data,t.open=!0,t.title="修改设备"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deviceId?Object(a["s"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(a["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.deviceId||this.ids;this.$modal.confirm('是否确认删除设备编号为"'+n+'"的数据项？').then((function(){return Object(a["b"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},o=l,u=n("2877"),s=Object(u["a"])(o,r,i,!1,null,null,null);t["default"]=s.exports}}]);