(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d5c9c454","chunk-00213f14"],{"1b7a":function(e,t,i){"use strict";i("abf5")},6827:function(e,t,i){"use strict";i.d(t,"a",(function(){return a})),i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return r})),i.d(t,"d",(function(){return o})),i.d(t,"f",(function(){return c})),i.d(t,"e",(function(){return l}));var n=i("b775");function a(e,t,i){return Object(n["a"])({url:"/sip/record/devquery/"+e+"/"+t,method:"get",params:i})}function s(e){return Object(n["a"])({url:"/sip/record/serverRecord/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/sip/record/serverRecord/date/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/sip/record/serverRecord/file/list",method:"get",params:e})}function c(e,t){return Object(n["a"])({url:"/sip/record/play/"+e+"/"+t,method:"get"})}function l(e,t,i){return Object(n["a"])({url:"/sip/record/download/"+e+"/"+t,method:"get",params:i})}},"97d6":function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"root"},[i("div",{staticClass:"container-shell"},[i("div",{ref:"container",attrs:{id:"container"}})])])},a=[],s=i("c7eb"),r=i("1da1"),o=(i("a9e3"),i("ac1f"),i("00b4"),i("f5a7")),c={},l={name:"player",props:{playerinfo:{type:Object,default:null}},mounted:function(){console.log(this._uid)},watch:{playerinfo:function(e,t){console.log("playerinfo 发生变化"),this.playinfo=e,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype)}},jessibuca:null,data:function(){return{isPlaybackPause:!1,useWebGPU:!1,isInit:!1,playinfo:{},playtype:"play",operateBtns:{}}},beforeDestroy:function(){},created:function(){this.playinfo=this.playerinfo,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype),this.init()},methods:{init:function(){var e=this,t="gpu"in navigator;t?(console.log("支持webGPU"),this.useWebGPU=!0):(console.log("暂不支持webGPU，降级到webgl渲染"),this.useWebGPU=!1);var i=this.isMobile()||this.isPad();i&&window.VConsole&&new window.VConsole,this.$nextTick((function(){e.initplayer()}))},initplayer:function(){this.isPlaybackPause=!1,this.initconf(),c[this._uid]=new window.JessibucaPro({container:this.$refs.container,decoder:"/js/jessibuca-pro/decoder-pro.js",videoBuffer:Number(.2),isResize:!1,useWCS:!1,useMSE:!1,useSIMD:!0,wcsUseVideoRender:!1,loadingText:"加载中",debug:!1,showBandwidth:!0,showPlaybackOperate:!0,operateBtns:this.operateBtns,forceNoOffscreen:!0,isNotMute:!1,showPerformance:!1,playbackForwardMaxRateDecodeIFrame:4,useWebGPU:this.useWebGPU});var e=c[this._uid];this.initcallback(e),this.isInit=!0},initconf:function(){"play"===this.playtype?this.operateBtns={fullscreen:!0,zoom:!0,ptz:!0,play:!0}:this.operateBtns={fullscreen:!0,zoom:!0,play:!0,ptz:!1}},initcallback:function(e){var t=this;e.on("error",(function(e){console.log("error"),console.log(e),t.destroy()})),e.on("pause",(function(e){console.log("pause success!"),console.log(e)})),e.on("stats",(function(e){console.log("stats is",e)})),e.on("timeout",(function(){console.log("timeout")})),e.on("playbackPreRateChange",(function(t){e.forward(t)}));var i=0,n=0;e.on("timeUpdate",(function(e){n=parseInt(e/6e4),i!==n&&i++})),e.on(JessibucaPro.EVENTS.ptz,(function(e){console.log("ptz arrow",e),t.handlePtz(e)}))},registercallback:function(e,t){c[this._uid]&&c[this._uid].on(e,t)},isMobile:function(){return/iphone|ipad|android.*mobile|windows.*phone|blackberry.*mobile/i.test(window.navigator.userAgent.toLowerCase())},isPad:function(){return/ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(window.navigator.userAgent.toLowerCase())},play:function(e){c[this._uid]&&c[this._uid].play(e)},pause:function(){c[this._uid]&&c[this._uid].pause()},replay:function(e){var t=this;c[this._uid]?c[this._uid].destroy().then((function(){t.initplayer(),t.play(e)})):(this.initplayer(),this.play(e))},handlePtz:function(e){var t=0,i=0;"left"===e?t=2:"right"===e?t=1:"up"===e?i=1:"down"===e&&(i=2);var n={leftRight:t,upDown:i,moveSpeed:125};this.playinfo&&""!==this.playinfo.playtype&&Object(o["c"])(this.playinfo.deviceId,this.playinfo.channelId,n).then(function(){var e=Object(r["a"])(Object(s["a"])().mark((function e(t){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},playback:function(e,t){c[this._uid]&&(c[this._uid].playback(e,{playList:t,fps:25,showControl:!0,showRateBtn:!0,isUseFpsRender:!0,isCacheBeforeDecodeForFpsRender:!1,supportWheel:!0,rateConfig:[{label:"正常",value:1},{label:"2倍",value:2},{label:"4倍",value:4},{label:"8倍",value:8}]}),this.isPlaybackPause=!1)},playbackPause:function(){c[this._uid]&&(c[this._uid].playbackPause(),this.isPlaybackPause=!0)},replayback:function(e,t){var i=this;c[this._uid]?c[this._uid].destroy().then((function(){i.initplayer(),i.playback(e,t)})):(this.initplayer(),this.playback(e,t))},destroy:function(){var e=this;c[this._uid]&&c[this._uid].destroy().then((function(){e.initplayer()}))},close:function(){c[this._uid]&&c[this._uid].close()}}},u=l,d=(i("1b7a"),i("2877")),h=Object(d["a"])(u,n,a,!1,null,"263ca075",null);t["default"]=h.exports},abf5:function(e,t,i){},e2de:function(e,t,i){"use strict";i.d(t,"e",(function(){return a})),i.d(t,"d",(function(){return s})),i.d(t,"a",(function(){return r})),i.d(t,"c",(function(){return o})),i.d(t,"h",(function(){return c})),i.d(t,"f",(function(){return l})),i.d(t,"b",(function(){return u})),i.d(t,"g",(function(){return d}));var n=i("b775");function a(e){return Object(n["a"])({url:"/sip/channel/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"get"})}function r(e,t){return Object(n["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"delete"})}function c(e,t){return Object(n["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function l(e,t,i){return Object(n["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:i})}function u(e,t){return Object(n["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function d(e,t,i,a){return Object(n["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+i,method:"get",params:a})}},f5a7:function(e,t,i){"use strict";i.d(t,"b",(function(){return a})),i.d(t,"a",(function(){return s})),i.d(t,"c",(function(){return r}));var n=i("b775");function a(e){return Object(n["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function r(e,t,i){return Object(n["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:i})}},f5fc:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{display:"block",width:"1000px"}},[i("div",{staticStyle:{display:"flex"}},[i("el-row",[i("span",{staticStyle:{"margin-left":"10px"},attrs:{prop:"channelName"}},[e._v("通道：")]),i("el-select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择",size:"small"},on:{change:e.changeChannel},model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},e._l(e.channelList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),i("span",{staticStyle:{overflow:"auto","margin-left":"10px"}},[e._v("日期：")]),i("el-date-picker",{staticStyle:{width:"180px","margin-right":"10px"},attrs:{type:"date",size:"small","value-format":"yyyy-MM-dd",clearable:"",placeholder:"选择日期"},model:{value:e.queryDate,callback:function(t){e.queryDate=t},expression:"queryDate"}}),i("el-button-group",{staticStyle:{margin:"0"}},[i("el-button",{attrs:{size:"mini",type:"success",title:"查看录像",disabled:""===e.channelId||!e.queryDate},on:{click:function(t){return e.loadDevRecord()}}},[i("i",{staticClass:"el-icon-video-camera"}),e._v(" 查看 ")])],1),i("span",{staticStyle:{"margin-left":"82px",overflow:"auto"}},[e._v("时间：")]),i("el-button-group",[i("el-time-picker",{staticStyle:{width:"200px"},attrs:{size:"small","is-range":"",align:"left","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围",disabled:""===e.channelId||!e.queryDate},on:{change:e.timePickerChange},model:{value:e.timeRange,callback:function(t){e.timeRange=t},expression:"timeRange"}})],1),i("el-button-group",{staticStyle:{margin:"0 0 0 10px"}},[i("el-button",{attrs:{size:"mini",type:"primary",title:"下载选定录像",disabled:""===e.channelId||!e.timeRange},on:{click:function(t){return e.downloadRecord()}}},[i("i",{staticClass:"el-icon-download"}),e._v(" 转存 ")])],1)],1)],1),i("player",{ref:"playbacker",staticClass:"components-container",attrs:{playerinfo:e.playinfo}})],1)},a=[],s=(i("d81d"),i("d3b7"),i("97d6")),r=i("e2de"),o=i("6827"),c={name:"DeviceVideo",components:{player:s["default"]},data:function(){return{deviceId:"",channelId:"",streamId:"",ssrc:"",playurl:"",queryDate:"",playing:!1,vodData:{},hisData:[],playinfo:{},channelList:[],playbackinfo:{},timeRange:null,startTime:null,endTime:null,queryParams:{pageNum:1,pageSize:10,deviceSipId:null,channelSipId:null}}},props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.deviceId=this.device.serialNumber)}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.deviceId=this.device.serialNumber,this.getList(),this.playinfo={playtype:"playback",deviceId:this.device.serialNumber}},beforeDestroy:function(){},destroyed:function(){this.closeStream()},methods:{getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){e.channelList=t.rows.map((function(e){return{value:e.channelSipId,label:e.channelName}}))}))},changeChannel:function(){this.playinfo.channelId=this.channelId},initUrl:function(e){e?(this.streamId=e.ssrc,this.ssrc=e.ssrc,this.playurl=e.playurl):(this.streamId="",this.ssrc="",this.playurl="")},loadDevRecord:function(){var e=this;if(this.$refs.playbacker.registercallback("playbackSeek",this.seekPlay),this.deviceId&&this.channelId){var t=this.queryDate?new Date(this.queryDate).getTime():new Date((new Date).toLocaleDateString()).getTime(),i=t/1e3,n=Math.floor((t+864e5-1)/1e3),a={start:i,end:n};this.vodData={start:i,end:n,base:i},this.setTime(this.queryDate+" 00:00:00",this.queryDate+" 23:59:59"),Object(o["a"])(this.deviceId,this.channelId,a).then((function(t){if(e.hisData=t.data.recordItems,t.data.recordItems){var a=e.hisData.length;a>0?(e.hisData[0].start<i?(e.hisData[0].start=i,e.vodData.start=i):e.vodData.start=e.hisData[0].start,0!==e.hisData[0].end&&e.hisData[0].end<n&&(e.vodData.end=e.hisData[0].end),e.playback()):e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}else e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}))}},playback:function(){var e=this,t={start:this.vodData.start,end:this.vodData.end};this.ssrc?Object(r["b"])(this.deviceId,this.channelId,this.ssrc).then((function(i){Object(r["f"])(e.deviceId,e.channelId,t).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))})):Object(r["f"])(this.deviceId,this.channelId,t).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))},triggerPlay:function(e){this.$refs.playbacker.playback(this.playurl,e),this.playing=!0},seekPlay:function(e){var t=this.vodData.base+3600*e.hour+60*e.min+e.second,i=t-this.vodData.start;if(this.ssrc){var n={seek:i},a=this;Object(r["g"])(this.deviceId,this.channelId,this.streamId,n).then((function(e){a.$refs.playbacker.setPlaybackStartTime(t)}))}},closeStream:function(){var e=this;this.playing&&this.streamId&&Object(r["b"])(this.deviceId,this.streamId).then((function(t){e.streamId="",e.ssrc="",e.playurl="",e.playing=!1}))},destroy:function(){this.playing&&this.streamId&&this.$refs.playbacker.destroy()},timePickerChange:function(e){this.setTime(e[0],e[1])},setTime:function(e,t){this.startTime=e,this.endTime=t,this.timeRange=[e,t]},downloadRecord:function(){var e=this,t=new Date(this.startTime).getTime()/1e3,i=new Date(this.endTime).getTime()/1e3,n={startTime:t,endTime:i,speed:"4"};Object(o["e"])(this.deviceId,this.channelId,n).then((function(t){console.log("开始转存到流服务器："+e.deviceId+" : "+e.channelId),200===t.code&&e.$message({type:"success",message:"转存到流服务器,请前往视频中心->录像管理查看！"})}))}}},l=c,u=i("2877"),d=Object(u["a"])(l,n,a,!1,null,null,null);t["default"]=d.exports}}]);