(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d3071e38"],{"0062":function(t,e,o){"use strict";o.r(e);var a=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"log-wrap"},[o("el-card",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"search-card"},[o("div",{staticClass:"form-wrap"},[o("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"88px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{prop:"jobName"}},[o("el-input",{attrs:{placeholder:t.$t("system.job.log.085689-0"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.jobName,callback:function(e){t.$set(t.queryParams,"jobName",e)},expression:"queryParams.jobName"}})],1),o("el-form-item",{attrs:{prop:"jobGroup"}},[o("el-select",{attrs:{placeholder:t.$t("system.job.log.085689-2"),clearable:""},model:{value:t.queryParams.jobGroup,callback:function(e){t.$set(t.queryParams,"jobGroup",e)},expression:"queryParams.jobGroup"}},t._l(t.dict.type.sys_job_group,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),o("el-form-item",{attrs:{prop:"status"}},[o("el-select",{attrs:{placeholder:t.$t("system.job.log.085689-4"),clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.dict.type.sys_common_status,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),t.searchShow?[o("el-form-item",[o("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":t.$t("device.device-functionlog.399522-8"),"end-placeholder":t.$t("device.device-functionlog.399522-9")},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1)]:t._e()],2),o("div",{staticClass:"search-btn-group"},[o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),o("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))]),o("el-button",{attrs:{type:"text"},on:{click:t.searchChange}},[o("span",{staticStyle:{color:"#486ff2","margin-left":"14px"}},[t._v(t._s(t.searchShow?t.$t("template.index.891112-113"):t.$t("template.index.891112-112")))]),o("i",{class:{"el-icon-arrow-down":!t.searchShow,"el-icon-arrow-up":t.searchShow},staticStyle:{color:"#486ff2","margin-left":"10px"}})])],1)],1)]),o("el-card",{staticClass:"card-wrap"},[o("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{type:"primary",plain:"",icon:"el-icon-download",size:"small"},on:{click:t.handleExport}},[t._v(t._s(t.$t("export")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:t.multiple},on:{click:t.handleDelete}},[t._v(t._s(t.$t("del")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small"},on:{click:t.handleClean}},[t._v(t._s(t.$t("system.job.log.085689-6")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{attrs:{plain:"",icon:"el-icon-close",size:"small"},on:{click:t.handleClose}},[t._v(t._s(t.$t("close")))])],1),o("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.jobLogList,border:!1},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),o("el-table-column",{attrs:{label:t.$t("system.job.log.085689-7"),width:"80",align:"center",prop:"jobLogId"}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-0"),align:"center",prop:"jobName","show-overflow-tooltip":!0}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-2"),align:"center",prop:"jobGroup","show-overflow-tooltip":!0},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.sys_job_group,value:e.row.jobGroup}})]}}])}),o("el-table-column",{attrs:{label:t.$t("system.job.log.085689-8"),align:"center",prop:"invokeTarget","show-overflow-tooltip":!0}}),o("el-table-column",{attrs:{label:t.$t("system.job.log.085689-9"),align:"center",prop:"jobMessage","show-overflow-tooltip":!0}}),o("el-table-column",{attrs:{label:t.$t("system.job.log.085689-10"),align:"center",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.sys_common_status,value:e.row.status}})]}}])}),o("el-table-column",{attrs:{label:t.$t("system.job.log.085689-11"),align:"center",prop:"createTime",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("span",[t._v(t._s(t.parseTime(e.row.createTime)))])]}}])}),o("el-table-column",{attrs:{label:t.$t("opation"),align:"center","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{size:"small",type:"text",icon:"el-icon-view"},on:{click:function(o){return t.handleView(e.row)}}},[t._v(t._s(t.$t("system.job.log.085689-12")))])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),o("el-dialog",{attrs:{title:t.$t("system.job.log.085689-13"),visible:t.open,width:"700px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[o("div",{staticStyle:{"margin-top":"-55px"}},[o("el-divider",{staticStyle:{"margin-top":"-30px"}}),o("el-form",{ref:"form",attrs:{model:t.form,"label-width":"100px",size:"mini"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-14")}},[t._v(t._s(t.form.jobLogId))]),o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-15")}},[t._v(t._s(t.form.jobName))])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-16")}},[t._v(t._s(t.form.jobGroup))]),o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-17")}},[t._v(t._s(t.form.createTime))])],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-18")}},[t._v(t._s(t.form.invokeTarget))])],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-19")}},[t._v(t._s(t.form.jobMessage))])],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.log.085689-20")}},[0==t.form.status?o("div",[t._v(t._s(t.$t("system.job.356378-37")))]):1==t.form.status?o("div",[t._v(t._s(t.$t("system.job.356378-38")))]):t._e()])],1),o("el-col",{attrs:{span:24}},[1==t.form.status?o("el-form-item",{attrs:{label:t.$t("system.job.log.085689")}},[t._v(t._s(t.form.exceptionInfo))]):t._e()],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.open=!1}}},[t._v(t._s(t.$t("close")))])],1)])],1)],1)},r=[],s=o("5530"),n=(o("d81d"),o("a159")),l=o("65f2"),i={name:"JobLog",dicts:["sys_common_status","sys_job_group"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,searchShow:!1,total:0,jobLogList:[],open:!1,dateRange:[],form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}},created:function(){var t=this,e=this.$route.params&&this.$route.params.jobId;void 0!==e&&0!=e?Object(n["d"])(e).then((function(e){t.queryParams.jobName=e.data.jobName,t.queryParams.jobGroup=e.data.jobGroup,t.getList()})):this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(l["c"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(e){t.jobLogList=e.rows,t.total=e.total,t.loading=!1}))},handleClose:function(){var t={path:"/monitor/job"};this.$tab.closeOpenPage(t)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.jobLogId})),this.multiple=!t.length},handleView:function(t){this.open=!0,this.form=t},handleDelete:function(t){var e=this,o=this.ids;this.$modal.confirm(this.$t("system.job.log.085689-22",[o])).then((function(){return Object(l["b"])(o)})).then((function(){e.getList(),e.$modal.msgSuccess(e.$t("delSuccess"))})).catch((function(){}))},handleClean:function(){var t=this;this.$modal.confirm(this.$t("system.job.log.085689-23")).then((function(){return Object(l["a"])()})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("system.job.log.085689-24"))})).catch((function(){}))},handleExport:function(){this.download("/monitor/jobLog/export",Object(s["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))},searchChange:function(){this.searchShow=!this.searchShow}}},c=i,u=(o("4a12"),o("2877")),m=Object(u["a"])(c,a,r,!1,null,"a2d5114c",null);e["default"]=m.exports},"4a12":function(t,e,o){"use strict";o("9eb2")},"65f2":function(t,e,o){"use strict";o.d(e,"c",(function(){return r})),o.d(e,"b",(function(){return s})),o.d(e,"a",(function(){return n})),o.d(e,"d",(function(){return l}));var a=o("b775");function r(t){return Object(a["a"])({url:"/monitor/jobLog/list",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/monitor/jobLog/"+t,method:"delete"})}function n(){return Object(a["a"])({url:"/monitor/jobLog/clean",method:"delete"})}function l(){return Object(a["a"])({url:"/monitor/logininfor/userCount",method:"get"})}},"9eb2":function(t,e,o){},a159:function(t,e,o){"use strict";o.d(e,"e",(function(){return r})),o.d(e,"d",(function(){return s})),o.d(e,"a",(function(){return n})),o.d(e,"g",(function(){return l})),o.d(e,"c",(function(){return i})),o.d(e,"b",(function(){return c})),o.d(e,"f",(function(){return u}));var a=o("b775");function r(t){return Object(a["a"])({url:"/monitor/job/list",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/monitor/job/"+t,method:"get"})}function n(t){return Object(a["a"])({url:"/monitor/job",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/monitor/job",method:"put",data:t})}function i(t){return Object(a["a"])({url:"/monitor/job/"+t,method:"delete"})}function c(t,e){var o={jobId:t,status:e};return Object(a["a"])({url:"/monitor/job/changeStatus",method:"put",data:o})}function u(t,e){var o={jobId:t,jobGroup:e};return Object(a["a"])({url:"/monitor/job/run",method:"put",data:o})}}}]);