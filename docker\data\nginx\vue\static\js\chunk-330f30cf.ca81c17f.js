(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-330f30cf"],{"06d9":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.$t("device.import-record.086254-0"),visible:t.open,width:"890px"},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",[r("el-date-picker",{staticStyle:{width:"340px"},attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":t.$t("device.import-record.086254-3"),"end-placeholder":t.$t("device.import-record.086254-4")},model:{value:t.daterangeTime,callback:function(e){t.daterangeTime=e},expression:"daterangeTime"}})],1),r("el-form-item",{attrs:{prop:"status"}},[r("el-select",{attrs:{placeholder:t.$t("device.import-record.086254-2"),size:"small",clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.dict.type.common_status_type,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("device.import-record.086254-5")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("device.import-record.086254-6")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.dataList,size:"small",border:!1}},[r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-7"),align:"center",prop:"id",width:"80"}}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-8"),align:"center",prop:"total","min-width":"100"}}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-13"),align:"left",prop:"productName","min-width":"170"}}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-9"),align:"center",prop:"successQuantity","min-width":"100"}}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-10"),align:"center",prop:"failQuantity","min-width":"100"}}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-11"),align:"center",prop:"status","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.common_status_type,value:e.row.status,size:"small","min-width":"100"}})]}}])}),r("el-table-column",{attrs:{label:t.$t("device.import-record.086254-12"),align:"center",prop:"createTime",width:"200"}})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1)},i=[],o=(r("d81d"),r("9b9c")),a=r("584f"),u={name:"importRecord",dicts:["common_status_type"],data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],queryParams:{pageNum:1,pageSize:10,productName:null,type:1}}},created:function(){this.getProductList()},methods:{getProductList:function(){var t=this;this.loading=!0;var e={pageSize:999,showSenior:!0};Object(o["g"])(e).then((function(e){t.productList=e.rows.map((function(t){return{value:t.productId,label:t.productName}})),t.loading=!1}))},getList:function(){var t=this;this.loading=!0,Object(a["q"])(this.addDateRange(this.queryParams,this.daterangeTime)).then((function(e){t.dataList=e.rows,t.total=e.total,t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}},c=u,d=r("2877"),l=Object(d["a"])(c,n,i,!1,null,null,null);e["default"]=l.exports},"584f":function(t,e,r){"use strict";r.d(e,"n",(function(){return i})),r.d(e,"t",(function(){return o})),r.d(e,"o",(function(){return a})),r.d(e,"p",(function(){return u})),r.d(e,"m",(function(){return c})),r.d(e,"f",(function(){return d})),r.d(e,"c",(function(){return l})),r.d(e,"g",(function(){return s})),r.d(e,"i",(function(){return m})),r.d(e,"d",(function(){return p})),r.d(e,"u",(function(){return f})),r.d(e,"q",(function(){return h})),r.d(e,"r",(function(){return g})),r.d(e,"h",(function(){return b})),r.d(e,"a",(function(){return v})),r.d(e,"v",(function(){return y})),r.d(e,"b",(function(){return j})),r.d(e,"e",(function(){return O})),r.d(e,"k",(function(){return w})),r.d(e,"l",(function(){return $})),r.d(e,"j",(function(){return q})),r.d(e,"s",(function(){return _}));var n=r("b775");function i(t){return Object(n["a"])({url:"/iot/device/list",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function a(t){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:t})}function c(t){return Object(n["a"])({url:"/iot/device/all",method:"get",params:t})}function d(t){return Object(n["a"])({url:"/iot/device/"+t,method:"get"})}function l(t){return Object(n["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function m(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function p(t,e){return Object(n["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function f(t,e){return Object(n["a"])({url:"/iot/device/recovery?deviceIds="+t+"&recoveryDeptId="+e,method:"post"})}function h(t){return Object(n["a"])({url:"/iot/record/list",method:"get",params:t})}function g(t){return Object(n["a"])({url:"/iot/record/list",method:"get",params:t})}function b(t){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function v(t){return Object(n["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/iot/device",method:"put",data:t})}function j(t){return Object(n["a"])({url:"/iot/device/"+t,method:"delete"})}function O(t){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:t})}function w(t){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}function $(t){return Object(n["a"])({url:"/sip/sipconfig/auth/"+t,method:"get"})}function q(t){return Object(n["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:t})}function _(t){return Object(n["a"])({url:"/iot/device/listThingsModel",method:"get",params:t})}},"9b9c":function(t,e,r){"use strict";r.d(e,"g",(function(){return i})),r.d(e,"h",(function(){return o})),r.d(e,"f",(function(){return a})),r.d(e,"a",(function(){return u})),r.d(e,"i",(function(){return c})),r.d(e,"e",(function(){return d})),r.d(e,"b",(function(){return l})),r.d(e,"d",(function(){return s})),r.d(e,"c",(function(){return m}));var n=r("b775");function i(t){return Object(n["a"])({url:"/iot/product/list",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/iot/product/shortList",method:"get",params:t})}function a(t){return Object(n["a"])({url:"/iot/product/"+t,method:"get"})}function u(t){return Object(n["a"])({url:"/iot/product",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/iot/product",method:"put",data:t})}function d(t){return Object(n["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function l(t){return Object(n["a"])({url:"/iot/product/status",method:"put",data:t})}function s(t){return Object(n["a"])({url:"/iot/product/"+t,method:"delete"})}function m(t){return Object(n["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}}}]);