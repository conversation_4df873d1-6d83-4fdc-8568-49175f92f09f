(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-45206724","chunk-722c5e57","chunk-46e6d1bb"],{3568:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"device-add-wrap"},[r("el-dialog",{attrs:{title:e.$t("device.device-edit.148398-84"),visible:e.open,width:"900px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:12}},[r("el-form-item",{attrs:{label:e.$t("device.device-edit.148398-1"),prop:"deviceName"}},[r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:e.$t("device.device-edit.148398-2")},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1),r("el-form-item",{attrs:{label:"",prop:"productName"}},[r("template",{slot:"label"},[r("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" "+e._s(e.$t("device.device-edit.148398-4"))+" ")]),r("el-input",{staticStyle:{width:"300px"},attrs:{readonly:"",placeholder:e.$t("device.device-edit.148398-5")},model:{value:e.form.productName,callback:function(t){e.$set(e.form,"productName",t)},expression:"form.productName"}},[r("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v(e._s(e.$t("device.device-edit.148398-6")))])],1)],2),r("el-form-item",{attrs:{label:"",prop:"serialNumber"}},[r("template",{slot:"label"},[r("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" "+e._s(e.$t("device.device-edit.148398-7"))+" ")]),r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:e.$t("device.device-edit.148398-8"),maxlength:"32",readonly:3===e.form.deviceType},model:{value:e.form.serialNumber,callback:function(t){e.$set(e.form,"serialNumber",t)},expression:"form.serialNumber"}},[3!==e.form.deviceType?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{slot:"append",loading:e.genDisabled},on:{click:e.generateNum},slot:"append"},[e._v(" "+e._s(e.$t("device.device-edit.148398-9"))+" ")]):e._e(),3===e.form.deviceType?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{slot:"append"},on:{click:function(t){return e.genSipID()}},slot:"append"},[e._v(" "+e._s(e.$t("device.device-edit.148398-9"))+" ")]):e._e()],1),e.openServerTip?r("el-alert",{staticClass:"alert-wrap",attrs:{type:"info","show-icon":"",description:e.$t("device.device-edit.148398-10")}}):e._e()],2)],1),r("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:12}},[r("el-form-item",{attrs:{label:e.$t("device.device-edit.148398-12"),prop:"firmwareVersion"}},[r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:e.$t("device.device-edit.148398-13"),type:"number",step:"0.1",disabled:3===e.form.deviceType},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}},[r("template",{slot:"prepend"},[e._v("Version")]),r("template",{slot:"append"},[e._v(e._s(1===e.form.firmwareType?e.$t("firmware.index.222541-52"):e.$t("firmware.index.222541-53")))])],2)],1),r("el-form-item",{attrs:{label:e.$t("device.device-edit.148398-19"),prop:"locationWay"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:e.$t("device.device-edit.148398-20")},model:{value:e.form.locationWay,callback:function(t){e.$set(e.form,"locationWay",t)},expression:"form.locationWay"}},e._l(e.dict.type.iot_location_way,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:Number(e.value)}})})),1)],1),3!==e.form.deviceType?r("el-form-item",{attrs:{label:e.$t("device.device-edit.148398-15"),prop:"isShadow"}},[r("el-radio-group",{model:{value:e.form.isShadow,callback:function(t){e.$set(e.form,"isShadow",t)},expression:"form.isShadow"}},[r("el-radio",{attrs:{label:1}},[e._v(e._s(e.$t("device.device-edit.148398-85")))]),r("el-radio",{attrs:{label:0}},[e._v(e._s(e.$t("device.device-edit.148398-86")))])],1)],1):e._e()],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("device.product-list.058448-14")))]),r("el-button",{on:{click:e.closeDialog}},[e._v(e._s(e.$t("device.product-list.058448-15")))])],1)],1),r("product-list",{ref:"productList",attrs:{productId:e.form.productId},on:{productEvent:function(t){return e.getProductData(t)}}}),r("sipid",{ref:"sipidGen",attrs:{product:e.form},on:{addGenEvent:function(t){return e.getSipIDData(t)}}})],1)},o=[],n=(r("ac1f"),r("00b4"),r("e51f")),a=r("9467"),c=r("9b9c"),l=r("584f"),u={name:"DeviceAdd",dicts:["iot_device_status","iot_location_way"],components:{productList:n["default"],sipid:a["default"]},data:function(){return{open:!1,genDisabled:!1,loading:!0,deviceId:"",form:{productId:0,status:1,locationWay:1,firmwareType:1,firmwareVersion:1,serialNumber:"",deviceType:1,isSimulate:0,isShadow:0},openTip:!1,openServerTip:!1,serverType:1,rules:{deviceName:[{required:!0,message:this.$t("device.device-edit.148398-60"),trigger:"blur"},{min:2,max:32,message:this.$t("device.device-edit.148398-61"),trigger:"blur"}],firmwareVersion:[{required:!0,message:this.$t("device.device-edit.148398-62"),trigger:"blur"}]}}},computed:{deviceStatus:{set:function(e){1==e?this.form.status=2:0==e&&(this.form.status=1)},get:function(){return 2==this.form.status?1:0}}},created:function(){},methods:{selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(e){this.form.productId=e.productId,this.form.productName=e.productName,this.form.deviceType=e.deviceType,this.form.protocolCode=e.protocolCode,this.form.tenantId=e.tenantId,this.form.tenantName=e.tenantName,this.form.firmwareType=e.firmwareType,3==e.deviceType?this.form.locationWay=3:this.form.locationWay=1,"TCP"===e.transport?(this.openServerTip=!0,this.serverType=3):(this.openServerTip=!1,this.serverType=1)},generateNum:function(){var e=this;if(this.form.productId&&0!=this.form.productId){this.genDisabled=!0;var t={type:this.serverType};Object(l["e"])(t).then((function(t){e.form.serialNumber=t.data,e.genDisabled=!1}))}else this.$modal.alertError(this.$t("device.device-edit.148398-72"))},genSipID:function(){this.$refs.sipidGen.open=!0},getSipIDData:function(e){this.$set(this.form,"serialNumber",e)},getProduct:function(){var e=this;Object(c["f"])(this.form.productId).then((function(t){e.form=t.data,e.changeProductCode(e.form.protocolCode)}))},reset:function(){this.form={deviceId:null,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:"",firmwareType:1,firmwareVersion:1,status:1,rssi:null,networkAddress:null,networkIp:null,longitude:null,latitude:null,activeTime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,locationWay:1,clientId:0,isShadow:0},this.deviceStatus=0,this.resetForm("form")},submitForm:function(){var e=this;if(null!=this.form.serialNumber&&0!=this.form.serialNumber){var t=/^[0-9a-zA-Z]+$/;t.test(this.form.serialNumber)?null!=this.form.productId&&0!=this.form.productId?this.$refs["form"].validate((function(t){t&&Object(l["a"])(e.form).then((function(t){200==t.code?(e.$message.success("设备添加成功"),2==e.form.status&&(e.deviceStatus=1),e.$parent.getList(),e.open=!1):e.$message.error(t.msg)}))})):this.$modal.alertError(this.$t("device.device-edit.148398-67")):this.$modal.alertError(this.$t("device.device-edit.148398-66"))}else this.$modal.alertError(this.$t("device.device-edit.148398-65"))},closeDialog:function(){this.open=!1}}},d=u,s=(r("4706"),r("2877")),p=Object(s["a"])(d,i,o,!1,null,"21fd8d8e",null);t["default"]=p.exports},4706:function(e,t,r){"use strict";r("ad6e")},"584f":function(e,t,r){"use strict";r.d(t,"n",(function(){return o})),r.d(t,"t",(function(){return n})),r.d(t,"o",(function(){return a})),r.d(t,"p",(function(){return c})),r.d(t,"m",(function(){return l})),r.d(t,"f",(function(){return u})),r.d(t,"c",(function(){return d})),r.d(t,"g",(function(){return s})),r.d(t,"i",(function(){return p})),r.d(t,"d",(function(){return m})),r.d(t,"u",(function(){return f})),r.d(t,"q",(function(){return h})),r.d(t,"r",(function(){return v})),r.d(t,"h",(function(){return b})),r.d(t,"a",(function(){return y})),r.d(t,"v",(function(){return g})),r.d(t,"b",(function(){return $})),r.d(t,"e",(function(){return w})),r.d(t,"k",(function(){return _})),r.d(t,"l",(function(){return N})),r.d(t,"j",(function(){return k})),r.d(t,"s",(function(){return O}));var i=r("b775");function o(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function a(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/device/all",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function p(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function m(e,t){return Object(i["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function f(e,t){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function h(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function b(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function y(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function $(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function w(e){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:e})}function _(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function N(e){return Object(i["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function k(e){return Object(i["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function O(e){return Object(i["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},9467:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[r("el-form-item",{attrs:{label:e.$t("sip.sipidGen.998538-0")}},[r("el-cascader",{staticStyle:{width:"350px"},attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-9"),prop:"deviceType"}},[r("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("sip.index.998533-14")},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-15"),prop:"channelType"}},[r("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("sip.index.998533-16")},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-20"),prop:"createNum"}},[r("el-input-number",{staticStyle:{width:"350px"},attrs:{"controls-position":"right",placeholder:e.$t("sip.index.998533-19"),type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("sip.index.998533-21")))]),r("el-button",{on:{click:e.closeDialog}},[e._v(e._s(e.$t("cancel")))])],1)],1)},o=[],n=r("ef6c"),a=r("e2de"),c={name:"SipidDialog",dicts:["video_type","channel_type"],props:{product:{type:Object,default:null}},data:function(){return{loading:!0,title:this.$t("sip.sipidGen.998538-1"),total:0,open:!1,createForm:{city:"",deviceType:"",channelType:"",createNum:1},cityOptions:n["regionData"],city:"",cityCode:""}},created:function(){},methods:{changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=n["CodeToText"][e[0]]+"/"+n["CodeToText"][e[1]]+"/"+n["CodeToText"][e[2]];this.createForm.citycode=t}},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError(this.$t("sip.index.998533-42")):(this.createForm.productId=this.product.productId,this.createForm.productName=this.product.productName,this.createForm.tenantId=this.product.tenantId,this.createForm.tenantName=this.product.tenantName,this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?Object(a["a"])(this.createForm.createNum,this.createForm).then((function(t){if(200===t.code){var r=t.data;e.$emit("addGenEvent",r),e.$modal.msgSuccess(e.$t("sip.sipidGen.998538-2"))}e.open=!1})):this.$message({type:"error",message:this.$t("sip.sipidGen.998538-3")}))},closeDialog:function(){this.open=!1}}},l=c,u=r("2877"),d=Object(u["a"])(l,i,o,!1,null,"aa9c31e2",null);t["default"]=d.exports},"9b9c":function(e,t,r){"use strict";r.d(t,"g",(function(){return o})),r.d(t,"h",(function(){return n})),r.d(t,"f",(function(){return a})),r.d(t,"a",(function(){return c})),r.d(t,"i",(function(){return l})),r.d(t,"e",(function(){return u})),r.d(t,"b",(function(){return d})),r.d(t,"d",(function(){return s})),r.d(t,"c",(function(){return p}));var i=r("b775");function o(e){return Object(i["a"])({url:"/iot/product/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/product/shortList",method:"get",params:e})}function a(e){return Object(i["a"])({url:"/iot/product/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/product",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/product",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/product/status",method:"put",data:e})}function s(e){return Object(i["a"])({url:"/iot/product/"+e,method:"delete"})}function p(e){return Object(i["a"])({url:"/iot/product/copy?productId="+e,method:"post"})}},ad6e:function(e,t,r){},e2de:function(e,t,r){"use strict";r.d(t,"g",(function(){return o})),r.d(t,"e",(function(){return n})),r.d(t,"a",(function(){return a})),r.d(t,"d",(function(){return c})),r.d(t,"k",(function(){return l})),r.d(t,"h",(function(){return u})),r.d(t,"c",(function(){return d})),r.d(t,"i",(function(){return s})),r.d(t,"b",(function(){return p})),r.d(t,"f",(function(){return m})),r.d(t,"j",(function(){return f})),r.d(t,"l",(function(){return h}));var i=r("b775");function o(e){return Object(i["a"])({url:"/sip/channel/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"get"})}function a(e,t){return Object(i["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function c(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(i["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function u(e,t,r){return Object(i["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:r})}function d(e,t,r){return Object(i["a"])({url:"/sip/player/closeStream/"+e+"/"+t+"/"+r,method:"get"})}function s(e,t,r,o){return Object(i["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+r,method:"get",params:o})}function p(e){return Object(i["a"])({url:"/iot/relation/addOrUp",method:"post",data:e})}function m(e,t){return Object(i["a"])({url:"/sip/talk/getPushUrl/"+e+"/"+t,method:"get"})}function f(e,t){return Object(i["a"])({url:"/sip/talk/broadcast/"+e+"/"+t,method:"get"})}function h(e,t){return Object(i["a"])({url:"/sip/talk/broadcast/stop/"+e+"/"+t,method:"get"})}},e51f:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.$t("device.product-list.058448-0"),visible:e.open,width:"910px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:e.$t("device.product-list.058448-2"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.product-list.058448-3")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.product-list.058448-4")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":e.rowClick}},[r("el-table-column",{attrs:{label:e.$t("device.device-edit.148398-6"),width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-6"),align:"left",prop:"categoryName","min-width":"150"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-7"),align:"left",prop:"tenantName","min-width":"100"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-8"),align:"center",prop:"status",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isAuthorize?r("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("device.product-list.058448-9")))]):e._e(),0==t.row.isAuthorize?r("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("device.product-list.058448-10")))]):e._e()]}}])}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-11"),align:"center",prop:"status","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_vertificate_method,value:t.row.vertificateMethod}})]}}])}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-12"),align:"center",prop:"networkMethod","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_network_method,value:t.row.networkMethod}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectProduct}},[e._v(e._s(e.$t("device.product-list.058448-14")))]),r("el-button",{attrs:{type:"info"},on:{click:e.closeDialog}},[e._v(e._s(e.$t("device.product-list.058448-15")))])],1)],1)},o=[],n=(r("a9e3"),r("9b9c")),a={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0},showSenior:{type:Boolean,default:!0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.showSenior=this.showSenior,Object(n["g"])(this.queryParams).then((function(t){for(var r=0;r<t.rows.length;r++)t.rows[r].isSelect=!1;e.productList=t.rows,e.total=t.total,0!=e.productId&&e.setRadioSelected(e.productId),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.productId),this.product=e)},setRadioSelected:function(e){for(var t=0;t<this.productList.length;t++)this.productList[t].productId==e?this.productList[t].isSelect=!0:this.productList[t].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},c=a,l=r("2877"),u=Object(l["a"])(c,i,o,!1,null,null,null);t["default"]=u.exports}}]);