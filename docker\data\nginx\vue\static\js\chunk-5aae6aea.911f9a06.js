(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5aae6aea"],{"09cb":function(e,t,o){"use strict";o.d(t,"a",(function(){return i}));o("d3b7");function i(){return new Promise((function(e,t){if("undefined"!==typeof BMap)return e(BMap),!0;window.onBMapCallback=function(){e(BMap)};var o=document.location.protocol;if("https:"==o){var i=document.createElement("meta");i.httpEquiv="Content-Security-Policy",i.content="upgrade-insecure-requests",i.onerror=t,document.head.appendChild(i)}var n=document.createElement("script");n.type="text/javascript",n.src="http://api.map.baidu.com/api?v=2.0&ak=nAtaBg9FYzav6c8P9rF9qzsWZfTXXXXX&s=1&__ec_v__=20190126&callback=onBMapCallback",n.onerror=t,document.head.appendChild(n)}))}},"584f":function(e,t,o){"use strict";o.d(t,"k",(function(){return n})),o.d(t,"n",(function(){return r})),o.d(t,"l",(function(){return a})),o.d(t,"m",(function(){return s})),o.d(t,"j",(function(){return l})),o.d(t,"e",(function(){return c})),o.d(t,"c",(function(){return p})),o.d(t,"f",(function(){return u})),o.d(t,"h",(function(){return d})),o.d(t,"g",(function(){return f})),o.d(t,"a",(function(){return m})),o.d(t,"o",(function(){return y})),o.d(t,"b",(function(){return h})),o.d(t,"d",(function(){return v})),o.d(t,"i",(function(){return b}));var i=o("b775");function n(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function a(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function c(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function p(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function d(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function f(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function m(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function y(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function h(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function v(e){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:e})}function b(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},"655b":function(e,t,o){"use strict";o("7ec6")},"731b":function(e,t,o){"use strict";o.r(t);var i=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"centermap"},[e._m(0),o("div",{staticStyle:{height:"640px","background-color":"#0e2e87"}},[o("dv-border-box-8",[o("div",{ref:"map",staticStyle:{height:"600px",width:"760px",padding:"10px"}})])],1)])},n=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"maptitle"},[o("div",{staticClass:"zuo"}),o("span",{staticClass:"titletext"},[e._v("设备分布图")]),o("div",{staticClass:"you"})])}],r=o("ade3"),a=(o("d81d"),o("14d9"),o("b0c0"),o("09cb")),s=o("584f");o("a00a");var l={data:function(){return{deviceList:[]}},created:function(){this.getAllDevice()},beforeDestroy:function(){this.clearData()},methods:{clearData:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},switper:function(){var e=this;if(!this.timer){var t=function(t){e.getAllDevice()};this.timer=setInterval(t,12e4)}},getAllDevice:function(){var e=this;Object(s["j"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.deviceCount=t.total,e.loadMap(),e.switper()}))},loadMap:function(){var e=this;Object(a["a"])().then((function(){e.getmap()}))},getmap:function(){var e,t=this,o=this.$echarts.init(this.$refs.map);o.on("click",(function(e){e.data.deviceId&&t.$router.push({path:"/iot/device-edit",query:{t:Date.now(),deviceId:e.data.deviceId}})}));var i=function(e,t){for(var o=[],i=0;i<e.length;i++){var n,a=[e[i].longitude,e[i].latitude];if(a&&e[i].status==t)o.push((n={name:e[i].deviceName,value:a,serialNumber:e[i].serialNumber,status:e[i].status,isShadow:e[i].isShadow,firmwareVersion:e[i].firmwareVersion,networkAddress:e[i].networkAddress,productName:e[i].productName,activeTime:null==e[i].activeTime?"":e[i].activeTime,deviceId:e[i].deviceId},Object(r["a"])(n,"serialNumber",e[i].serialNumber),Object(r["a"])(n,"locationWay",e[i].locationWay),n))}return o};e={tooltip:{trigger:"item",backgroundColor:"rgba(58,73,116,0.7)",textStyle:{color:"rgba(65,235,246,1)"},formatter:function(e){var t='<div style="padding:5px;line-height:28px;">';return t+="设备名称： <span style='color:#FFF'>"+e.data.name+"</span><br />",t+="设备编号： "+e.data.serialNumber+"<br />",t+="设备状态： ",1==e.data.status?t+="<span style='color:#E6A23C'>未激活</span><br />":2==e.data.status?t+="<span style='color:#F56C6C'>禁用</span><br />":3==e.data.status?t+="<span style='color:#67C23A'>在线</span><br />":4==e.data.status&&(t+="<span style='color:#909399'>离线</span><br />"),1==e.data.isShadow?t+="设备影子： <span style='color:#67C23A'>启用</span><br />":t+="设备影子： <span style='color:#909399'>未启用</span><br />",t+="产品名称： "+e.data.productName+"<br />",t+="固件版本： Version "+e.data.firmwareVersion+"<br />",t+="激活时间： "+e.data.activeTime+"<br />",t+="定位方式： ",1==e.data.locationWay?t+="自动定位<br />":2==e.data.locationWay?t+="设备定位<br />":3==e.data.locationWay?t+="自定义位置<br />":t+="未知<br />",t+="所在地址： "+e.data.networkAddress+"<br />",t+="</div>",t}},bmap:{center:[106,37.5],zoom:5,roam:"move",mapStyle:{styleJson:[{featureType:"water",elementType:"all",stylers:{color:"#3863db"}},{featureType:"land",elementType:"all",stylers:{color:"#0e2e87"}},{featureType:"railway",elementType:"all",stylers:{visibility:"off"}},{featureType:"highway",elementType:"all",stylers:{visibility:"off",color:"#fdfdfd"}},{featureType:"highway",elementType:"labels",stylers:Object(r["a"])({visibility:"off"},"visibility","off")},{featureType:"arterial",elementType:"geometry",stylers:{visibility:"off",color:"#fefefe"}},{featureType:"arterial",elementType:"geometry.fill",stylers:{visibility:"off",color:"#fefefe"}},{featureType:"poi",elementType:"all",stylers:Object(r["a"])({visibility:"off"},"visibility","off")},{featureType:"green",elementType:"all",stylers:{visibility:"off"}},{featureType:"subway",elementType:"all",stylers:{visibility:"off"}},{featureType:"manmade",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"local",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"arterial",elementType:"labels",stylers:{visibility:"off"}},{featureType:"boundary",elementType:"all",stylers:{color:"#23cdd8"}},{featureType:"building",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"label",elementType:"labels.text.fill",stylers:{color:"#264194",visibility:"off"}}]}},series:[{type:"scatter",coordinateSystem:"bmap",data:i(this.deviceList,1),symbolSize:10,itemStyle:{color:"#e8fc05"}},{type:"scatter",coordinateSystem:"bmap",data:i(this.deviceList,2),symbolSize:10,itemStyle:{color:"#fc3464"}},{type:"scatter",coordinateSystem:"bmap",data:i(this.deviceList,4),symbolSize:10,itemStyle:{color:"#eee"}},{type:"effectScatter",coordinateSystem:"bmap",data:i(this.deviceList,3),symbolSize:12,showEffectOn:"render",rippleEffect:{brushType:"stroke",scale:5},label:{formatter:"{b}",position:"right",show:!1},itemStyle:{color:"#5de88e",shadowBlur:100,shadowColor:"#333"},zlevel:1}]},e&&o.setOption(e,!0)}}},c=l,p=(o("655b"),o("2877")),u=Object(p["a"])(c,i,n,!1,null,null,null);t["default"]=u.exports},"7ec6":function(e,t,o){},a00a:function(e,t,o){"use strict";o.r(t),o.d(t,"version",(function(){return u}));var i,n=o("313e");function r(e,t){this._bmap=e,this.dimensions=["lng","lat"],this._mapOffset=[0,0],this._api=t,this._projection=new BMap.MercatorProjection}function a(e,t){return t=t||[0,0],n["util"].map([0,1],(function(o){var i=t[o],n=e[o]/2,r=[],a=[];return r[o]=i-n,a[o]=i+n,r[1-o]=a[1-o]=t[1-o],Math.abs(this.dataToPoint(r)[o]-this.dataToPoint(a)[o])}),this)}function s(){function e(e){this._root=e}return e.prototype=new BMap.Overlay,e.prototype.initialize=function(e){return e.getPanes().labelPane.appendChild(this._root),this._root},e.prototype.draw=function(){},e}r.prototype.type="bmap",r.prototype.dimensions=["lng","lat"],r.prototype.setZoom=function(e){this._zoom=e},r.prototype.setCenter=function(e){this._center=this._projection.lngLatToPoint(new BMap.Point(e[0],e[1]))},r.prototype.setMapOffset=function(e){this._mapOffset=e},r.prototype.getBMap=function(){return this._bmap},r.prototype.dataToPoint=function(e){var t=new BMap.Point(e[0],e[1]),o=this._bmap.pointToOverlayPixel(t),i=this._mapOffset;return[o.x-i[0],o.y-i[1]]},r.prototype.pointToData=function(e){var t=this._mapOffset;return e=this._bmap.overlayPixelToPoint({x:e[0]+t[0],y:e[1]+t[1]}),[e.lng,e.lat]},r.prototype.getViewRect=function(){var e=this._api;return new n["graphic"].BoundingRect(0,0,e.getWidth(),e.getHeight())},r.prototype.getRoamTransform=function(){return n["matrix"].create()},r.prototype.prepareCustoms=function(){var e=this.getViewRect();return{coordSys:{type:"bmap",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:n["util"].bind(this.dataToPoint,this),size:n["util"].bind(a,this)}}},r.prototype.convertToPixel=function(e,t,o){return this.dataToPoint(o)},r.prototype.convertFromPixel=function(e,t,o){return this.pointToData(o)},r.dimensions=r.prototype.dimensions,r.create=function(e,t){var o,a=t.getDom();return e.eachComponent("bmap",(function(e){var l,c=t.getZr().painter,p=c.getViewportRoot();if("undefined"===typeof BMap)throw new Error("BMap api is not loaded");if(i=i||s(),o)throw new Error("Only one bmap component can exist");if(!e.__bmap){var u=a.querySelector(".ec-extension-bmap");u&&(p.style.left="0px",p.style.top="0px",a.removeChild(u)),u=document.createElement("div"),u.className="ec-extension-bmap",u.style.cssText="position:absolute;width:100%;height:100%",a.appendChild(u);var d=e.get("mapOptions");d&&(d=n["util"].clone(d),delete d.mapType),l=e.__bmap=new BMap.Map(u,d);var f=new i(p);l.addOverlay(f),c.getViewportRootOffset=function(){return{offsetLeft:0,offsetTop:0}}}l=e.__bmap;var m=e.get("center"),y=e.get("zoom");if(m&&y){var h=l.getCenter(),v=l.getZoom(),b=e.centerOrZoomChanged([h.lng,h.lat],v);if(b){var g=new BMap.Point(m[0],m[1]);l.centerAndZoom(g,y)}}o=new r(l,t),o.setMapOffset(e.__mapOffset||[0,0]),o.setZoom(y),o.setCenter(m),e.coordinateSystem=o})),e.eachSeries((function(e){"bmap"===e.get("coordinateSystem")&&(e.coordinateSystem=o)})),o&&[o]};var l=r;function c(e,t){return e&&t&&e[0]===t[0]&&e[1]===t[1]}n["extendComponentModel"]({type:"bmap",getBMap:function(){return this.__bmap},setCenterAndZoom:function(e,t){this.option.center=e,this.option.zoom=t},centerOrZoomChanged:function(e,t){var o=this.option;return!(c(e,o.center)&&t===o.zoom)},defaultOption:{center:[104.114129,37.550339],zoom:5,mapStyle:{},mapStyleV2:{},mapOptions:{},roam:!1}});function p(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}n["extendComponentView"]({type:"bmap",render:function(e,t,o){var i=!0,r=e.getBMap(),a=o.getZr().painter.getViewportRoot(),s=e.coordinateSystem,l=function(t,n){if(!i){var r=a.parentNode.parentNode.parentNode,l=[-parseInt(r.style.left,10)||0,-parseInt(r.style.top,10)||0],c=a.style,p=l[0]+"px",u=l[1]+"px";c.left!==p&&(c.left=p),c.top!==u&&(c.top=u),s.setMapOffset(l),e.__mapOffset=l,o.dispatchAction({type:"bmapRoam",animation:{duration:0}})}};function c(){i||o.dispatchAction({type:"bmapRoam",animation:{duration:0}})}r.removeEventListener("moving",this._oldMoveHandler),r.removeEventListener("moveend",this._oldMoveHandler),r.removeEventListener("zoomend",this._oldZoomEndHandler),r.addEventListener("moving",l),r.addEventListener("moveend",l),r.addEventListener("zoomend",c),this._oldMoveHandler=l,this._oldZoomEndHandler=c;var u=e.get("roam");u&&"scale"!==u?r.enableDragging():r.disableDragging(),u&&"move"!==u?(r.enableScrollWheelZoom(),r.enableDoubleClickZoom(),r.enablePinchToZoom()):(r.disableScrollWheelZoom(),r.disableDoubleClickZoom(),r.disablePinchToZoom());var d=e.__mapStyle,f=e.get("mapStyle")||{},m=JSON.stringify(f);JSON.stringify(d)!==m&&(p(f)||r.setMapStyle(n["util"].clone(f)),e.__mapStyle=JSON.parse(m));var y=e.__mapStyle2,h=e.get("mapStyleV2")||{},v=JSON.stringify(h);JSON.stringify(y)!==v&&(p(h)||r.setMapStyleV2(n["util"].clone(h)),e.__mapStyle2=JSON.parse(v)),i=!1}});n["registerCoordinateSystem"]("bmap",l),n["registerAction"]({type:"bmapRoam",event:"bmapRoam",update:"updateLayout"},(function(e,t){t.eachComponent("bmap",(function(e){var t=e.getBMap(),o=t.getCenter();e.setCenterAndZoom([o.lng,o.lat],t.getZoom())}))}));var u="1.0.0"}}]);