(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d9962ce6"],{"07f7":function(e,t,r){"use strict";r("8b5e")},"1f34":function(e,t,r){"use strict";r.r(t);var s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:10}},[r("el-col",{attrs:{span:4,xs:24}},[r("el-card",[r("div",{staticClass:"head-container"},[r("el-input",{staticStyle:{"margin-bottom":"20px","margin-right":"10px"},attrs:{placeholder:"请输入归属机构",clearable:"",size:"small","prefix-icon":"el-icon-search"},model:{value:e.deptName,callback:function(t){e.deptName=t},expression:"deptName"}}),r("el-tree",{ref:"tree",attrs:{data:e.deptOptions,props:e.defaultProps,"expand-on-click-node":!1,"filter-node-method":e.filterNode,"node-key":"id","default-expand-all":"","highlight-current":""},on:{"node-click":e.handleNodeClick}})],1)])],1),r("el-col",{attrs:{span:20,xs:24}},[r("el-card",[r("div",{staticClass:"head-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"用户账号",prop:"userName"}},[r("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入用户账号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),r("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[r("el-input",{staticStyle:{width:"240px"},attrs:{placeholder:"请输入手机号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phonenumber,callback:function(t){e.$set(e.queryParams,"phonenumber",t)},expression:"queryParams.phonenumber"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{staticStyle:{width:"240px"},attrs:{placeholder:"用户状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增用户")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:import"],expression:"['system:user:import']"}],attrs:{type:"info",plain:"",icon:"el-icon-upload2",size:"mini"},on:{click:e.handleImport}},[e._v("导入")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:export"],expression:"['system:user:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("el-col",{attrs:{span:1.5}},[r("el-checkbox",{staticStyle:{margin:"5px 0 0"},on:{change:e.handleQuery},model:{value:e.queryParams.showChild,callback:function(t){e.$set(e.queryParams,"showChild",t)},expression:"queryParams.showChild"}},[e._v("显示下级机构数据")]),r("el-tooltip",{attrs:{content:"选中后，本级可以看下级的数据",placement:"top"}},[r("i",{staticClass:"el-icon-question"})])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch,columns:e.columns},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList}},[e.columns[1].visible?r("el-table-column",{key:"userName",attrs:{label:"用户账号",align:"left",prop:"userName","show-overflow-tooltip":!0}}):e._e(),e.columns[2].visible?r("el-table-column",{key:"nickName",attrs:{label:"用户昵称",align:"left",prop:"nickName","show-overflow-tooltip":!0}}):e._e(),e.columns[3].visible?r("el-table-column",{key:"deptName",attrs:{label:"归属机构",align:"left",prop:"dept.deptName","show-overflow-tooltip":!0}}):e._e(),e.columns[4].visible?r("el-table-column",{key:"phonenumber",attrs:{label:"手机号码",align:"left",prop:"phonenumber",width:"120"}}):e._e(),e.columns[5].visible?r("el-table-column",{key:"status",attrs:{label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}):e._e(),e.columns[6].visible?r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}):e._e(),r("el-table-column",{attrs:{label:"操作",align:"center",width:"160","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!==t.row.userId?[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")]),r("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd","system:user:edit"],expression:"['system:user:resetPwd', 'system:user:edit']"}],attrs:{size:"mini"},on:{command:function(r){return e.handleCommand(r,t.row)}}},[r("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-d-arrow-right"}},[e._v("更多")]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd"],expression:"['system:user:resetPwd']"}],attrs:{command:"handleResetPwd",icon:"el-icon-key"}},[e._v("重置密码")])],1)],1)]:void 0}}],null,!0)})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)])],1)],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("div",{staticStyle:{"margin-top":"-55px"}},[r("el-divider",{staticStyle:{"margin-top":"-30px"}}),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"用户昵称",prop:"nickName"}},[r("el-input",{attrs:{placeholder:"请输入用户昵称",maxlength:"30"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1),r("el-form-item",{attrs:{label:"归属机构",prop:"deptId"}},[r("treeselect",{attrs:{options:e.deptOptions,"show-count":!0,placeholder:"请选择归属机构",disabled:void 0!=e.form.userId},on:{input:e.getRoleList},model:{value:e.form.deptId,callback:function(t){e.$set(e.form,"deptId",t)},expression:"form.deptId"}})],1),r("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[r("el-input",{attrs:{placeholder:"请输入手机号码",maxlength:"11"},model:{value:e.form.phonenumber,callback:function(t){e.$set(e.form,"phonenumber",t)},expression:"form.phonenumber"}})],1),r("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[r("el-input",{attrs:{placeholder:"请输入邮箱",maxlength:"50"},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}})],1),void 0==e.form.userId?r("el-form-item",{attrs:{label:"用户账号",prop:"userName"}},[r("el-input",{attrs:{placeholder:"请输入用户账号",maxlength:"30"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1):e._e(),void 0==e.form.userId?r("el-form-item",{attrs:{label:"用户密码",prop:"password"}},[r("el-input",{attrs:{placeholder:"请输入用户密码",type:"password",maxlength:"20","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1):e._e(),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-radio-group",{attrs:{disabled:0==this.isEdit},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),r("el-form-item",{attrs:{label:"角色",prop:"roleIds"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",placeholder:"请选择角色",disabled:0==this.isEdit},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}},e._l(e.roleOptions,(function(e){return r("el-option",{key:e.roleId,attrs:{label:e.roleName,value:e.roleId,disabled:1==e.status}})})),1)],1),r("el-form-item",{attrs:{label:"备注"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)]),r("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[r("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?updateSupport="+e.upload.updateSupport,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,drag:""}},[r("i",{staticClass:"el-icon-upload"}),r("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),r("em",[e._v("点击上传")])]),r("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[r("div",{staticClass:"el-upload__tip",staticStyle:{"margin-top":"10px"},attrs:{slot:"tip"},slot:"tip"},[r("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),r("div",{staticStyle:{"margin-top":"10px"}},[r("span",[e._v("仅允许导入xls、xlsx格式文件。")]),r("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[e._v("下载模板")])],1)])]),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),r("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},a=[],o=r("5530"),n=(r("4de4"),r("d3b7"),r("c0c7")),i=r("5f87"),l=r("ca17"),u=r.n(l),d=(r("542c"),{name:"User",dicts:["sys_normal_disable","sys_user_sex"],components:{Treeselect:u.a},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userList:null,title:"",deptOptions:void 0,open:!1,deptName:void 0,initPassword:void 0,isEdit:!0,dateRange:[],roleOptions:[],form:{},defaultProps:{children:"children",label:"label"},upload:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(i["a"])()},url:"/prod-api/system/user/importData"},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0,showChild:!0},columns:[{key:0,label:"用户编号",visible:!0},{key:1,label:"用户账号",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"机构",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}],rules:{userName:[{required:!0,message:"用户账号不能为空",trigger:"blur"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"}],roleIds:[{required:!0,message:"角色不能为空",trigger:"change"}],status:[{required:!0}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},watch:{deptName:function(e){this.$refs.tree.filter(e)}},created:function(){var e=this,t=this.$route.params&&this.$route.params.deptId;t?(this.queryParams.deptId=t,this.getList()):this.getList(),this.getDeptTree(),this.getConfigKey("sys.user.initPassword").then((function(t){e.initPassword=t.msg}))},methods:{getList:function(){var e=this;this.loading=!0,this.form.deptId=this.queryParams.deptId,Object(n["k"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.userList=t.rows,e.total=t.total,e.loading=!1}))},getDeptTree:function(){var e=this;Object(n["d"])().then((function(t){e.deptOptions=t.data}))},filterNode:function(e,t){return!e||-1!==t.label.indexOf(e)},handleNodeClick:function(e){this.queryParams.deptId=e.id,this.handleQuery()},handleStatusChange:function(e){var t=this,r="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+r+'""'+e.userName+'"用户吗？').then((function(){return Object(n["b"])(e.userId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:void 0,phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[]},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.deptId=void 0,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleCommand:function(e,t){switch(e){case"handleResetPwd":this.handleResetPwd(t);break;case"handleAuthRole":this.handleAuthRole(t);break;default:break}},handleAdd:function(){var e=this;this.reset(),Object(n["i"])().then((function(t){e.roleOptions=t.roles,e.open=!0,e.title="添加用户",e.form.deptId=e.queryParams.deptId,e.form.password=e.initPassword}))},handleUpdate:function(e){var t=this;this.reset();var r=e.userId||this.ids;Object(n["i"])(r).then((function(r){t.isEdit=e.canEditRole,t.form=r.data,t.roleOptions=r.roles,t.$set(t.form,"postIds",r.postIds),t.$set(t.form,"roleIds",r.roleIds),t.open=!0,t.title="修改用户",t.form.password=""}))},getRoleList:function(){var e=this;if(void 0!=this.form.deptId&&null!=this.form.deptId){var t=this.form.deptId;Object(n["h"])(t).then((function(t){e.roleOptions=t.roles}))}},handleResetPwd:function(e){var t=this;this.$prompt('请输入"'+e.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间"}).then((function(r){var s=r.value;Object(n["l"])(e.userId,s).then((function(e){t.$modal.msgSuccess("修改成功，新密码是："+s)}))})).catch((function(){}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.userId?Object(n["o"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.userId||this.ids;this.$modal.confirm('是否确认删除用户编号为"'+r+'"的数据项？').then((function(){return Object(n["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/user/export",Object(o["a"])({},this.queryParams),"user_".concat((new Date).getTime(),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/user/importTemplate",{},"user_template_".concat((new Date).getTime(),".xlsx"))},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()}}}),c=d,m=(r("07f7"),r("2877")),p=Object(m["a"])(c,s,a,!1,null,null,null);t["default"]=p.exports},"8b5e":function(e,t,r){},c0c7:function(e,t,r){"use strict";r.d(t,"k",(function(){return o})),r.d(t,"i",(function(){return n})),r.d(t,"h",(function(){return i})),r.d(t,"a",(function(){return l})),r.d(t,"o",(function(){return u})),r.d(t,"c",(function(){return d})),r.d(t,"l",(function(){return c})),r.d(t,"b",(function(){return m})),r.d(t,"g",(function(){return p})),r.d(t,"m",(function(){return h})),r.d(t,"j",(function(){return f})),r.d(t,"p",(function(){return b})),r.d(t,"q",(function(){return v})),r.d(t,"r",(function(){return y})),r.d(t,"f",(function(){return g})),r.d(t,"n",(function(){return w})),r.d(t,"d",(function(){return x})),r.d(t,"e",(function(){return k}));var s=r("b775"),a=r("c38a");function o(e){return Object(s["a"])({url:"/system/user/list",method:"get",params:e})}function n(e){return Object(s["a"])({url:"/system/user/"+Object(a["e"])(e),method:"get"})}function i(e){return Object(s["a"])({url:"/system/dept/getRole?deptId="+e,method:"get"})}function l(e){return Object(s["a"])({url:"/system/user",method:"post",data:e})}function u(e){return Object(s["a"])({url:"/system/user",method:"put",data:e})}function d(e){return Object(s["a"])({url:"/system/user/"+e,method:"delete"})}function c(e,t){var r={userId:e,password:t};return Object(s["a"])({url:"/system/user/resetPwd",method:"put",data:r})}function m(e,t){var r={userId:e,status:t};return Object(s["a"])({url:"/system/user/changeStatus",method:"put",data:r})}function p(){return Object(s["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(e){return Object(s["a"])({url:"/wechat/cancelBind",method:"post",data:e})}function f(){return Object(s["a"])({url:"/system/user/profile",method:"get"})}function b(e){return Object(s["a"])({url:"/system/user/profile",method:"put",data:e})}function v(e,t){var r={oldPassword:e,newPassword:t};return Object(s["a"])({url:"/system/user/profile/updatePwd",method:"put",params:r})}function y(e){return Object(s["a"])({url:"/system/user/profile/avatar",method:"post",data:e})}function g(e){return Object(s["a"])({url:"/system/user/authRole/"+e,method:"get"})}function w(e){return Object(s["a"])({url:"/system/user/authRole",method:"put",params:e})}function x(){return Object(s["a"])({url:"/system/user/deptTree",method:"get"})}function k(e){return Object(s["a"])({url:"/system/user/deptTree?showOwner="+e,method:"get"})}}}]);