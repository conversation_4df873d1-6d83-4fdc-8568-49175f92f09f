## NOTE:
## This config file overrides data/configs/cluster.hocon,
## and is merged with environment variables which start with 'EMQX_' prefix.
##
## Config changes made from EMQX dashboard UI, management HTTP API, or CLI
## are stored in data/configs/cluster.hocon.
## To avoid confusion, please do not store the same configs in both files.
##
## See https://docs.emqx.com/en/enterprise/v5.0/configuration/configuration.html
## Configuration full example can be found in emqx.conf.example

node {
  name = "emqx@177.7.0.12"
  cookie = "emqxsecretcookie"
  data_dir = "data"
}

cluster {
  name = emqxcl
  discovery_strategy = manual
}


dashboard {
    listeners.http {
        bind = 18083
    }
    default_username = "admin"
    default_password = "admin123"
}

authorization {
  deny_action = ignore
  no_match = allow
  cache = { enable = true }
}


## http 认证
authentication  = [
  {
    mechanism = password_based
    backend = http
    enable = true
    method = post
    url = "http://java:8080/iot/tool/mqtt/authv5"
    body {
        clientid = "${clientid}"
        username = "${username}"
        password = "${password}"
        peerhost = "${peerhost}"
    }
    headers {
        "Content-Type" = "application/json"
        "X-Request-Source" = "EMQX"
    }
  }
]

# WebHook(匹配上线和下线规则后触发)
bridges {
  webhook.fastbee_hook  =
  {
    enable  =  true
    connect_timeout  =  15s
    retry_interval  =  60s
    pool_type  =  random
    pool_size  =  8
    enable_pipelining  =  100
    max_retries  =  2
    request_timeout  =  15s
    method  =  post
    url  =  "http://java:8080/iot/tool/mqtt/webhookv5"
    body  =  "{\"clientid\" : \"${clientid}\",\"event\" : \"${event}\",\"peername\" : \"${peername}\"}"
    headers  =  {  accept = "application/json"  "cache-control" = "no-cache"  connection = "keep-alive"  "content-type" = "application/json"  "keep-alive" = "timeout=5"}
  }
}

# 规则（处理上线和下线）
rule_engine {
  ignore_sys_message  =  true
  jq_function_default_timeout  =  10s
    rules.fastbee_rule_connected  =
    {
      sql  =  "SELECT * FROM \"$events/client_connected\""
      actions  =  ["webhook:fastbee_hook"]
      enable  =  true
      description  =  "处理设备上线"
    }
    rules.fastbee_rule_disconnected  =
    {
      sql  =  "SELECT * FROM  \"$events/client_disconnected\""
      actions  =  ["webhook:fastbee_hook"]
      enable  =  true
      description  =  "处理设备下线"
    }
    rules.fastbee_rule_subscribed  =
    {
      sql  =  "SELECT * FROM  \"$events/session_subscribed\""
      actions  =  ["webhook:fastbee_hook"]
      enable  =  true
      description  =  "处理订阅完主题的规则"
    }
}


