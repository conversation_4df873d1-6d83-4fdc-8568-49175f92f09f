(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-db2f37b2"],{"31f1":function(t,e,r){"use strict";r("ed2e")},"584f":function(t,e,r){"use strict";r.d(e,"l",(function(){return a})),r.d(e,"q",(function(){return o})),r.d(e,"m",(function(){return i})),r.d(e,"n",(function(){return u})),r.d(e,"k",(function(){return c})),r.d(e,"f",(function(){return l})),r.d(e,"c",(function(){return s})),r.d(e,"g",(function(){return d})),r.d(e,"i",(function(){return f})),r.d(e,"d",(function(){return h})),r.d(e,"r",(function(){return p})),r.d(e,"o",(function(){return m})),r.d(e,"p",(function(){return b})),r.d(e,"h",(function(){return g})),r.d(e,"a",(function(){return v})),r.d(e,"s",(function(){return y})),r.d(e,"b",(function(){return w})),r.d(e,"e",(function(){return O})),r.d(e,"j",(function(){return j}));var n=r("b775");function a(t){return Object(n["a"])({url:"/iot/device/list",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function u(t){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:t})}function c(){return Object(n["a"])({url:"/iot/device/all",method:"get"})}function l(t){return Object(n["a"])({url:"/iot/device/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function d(t){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function f(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function h(t,e){return Object(n["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function p(t){return Object(n["a"])({url:"/iot/device/recovery?deviceIds="+t,method:"post"})}function m(){return Object(n["a"])({url:"",method:"get"})}function b(){return Object(n["a"])({url:"",method:"get"})}function g(t){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function v(t){return Object(n["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/iot/device",method:"put",data:t})}function w(t){return Object(n["a"])({url:"/iot/device/"+t,method:"delete"})}function O(t){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:t})}function j(t){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}},"9b9c":function(t,e,r){"use strict";r.d(e,"f",(function(){return a})),r.d(e,"g",(function(){return o})),r.d(e,"e",(function(){return i})),r.d(e,"a",(function(){return u})),r.d(e,"i",(function(){return c})),r.d(e,"d",(function(){return l})),r.d(e,"b",(function(){return s})),r.d(e,"c",(function(){return d})),r.d(e,"h",(function(){return f}));var n=r("b775");function a(t){return Object(n["a"])({url:"/iot/product/list",method:"get",params:t})}function o(){return Object(n["a"])({url:"/iot/product/shortList",method:"get"})}function i(t){return Object(n["a"])({url:"/iot/product/"+t,method:"get"})}function u(t){return Object(n["a"])({url:"/iot/product",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/iot/product",method:"put",data:t})}function l(t){return Object(n["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/iot/product/status/",method:"put",data:t})}function d(t){return Object(n["a"])({url:"/iot/product/"+t,method:"delete"})}function f(t){return Object(n["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:t})}},c0c7:function(t,e,r){"use strict";r.d(e,"k",(function(){return o})),r.d(e,"i",(function(){return i})),r.d(e,"h",(function(){return u})),r.d(e,"a",(function(){return c})),r.d(e,"o",(function(){return l})),r.d(e,"c",(function(){return s})),r.d(e,"l",(function(){return d})),r.d(e,"b",(function(){return f})),r.d(e,"g",(function(){return h})),r.d(e,"m",(function(){return p})),r.d(e,"j",(function(){return m})),r.d(e,"p",(function(){return b})),r.d(e,"q",(function(){return g})),r.d(e,"r",(function(){return v})),r.d(e,"f",(function(){return y})),r.d(e,"n",(function(){return w})),r.d(e,"d",(function(){return O})),r.d(e,"e",(function(){return j}));var n=r("b775"),a=r("c38a");function o(t){return Object(n["a"])({url:"/system/user/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/system/user/"+Object(a["e"])(t),method:"get"})}function u(t){return Object(n["a"])({url:"/system/dept/getRole?deptId="+t,method:"get"})}function c(t){return Object(n["a"])({url:"/system/user",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/system/user",method:"put",data:t})}function s(t){return Object(n["a"])({url:"/system/user/"+t,method:"delete"})}function d(t,e){var r={userId:t,password:e};return Object(n["a"])({url:"/system/user/resetPwd",method:"put",data:r})}function f(t,e){var r={userId:t,status:e};return Object(n["a"])({url:"/system/user/changeStatus",method:"put",data:r})}function h(){return Object(n["a"])({url:"/wechat/getWxBindQr",method:"get"})}function p(t){return Object(n["a"])({url:"/wechat/cancelBind",method:"post",data:t})}function m(){return Object(n["a"])({url:"/system/user/profile",method:"get"})}function b(t){return Object(n["a"])({url:"/system/user/profile",method:"put",data:t})}function g(t,e){var r={oldPassword:t,newPassword:e};return Object(n["a"])({url:"/system/user/profile/updatePwd",method:"put",params:r})}function v(t){return Object(n["a"])({url:"/system/user/profile/avatar",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/system/user/authRole/"+t,method:"get"})}function w(t){return Object(n["a"])({url:"/system/user/authRole",method:"put",params:t})}function O(){return Object(n["a"])({url:"/system/user/deptTree",method:"get"})}function j(t){return Object(n["a"])({url:"/system/user/deptTree?showOwner="+t,method:"get"})}},e000:function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("el-card",{staticStyle:{margin:"10px"}},[r("el-row",{attrs:{gutter:10}},[r("i",{staticClass:"el-icon-arrow-left",on:{click:function(e){return t.goBack()}}}),r("el-divider",{attrs:{direction:"vertical"}}),r("span",[t._v("分配设备")])],1),t._v(" "),r("el-divider"),r("div",{staticStyle:{width:"70%"}},[r("el-row",[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams}},[r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{prop:"productId"}},[r("el-select",{staticStyle:{width:"95%"},attrs:{placeholder:"请选择产品",filterable:"",clearable:""},model:{value:t.queryParams.productId,callback:function(e){t.$set(t.queryParams,"productId",e)},expression:"queryParams.productId"}},t._l(t.productList,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{prop:"deviceName"}},[r("el-input",{staticStyle:{width:"95%"},attrs:{placeholder:"请输入设备名称",clearable:""},model:{value:t.queryParams.deviceName,callback:function(e){t.$set(t.queryParams,"deviceName",e)},expression:"queryParams.deviceName"}})],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{prop:"serialNumber"}},[r("el-input",{staticStyle:{width:"95%"},attrs:{placeholder:"请输入设备编号",clearable:""},model:{value:t.queryParams.serialNumber,callback:function(e){t.$set(t.queryParams,"serialNumber",e)},expression:"queryParams.serialNumber"}})],1)],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v("查询 ")])],1),r("el-button",{staticStyle:{"margin-left":"5px"},attrs:{icon:"el-icon-refresh-left"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),r("div",{staticClass:"general"},[r("div",{staticClass:"topLeft"},[r("div",{staticStyle:{padding:"10px 10px","background-color":"#f8f8f9"}},[r("span",{staticStyle:{"font-size":"15px","font-weight":"bold"}},[t._v("所有设备")]),r("span",{staticStyle:{"font-size":"15px","font-weight":"bold",float:"right"}},[t._v(t._s(t.selectedCount)+"/"+t._s(this.count))])]),r("el-row",[r("el-table",{ref:"leftTableData",staticStyle:{width:"100%"},attrs:{data:t.menuTableData,"max-height":"373"},on:{"selection-change":t.changeCheckBoxValueLeft}},[r("template",{slot:"empty"},[r("el-empty",{attrs:{"image-size":100,description:"暂无数据"}})],1),r("el-table-column",{attrs:{type:"selection",width:"55",selectable:t.checkSelectable}}),r("el-table-column",{attrs:{prop:"deviceId",label:"DeviceKey",fixed:"","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"productName",label:"产品名称","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"serialNumber",label:"设备编号","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"deviceName",label:"设备名称","show-overflow-tooltip":""}})],2)],1)],1),r("div",{staticClass:"topCenter"},[r("el-button",{attrs:{type:"primary",disabled:t.add},on:{click:t.rightAdd}},[r("i",{staticClass:"el-icon-arrow-right el-icon--right"})]),r("el-button",{attrs:{type:"primary",disabled:t.del},on:{click:t.leftDelete}},[r("i",{staticClass:"el-icon-arrow-left el-icon--left"})])],1),r("div",{staticClass:"topRight"},[r("div",{staticStyle:{padding:"10px 10px","background-color":"#f8f8f9"}},[r("span",{staticStyle:{"font-size":"15px","font-weight":"bold"}},[t._v("已选设备")]),r("span",{staticStyle:{"font-size":"15px","font-weight":"bold",float:"right"}},[t._v(t._s(t.selectedCount1)+"/500")])]),r("el-row",[r("el-table",{ref:"rightTableData",staticStyle:{width:"100%"},attrs:{data:t.rightTableData,"max-height":"373"},on:{"selection-change":t.changeCheckBoxValueRight}},[r("template",{slot:"empty"},[r("el-empty",{attrs:{"image-size":100,description:"暂无数据"}})],1),r("el-table-column",{attrs:{type:"selection",width:"55"}}),r("el-table-column",{attrs:{prop:"deviceId",label:"DeviceKey","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"productName",label:"产品名称","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"serialNumber",label:"设备编号","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"deviceName",label:"设备名称","show-overflow-tooltip":""}})],2)],1)],1)]),r("div",{staticClass:"pagination-container"},[r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,"pager-count":5,limit:t.queryParams.pageSize,pageSizes:[10,20,30,40]},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1),r("div",{staticStyle:{width:"100%"}},[r("el-form",{ref:"allotForm",attrs:{"label-position":"top",model:t.allotForm,rules:t.allotRules}},[r("div",{staticStyle:{width:"45%",margin:"60px 0"}},[r("el-form-item",{attrs:{label:"目标机构",prop:"deptId"}},[r("treeselect",{attrs:{options:t.deptOptions,"show-count":!0,placeholder:"请选择目标机构",appendToBody:!0,"z-index":"9000"},model:{value:t.allotForm.deptId,callback:function(e){t.$set(t.allotForm,"deptId",e)},expression:"allotForm.deptId"}})],1)],1)])],1)],1),r("div",{staticClass:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirmDistribution}},[t._v("确定分配")])],1)],1)},a=[],o=(r("99af"),r("4de4"),r("a15b"),r("d81d"),r("14d9"),r("4e82"),r("e9c4"),r("b64b"),r("d3b7"),r("159b"),r("ca17")),i=r.n(o),u=(r("542c"),r("c0c7")),c=r("584f"),l=r("9b9c"),s={components:{Treeselect:i.a},data:function(){return{total:0,productList:[],queryParams:{productId:null,deviceName:"",pageNum:1,pageSize:10,productName:null,serialNumber:null,showChild:!1},count:0,allotForm:{productId:0,deptId:0},deviceIds:{},selectedCount:0,deptOptions:[],selectedRow:null,add:!0,del:!0,leftTableData:[],rightTableData:[],selectedListLeft:[],selectedListRight:[],menuTableData:[],allotRules:{deptId:[{required:!0,message:"目标机构不能为空",trigger:"change"}]}}},created:function(){this.getProductList(),this.getDeptTree(),this.getList()},computed:{selectedCount1:function(){return this.rightTableData.length}},watch:{selectedListLeft:function(t){t.length?this.add=!1:this.add=!0},selectedListRight:function(t){t.length?this.del=!1:this.del=!0}},methods:{getList:function(){var t=this;this.loading=!0,Object(c["l"])(this.queryParams).then((function(e){t.menuTableData=e.rows,t.menuTableData.map((function(e){t.leftTableData.push(Object.assign(e,{isSelect:0}))})),0!=t.rightTableData.length&&t.menuTableData.forEach((function(e,r){t.rightTableData.forEach((function(t){t.deviceId==e.deviceId&&(e.isSelect=1)}))})),t.total=e.total,0===t.count&&(t.count=t.total),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.rightTableData=[],this.queryParams.productId=null,this.resetForm("queryForm"),this.handleQuery()},getProductList:function(){var t=this;this.loading=!0;var e={pageSize:999};Object(l["f"])(e).then((function(e){t.productList=e.rows.map((function(t){return{value:t.productId,label:t.productName}}))}))},getDeptTree:function(){var t=this;this.allotForm.deptId=null,Object(u["d"])().then((function(e){t.deptOptions=e.data}))},goBack:function(){var t={path:"/iot/device"};this.$tab.closeOpenPage(t)},rightAdd:function(){var t=this,e=JSON.parse(JSON.stringify(this.menuTableData));e.forEach((function(e,r){t.selectedListLeft.forEach((function(r){e.deviceId==r.deviceId&&(t.rightTableData=t.rightTableData.concat(e).sort((function(t,e){return t.deviceId-e.deviceId})),e.isSelect=1)}))})),0!=this.selectedCount1&&(this.count=this.count-this.selectedListLeft.length),e=e.filter((function(t){return t})),this.menuTableData=e,this.selectedListLeft=[]},leftDelete:function(){var t=this,e=JSON.parse(JSON.stringify(this.rightTableData));e.forEach((function(r,n){t.selectedListRight.forEach((function(a){t.menuTableData.forEach((function(t){t.deviceId==a.deviceId&&(t.isSelect=0),a.deviceId==r.deviceId&&delete e[n]}))}))})),0!=this.selectedCount1&&(this.count=this.count+this.selectedListRight.length),e=e.filter((function(t){return t})),this.rightTableData=e,this.selectedListRight=[]},checkSelectable:function(t){var e=!0;return e=0===t.isSelect,e},changeCheckBoxValueLeft:function(t){this.selectedListLeft=t,this.selectedCount=t.length},changeCheckBoxValueRight:function(t){this.selectedListRight=t},confirmDistribution:function(){var t=this;this.$refs["allotForm"].validate((function(e){if(e){t.deviceIds=t.rightTableData.map((function(t){return t.deviceId}));var r=t.deviceIds.join(","),n=t.allotForm.deptId;Object(c["d"])(n,r).then((function(e){200==e.code?(t.$modal.msgSuccess(e.msg),t.resetQuery()):t.$modal.msgError(e.msg)}))}}))}}},d=s,f=(r("31f1"),r("2877")),h=Object(f["a"])(d,n,a,!1,null,null,null);e["default"]=h.exports},ed2e:function(t,e,r){}}]);