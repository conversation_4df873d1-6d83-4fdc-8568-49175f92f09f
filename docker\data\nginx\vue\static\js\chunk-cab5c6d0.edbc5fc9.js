(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-cab5c6d0"],{"6cf3":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container"},[a("el-row",[a("el-col",{attrs:{xs:24}},[a("div",{staticClass:"content"},[a("div",{staticStyle:{color:"#fff","background-color":"#0f73ee",width:"100%",height:"200px","text-align":"center",padding:"15px","font-family":"'微软雅黑'"}},[a("div",{staticStyle:{"font-size":"42px","padding-top":"40px",width:"300px",margin:"0 auto"}},[a("img",{staticStyle:{width:"100px",height:"100px",float:"left"},attrs:{src:t.logo,alt:"logo"}}),a("div",{staticStyle:{float:"left","margin-top":"13px",width:"200px","text-align":"left"}},[a("div",[t._v("FastBee")]),a("div",{staticStyle:{"letter-spacing":"1.5px","font-size":"20px","font-weight":"600","margin-top":"-8px","margin-left":"3px"}},[t._v(" 开源物联网平台")])])])]),a("div",{staticClass:"form-cont"},[a("el-tabs",{staticClass:"form",staticStyle:{float:"none"},attrs:{value:"uname"}},[a("el-tab-pane",{attrs:{label:"三方授权（"+t.client.name+")",name:"uname"}})],1),a("div",[a("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:t.loginForm}},[t._v(" 此第三方应用请求获得以下权限： "),a("el-form-item",{attrs:{prop:"scopes"}},[a("el-checkbox-group",{model:{value:t.loginForm.scopes,callback:function(e){t.$set(t.loginForm,"scopes",e)},expression:"loginForm.scopes"}},t._l(t.params.scopes,(function(e){return a("el-checkbox",{key:e,staticStyle:{display:"block","margin-bottom":"-10px"},attrs:{label:e}},[t._v(t._s(t.formatScope(e)))])})),1)],1),a("el-form-item",{staticStyle:{width:"100%"}},[a("el-button",{staticStyle:{width:"50%"},attrs:{loading:t.loading,size:"medium",type:"primary"},nativeOn:{click:function(e){return e.preventDefault(),t.handleAuthorize(!0)}}},[t.loading?a("span",[t._v("授 权 中...")]):a("span",[t._v("同意授权")])]),a("el-button",{staticStyle:{width:"36%"},attrs:{size:"medium"},nativeOn:{click:function(e){return e.preventDefault(),t.handleAuthorize(!1)}}},[t._v("拒绝")])],1)],1)],1)],1)])])],1),t._m(0)],1)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"el-login-footer"},[a("span",[t._v(" Copyright © 2024 "),a("a",{attrs:{target:"_blank",href:"http://fastbee.cn"}},[t._v("FastBee")]),t._v(" All Rights Reserved. ")])])}],o=a("b85c"),r=(a("4de4"),a("14d9"),a("d3b7"),a("7ded")),n=a("4309"),c=a.n(n),l={name:"Login",data:function(){return{logo:c.a,loginForm:{scopes:[]},params:{responseType:void 0,clientId:void 0,redirectUri:void 0,state:void 0,scopes:[]},client:{name:"",logo:""},loading:!1}},created:function(){this.params.responseType=this.$route.query.responseType,this.params.clientId=this.$route.query.clientId,this.params.redirectUri=this.$route.query.redirectUri,this.params.state=this.$route.query.state,this.$route.query.scope&&(this.params.scopes=this.$route.query.scope.split(" ")),this.params.scopes.length>0&&this.doAuthorize(!0,this.params.scopes,[]).then((function(t){var e=t.data;e?location.href=e:console.log("自动授权未通过！")})),this.getAuthorize()},methods:{getAuthorize:function(){var t=this;Object(r["e"])(this.params.clientId).then((function(e){var a;if(t.client=e.data.client,t.params.scopes.length>0){a=[];var s,i=Object(o["a"])(e.data.scopes);try{for(i.s();!(s=i.n()).done;){var r=s.value;t.params.scopes.indexOf(r.key)>=0&&a.push(r)}}catch(f){i.e(f)}finally{i.f()}}else{a=e.data.scopes;var n,c=Object(o["a"])(a);try{for(c.s();!(n=c.n()).done;){var l=n.value;t.params.scopes.push(l.key)}}catch(f){c.e(f)}finally{c.f()}}var p,u=Object(o["a"])(a);try{for(u.s();!(p=u.n()).done;){var d=p.value;d.value&&t.loginForm.scopes.push(d.key)}}catch(f){u.e(f)}finally{u.f()}}))},handleAuthorize:function(t){var e=this;this.$refs.loginForm.validate((function(a){var s,i;a&&(e.loading=!0,t?(s=e.loginForm.scopes,i=e.params.scopes.filter((function(t){return-1===s.indexOf(t)}))):(s=[],i=e.params.scopes),e.doAuthorize(!1,s,i).then((function(t){var e=t.data;e&&(location.href=e)})).finally((function(){e.loading=!1})))}))},doAuthorize:function(t,e,a){return Object(r["a"])(this.params.responseType,this.params.clientId,this.params.redirectUri,this.params.state,t,e,a)},formatScope:function(t){switch(t){case"user.read":return"访问你的个人信息";case"user.write":return"修改你的个人信息";default:return t}}}},p=l,u=(a("a58e"),a("2877")),d=Object(u["a"])(p,s,i,!1,null,"f1641f4c",null);e["default"]=d.exports},a58e:function(t,e,a){"use strict";a("d086")},d086:function(t,e,a){}}]);