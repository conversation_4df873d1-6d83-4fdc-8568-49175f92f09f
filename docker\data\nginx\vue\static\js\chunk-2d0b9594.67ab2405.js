(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b9594"],{3320:function(t,s,e){"use strict";e.r(s);var i=function(){var t=this,s=t.$createElement,e=t._self._c||s;return e("transition",{attrs:{name:"yh-setting-fade"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.settingShow,expression:"settingShow"}],staticClass:"setting",class:{settingShow:t.settingShow}},[e("div",{staticClass:"setting_dislog",on:{click:function(s){t.settingShow=!1}}}),e("div",{staticClass:"setting_inner"},[e("div",{staticClass:"setting_header"},[t._v(" 设置 ")]),e("div",{staticClass:"setting_body"},[e("div",{staticClass:"left_shu"},[t._v(" 全局设置")]),e("div",{staticClass:"setting_item"},[e("span",{staticClass:"setting_label"},[t._v(" 是否进行自动适配"),e("span",{staticClass:"setting_label_tip"},[t._v("(默认分辨率1920*1080)")]),t._v(": ")]),e("div",{staticClass:"setting_content"},[e("el-radio-group",{on:{change:function(s){return t.radiochange(s,"isScale")}},model:{value:t.isScaleradio,callback:function(s){t.isScaleradio=s},expression:"isScaleradio"}},[e("el-radio",{attrs:{label:!0}},[t._v("是")]),e("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1)]),e("div",{staticClass:"left_shu"},[t._v(" 实时监测")]),e("div",{staticClass:"setting_item"},[e("span",{staticClass:"setting_label"},[t._v(" 设备提醒自动轮询: "),e("span",{staticClass:"setting_label_tip"})]),e("div",{staticClass:"setting_content"},[e("el-radio-group",{on:{change:function(s){return t.radiochange(s,"sbtxSwiper")}},model:{value:t.sbtxradio,callback:function(s){t.sbtxradio=s},expression:"sbtxradio"}},[e("el-radio",{attrs:{label:!0}},[t._v("是")]),e("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1)]),e("div",{staticClass:"setting_item"},[e("span",{staticClass:"setting_label"},[t._v(" 实时预警轮播: ")]),e("div",{staticClass:"setting_content"},[e("el-radio-group",{on:{change:function(s){return t.radiochange(s,"ssyjSwiper")}},model:{value:t.ssyjradio,callback:function(s){t.ssyjradio=s},expression:"ssyjradio"}},[e("el-radio",{attrs:{label:!0}},[t._v("是")]),e("el-radio",{attrs:{label:!1}},[t._v("否")])],1)],1)]),e("div",{staticClass:"flex justify-center"})])])])])},a=[],n={components:{},data:function(){return{settingShow:!1,sbtxradio:!0,ssyjradio:!0,isScaleradio:!0}},computed:{},methods:{init:function(){this.settingShow=!0},radiochange:function(t,s){this.$store.commit("setting/updateSwiper",{val:t,type:s}),"isScale"===s&&this.$router.go(0)}},created:function(){},mounted:function(){document.body.appendChild(this.$el)},beforeDestroy:function(){},destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)}},o=n,l=e("2877"),c=Object(l["a"])(o,i,a,!1,null,"1fcee248",null);s["default"]=c.exports}}]);