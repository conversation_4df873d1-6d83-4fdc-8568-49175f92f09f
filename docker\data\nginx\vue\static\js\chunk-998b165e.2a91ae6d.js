(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-998b165e"],{"584f":function(e,t,i){"use strict";i.d(t,"k",(function(){return r})),i.d(t,"n",(function(){return n})),i.d(t,"l",(function(){return l})),i.d(t,"m",(function(){return o})),i.d(t,"j",(function(){return s})),i.d(t,"e",(function(){return c})),i.d(t,"c",(function(){return u})),i.d(t,"f",(function(){return d})),i.d(t,"h",(function(){return m})),i.d(t,"g",(function(){return p})),i.d(t,"a",(function(){return v})),i.d(t,"o",(function(){return h})),i.d(t,"b",(function(){return f})),i.d(t,"d",(function(){return b})),i.d(t,"i",(function(){return y}));var a=i("b775");function r(e){return Object(a["a"])({url:"/iot/device/list",method:"get",params:e})}function n(e){return Object(a["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/iot/device/shortList",method:"get",params:e})}function s(){return Object(a["a"])({url:"/iot/device/all",method:"get"})}function c(e){return Object(a["a"])({url:"/iot/device/"+e,method:"get"})}function u(e){return Object(a["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(a["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(a["a"])({url:"/iot/device/statistic",method:"get"})}function p(e){return Object(a["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function v(e){return Object(a["a"])({url:"/iot/device",method:"post",data:e})}function h(e){return Object(a["a"])({url:"/iot/device",method:"put",data:e})}function f(e){return Object(a["a"])({url:"/iot/device/"+e,method:"delete"})}function b(){return Object(a["a"])({url:"/iot/device/generator",method:"get"})}function y(e){return Object(a["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},a67d:function(e,t,i){"use strict";i("eab6")},ddac:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"padding-left":"20px"}},[i("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[i("el-input",{attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),i("el-form-item",{attrs:{label:"授权码",prop:"authorizeCode"}},[i("el-input",{attrs:{placeholder:"请输入授权码",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.authorizeCode,callback:function(t){e.$set(e.queryParams,"authorizeCode",t)},expression:"queryParams.authorizeCode"}})],1),i("el-form-item",{attrs:{label:"状态",prop:"status"}},[i("el-select",{attrs:{placeholder:"请选择状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_auth_status,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:add"],expression:"['iot:authorize:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("生成授权码")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:remove"],expression:"['iot:authorize:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("批量删除")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:export"],expression:"['iot:authorize:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),i("el-col",{attrs:{span:1.5}},[i("el-link",{staticStyle:{"padding-top":"5px"},attrs:{type:"info",underline:!1}},[e._v("Tips：双击可以复制授权码。")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.authorizeList,size:"small"},on:{"selection-change":e.handleSelectionChange,"cell-dblclick":e.celldblclick}},[i("el-table-column",{attrs:{type:"selection",selectable:e.selectable,width:"55",align:"center"}}),i("el-table-column",{attrs:{label:"授权码",width:"320",align:"center",prop:"authorizeCode"}}),i("el-table-column",{attrs:{label:"状态",align:"center",prop:"active",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_auth_status,value:t.row.status}})]}}])}),i("el-table-column",{attrs:{label:"设备编号",width:"150",align:"center",prop:"serialNumber"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(i){return e.getDeviceBySerialNumber(t.row.serialNumber)}}},[e._v(e._s(t.row.serialNumber))])]}}])}),i("el-table-column",{attrs:{label:"授权时间",align:"center",prop:"updateTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.updateTime,"{y}-{m}-{d} {h}:{m}:{s}")))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[1!=t.row.status||t.row.deviceId?e._e():i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:edit"],expression:"['iot:authorize:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-s-check"},on:{click:function(i){return e.handleUpdate(t.row,"auth")}}},[e._v("设备授权")]),i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:edit"],expression:"['iot:authorize:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-notebook-1"},on:{click:function(i){return e.handleUpdate(t.row,"remark")}}},[e._v("备注")]),t.row.deviceId?e._e():i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:authorize:remove"],expression:"['iot:authorize:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("el-dialog",{attrs:{title:e.title,visible:e.open,width:e.editWidth,"append-to-body":""},on:{"update:visible":function(t){e.open=t}}},["auth"==e.editType?i("div",[i("div",{staticClass:"el-divider el-divider--horizontal",staticStyle:{"margin-top":"-25px"}}),i("el-form",{ref:"queryDeviceForm",attrs:{model:e.deviceParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[i("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.deviceParams.deviceName,callback:function(t){e.$set(e.deviceParams,"deviceName",t)},expression:"deviceParams.deviceName"}})],1),i("el-form-item",{staticStyle:{margin:"0 30px"},attrs:{label:"设备编号",prop:"serialNumber"}},[i("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.deviceParams.serialNumber,callback:function(t){e.$set(e.deviceParams,"serialNumber",t)},expression:"deviceParams.serialNumber"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleDeviceQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetDeviceQuery}},[e._v("重置")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.deviceLoading,expression:"deviceLoading"}],ref:"singleTable",attrs:{data:e.deviceList,size:"mini","highlight-current-row":""},on:{"row-click":e.rowClick}},[i("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("input",{attrs:{type:"radio",name:"device"},domProps:{checked:e.row.isSelect}})]}}],null,!1,1388052008)}),i("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),i("el-table-column",{attrs:{label:"设备ID",align:"center",prop:"deviceId"}}),i("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),i("el-table-column",{attrs:{label:"用户名称",align:"center",prop:"userName"}}),i("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}],null,!1,2431977129)})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.deviceTotal>0,expression:"deviceTotal>0"}],attrs:{total:e.deviceTotal,page:e.deviceParams.pageNum,limit:e.deviceParams.pageSize},on:{"update:page":function(t){return e.$set(e.deviceParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.deviceParams,"pageSize",t)},pagination:e.getDeviceList}})],1):e._e(),"remark"==e.editType?i("div",[i("el-input",{attrs:{type:"textarea",rows:"4",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1):e._e(),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),i("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)]),i("el-dialog",{attrs:{title:"设备详情",visible:e.openDevice,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openDevice=t}}},[null==e.device?i("div",{staticStyle:{"text-align":"center"}},[i("i",{staticClass:"el-icon-warning",staticStyle:{color:"#E6A23C"}}),e._v(" 提示：查找不到设备，可能已经被删除")]):e._e(),null!=e.device?i("el-descriptions",{attrs:{border:"",column:2,size:"medium"}},[i("el-descriptions-item",{attrs:{label:"设备ID"}},[e._v(e._s(e.device.deviceId))]),i("el-descriptions-item",{attrs:{label:"设备名称"}},[e._v(e._s(e.device.deviceName))]),i("el-descriptions-item",{attrs:{label:"设备编号"}},[e._v(e._s(e.device.serialNumber))]),i("el-descriptions-item",{attrs:{label:"设备状态"}},[1==e.device.status?i("el-tag",{attrs:{type:"warning"}},[e._v("未激活")]):2==e.device.status?i("el-tag",{attrs:{type:"danger"}},[e._v("禁用")]):3==e.device.status?i("el-tag",{attrs:{type:"success"}},[e._v("在线")]):4==e.device.status?i("el-tag",{attrs:{type:"info"}},[e._v("离线")]):e._e()],1),i("el-descriptions-item",{attrs:{label:"设备影子"}},[1==e.device.isShadow?i("el-tag",{attrs:{type:"success"}},[e._v("启用")]):i("el-tag",{attrs:{type:"info"}},[e._v("未启用")])],1),i("el-descriptions-item",{attrs:{label:"定位方式"}},[1==e.device.locationWay?i("el-tag",{attrs:{type:"success"}},[e._v("自动定位")]):2==e.device.locationWay?i("el-tag",{attrs:{type:"warning"}},[e._v("设备定位")]):3==e.device.locationWay?i("el-tag",{attrs:{type:"primary"}},[e._v("自定义位置")]):e._e()],1),i("el-descriptions-item",{attrs:{label:"产品名称"}},[e._v(e._s(e.device.productName))]),i("el-descriptions-item",{attrs:{label:"租户名称"}},[e._v(e._s(e.device.userName))]),i("el-descriptions-item",{attrs:{label:"固件版本"}},[e._v("Version "+e._s(e.device.firmwareVersion))]),i("el-descriptions-item",{attrs:{label:"所在地址"}},[e._v(e._s(e.device.networkAddress))]),i("el-descriptions-item",{attrs:{label:"设备经度"}},[e._v(e._s(e.device.longitude))]),i("el-descriptions-item",{attrs:{label:"设备纬度"}},[e._v(e._s(e.device.latitude))]),i("el-descriptions-item",{attrs:{label:"入网IP"}},[e._v(e._s(e.device.networkIp))]),i("el-descriptions-item",{attrs:{label:"设备信号"}},[e._v(e._s(e.device.rssi))]),i("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.device.createTime))]),i("el-descriptions-item",{attrs:{label:"激活时间"}},[e._v(e._s(e.device.activeTime))]),i("el-descriptions-item",{attrs:{label:"备注信息"}},[e._v(e._s(e.device.remark))])],1):e._e(),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.goToEditDevice(e.device.deviceId)}}},[e._v("查看设备")]),i("el-button",{on:{click:e.closeDevice}},[e._v("关 闭")])],1)],1)],1)},r=[],n=i("5530"),l=i("ade3"),o=(i("14d9"),i("d81d"),i("584f")),s=i("b775");function c(e){return Object(s["a"])({url:"/iot/authorize/list",method:"get",params:e})}function u(e){return Object(s["a"])({url:"/iot/authorize/"+e,method:"get"})}function d(e){return Object(s["a"])({url:"/iot/authorize/addProductAuthorizeByNum",method:"post",data:e})}function m(e){return Object(s["a"])({url:"/iot/authorize",method:"put",data:e})}function p(e){return Object(s["a"])({url:"/iot/authorize/"+e,method:"delete"})}var v={name:"product-authorize",dicts:["iot_auth_status","iot_device_status"],props:{product:{type:Object,default:null}},watch:{product:function(e,t){this.productInfo=e,this.productInfo&&0!=this.productInfo.productId&&(this.queryParams.productId=this.productInfo.productId,this.deviceParams.productId=this.productInfo.productId,this.getList(),this.getDeviceList())}},data:function(){var e;return{device:{},openDevice:!1,deviceLoading:!0,deviceTotal:0,deviceList:[],deviceParams:(e={pageNum:1,pageSize:10,userId:null,deviceName:null,productId:0,productName:null},Object(l["a"])(e,"userId",null),Object(l["a"])(e,"userName",null),Object(l["a"])(e,"tenantId",null),Object(l["a"])(e,"tenantName",null),Object(l["a"])(e,"serialNumber",null),Object(l["a"])(e,"status",null),Object(l["a"])(e,"networkAddress",null),Object(l["a"])(e,"activeTime",null),e),editType:"",editWidth:"500px",loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,authorizeList:[],title:"",open:!1,createNum:10,queryParams:{pageNum:1,pageSize:10,authorizeCode:null,productId:null,deviceId:null,serialNumber:null,userId:null,userName:null,status:null},form:{},productInfo:{}}},created:function(){},methods:{getDeviceBySerialNumber:function(e){var t=this;this.openDevice=!0,Object(o["f"])(e).then((function(e){t.device=e.data}))},goToEditDevice:function(e){this.openDevice=!1,this.$router.push({path:"/iot/device-edit",query:{deviceId:e}})},getDeviceList:function(){var e=this;this.deviceLoading=!0,this.deviceParams.params={},Object(o["n"])(this.deviceParams).then((function(t){for(var i=0;i<t.rows.length;i++)t.rows[i].isSelect=!1;e.deviceList=t.rows,e.deviceTotal=t.total,e.deviceLoading=!1}))},handleDeviceQuery:function(){this.deviceParams.pageNum=1,this.getDeviceList()},resetDeviceQuery:function(){this.resetForm("queryDeviceForm"),this.handleDeviceQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.deviceId),this.form.userId=e.userId,this.form.userName=e.userName,this.form.deviceId=e.deviceId,this.form.serialNumber=e.serialNumber)},setRadioSelected:function(e){for(var t=0;t<this.deviceList.length;t++){var i=this.deviceList[t];this.deviceList[t].deviceId==e?(i.isSelect=!0,this.$set(this.deviceList,t,i)):(i.isSelect=!1,this.$set(this.deviceList,t,i))}},getList:function(){var e=this;this.loading=!0,c(this.queryParams).then((function(t){e.authorizeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},closeDevice:function(){this.openDevice=!1},reset:function(){this.form={authorizeId:null,authorizeCode:null,productId:"",userId:"",deviceId:null,serialNumber:null,userName:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.device={},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.authorizeId})),this.multiple=!e.length},handleAdd:function(){var e=this;this.$prompt("","输入授权码数量",{customClass:"createNum",confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/[0-9\-]/,inputErrorMessage:"数量内容不正确",inputType:"number",inputValue:this.createNum}).then((function(t){var i=t.value;if(e.createNum=i,null!=e.queryParams.productId){var a={productId:e.queryParams.productId,createNum:e.createNum};d(a).then((function(t){e.$modal.msgSuccess("新增授权码成功"),e.getList(),e.createNum=10}))}})).catch((function(){e.$message({type:"info",message:"取消新增"})}))},handleUpdate:function(e,t){var i=this;this.reset(),this.editType=t;var a=e.authorizeId||this.ids;u(a).then((function(e){i.form=e.data,i.open=!0,"auth"==i.editType?(i.title="选择设备",i.editWidth="800px"):(i.title="备注信息",i.editWidth="500px");for(var t=0;t<i.deviceList.length;t++){var a=i.deviceList[t];a.isSelect=!1,i.$set(i.deviceList,t,a)}}))},submitForm:function(){var e=this;"auth"==this.editType?null!=this.form.deviceId&&0!=this.form.deviceId?m(this.form).then((function(t){e.$modal.msgSuccess("设备授权成功"),e.open=!1,e.getList()})):this.$modal.msg("请选择要授权的设备"):null!=this.form.authorizeId&&m(this.form).then((function(t){e.$modal.msgSuccess("备注成功"),e.open=!1,e.getList()}))},handleDelete:function(e){var t=this,i=e.authorizeId||this.ids;this.$modal.confirm('是否确认删除产品授权码编号为"'+i+'"的数据项？').then((function(){return p(i)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/authorize/export",Object(n["a"])({},this.queryParams),"authorize_".concat((new Date).getTime(),".xlsx"))},selectable:function(e){return null==e.deviceId},celldblclick:function(e,t,i,a){var r=this;this.$copyText(e[t.property]).then((function(e){r.onCopy()}),(function(e){this.onError()}))},onCopy:function(){this.$notify({title:"成功",message:"复制成功！",type:"success",offset:50,duration:2e3})},onError:function(){this.$notify({title:"失败",message:"复制失败！",type:"error",offset:50,duration:2e3})}}},h=v,f=(i("a67d"),i("2877")),b=Object(f["a"])(h,a,r,!1,null,null,null);t["default"]=b.exports},eab6:function(e,t,i){}}]);