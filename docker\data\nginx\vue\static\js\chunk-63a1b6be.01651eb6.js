(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63a1b6be"],{"5b52":function(t,e,n){"use strict";n.d(e,"h",(function(){return a})),n.d(e,"c",(function(){return u})),n.d(e,"i",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"e",(function(){return c})),n.d(e,"g",(function(){return s})),n.d(e,"b",(function(){return l})),n.d(e,"f",(function(){return d})),n.d(e,"d",(function(){return p}));var r=n("b775");function a(t){return Object(r["a"])({url:"/sub/gateway/list",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/sub/gateway/"+t,method:"delete"})}function i(t){return Object(r["a"])({url:"/sub/gateway/subDevice",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/sub/gateway/addBatch",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/sub/gateway/editBatch",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/productModbus/gateway/list",method:"get",params:t})}function l(t){return Object(r["a"])({url:"productModbus/gateway/addBatch",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/productModbus/gateway/editBatch",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/productModbus/gateway/"+t,method:"delete"})}},"9b9c":function(t,e,n){"use strict";n.d(e,"g",(function(){return a})),n.d(e,"h",(function(){return u})),n.d(e,"f",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"i",(function(){return c})),n.d(e,"e",(function(){return s})),n.d(e,"b",(function(){return l})),n.d(e,"d",(function(){return d})),n.d(e,"c",(function(){return p}));var r=n("b775");function a(t){return Object(r["a"])({url:"/iot/product/list",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/iot/product/shortList",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/iot/product/"+t,method:"get"})}function o(t){return Object(r["a"])({url:"/iot/product",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/iot/product",method:"put",data:t})}function s(t){return Object(r["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function l(t){return Object(r["a"])({url:"/iot/product/status",method:"put",data:t})}function d(t){return Object(r["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(r["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}},b77d:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{title:t.$t("scene.index.670805-36"),visible:t.openDeviceList,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.openDeviceList=e}}},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"48px"},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-form-item",{attrs:{prop:"productName"}},[n("el-input",{attrs:{size:"small",placeholder:"请输入子产品名称",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))])],1)],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.gatewayList,size:"small",border:!1},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"ID",align:"left",prop:"productId",width:"120"}}),n("el-table-column",{attrs:{label:t.$t("device.device-edit.148398-1"),align:"left",prop:"productName"}})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:t.handleDeviceSelected}},[t._v(t._s(t.$t("confirm")))]),n("el-button",{on:{click:t.closeSelectDeviceList}},[t._v(t._s(t.$t("cancel")))])],1)],1)},a=[],u=(n("d81d"),n("5b52")),i=n("9b9c"),o={name:"sub-product-list",props:{gateway:{type:Object,default:null}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,gatewayList:[],title:"",open:!1,openDeviceList:!1,queryParams:{pageNum:1,pageSize:10,productName:null,deviceType:4}}},created:function(){},watch:{gateway:{handler:function(){this.queryParams.pageNum=1,this.getList()},immediate:!0}},methods:{getList:function(){var t=this;this.loading=!0,Object(i["g"])(this.queryParams).then((function(e){t.gatewayList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.resetForm("queryForm")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.reset(),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.productId})),this.single=1!==t.length,this.multiple=!t.length},closeSelectDeviceList:function(){this.openDeviceList=!1},handleDeviceSelected:function(){var t=this;this.gateway.subProductIds=this.ids,Object(u["b"])(this.gateway).then((function(e){t.$modal.msgSuccess(t.$t("device.sub-device-list.323213-4")),t.openDeviceList=!1,t.$emit("addSuccess")}))}}},c=o,s=n("2877"),l=Object(s["a"])(c,r,a,!1,null,null,null);e["default"]=l.exports}}]);