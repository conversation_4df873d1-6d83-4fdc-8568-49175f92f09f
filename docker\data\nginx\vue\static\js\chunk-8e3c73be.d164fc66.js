(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8e3c73be","chunk-18563c98","chunk-2b4b6c04","chunk-6b984810","chunk-2eb20b18","chunk-03fb653e","chunk-4145a4bd","chunk-e58acb58","chunk-722c5e57","chunk-e0347614","chunk-46e6d1bb","chunk-2d0d6012","chunk-2d229411","chunk-2d0a3715"],{"01ca":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"g",(function(){return s})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"i",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"b",(function(){return u})),a.d(t,"h",(function(){return m}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/model/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/model/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/model/permList/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/model",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/model/import",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/iot/model",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/model/"+e,method:"delete"})}function u(e){return Object(i["a"])({url:"/iot/model/cache/"+e,method:"get"})}function m(e){return Object(i["a"])({url:"/iot/model/synchron",method:"post",data:e})}},"09cb":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("d3b7");function i(){return new Promise((function(e,t){if("undefined"!==typeof BMap)return e(BMap),!0;window.onBMapCallback=function(){e(BMap)};var a=document.location.protocol;if("https:"==a){var i=document.createElement("meta");i.httpEquiv="Content-Security-Policy",i.content="upgrade-insecure-requests",i.onerror=t,document.head.appendChild(i)}var n=document.createElement("script");n.type="text/javascript",n.src="http://api.map.baidu.com/api?v=2.0&ak=nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD&s=1&__ec_v__=20190126&callback=onBMapCallback",n.onerror=t,document.head.appendChild(n)}))}},"0bc2":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/runtime/service/invoke",method:"post",data:e})}function r(e){return Object(i["a"])({url:"/iot/runtime/funcLog",method:"get",params:e})}},"1c4f":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[e.isSubDev?a("el-form-item",{attrs:{label:"请选择设备从机:","label-width":"120px"}},[a("el-select",{attrs:{placeholder:"请选择设备从机"},on:{change:e.selectSlave},model:{value:e.queryParams.slaveId,callback:function(t){e.$set(e.queryParams,"slaveId",t)},expression:"queryParams.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+"   (从机地址:"+e.slaveId+")",value:e.slaveId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"日志类型",prop:"funType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.funType,callback:function(t){e.$set(e.queryParams,"funType",t)},expression:"queryParams.funType"}},e._l(e.dict.type.iot_function_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identify"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identify,callback:function(t){e.$set(e.queryParams,"identify",t)},expression:"queryParams.identify"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:e.showName,align:"center",prop:"identify"}}),a("el-table-column",{attrs:{label:"指令类型",align:"center",prop:"funType",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_function_type,value:t.row.funType}})]}}])}),a("el-table-column",{attrs:{label:"设置值",align:"center",prop:"funValue"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),a("el-table-column",{attrs:{label:"下发时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"下发结果描述",align:"center",prop:"resultMsg"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},n=[],r=a("5530"),s=(a("d81d"),a("dc9c")),o=(a("01ca"),{name:"device-func",dicts:["iot_function_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.isSubDev=this.deviceInfo.subDeviceList&&this.deviceInfo.subDeviceList.length>0,this.showName=this.isSubDev?"寄存器地址":"标识符",this.queryParams.deviceId=this.deviceInfo.deviceId,this.queryParams.slaveId=this.deviceInfo.slaveId,this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.slaveList=e.subDeviceList,this.getList())}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,logList:[],title:"",open:!1,deviceInfo:{},daterangeTime:[],queryParams:{pageNum:1,pageSize:10,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,slaveId:null},form:{},isSubDev:!1,showName:null,slaveList:[],rules:{identify:[{required:!0,message:"标识符不能为空",trigger:"blur"}],funType:[{required:!0,message:"1==服务下发，2=属性获取，3.OTA升级不能为空",trigger:"change"}],funValue:[{required:!0,message:"日志值不能为空",trigger:"blur"}],serialNumber:[{required:!0,message:"设备编号不能为空",trigger:"blur"}]}}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]),this.queryParams.slaveId&&(this.queryParams.serialNumber=this.queryParams.serialNumber+"_"+this.queryParams.slaveId),Object(s["a"])(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,createBy:null,createTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleExport:function(){this.download("iot/log/export",Object(r["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))},selectSlave:function(){}}}),l=o,c=a("2877"),d=Object(c["a"])(l,i,n,!1,null,null,null);t["default"]=d.exports},2544:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"f",(function(){return s})),a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"g",(function(){return c})),a.d(t,"c",(function(){return d}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/deviceUser/list",method:"get",params:e})}function r(e,t){return Object(i["a"])({url:"/iot/deviceUser/"+e+"/"+t,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/deviceUser/shareUser",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/deviceUser",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/deviceUser/addDeviceUsers",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/iot/deviceUser",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/deviceUser",method:"delete",data:e})}},"38da":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"g",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return c})),a.d(t,"e",(function(){return d}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/temp/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/temp/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/temp",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/temp",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/iot/temp/"+e,method:"delete"})}function c(e){return Object(i["a"])({url:"/iot/temp/getTemp",method:"get",params:e})}function d(e){return Object(i["a"])({url:"/iot/temp/getTempByPid",method:"get",params:e})}},"466d":function(e,t,a){"use strict";var i=a("c65b"),n=a("d784"),r=a("825a"),s=a("7234"),o=a("50c4"),l=a("577e"),c=a("1d80"),d=a("dc4a"),u=a("8aa5"),m=a("14c3");n("match",(function(e,t,a){return[function(t){var a=c(this),n=s(t)?void 0:d(t,e);return n?i(n,t,a):new RegExp(t)[e](l(a))},function(e){var i=r(this),n=l(e),s=a(t,i,n);if(s.done)return s.value;if(!i.global)return m(i,n);var c=i.unicode;i.lastIndex=0;var d,p=[],h=0;while(null!==(d=m(i,n))){var f=l(d[0]);p[h]=f,""===f&&(i.lastIndex=u(n,o(i.lastIndex),c)),h++}return 0===h?null:p}]}))},"584f":function(e,t,a){"use strict";a.d(t,"k",(function(){return n})),a.d(t,"n",(function(){return r})),a.d(t,"l",(function(){return s})),a.d(t,"m",(function(){return o})),a.d(t,"j",(function(){return l})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"f",(function(){return u})),a.d(t,"h",(function(){return m})),a.d(t,"g",(function(){return p})),a.d(t,"a",(function(){return h})),a.d(t,"o",(function(){return f})),a.d(t,"b",(function(){return v})),a.d(t,"d",(function(){return y})),a.d(t,"i",(function(){return b}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function c(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function p(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function h(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function f(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function v(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function y(){return Object(i["a"])({url:"/iot/device/generator",method:"get"})}function b(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},"5daf":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"running-status H100"},[a("div",[a("el-tabs",{staticStyle:{flex:"1",height:"800px","margin-bottom":"5px"},attrs:{type:"border-card"},on:{"tab-click":e.handleClick},model:{value:e.thingsType,callback:function(t){e.thingsType=t},expression:"thingsType"}},[a("el-tab-pane",{attrs:{label:"属性上报",name:"prop"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},e._l(e.runningData,(function(t,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{xs:24,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticStyle:{padding:"0px",height:"90px"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[e._v(e._s(t.name)+"("+e._s(t.id)+")")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[e._v(e._s(t.value))]),t.datatype.unit?a("span",[e._v(e._s(t.datatype.unit||t.datatype.unitName))]):e._e()])]),a("div",[e._v("时间："+e._s(t.ts))])])],1)})),1)],1)],1),a("el-tab-pane",{attrs:{label:"服务下发",name:"function"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},[e._l(e.functionData,(function(t,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{":xs":17,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticClass:"elcard",staticStyle:{height:"90px"},attrs:{shadow:"hover"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[e._v(" "+e._s(t.name)+" ")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[e._v(e._s(t.value))]),t.datatype.unit?a("span",[e._v(e._s(t.datatype.unit))]):e._e(),a("el-button",{staticStyle:{float:"right","margin-right":"-5px",padding:"3px 5px"},attrs:{type:"primary",plain:"",icon:"el-icon-s-promotion",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.editFunc(t)}}},[e._v("发送")])],1)]),a("div",[a("span",[e._v("时间："+e._s(t.ts))])])])],1)})),a("el-col",{staticClass:"phone-main",attrs:{":xs":7,sm:12,md:12,lg:8,xl:6}},[a("div",{staticClass:"phone"},[a("div",{staticClass:"phone-container"},[a("div",{staticClass:"phone-title"},[e._v("设 备 指 令")]),a("div",{ref:"logContent",staticClass:"log-content"},[a("el-scrollbar",{ref:"scrollContent",staticStyle:{height:"100%"}},e._l(e.logList,(function(t,i){return a("ul",{key:i},[a("li",[a("a",{staticStyle:{float:"left","text-align":"left"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.createTime))]),a("div",{staticClass:"spa"},[a("span",{staticClass:"lable-s1"},[e._v("服务下发:")]),e._v(" "+e._s(t.modelName)+" ")])]),a("a",{staticStyle:{float:"right","text-align":"right"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.replyTime))]),a("div",{class:{fail:201==t.resultCode,wait:203==t.resultCode}},[a("span",{staticClass:"lable-s1"},[e._v("设备应答:")]),e._v(" "+e._s(t.showValue)+" ")])])])])})),0)],1)])])])],2),a("el-empty",{directives:[{name:"show",rawName:"v-show",value:0==e.runningData.length,expression:"runningData.length == 0"}],attrs:{description:"暂无数据"}})],1)],1),a("el-tab-pane",{attrs:{disabled:"",name:"slave"}},[a("span",{staticStyle:{"margin-left":"50px"},attrs:{slot:"label"},slot:"label"},[a("span",{ref:"statusTitle",staticStyle:{color:"#409eff","margin-right":"30px"}},[e._v(e._s(e.title))]),a("el-select",{attrs:{placeholder:"请选择设备从机",size:"mini"},on:{change:e.selectSlave},model:{value:e.params.slaveId,callback:function(t){e.$set(e.params,"slaveId",t)},expression:"params.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+"   ("+e.slaveId+")",value:e.slaveId}})})),1)],1)])],1)],1),a("el-dialog",{attrs:{title:"服务调用",visible:e.dialogValue,width:"30%"},on:{"update:visible":function(t){e.dialogValue=t}}},[a("el-form",{staticStyle:{height:"100%",padding:"0 20px"},attrs:{size:"mini"},model:{value:e.from,callback:function(t){e.from=t},expression:"from"}},[a("el-form-item",{attrs:{label:e.from.name,"label-width":"180px"}},["integer"==e.from.datatype.type||"decimal"==e.from.datatype.type||"string"==e.from.datatype.type?a("el-input",{staticStyle:{width:"50%"},attrs:{type:"number"},on:{input:function(t){return e.justicNumber()}},model:{value:e.showValue,callback:function(t){e.showValue=t},expression:"showValue"}}):e._e(),"enum"==e.from.datatype.type?a("el-select",{on:{change:function(t){return e.changeSelect()}},model:{value:e.showValue,callback:function(t){e.showValue=t},expression:"showValue"}},e._l(e.from.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1):e._e(),"bool"===e.from.datatype.type?a("el-switch",{attrs:{"active-value":"1","inactive-value":"0","inline-prompt":""},model:{value:e.showValue,callback:function(t){e.showValue=t},expression:"showValue"}}):e._e(),"integer"!=e.from.datatype.type&&"decimal"!=e.from.datatype.type||!e.from.datatype.type.unit||"un"==e.from.datatype.type.unit||"/"==e.from.datatype.type.unit?e._e():a("span",[e._v("（"+e._s(e.from.unit)+"）")]),"integer"==e.from.datatype.type||"decimal"==e.from.datatype.type?a("div",{staticClass:"range"},[e._v(" (数据范围:"+e._s("null"==e.from.datatype.max?"bool"==e.from.datatype.type?0:"":e.from.datatype.min)+" ~ "+e._s("null"==e.from.datatype.max?"bool"==e.from.datatype.type?1:"":e.from.datatype.max)+") ")]):e._e()],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogValue=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading,disabled:!e.canSend},on:{click:e.sendService}},[e._v("确认")])],1)],1)],1)},n=[],r=a("2909"),s=(a("14d9"),a("d3b7"),a("159b"),a("4de4"),a("b0c0"),a("0bc2")),o=(a("ed08"),a("a824"),a("584f")),l=(a("ba95"),"integer"),c="decimal",d="bool",u="enum",m={name:"realTime-status",props:{device:{type:Object,default:null}},data:function(){return{messageList:[],simulateForm:{},deviceInfo:{},dialogValue:!1,gridData:[],groupId:1,treeData:[],runningData:[],functionData:[],loading:!1,debounceGetRuntime:"",runtimeName:"",serialNumber:"",params:{serialNumber:void 0,type:1},slaveList:[],queryParams:{},thingsType:"prop",opationList:[],funVal:{},canSend:!1,functionName:{},btnLoading:!1,logList:[],showValue:"",from:{datatype:{type:""}},title:"在线模式"}},created:function(){},watch:{device:function(e){var t=this;e&&e.serialNumber&&(this.params.serialNumber=e.serialNumber,this.serialNumber=e.serialNumber,this.params.productId=e.productId,this.params.slaveId=e.slaveId,this.params.deviceId=e.deviceId,this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.slaveList=e.subDeviceList,this.getSlaveList(this.deviceInfo),this.$busEvent.$on("updateData",(function(e){e.data&&e.data[0].remark&&(t.getDeviceFuncLog(),e.data[0].ts=e.data[0].remark),t.updateData(e)})),this.$busEvent.$on("logData",(function(e){var a;(a=t.messageList).push.apply(a,Object(r["a"])(e.data))})))}},methods:{qosChange:function(e){},payloadTypeChange:function(e){},getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate(),n=e.getHours(),r=e.getMinutes(),s=e.getSeconds();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,t+"-"+a+"-"+i+" "+n+":"+r+":"+s},getRuntimeStatus:function(){var e=this;Object(o["g"])(this.params).then((function(t){e.runningData=t.data.thingsModels,e.runningData.forEach((function(e){"enum"==e.datatype.type?e.datatype.enumList.forEach((function(t){t.value==e.value&&(e.value=t.text)})):"bool"==e.datatype.type&&(e.value=0==e.value?e.falseText:e.trueText)})),e.functionData=e.runningData.filter((function(e){return 0==e.isReadonly}))}))},getSlaveList:function(){this.getRuntimeStatus(),this.getDeviceFuncLog()},selectSlave:function(){this.params.serialNumber=this.serialNumber+"_"+this.params.slaveId,this.getRuntimeStatus()},handleClick:function(){"prop"===this.thingsType?this.params.type=1:"function"===this.thingsType&&(this.params.type=2,this.functionData=this.runningData.filter((function(e){return 0==e.isReadonly})))},updateParam:function(e){},editFunc:function(e){this.dialogValue=!0,this.canSend=!0,this.funVal={},this.getValueName(e),this.from=e},updateDeviceStatus:function(e){3==e.status?this.title="在线模式":1==e.isShadow?this.title="影子模式":this.title="离线模式",this.$emit("statusEvent",this.deviceInfo.status)},getValueName:function(e){this.funVal[e.id]=e.value},sendService:function(){var e=this;console.log("下发指令",this.showValue);try{this.funVal[this.from.id]=this.showValue;var t={serialNumber:this.serialNumber,productId:this.params.productId,remoteCommand:this.funVal,identifier:this.from.id,slaveId:this.params.slaveId,modelName:this.from.name,isShadow:3!=this.device.status,type:this.from.type};Object(s["b"])(t).then((function(t){200==t.code&&e.$message({type:"success",message:"服务调用成功!"})}))}finally{this.dialogValue=!1}},getShowValue:function(e){var t=this;switch(this.from.datatype.type){case u:var a=this.from.datatype.enumList;a.forEach((function(a){a.value===e&&(t.showValue=a.text)}));break;case l:case c:this.showValue=e;case d:this.showValue=1==e?this.from.datatype.trueText:this.from.datatype.falseText;break}},changeSelect:function(){this.$forceUpdate()},justicNumber:function(){if(this.canSend=!0,this.from.datatype.max<this.funVal[this.from.identity]||this.from.datatype.min>this.funVal[this.from.identity])return this.canSend=!1,!0;this.$forceUpdate()},getDeviceFuncLog:function(){var e=this,t={serialNumber:this.params.serialNumber+"_"+this.params.slaveId};Object(s["a"])(t).then((function(t){e.logList=t.rows}))},updateData:function(e){var t=this;e.data&&e.data.forEach((function(e){t.runningData.some((function(a,i){if(e.slaveId===a.slaveId&&e.id==a.id){var n=t.runningData[i];return n.ts=e.ts,n.value=e.value,"enum"==a.datatype.type?a.datatype.enumList.forEach((function(e){e.value==n.value&&(n.value=e.text)})):"bool"==a.datatype.type&&(n.value=0==n.value?a.datatype.falseText:a.datatype.trueText),t.$set(t.runningData,i,n),!0}}))}))}}},p=m,h=(a("df7b"),a("2877")),f=Object(h["a"])(p,i,n,!1,null,"229ed0c8",null);t["default"]=f.exports},"5f43":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{attrs:{gutter:120}},[a("el-col",{staticStyle:{"margin-bottom":"50px"},attrs:{xs:24,sm:24,md:24,lg:14,xl:10}},[a("el-descriptions",{staticStyle:{"margin-bottom":"50px"},attrs:{column:1,border:""}},[a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-menu"}),e._v(" 设备模式 ")]),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v(e._s(e.title))])],2),e.hasShrarePerm("ota")?a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("svg-icon",{attrs:{"icon-class":"ota"}}),e._v(" OTA升级 ")],1),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v("Version "+e._s(e.deviceInfo.firmwareVersion))]),a("el-button",{staticStyle:{float:"right"},attrs:{type:"success",size:"mini",disabled:3!=e.deviceInfo.status},on:{click:function(t){return e.getLatestFirmware(e.deviceInfo.deviceId)}}},[e._v("检查更新")])],2):e._e(),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串 "+(t.datatype.unit?"，单位："+t.datatype.unit:""),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"2px 0 0 5px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"integer"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"4px 0 0 10px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{size:"small",placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",size:"small",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2),1==e.deviceInfo.isShadow&&3!=e.deviceInfo.status?a("el-descriptions",{attrs:{column:1,border:"",size:"mini"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("设备离线时状态")])]),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(t){return a("el-button",{key:t.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:""}},[e._v(e._s(t.text))])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{size:"mini","active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2):e._e()],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:10,xl:14}},[e.deviceInfo.chartList.length>0?a("el-row",{staticStyle:{"background-color":"#f5f7fa",padding:"20px 10px 20px 10px","border-radius":"15px","margin-right":"5px"},attrs:{gutter:20}},e._l(e.deviceInfo.chartList,(function(e,t){return a("el-col",{key:t,attrs:{xs:24,sm:12,md:12,lg:24,xl:8}},[a("el-card",{staticStyle:{"border-radius":"30px","margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("div",{ref:"map",refInFor:!0,staticStyle:{height:"230px",width:"185px",margin:"0 auto"}})])],1)})),1):e._e()],1)],1),a("el-dialog",{attrs:{title:"设备固件升级",visible:e.openFirmware,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openFirmware=t}}},[null==e.firmware?a("div",{staticStyle:{"text-align":"center","font-size":"16px"}},[a("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a"}}),e._v(" 已经是最新版本，不需要升级 ")]):e._e(),null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-descriptions",{attrs:{column:1,border:"",size:"large",labelStyle:{width:"150px","font-weight":"bold"}}},[a("template",{slot:"title"},[a("el-link",{attrs:{icon:"el-icon-success",type:"success",underline:!1}},[e._v("可以升级到以下版本")])],1),a("el-descriptions-item",{attrs:{label:"固件名称"}},[e._v(e._s(e.firmware.firmwareName))]),a("el-descriptions-item",{attrs:{label:"所属产品"}},[e._v(e._s(e.firmware.productName))]),a("el-descriptions-item",{attrs:{label:"固件版本"}},[e._v("Version "+e._s(e.firmware.version))]),a("el-descriptions-item",{attrs:{label:"下载地址"}},[a("el-link",{attrs:{href:e.getDownloadUrl(e.firmware.filePath),underline:!1,type:"primary"}},[e._v(e._s(e.getDownloadUrl(e.firmware.filePath)))])],1),a("el-descriptions-item",{attrs:{label:"固件描述"}},[e._v(e._s(e.firmware.remark))])],2):e._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-button",{attrs:{type:"success"},on:{click:e.otaUpgrade}},[e._v("升 级")]):e._e(),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=(a("d3b7"),a("25f0"),a("8a79"),a("b0c0"),a("d81d"),a("814a")),s=a("0bc2"),o={name:"running-status",props:{device:{type:Object,default:null}},watch:{device:function(e,t){e&&0!=e.deviceId&&(this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.$nextTick((function(){this.MonitorChart()})),this.mqttCallback())}},data:function(){return{title:"设备控制 ",shadowUnEnable:!1,statusColor:{background:"#67C23A",color:"#fff",minWidth:"100px"},firmware:{},openFirmware:!1,loading:!0,deviceInfo:{boolList:[],enumList:[],stringList:[],integerList:[],decimalList:[],arrayList:[],thingsModels:[],chartList:[]},monitorChart:[{chart:{},data:{id:"",name:"",value:""}}],remoteCommand:{}}},created:function(){},methods:{mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态-运行】主题：",t),console.log("接收到【设备状态-运行】内容：",a),e.deviceInfo.serialNumber==r&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi,e.updateDeviceStatus(e.deviceInfo))),"reply"==n[4]&&(console.log("xiao",a),e.$modal.notifySuccess(a)),t.endsWith("ws/service")&&(console.log("接收到【物模型】主题1：",t),console.log("接收到【物模型】内容：",a),e.deviceInfo.serialNumber==r)))for(var s=0;s<a.length;s++){for(var o=!1,l=0;l<e.deviceInfo.thingsModels.length&&!o;l++){if(e.deviceInfo.thingsModels[l].id==a[s].id){e.deviceInfo.thingsModels[l].shadow=a[s].value,o=!0;break}if("object"==e.deviceInfo.thingsModels[l].datatype.type){for(var c=0;c<e.deviceInfo.thingsModels[l].datatype.params.length;c++)if(e.deviceInfo.thingsModels[l].datatype.params[c].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.params[c].shadow=a[s].value,o=!0;break}}else if("array"==e.deviceInfo.thingsModels[l].datatype.type)if("object"==e.deviceInfo.thingsModels[l].datatype.arrayType)if(0==String(a[s].id).indexOf("array_"))for(var d=0;d<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;d++){for(var u=0;u<e.deviceInfo.thingsModels[l].datatype.arrayParams[d].length;u++)if(e.deviceInfo.thingsModels[l].datatype.arrayParams[d][u].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayParams[d][u].shadow=a[s].value,o=!0;break}if(o)break}else for(var m=0;m<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;m++){for(var p=0;p<e.deviceInfo.thingsModels[l].datatype.arrayParams[m].length;p++){var h=m>9?String(m):"0"+l,f="array_"+h+"_";e.deviceInfo.thingsModels[l].datatype.arrayParams[m][p].id==f+a[s].id&&(e.deviceInfo.thingsModels[l].datatype.arrayParams[m][p].shadow=a[s].value,o=!0)}if(o)break}else for(var v=0;v<e.deviceInfo.thingsModels[l].datatype.arrayModel.length;v++)if(e.deviceInfo.thingsModels[l].datatype.arrayModel[v].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayModel[v].shadow=a[s].value,o=!0;break}}for(var y=0;y<e.deviceInfo.chartList.length;y++){if(0==e.deviceInfo.chartList[y].id.indexOf("array_")){if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var b=0;b<e.monitorChart.length;b++)if(a[s].id==e.monitorChart[b].data.id){var g=[{value:a[s].value,name:e.monitorChart[b].data.name}];e.monitorChart[b].chart.setOption({series:[{data:g}]});break}}}else if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var w=0;w<e.monitorChart.length;w++)if(a[s].id==e.monitorChart[w].data.id){o=!0;var x=[{value:a[s].value,name:e.monitorChart[w].data.name}];e.monitorChart[w].chart.setOption({series:[{data:x}]});break}}if(o)break}}}))},mqttPublish:function(e,t){var a=this,i={};i[t.id]=t.shadow;var n={serialNumber:e.serialNumber,productId:e.productId,remoteCommand:i,identifier:t.id,modelName:t.name,isShadow:3!=e.status,type:t.type};Object(s["b"])(n).then((function(e){200===e.code&&a.$message({type:"success",message:"服务调用成功!"})}))},enumButtonClick:function(e,t,a){t.shadow=a,this.mqttPublish(e,t)},updateDeviceStatus:function(e){3==e.status?(this.statusColor.background="#12d09f",this.title="在线模式"):1==e.isShadow?(this.statusColor.background="#409EFF",this.title="影子模式"):(this.statusColor.background="#909399",this.title="离线模式",this.shadowUnEnable=!0),this.$emit("statusEvent",this.deviceInfo.status)},arrayItemChange:function(e,t){for(var a="",i=0;i<t.datatype.arrayCount;i++)a+=t.datatype.arrayModel[i].shadow+",";a=a.substring(0,a.length-1),t.shadow=a},arrayInputChange:function(e,t){var a=e.split(",");if(a.length!=t.datatype.arrayCount)this.$modal.alertWarning("元素个数不匹配，数组元素个数为"+t.datatype.arrayCount+"个，以英文逗号分隔。");else for(var i=0;i<t.datatype.arrayCount;i++)t.datatype.arrayModel[i].shadow=a[i]},hasShrarePerm:function(e){return 0!=this.deviceInfo.isOwner||-1!=this.deviceInfo.userPerms.indexOf(e)},otaUpgrade:function(){var e=this,t="/"+this.deviceInfo.productId+"/"+this.deviceInfo.serialNumber+"/ota/get",a='{"version":'+this.firmware.version+',"downloadUrl":"'+this.getDownloadUrl(this.firmware.filePath)+'"}';this.$mqttTool.publish(t,a,"设备升级").then((function(t){e.$modal.notifySuccess(t)})).catch((function(t){e.$modal.notifyError(t)})),this.openFirmware=!1},getLatestFirmware:function(e){var t=this;Object(r["d"])(e).then((function(e){t.firmware=e.data,t.openFirmware=!0}))},cancel:function(){this.openFirmware=!1},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},MonitorChart:function(){for(var e=0;e<this.deviceInfo.chartList.length;e++){var t;this.monitorChart[e]={chart:this.$echarts.init(this.$refs.map[e]),data:{id:this.deviceInfo.chartList[e].id,name:this.deviceInfo.chartList[e].name,value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min}},t={tooltip:{formatter:" {b} <br/> {c}"+this.deviceInfo.chartList[e].datatype.unit},series:[{name:this.deviceInfo.chartList[e].datatype.type,type:"gauge",min:this.deviceInfo.chartList[e].datatype.min,max:this.deviceInfo.chartList[e].datatype.max,colorBy:"data",splitNumber:10,radius:"100%",splitLine:{distance:4},axisLabel:{fontSize:10,distance:10},axisTick:{distance:4},axisLine:{lineStyle:{width:8,color:[[.2,"#409EFF"],[.8,"#12d09f"],[1,"#F56C6C"]],opacity:.3}},pointer:{icon:"triangle",length:"60%",width:7},progress:{show:!0,width:8},detail:{valueAnimation:!0,formatter:"{value} "+this.deviceInfo.chartList[e].datatype.unit,offsetCenter:[0,"80%"],fontSize:20},data:[{value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min,name:this.deviceInfo.chartList[e].name}],title:{offsetCenter:[0,"115%"],fontSize:16}}]},t&&this.monitorChart[e].chart.setOption(t)}}}},l=o,c=(a("b532"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,null,null);t["default"]=d.exports},"67dd":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.channelList,size:"mini"}},[a("el-table-column",{attrs:{label:"设备ID",align:"center",prop:"deviceSipId"}}),a("el-table-column",{attrs:{label:"通道ID",align:"center",prop:"channelSipId"}}),a("el-table-column",{attrs:{label:"快照","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isVideoChannel(t.row)?a("el-image",{staticStyle:{width:"60px"},attrs:{src:e.getSnap(t.row),"preview-src-list":e.getBigSnap(t.row),fit:"contain"}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline"})])]):e._e()]}}])}),a("el-table-column",{attrs:{label:"通道名称",align:"center",prop:"channelName"}}),a("el-table-column",{attrs:{label:"产品型号",align:"center",prop:"model"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sip_gen_status,value:t.row.status,size:"mini"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isVideoChannel(t.row)?a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",type:"success",icon:"el-icon-video-play",disabled:2!=t.row.status},on:{click:function(a){return e.sendDevicePush(t.row)}}},[e._v(" 查看直播")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},n=[],r=(a("14d9"),a("e2de")),s={name:"Channel",dicts:["sip_gen_status","video_type","channel_type"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.getList())}},data:function(){return{loadSnap:{},deviceInfo:{},loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,channelList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deviceSipId:null},form:{}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.getList()},methods:{sendDevicePush:function(e){var t=e.deviceSipId,a=e.channelSipId;console.log("通知设备推流1："+t+" : "+a),this.$router.push({name:"Player",params:{activeName:"play",deviceId:e.deviceSipId,channelId:e.channelSipId}})},getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){e.channelList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={channelId:null,channelSipId:null,deviceSipId:null,channelName:null,manufacture:null,model:null,owner:null,civilcode:null,block:null,address:null,parentid:null,ipaddress:null,port:null,password:null,ptztype:null,ptztypetext:null,status:0,longitude:null,latitude:null,streamid:null,subcount:null,parental:1,hasaudio:1},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset();var a=e.channelId||this.ids;Object(r["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改监控设备通道信息"}))},handleDelete:function(e){var t=this,a=e.channelId||this.ids;this.$modal.confirm('是否确认删除监控设备通道信息编号为"'+a+'"的数据项？').then((function(){return Object(r["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getSnap:function(e){return console.log("getSnap:/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"),"/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"},getBigSnap:function(e){return[this.getSnap(e)]},isVideoChannel:function(e){var t=e.channelSipId.substring(10,13);return!("111"!==t&&"112"!==t&&"118"!==t&&"131"!==t&&"132"!==t)}}},o=s,l=a("2877"),c=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=c.exports},7168:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"日志类型",prop:"logType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.dict.type.iot_event_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identity"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identity,callback:function(t){e.$set(e.queryParams,"identity",t)},expression:"queryParams.identity"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceLogList,size:"mini"}},[a("el-table-column",{attrs:{label:"类型",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_event_type,value:t.row.logType}})]}}])}),a("el-table-column",{attrs:{label:"模式",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.mode?a("el-tag",{attrs:{type:"primary"}},[e._v("影子模式")]):2==t.row.mode?a("el-tag",{attrs:{type:"success"}},[e._v("在线模式")]):a("el-tag",{attrs:{type:"info"}},[e._v("其他信息")])]}}])}),a("el-table-column",{attrs:{label:"时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createTime))])]}}])}),a("el-table-column",{attrs:{label:"标识符",align:"center",prop:"identity"}}),a("el-table-column",{attrs:{label:"动作",align:"left","header-align":"center",prop:"logValue"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatValueDisplay(t.row))}})]}}])}),a("el-table-column",{attrs:{label:"备注","header-align":"center",align:"left",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null==t.row.remark?"无":t.row.remark)+" ")]}}])})],1),a("div",{staticStyle:{height:"40px"}},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1)},n=[],r=a("5530"),s=(a("b0c0"),a("a9e3"),a("b775"));function o(e){return Object(s["a"])({url:"/iot/event/list",method:"get",params:e})}var l={name:"DeviceLog",dicts:["iot_event_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList(),this.thingsModel=this.deviceInfo.cacheThingsModel)}},data:function(){return{thingsModel:{},loading:!0,showSearch:!0,total:0,deviceLogList:[],queryParams:{pageNum:1,pageSize:10,logType:null,logValue:null,deviceId:null,serialNumber:null,deviceName:null,identity:null,isMonitor:null},daterangeTime:[]}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]),o(this.queryParams).then((function(t){e.deviceLogList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleExport:function(){this.download("iot/event/export",Object(r["a"])({},this.queryParams),"eventLog_".concat((new Date).getTime(),".xlsx"))},formatValueDisplay:function(e){if(1==e.logType){var t=this.getThingsModelItem(1,e.identity);if(""!=t)return(t.parentName?"["+t.parentName+(t.arrayIndex?t.arrayIndex:"")+"] ":"")+t.name+'： <span style="color:#409EFF;">'+this.getThingsModelItemValue(t,e.logValue)+" "+(void 0!=t.datatype.unit?t.datatype.unit:"")+"</span>"}else if(2==e.logType){var a=this.getThingsModelItem(2,e.identity);if(""!=a)return(a.parentName?"["+a.parentName+(a.arrayIndex?a.arrayIndex:"")+"] ":"")+a.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(a,e.logValue)+" "+(void 0!=a.datatype.unit?a.datatype.unit:"")+"</span>"}else if(3==e.logType){var i=this.getThingsModelItem(3,e.identity);if(""!=i)return(i.parentName?"["+i.parentName+(i.arrayIndex?i.arrayIndex:"")+"] ":"")+i.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(i,e.logValue)+" "+(void 0!=i.datatype.unit?i.datatype.unit:"")+"</span>"}else{if(4==e.logType)return'<span style="font-weight:bold">设备升级</span>';if(5==e.logType)return'<span style="font-weight:bold">设备上线</span>';if(6==e.logType)return'<span style="font-weight:bold">设备离线</span>'}return""},getThingsModelItemValue:function(e,t){if("bool"==e.datatype.type){if("0"==t)return e.datatype.falseText;if("1"==t)return e.datatype.trueText}else if("enum"==e.datatype.type)for(var a=0;a<e.datatype.enumList.length;a++)if(t==e.datatype.enumList[a].value)return e.datatype.enumList[a].text;return t},getThingsModelItem:function(e,t){if(1==e&&this.thingsModel.properties)for(var a=0;a<this.thingsModel.properties.length;a++){if(this.thingsModel.properties[a].id==t)return this.thingsModel.properties[a];if("object"==this.thingsModel.properties[a].datatype.type)for(var i=0;i<this.thingsModel.properties[a].datatype.params.length;i++)if(this.thingsModel.properties[a].datatype.params[i].id==t)return this.thingsModel.properties[a].datatype.params[i].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[i];if("array"==this.thingsModel.properties[a].datatype.type&&this.thingsModel.properties[a].datatype.arrayType)if("object"==this.thingsModel.properties[a].datatype.arrayType){var n=t,r=0;t.indexOf("array_")>-1&&(r=t.substring(6,8),n=t.substring(9));for(var s=0;s<this.thingsModel.properties[a].datatype.params.length;s++)if(this.thingsModel.properties[a].datatype.params[s].id==n)return this.thingsModel.properties[a].datatype.params[s].arrayIndex=Number(r)+1,this.thingsModel.properties[a].datatype.params[s].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[s]}else for(var o=0;o<this.thingsModel.properties[a].datatype.arrayCount.length;o++)if(this.thingsModel.properties[a].id==realIdentity)return this.thingsModel.properties[a].arrayIndex=Number(arrayIndex)+1,this.thingsModel.properties[a].parentName="元素",this.thingsModel.properties[a]}else if(2==e&&this.thingsModel.functions)for(var l=0;l<this.thingsModel.functions.length;l++){if(this.thingsModel.functions[l].id==t)return this.thingsModel.functions[l];if("object"==this.thingsModel.functions[l].datatype.type)for(var c=0;c<this.thingsModel.functions[l].datatype.params.length;c++)if(this.thingsModel.functions[l].datatype.params[c].id==t)return this.thingsModel.functions[l].datatype.params[c].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[c];if("array"==this.thingsModel.functions[l].datatype.type&&this.thingsModel.functions[l].datatype.arrayType){var d=t,u=0;if(t.indexOf("array_")>-1&&(u=t.substring(6,8),d=t.substring(9)),"object"==this.thingsModel.functions[l].datatype.arrayType){for(var m=0;m<this.thingsModel.functions[l].datatype.params.length;m++)if(this.thingsModel.functions[l].datatype.params[m].id==d)return this.thingsModel.functions[l].datatype.params[m].arrayIndex=Number(u)+1,this.thingsModel.functions[l].datatype.params[m].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[m]}else for(var p=0;p<this.thingsModel.functions[l].datatype.arrayCount.length;p++)if(this.thingsModel.functions[l].id==d)return this.thingsModel.functions[l].arrayIndex=Number(u)+1,this.thingsModel.functions[l].parentName="元素",this.thingsModel.functions[l]}}else if(3==e&&this.thingsModel.events)for(var h=0;h<this.thingsModel.events.length;h++)if(this.thingsModel.events[h].id==t)return this.thingsModel.events[h];return""}}},c=l,d=a("2877"),u=Object(d["a"])(c,i,n,!1,null,null,null);t["default"]=u.exports},"7a72":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"70px"}},[a("el-form-item",{attrs:{label:"定时名称",prop:"jobName"}},[a("el-input",{attrs:{placeholder:"请输入定时名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"定时状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择定时状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_job_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer"],expression:"['iot:device:timer']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobList,size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"描述",align:"center",prop:"cronText"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatCronDisplay(t.row))}})]}}])}),a("el-table-column",{attrs:{label:"CRON表达式",align:"center",prop:"cronExpression","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"动作",align:"left",prop:"actions","show-overflow-tooltip":!0},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{overflow:"hidden","white-space":"nowrap"},domProps:{innerHTML:e._s(e.formatActionsDisplay(t.row.actions))}})]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1","active-text":"启用"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer"],expression:"['iot:device:timer']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer"],expression:"['iot:device:timer']"}],attrs:{size:"mini",type:"text",icon:"el-icon-caret-right"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("定时详细")]),a("br"),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer"],expression:"['iot:device:timer']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer"],expression:"['iot:device:timer']"}],attrs:{size:"mini",type:"text",icon:"el-icon-caret-right"},on:{click:function(a){return e.handleRun(t.row)}}},[e._v("执行一次")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"定时名称",prop:"jobName"}},[a("el-input",{staticStyle:{width:"340px"},attrs:{placeholder:"请输入定时名称"},model:{value:e.form.jobName,callback:function(t){e.$set(e.form,"jobName",t)},expression:"form.jobName"}})],1),a("el-form-item",{attrs:{label:"执行时间",prop:"timerTimeValue"}},[a("el-time-picker",{staticStyle:{width:"340px"},attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"选择时间",disabled:1==e.form.isAdvance},on:{change:e.timeChange},model:{value:e.timerTimeValue,callback:function(t){e.timerTimeValue=t},expression:"timerTimeValue"}})],1),a("el-form-item",{attrs:{label:"选择星期",prop:"timerWeek"}},[a("el-row",[a("el-col",{attrs:{span:18}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",multiple:"",disabled:1==e.form.isAdvance},on:{change:e.weekChange},model:{value:e.timerWeekValue,callback:function(t){e.timerWeekValue=t},expression:"timerWeekValue"}},e._l(e.timerWeeks,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"cron表达式",prop:"cron"}},[a("el-row",[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"cron执行表达式",disabled:0==e.form.isAdvance},model:{value:e.form.cronExpression,callback:function(t){e.$set(e.form,"cronExpression",t)},expression:"form.cronExpression"}},[a("template",{slot:"append"},[a("el-button",{attrs:{type:"primary",disabled:0==e.form.isAdvance},on:{click:e.handleShowCron}},[e._v(" 生成表达式 "),a("i",{staticClass:"el-icon-time el-icon--right"})])],1)],2)],1),a("el-col",{attrs:{span:4,offset:1}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:e.customerCronChange},model:{value:e.form.isAdvance,callback:function(t){e.$set(e.form,"isAdvance",t)},expression:"form.isAdvance"}},[e._v("自定义表达式")])],1)],1)],1),a("el-form-item",{attrs:{label:"定时状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_job_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a("div",{staticStyle:{"padding-bottom":"15px",padding:"0 20px"}},[a("el-divider")],1),a("el-form-item",{attrs:{label:"执行动作",prop:"actions"}},[e._l(e.actionList,(function(t,i){return a("el-row",{key:i+"action",staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:4}},[a("el-select",{attrs:{placeholder:"请选择类别"},on:{change:function(t){return e.actionTypeChange(t,i)}},model:{value:t.type,callback:function(a){e.$set(t,"type",a)},expression:"actionItem.type"}},e._l(e.modelTypes,(function(e,t){return a("el-option",{key:t+"type",attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:4,offset:1}},[1==t.type?a("el-select",{attrs:{placeholder:"请选择"},on:{change:function(t){return e.thingsModelItemChange(t,i)}},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"actionItem.id"}},e._l(e.thingsModel.properties,(function(e,t){return a("el-option",{key:t+"property",attrs:{label:e.name,value:e.id}})})),1):2==t.type?a("el-select",{attrs:{placeholder:"请选择"},on:{change:function(t){return e.thingsModelItemChange(t,i)}},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"actionItem.id"}},e._l(e.thingsModel.functions,(function(e,t){return a("el-option",{key:t+"func",attrs:{label:e.name,value:e.id}})})),1):3==t.type?a("el-select",{attrs:{placeholder:"请选择"},on:{change:function(t){return e.thingsModelItemChange(t,i)}},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}},e._l(e.thingsModel.functions,(function(e,t){return a("el-option",{key:t+"func",attrs:{label:e.name,value:e.id}})})),1):e._e()],1),a("el-col",{attrs:{span:10,offset:1}},[!t.thingsModelItem||"integer"!=t.thingsModelItem.datatype.type&&"decimal"!=t.thingsModelItem.datatype.type?t.thingsModelItem&&"bool"==t.thingsModelItem.datatype.type?a("span",[a("el-switch",{attrs:{"active-text":t.thingsModelItem.datatype.trueText,"inactive-text":t.thingsModelItem.datatype.falseText,"active-value":"1","inactive-value":"0"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}})],1):t.thingsModelItem&&"enum"==t.thingsModelItem.datatype.type?a("span",[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}},e._l(t.thingsModelItem.datatype.enumList,(function(e,t){return a("el-option",{key:t+"things",attrs:{label:e.text,value:e.value}})})),1)],1):t.thingsModelItem&&"string"==t.thingsModelItem.datatype.type?a("span",[a("el-input",{attrs:{placeholder:"请输入字符串",max:t.thingsModelItem.datatype.maxLength},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}})],1):t.thingsModelItem&&"array"==t.thingsModelItem.datatype.type?a("span",[a("el-input",{attrs:{placeholder:"请输入英文逗号分隔的数组"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}})],1):e._e():a("span",[a("el-input",{attrs:{placeholder:"值",max:t.thingsModelItem.datatype.max,min:t.thingsModelItem.datatype.min,type:"number",size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}},[a("template",{slot:"append"},[e._v(e._s(t.thingsModelItem.datatype.unit))])],2)],1)]),0!=i?a("el-col",{attrs:{span:2,offset:1}},[a("a",{staticStyle:{color:"#f56c6c"},on:{click:function(t){return e.removeEnumItem(i)}}},[e._v("删除")])]):e._e()],1)})),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addEnumItem()}}},[e._v("添加执行动作")])])],2)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",loading:e.submitButtonLoading},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"scrollbar",attrs:{title:"Cron表达式生成器",visible:e.openCron,"append-to-body":"","destroy-on-close":""},on:{"update:visible":function(t){e.openCron=t}}},[a("crontab",{staticStyle:{"padding-bottom":"80px"},attrs:{expression:e.expression},on:{hide:function(t){e.openCron=!1},fill:e.crontabFill}})],1),a("el-dialog",{attrs:{title:"定时详细",visible:e.openView,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openView=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px",size:"mini"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时编号："}},[e._v(e._s(e.form.jobId))]),a("el-form-item",{attrs:{label:"定时名称："}},[e._v(e._s(e.form.jobName))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时分组："}},[e._v(e._s(e.jobGroupFormat(e.form)))]),a("el-form-item",{attrs:{label:"创建时间："}},[e._v(e._s(e.form.createTime))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否并发："}},[0==e.form.concurrent?a("div",[e._v("允许")]):1==e.form.concurrent?a("div",[e._v("禁止")]):e._e()])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"cron表达式："}},[e._v(e._s(e.form.cronExpression))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"执行策略："}},[0==e.form.misfirePolicy?a("div",[e._v("默认策略")]):1==e.form.misfirePolicy?a("div",[e._v("立即执行")]):2==e.form.misfirePolicy?a("div",[e._v("执行一次")]):3==e.form.misfirePolicy?a("div",[e._v("放弃执行")]):e._e()])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下次执行时间："}},[e._v(e._s(e.parseTime(e.form.nextValidTime)))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时状态："}},[0==e.form.status?a("div",[e._v("正常")]):1==e.form.status?a("div",[e._v("暂停")]):e._e()])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行动作："}},[a("div",{staticStyle:{border:"1px solid #ddd",padding:"10px","border-radius":"5px",width:"465px"},domProps:{innerHTML:e._s(e.formatActionsDisplay(e.form.actions))}})])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.openView=!1}}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("5530"),s=(a("d81d"),a("a9e3"),a("b0c0"),a("e9c4"),a("14d9"),a("a434"),a("4e82"),a("b775"));function o(e){return Object(s["a"])({url:"/iot/job/list",method:"get",params:e})}function l(e){return Object(s["a"])({url:"/iot/job/"+e,method:"get"})}function c(e){return Object(s["a"])({url:"/iot/job",method:"post",data:e})}function d(e){return Object(s["a"])({url:"/iot/job",method:"put",data:e})}function u(e){return Object(s["a"])({url:"/iot/job/"+e,method:"delete"})}function m(e,t){var a={jobId:e,status:t};return Object(s["a"])({url:"/iot/job/changeStatus",method:"put",data:a})}function p(e,t){var a={jobId:e,jobGroup:t};return Object(s["a"])({url:"/iot/job/run",method:"put",data:a})}var h=a("bdd0"),f={components:{Crontab:h["a"]},name:"device-timer",dicts:["sys_job_group","sys_job_status"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.thingsModel=this.deviceInfo.cacheThingsModel,this.queryParams.deviceId=this.deviceInfo.deviceId)}},data:function(){return{thingsModel:{},actionList:[],deviceInfo:{},loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,jobList:[],title:"",open:!1,openView:!1,openCron:!1,expression:"",submitButtonLoading:!1,queryParams:{pageNum:1,pageSize:10,deviceId:0,jobName:void 0,jobGroup:void 0,status:void 0},timerWeeks:[{value:1,label:"周一"},{value:2,label:"周二"},{value:3,label:"周三"},{value:4,label:"周四"},{value:5,label:"周五"},{value:6,label:"周六"},{value:7,label:"周日"}],timerWeekValue:[1,2,3,4,5,6,7],timerTimeValue:"",modelTypes:[{value:1,label:"属性"},{value:2,label:"功能"}],form:{},rules:{jobName:[{required:!0,message:"定时名称不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.jobList=t.rows,e.total=t.total,e.loading=!1}))},jobGroupFormat:function(e,t){return this.selectDictLabel(this.dict.type.sys_job_group,e.jobGroup)},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={jobId:void 0,jobName:void 0,cronExpression:void 0,status:"0",jobGroup:"DEFAULT",misfirePolicy:2,concurrent:1,isAdvance:0,jobType:1,productId:0,productName:"",sceneId:0,alertId:0,actions:""},this.submitButtonLoading=!1,this.timerWeekValue=[1,2,3,4,5,6,7],this.timerTimeValue="",this.actionList=[{id:"",name:"",value:"",type:2,deviceId:this.deviceInfo.deviceId,deviceName:this.deviceInfo.deviceName,thingsModelItem:{id:"",name:"",datatype:{type:""}}}],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobId})),this.single=1!=e.length,this.multiple=!e.length},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+'""'+e.jobName+'"定时吗？').then((function(){return m(e.jobId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleRun:function(e){var t=this;this.$modal.confirm('确认要立即执行一次"'+e.jobName+'"定时吗？').then((function(){return p(e.jobId,e.jobGroup)})).then((function(){t.$modal.msgSuccess("执行成功")})).catch((function(){}))},handleView:function(e){var t=this;l(e.jobId).then((function(e){t.form=e.data,t.openView=!0}))},handleShowCron:function(){this.expression=this.form.cronExpression,this.openCron=!0},crontabFill:function(e){this.form.cronExpression=e},handleAdd:function(){this.reset(),this.open=!0,this.title="添加定时"},handleUpdate:function(e){var t=this;this.reset();var a=e.jobId||this.ids;l(a).then((function(e){t.form=e.data,t.actionList=JSON.parse(t.form.actions);for(var a=0;a<t.actionList.length;a++)if(1==t.actionList[a].type){for(var i=0;i<t.thingsModel.properties.length;i++)if(t.actionList[a].id==t.thingsModel.properties[i].id){t.actionList[a].thingsModelItem=t.thingsModel.properties[i];break}}else if(2==t.actionList[a].type)for(var n=0;n<t.thingsModel.functions.length;n++)if(t.actionList[a].id==t.thingsModel.functions[n].id){t.actionList[a].thingsModelItem=t.thingsModel.functions[n];break}if(0==t.form.isAdvance){var r=t.form.cronExpression.substring(12).split(",").map(Number);t.timerWeekValue=r,t.timerTimeValue=t.form.cronExpression.substring(5,7)+":"+t.form.cronExpression.substring(2,4)}t.open=!0,t.title="修改定时"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){if(0==e.form.isAdvance){if(""==e.timerTimeValue||null==e.timerTimeValue)return void e.$modal.alertError("执行时间不能空");if(null==e.timerWeekValue||""==e.timerWeekValue)return void e.$modal.alertError("请选择要执行的星期")}else if(1==e.form.isAdvance&&""==e.form.cronExpression)return void e.$modal.alertError("cron表达式不能为空");for(var a=0;a<e.actionList.length;a++)if(""==e.actionList[a].id||""==e.actionList[a].name||""==e.actionList[a].value)return void e.$modal.alertError("执行动作中的选项和值不能为空");e.actionList[0].deviceId=e.deviceInfo.deviceId,e.actionList[0].deviceName=e.deviceInfo.deviceName;for(var i=0;i<e.actionList.length;i++)e.$delete(e.actionList[i],"thingsModelItem");e.form.actions=JSON.stringify(e.actionList),e.form.deviceId=e.deviceInfo.deviceId,e.form.deviceName=e.deviceInfo.deviceName,e.form.serialNumber=e.deviceInfo.serialNumber,e.form.productId=e.deviceInfo.productId,e.form.productName=e.deviceInfo.productName,e.submitButtonLoading=!0,void 0!=e.form.jobId?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.submitButtonLoading=!1,e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.submitButtonLoading=!1,e.open=!1,e.getList()}))}}))},handleDelete:function(e){var t=this,a=e.jobId||this.ids;this.$modal.confirm('是否确认删除定时定时编号为"'+a+'"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/job/export",Object(r["a"])({},this.queryParams),"job_".concat((new Date).getTime(),".xlsx"))},addEnumItem:function(){this.actionList.push({id:"",name:"",value:"",type:2,deviceId:this.deviceInfo.deviceId,deviceName:this.deviceInfo.deviceName,thingsModelItem:{id:"",name:"",datatype:{type:""}}})},removeEnumItem:function(e){this.actionList.splice(e,1)},weekChange:function(e){this.gentCronExpression()},timeChange:function(e){this.gentCronExpression()},customerCronChange:function(e){0==e&&this.gentCronExpression()},gentCronExpression:function(){var e="00",t="00";null!=this.timerTimeValue&&""!=this.timerTimeValue&&(e=this.timerTimeValue.substring(0,2),t=this.timerTimeValue.substring(3));var a="*";this.timerWeekValue.length>0&&(a=this.timerWeekValue.sort()),this.form.cronExpression="0 "+t+" "+e+" ? * "+a},actionTypeChange:function(e,t){this.actionList[t].id="",this.actionList[t].value="",this.actionList[t].thingsModelItem=null},thingsModelItemChange:function(e,t){if(this.actionList[t].value="",1==this.actionList[t].type){for(var a=0;a<this.thingsModel.properties.length;a++)if(this.thingsModel.properties[a].id==e){this.actionList[t].name=this.thingsModel.properties[a].name,this.actionList[t].thingsModelItem=this.thingsModel.properties[a];break}}else if(2==this.actionList[t].type)for(var i=0;i<this.thingsModel.functions.length;i++)if(this.thingsModel.functions[i].id==e){this.actionList[t].name=this.thingsModel.functions[i].name,this.actionList[t].thingsModelItem=this.thingsModel.functions[i];break}},formatActionsDisplay:function(e){if(null!=e&&""!=e){for(var t=JSON.parse(e),a="",i=0;i<t.length;i++){var n=t[i].value;if(1==t[i].type){for(var r=0;r<this.thingsModel.properties.length;r++)if(t[i].id==this.thingsModel.properties[r].id){if("decimal"==this.thingsModel.properties[r].datatype.type||"integer"==this.thingsModel.properties[r].datatype.type)n=t[i].value+this.thingsModel.properties[r].datatype.unit;else if("enum"==this.thingsModel.properties[r].datatype.type){for(var s=0;s<this.thingsModel.properties[r].datatype.enumList.length;s++)if(t[i].value==this.thingsModel.properties[r].datatype.enumList[s].value){n=this.thingsModel.properties[r].datatype.enumList[s].text;break}}else"bool"==this.thingsModel.properties[r].datatype.type&&(n="1"==t[i].value?this.thingsModel.properties[r].datatype.trueText:this.thingsModel.properties[r].datatype.falseText);break}}else if(2==t[i].type)for(var o=0;o<this.thingsModel.functions.length;o++)if(t[i].id==this.thingsModel.functions[o].id){if("decimal"==this.thingsModel.functions[o].datatype.type||"integer"==this.thingsModel.functions[o].datatype.type)n=t[i].value+this.thingsModel.functions[o].datatype.unit;else if("enum"==this.thingsModel.functions[o].datatype.type){for(var l=0;l<this.thingsModel.functions[o].datatype.enumList.length;l++)if(t[i].value==this.thingsModel.functions[o].datatype.enumList[l].value){n=this.thingsModel.functions[o].datatype.enumList[l].text;break}}else"bool"==this.thingsModel.functions[o].datatype.type&&(n="1"==t[i].value?this.thingsModel.functions[o].datatype.trueText:this.thingsModel.functions[o].datatype.falseText);break}a=a+t[i].name+'：<span style="color:#F56C6C">'+n+"</span><br />"}return a}},formatCronDisplay:function(e){var t="";if(0==e.isAdvance){var a='<br /><span style="color:#F56C6C">时间 '+e.cronExpression.substring(5,7)+":"+e.cronExpression.substring(2,4)+"</span>",i=e.cronExpression.substring(12);if("1,2,3,4,5,6,7"==i)t="每天 "+a;else{for(var n=i.split(","),r=0;r<n.length;r++)"1"==n[r]?t+="周一、":"2"==n[r]?t+="周二、":"3"==n[r]?t+="周三、":"4"==n[r]?t+="周四、":"5"==n[r]?t+="周五、":"6"==n[r]?t+="周六、":"7"==n[r]&&(t+="周日、");t=t.substring(0,t.length-1)+" "+a}}else t="自定义Cron表达式";return t}}},v=f,y=a("2877"),b=Object(y["a"])(v,i,n,!1,null,null,null);t["default"]=b.exports},"814a":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"f",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return c})),a.d(t,"b",(function(){return d}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/getLatest/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},"8a79":function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("06cf").f,s=a("50c4"),o=a("577e"),l=a("5a34"),c=a("1d80"),d=a("ab13"),u=a("c430"),m=n("".endsWith),p=n("".slice),h=Math.min,f=d("endsWith"),v=!u&&!f&&!!function(){var e=r(String.prototype,"endsWith");return e&&!e.writable}();i({target:"String",proto:!0,forced:!v&&!f},{endsWith:function(e){var t=o(c(this));l(e);var a=arguments.length>1?arguments[1]:void 0,i=t.length,n=void 0===a?i:h(s(a),i),r=o(e);return m?m(t,r,n):p(t,n-r.length,n)===r}})},9467:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"行政区划"}},[a("el-cascader",{attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),a("el-form-item",{attrs:{label:"设备类型",prop:"deviceType"}},[a("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"通道类型",prop:"channelType"}},[a("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"通道数量",prop:"createNum"}},[a("el-input-number",{staticStyle:{width:"220px"},attrs:{"controls-position":"right",placeholder:"请输入生成通道数量",type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("生 成")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")])],1)],1)},n=[],r=a("ef6c"),s=a("e2de"),o={name:"SipidDialog",dicts:["video_type","channel_type"],props:{product:{type:Object,default:null}},data:function(){return{loading:!0,title:"生成设备编号和通道",total:0,open:!1,devsipid:"",createForm:{city:"",deviceType:"",channelType:"",createNum:1},cityOptions:r["regionData"],city:"",cityCode:""}},created:function(){},methods:{changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=r["CodeToText"][e[0]]+"/"+r["CodeToText"][e[1]]+"/"+r["CodeToText"][e[2]];this.createForm.citycode=t}},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError("通道数量至少一个"):(this.createForm.productId=this.product.productId,this.createForm.productName=this.product.productName,this.createForm.tenantId=this.product.tenantId,this.createForm.tenantName=this.product.tenantName,this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?Object(s["a"])(this.createForm.createNum,this.createForm).then((function(t){e.$modal.msgSuccess("已生成设备编号和通道"),e.devsipid=t.data,e.confirmSelectProduct()})):this.$message({type:"error",message:"请选择地区，设备类型，通道类型！！"}))},confirmSelectProduct:function(){this.open=!1,this.$emit("addGenEvent",this.devsipid)},closeDialog:function(){this.open=!1}}},l=o,c=a("2877"),d=Object(c["a"])(l,i,n,!1,null,"0575409a",null);t["default"]=d.exports},9626:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[a("el-input",{attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),a("el-form-item",{attrs:{label:"设备状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择设备状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),a("el-table-column",{attrs:{label:"网关编码",align:"center",prop:"gwDevCode"}}),a("el-table-column",{attrs:{label:"从机地址",align:"center",prop:"addr"}}),a("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"firmwareVersion"}}),a("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"激活时间",align:"center",prop:"activeTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.activeTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"设备名",prop:"deviceName"}},[a("el-input",{attrs:{placeholder:"请输入设备名"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1),a("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[a("el-input",{attrs:{placeholder:"请输入固件版本"},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=(a("d81d"),a("584f")),s={name:"device-sub",props:{device:{type:Object,default:null}},dicts:["iot_device_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,deviceList:[],title:"",open:!1,daterangeActiveTime:[],queryParams:{pageNum:1,pageSize:10,gwDevCode:""},form:{},rules:{deviceName:[{required:!0,message:"设备名不能为空",trigger:"blur"}],firmwareVersion:[{required:!0,message:"固件版本不能为空",trigger:"blur"}]}}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.gwDevCode=this.deviceInfo.serialNumber,this.getList())}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeActiveTime&&""!=this.daterangeActiveTime&&(this.queryParams.params["beginActiveTime"]=this.daterangeActiveTime[0],this.queryParams.params["endActiveTime"]=this.daterangeActiveTime[1]),Object(r["k"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.map((function(e){var t=e.serialNumber.split("_");e.addr=t[1]}))}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deviceId:null,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,firmwareVersion:null,status:0,isShadow:null,rssi:null,isCustomerLocation:null,networkAddress:null,networkIp:null,thingsModelValue:null,longitude:null,latitude:null,activeTime:null,delFlag:null,createBy:null,imgUrl:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeActiveTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.deviceId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加设备"},handleUpdate:function(e){var t=this;this.reset();var a=e.deviceId||this.ids;Object(r["e"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改设备"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deviceId?Object(r["o"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.deviceId||this.ids;this.$modal.confirm('是否确认删除设备编号为"'+a+'"的数据项？').then((function(){return Object(r["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},o=s,l=a("2877"),c=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=c.exports},"9b9c":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"g",(function(){return r})),a.d(t,"e",(function(){return s})),a.d(t,"a",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"d",(function(){return c})),a.d(t,"b",(function(){return d})),a.d(t,"c",(function(){return u})),a.d(t,"h",(function(){return m}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/product/list",method:"get",params:e})}function r(){return Object(i["a"])({url:"/iot/product/shortList",method:"get"})}function s(e){return Object(i["a"])({url:"/iot/product/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/product",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/product",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/product/status/",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/product/"+e,method:"delete"})}function m(e){return Object(i["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:e})}},a035:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/deviceLog/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/deviceLog/history",method:"get",params:e})}},a726:function(e,t,a){},a824:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"d",(function(){return c}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/salve/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/salve/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/salve",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/salve",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/iot/salve/"+e,method:"delete"})}function c(e){return Object(i["a"])({url:"/iot/salve/listByPId",method:"get",params:e})}},b52e:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:share"],expression:"['iot:device:share']"}],attrs:{type:"primary",plain:"",icon:"el-icon-share",size:"mini"},on:{click:e.shareDevice}},[e._v("分享设备")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceUserList,size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"用户编号",align:"center",prop:"userId",width:"100"}}),a("el-table-column",{attrs:{label:"用户名称",align:"center",prop:"userName"}}),a("el-table-column",{attrs:{label:"手机号码",align:"center",prop:"phonenumber",width:"150"}}),a("el-table-column",{attrs:{label:"用户类型",align:"center",prop:"isOwner",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isOwner?a("el-tag",{attrs:{type:"primary"}},[e._v("主人")]):a("el-tag",{attrs:{type:"success"}},[e._v("分享")])]}}])}),a("el-table-column",{attrs:{label:"分享时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"left",prop:"remark","header-align":"center","min-width":"150"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:share"],expression:"['iot:device:share']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("编辑")]):e._e(),0==t.row.isOwner?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:share"],expression:"['iot:device:share']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("取消分享")]):e._e()]}}])})],1),a("el-dialog",{attrs:{title:"设备分享",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-50px"}},[a("el-divider")],1),1==e.type?a("el-form",{ref:"queryForm",attrs:{model:e.permParams,rules:e.rules,inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{type:"text",placeholder:"请输入用户手机号码",minlength:"10",clearable:"",size:"small","show-word-limit":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.permParams.phonenumber,callback:function(t){e.$set(e.permParams,"phonenumber",t)},expression:"permParams.phonenumber"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.userQuery}},[e._v("查询用户")])],1)],1):e._e(),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.permsLoading,expression:"permsLoading"}],staticStyle:{"background-color":"#f8f8f9","line-height":"28px"}},[e.message?a("div",{staticStyle:{padding:"20px"}},[e._v(e._s(e.message))]):e._e(),e.form.userId?a("div",{staticStyle:{padding:"15px"}},[a("div",{staticStyle:{"font-weight":"bold","line-height":"28px"}},[e._v("用户信息")]),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户ID：")]),a("span",[e._v(e._s(e.form.userId))]),a("br"),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("手机号码：")]),a("span",[e._v(e._s(e.form.phonenumber))]),a("br"),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户名称：")]),a("span",[e._v(e._s(e.form.userName))]),a("br"),a("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("设置用户权限")]),a("el-table",{ref:"multipleTable",attrs:{data:e.sharePermissionList,"highlight-current-row":"",size:"mini"},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{key:"modelName",attrs:{label:"权限名称",align:"center",prop:"modelName"}}),a("el-table-column",{key:"identifier",attrs:{label:"权限标识",align:"center",prop:"identifier"}}),a("el-table-column",{key:"remark",attrs:{label:"备注信息",align:"left","min-width":"100","header-align":"center",prop:"remark"}})],1),a("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("备注信息")]),a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",rows:"2"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1):e._e()]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",disabled:!e.form.userId||!e.deviceInfo.deviceId},on:{click:e.submitForm}},[e._v("确定")]),a("el-button",{on:{click:e.closeSelectUser}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("c7eb"),s=a("1da1"),o=a("ade3"),l=(a("99af"),a("a15b"),a("d81d"),a("2544")),c=a("01ca"),d={name:"device-user",dicts:["iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceId=this.deviceInfo.deviceId,this.getList())}},data:function(){var e;return e={type:1,message:"",permsLoading:!1,sharePermissionList:[],deviceInfo:{},open:!1,permParams:{userName:void 0,phonenumber:void 0,deviceId:null},rules:{phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{min:11,max:11,message:"手机号码长度为11位",trigger:"blur"}]},loading:!0,total:0,deviceUserList:[]},Object(o["a"])(e,"deviceInfo",{}),Object(o["a"])(e,"queryParams",{pageNum:1,pageSize:10,deviceName:null,userName:null,userId:null,tenantName:null,isOwner:null}),Object(o["a"])(e,"form",{}),e},created:function(){this.queryParams.deviceId=this.device.deviceId,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.deviceUserList=t.rows,e.total=t.total,e.loading=!1}))},reset:function(){this.form={deviceId:null,userId:null,deviceName:null,userName:null,perms:null,phonenumber:null,remark:null},this.sharePermissionList=[],this.message="",this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset(),this.type=2,Object(l["d"])(e.deviceId,e.userId).then((function(e){t.form=e.data,t.getPermissionList(),t.open=!0}))},shareDevice:function(){this.type=1,this.open=!0},handleDelete:function(e){var t=this,a=e;this.$modal.confirm("确认取消分享设备？").then((function(){return Object(l["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("取消分享成功")})).catch((function(){}))},userQuery:function(){var e=this;this.$refs["queryForm"].validate((function(t){t&&(e.reset(),e.getShareUser())}))},getShareUser:function(){var e=this;this.permsLoading=!0,this.deviceInfo.deviceId?(this.permParams.deviceId=this.deviceInfo.deviceId,Object(l["f"])(this.permParams).then((function(t){t.data?(e.form=t.data,e.getPermissionList()):(e.permsLoading=!1,e.message="查询不到用户信息，或者该用户已经是设备用户")}))):this.$modal.alert("查询不到设备信息，请刷新后重试")},getPermissionList:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=[],e.form.perms&&(a=e.form.perms.split(",")),console.log("deviceInfo",e.deviceInfo),Object(c["g"])(e.deviceInfo.productId).then((function(t){if(e.sharePermissionList=[{identifier:"ota",modelName:"设备升级",remark:"设备OTA升级"},{identifier:"timer",modelName:"设备定时",remark:"定时执行任务"},{identifier:"log",modelName:"设备日志",remark:"包含事件日志和指令日志"},{identifier:"monitor",modelName:"实时监测",remark:"下发实时监测指令后，图表实时显示设备上报数据"},{identifier:"statistic",modelName:"监测统计",remark:"图表显示存储的历史监测数据"}],e.sharePermissionList=e.sharePermissionList.concat(t.data),a.length>0)for(var i=function(t){for(var i=0;i<a.length;i++)if(e.sharePermissionList[t].identifier==a[i]){e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(e.sharePermissionList[t],!0)}));break}},n=0;n<e.sharePermissionList.length;n++)i(n);e.permsLoading=!1}));case 4:case"end":return t.stop()}}),t)})))()},resetUserQuery:function(){this.resetForm("queryForm"),this.reset()},closeSelectUser:function(){this.open=!1,this.resetUserQuery()},handleSelectionChange:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},handleSelectionAll:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},submitForm:function(){var e=this;2==this.type?Object(l["g"])(this.form).then((function(t){e.$modal.msgSuccess("更新成功"),e.resetUserQuery(),e.open=!1,e.getList()})):1==this.type&&(this.form.deviceId=this.deviceInfo.deviceId,this.form.deviceName=this.deviceInfo.deviceName,Object(l["a"])(this.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.resetUserQuery(),e.open=!1,e.getList()})))}}},u=d,m=a("2877"),p=Object(m["a"])(u,i,n,!1,null,null,null);t["default"]=p.exports},b532:function(e,t,a){"use strict";a("a726")},ba95:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/simulate/list",method:"get",params:e})}},dc9c:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/log/list",method:"get",params:e})}},dd50:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{attrs:{inline:!0,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"监测间隔(ms)"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值范围500-10000毫秒",placement:"top"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测间隔",type:"number",clearable:"",size:"small"},model:{value:e.monitorInterval,callback:function(t){e.monitorInterval=t},expression:"monitorInterval"}})],1)],1),a("el-form-item",{attrs:{label:"监测次数"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值方位1-300",placement:"top"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测次数",type:"number",clearable:"",size:"small"},model:{value:e.monitorNumber,callback:function(t){e.monitorNumber=t},expression:"monitorNumber"}})],1)],1),a("el-form-item",[a("el-button",{staticStyle:{"margin-left":"30px"},attrs:{type:"success",icon:"el-icon-video-play",size:"mini"},on:{click:function(t){return e.beginMonitor()}}},[e._v("开始监测")]),a("el-button",{attrs:{type:"danger",icon:"el-icon-video-pause",size:"mini"},on:{click:function(t){return e.stopMonitor()}}},[e._v("停止监测")])],1)],1),a("el-row",{directives:[{name:"loading",rawName:"v-loading",value:e.chartLoading,expression:"chartLoading"}],attrs:{gutter:20,"element-loading-text":"正在接收设备数据，请耐心等待......","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},e._l(e.monitorThings,(function(e,t){return a("el-col",{key:t,staticStyle:{"margin-bottom":"20px"},attrs:{span:12}},[a("el-card",{attrs:{shadow:"hover","body-style":{paddingTop:"10px",marginBottom:"-20px"}}},[a("div",{ref:"monitor",refInFor:!0,staticStyle:{height:"210px",padding:"0"}})])],1)})),1)],1)},n=[],r=(a("14d9"),a("b0c0"),a("d3b7"),a("25f0"),{name:"DeviceMonitor",props:{device:{type:Object,default:null}},watch:{device:function(e,t){if(this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId){this.monitorThings=this.deviceInfo.monitorList,this.dataList=[];for(var a=0;a<this.monitorThings.length;a++)this.dataList.push({id:this.monitorThings[a].id,name:this.monitorThings[a].name,data:[]});this.$nextTick((function(){this.getMonitorChart()})),this.mqttCallback()}}},data:function(){return{monitorInterval:1e3,monitorNumber:60,chart:[],dataList:[],monitorThings:[],chartLoading:!1,deviceInfo:{}}},created:function(){},methods:{mqttPublish:function(e,t){var a=this,i="",n="";4==t.type&&(i="/"+e.productId+"/"+e.serialNumber+"/monitor/get",n='{"count":'+t.value+',"interval":'+this.monitorInterval+"}",""!=i&&this.$mqttTool.publish(i,n,t.name).then((function(e){a.$modal.notifySuccess(e)})).catch((function(e){a.$modal.notifyError(e)})))},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态】主题：",t),console.log("接收到【设备状态】内容：",a),e.deviceInfo.serialNumber==r&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi)),"monitor"==n[3])){console.log("接收到【实时监测】主题：",t),console.log("接收到【实时监测】内容：",a),e.chartLoading=!1;for(var s=0;s<a.length;s++)for(var o=a[s].value,l=a[s].id,c=(a[s].remark,0);c<e.dataList.length;c++){if(l==e.dataList[c].id){e.dataList[c].length>50&&e.dataList[c].shift(),e.dataList[c].data.push([e.getTime(),o]),e.chart[c].setOption({series:[{data:e.dataList[c].data}]});break}if(0==e.dataList[c].id.indexOf("array_")){var d=e.dataList[c].id.substring(6,8),u=e.dataList[c].id.substring(9);if(u==l){var m=o.split(",");e.dataList[c].length>50&&e.dataList[c].shift(),e.dataList[c].data.push([e.getTime(),m[d]]),e.chart[c].setOption({series:[{data:e.dataList[c].data}]});break}}}}}))},beginMonitor:function(){if(3==this.deviceInfo.status){for(var e=0;e<this.dataList.length;e++)this.dataList[e].data=[];(this.monitorInterval<500||this.monitorInterval>1e4)&&this.$modal.alertError("实时监测的间隔范围500-10000毫秒"),(0==this.monitorNumber||this.monitorNumber>300)&&this.$modal.alertError("实时监测数量范围1-300");var t={name:"更新实时监测"};t.value=this.monitorNumber,t.type=4,this.mqttPublish(this.deviceInfo,t),this.chartLoading=!0}else this.$modal.alertError("设备不在线，下发指令失败")},stopMonitor:function(){if(3==this.deviceInfo.status){this.chartLoading=!1;var e={name:"关闭实时监测",value:0,type:4};this.mqttPublish(this.deviceInfo,e)}else this.$modal.alertError("设备不在线，下发指令失败")},getMonitorChart:function(){for(var e=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],t=0;t<this.monitorThings.length;t++){var a;this.$refs.monitor[t].style.width=document.documentElement.clientWidth/2-255+"px",this.chart[t]=this.$echarts.init(this.$refs.monitor[t]),a={title:{left:"center",text:this.monitorThings[t].name+" （单位 "+(void 0!=this.monitorThings[t].datatype.unit?this.monitorThings[t].datatype.unit:"无")+"）",textStyle:{fontSize:14}},grid:{top:"50px",left:"20px",right:"20px",bottom:"10px",containLabel:!0},tooltip:{trigger:"axis",axisPointer:{animation:!0}},xAxis:{type:"time",show:!1,splitLine:{show:!1}},yAxis:{type:"value",boundaryGap:[0,"100%"],splitLine:{show:!0}},series:[{name:this.monitorThings[t].name,type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:t>9?e[0]:e[t]},areaStyle:{},data:[]}]},a&&this.chart[t].setOption(a)}},getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate(),n=e.getHours(),r=e.getMinutes(),s=e.getSeconds();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,t+"-"+a+"-"+i+" "+n+":"+r+":"+s}}}),s=r,o=a("2877"),l=Object(o["a"])(s,i,n,!1,null,null,null);t["default"]=l.exports},df78:function(e,t,a){},df7b:function(e,t,a){"use strict";a("df78")},e2de:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"k",(function(){return l})),a.d(t,"f",(function(){return c})),a.d(t,"b",(function(){return d})),a.d(t,"g",(function(){return u})),a.d(t,"h",(function(){return m})),a.d(t,"i",(function(){return p})),a.d(t,"j",(function(){return h}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/channel/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"get"})}function s(e,t){return Object(i["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(i["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function c(e,t,a){return Object(i["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:a})}function d(e,t){return Object(i["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function u(e,t,a){return Object(i["a"])({url:"/sip/player/playbackPause/"+e+"/"+t+"/"+a,method:"get"})}function m(e,t,a){return Object(i["a"])({url:"/sip/player/playbackReplay/"+e+"/"+t+"/"+a,method:"get"})}function p(e,t,a,n){return Object(i["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+a,method:"get",params:n})}function h(e,t,a,n){return Object(i["a"])({url:"/sip/player/playbackSpeed/"+e+"/"+t+"/"+a,method:"get",params:n})}},e51f:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"选择产品",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"产品名称",prop:"productName"}},[a("el-input",{attrs:{placeholder:"请输入产品名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.productList,"highlight-current-row":"",size:"mini"},on:{"row-click":e.rowClick}},[a("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName"}}),a("el-table-column",{attrs:{label:"分类名称",align:"center",prop:"categoryName"}}),a("el-table-column",{attrs:{label:"租户名称",align:"center",prop:"tenantName"}}),a("el-table-column",{attrs:{label:"授权码",align:"center",prop:"status",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isAuthorize?a("el-tag",{attrs:{type:"success"}},[e._v("启用")]):e._e(),0==t.row.isAuthorize?a("el-tag",{attrs:{type:"info"}},[e._v("未启用")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"认证方式",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_vertificate_method,value:t.row.vertificateMethod}})]}}])}),a("el-table-column",{attrs:{label:"联网方式",align:"center",prop:"networkMethod"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_network_method,value:t.row.networkMethod}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectProduct}},[e._v("确定")]),a("el-button",{attrs:{type:"info"},on:{click:e.closeDialog}},[e._v("关 闭")])],1)])},n=[],r=(a("a9e3"),a("9b9c")),s={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,Object(r["f"])(this.queryParams).then((function(t){for(var a=0;a<t.rows.length;a++)t.rows[a].isSelect=!1;e.productList=t.rows,e.total=t.total,0!=e.productId&&e.setRadioSelected(e.productId),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.productId),this.product=e)},setRadioSelected:function(e){for(var t=0;t<this.productList.length;t++)this.productList[t].productId==e?this.productList[t].isSelect=!0:this.productList[t].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},o=s,l=a("2877"),c=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=c.exports},e626:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticStyle:{margin:"6px","padding-bottom":"100px"}},[a("el-tabs",{staticStyle:{padding:"10px","min-height":"400px"},attrs:{"tab-position":"left"},on:{"tab-click":e.tabChange},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{name:"basic"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("* 基本信息")]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",{attrs:{gutter:100}},[a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[a("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}},[0!=e.form.deviceId?a("el-button",{attrs:{slot:"append"},on:{click:e.openSummaryDialog},slot:"append"},[e._v("摘要")]):e._e()],1)],1),a("el-form-item",{attrs:{label:"",prop:"productName"}},[a("template",{slot:"label"},[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" 所属产品 ")]),a("el-input",{attrs:{readonly:"",placeholder:"请选择产品",disabled:1!=e.form.status},model:{value:e.form.productName,callback:function(t){e.$set(e.form,"productName",t)},expression:"form.productName"}},[a("el-button",{attrs:{slot:"append",disabled:1!=e.form.status},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v("选择")])],1)],2),a("el-form-item",{attrs:{label:"",prop:"serialNumber"}},[a("template",{slot:"label"},[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" 设备编号 ")]),a("el-input",{attrs:{placeholder:"请输入设备编号",disabled:1!=e.form.status,maxlength:"32"},model:{value:e.form.serialNumber,callback:function(t){e.$set(e.form,"serialNumber",t)},expression:"form.serialNumber"}},[3!==e.form.deviceType?a("el-button",{attrs:{slot:"append",loading:e.genDisabled,disabled:1!=e.form.status},on:{click:e.generateNum},slot:"append"},[e._v("生成")]):e._e(),3===e.form.deviceType?a("el-button",{attrs:{slot:"append",disabled:1!=e.form.status},on:{click:function(t){return e.genSipID()}},slot:"append"},[e._v("生成")]):e._e()],1)],2),e.openTip?a("el-form-item",[[a("el-alert",{attrs:{type:"success","show-icon":"",description:"当前选择的产品属于modbus协议,将在网关设备创建后根据采集点模板生成子设备"}})]],2):e._e(),a("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[a("el-input",{attrs:{placeholder:"请输入固件版本",type:"number",step:"0.1",disabled:1!=e.form.status||3===e.form.deviceType},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}},[a("template",{slot:"prepend"},[e._v("Version")])],2)],1),a("el-form-item",{attrs:{label:"模拟设备",prop:"isSimulate"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0,disabled:3===e.form.deviceType},model:{value:e.form.isSimulate,callback:function(t){e.$set(e.form,"isSimulate",t)},expression:"form.isSimulate"}})],1),a("el-form-item",{attrs:{label:"设备影子",prop:"isShadow"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0,disabled:3===e.form.deviceType},model:{value:e.form.isShadow,callback:function(t){e.$set(e.form,"isShadow",t)},expression:"form.isShadow"}})],1),a("el-form-item",{attrs:{label:"禁用设备",prop:"deviceStatus"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"",disabled:1==e.form.status||3===e.form.deviceType,"active-value":1,"inactive-value":0,"active-color":"#F56C6C"},model:{value:e.deviceStatus,callback:function(t){e.deviceStatus=t},expression:"deviceStatus"}})],1),a("el-form-item",{attrs:{label:"备注信息",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",rows:"1"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"定位方式",prop:"locationWay"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择设备状态",clearable:"",size:"small",disabled:3===e.form.deviceType},model:{value:e.form.locationWay,callback:function(t){e.$set(e.form,"locationWay",t)},expression:"form.locationWay"}},e._l(e.dict.type.iot_location_way,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:Number(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"设备经度",prop:"longitude"}},[a("el-input",{attrs:{placeholder:"请输入设备经度",type:"number",disabled:3!=e.form.locationWay},model:{value:e.form.longitude,callback:function(t){e.$set(e.form,"longitude",t)},expression:"form.longitude"}},[a("el-link",{attrs:{slot:"append",underline:!1,href:"https://api.map.baidu.com/lbsapi/getpoint/index.html",target:"_blank",disabled:3!=e.form.locationWay},slot:"append"},[e._v("坐标拾取")])],1)],1),a("el-form-item",{attrs:{label:"设备纬度",prop:"latitude"}},[a("el-input",{attrs:{placeholder:"请输入设备纬度",type:"number",disabled:3!=e.form.locationWay},model:{value:e.form.latitude,callback:function(t){e.$set(e.form,"latitude",t)},expression:"form.latitude"}},[a("el-link",{attrs:{slot:"append",underline:!1,href:"https://api.map.baidu.com/lbsapi/getpoint/index.html",target:"_blank",disabled:3!=e.form.locationWay},slot:"append"},[e._v("坐标拾取")])],1)],1),a("el-form-item",{attrs:{label:"所在地址",prop:"networkAddress"}},[a("el-input",{attrs:{placeholder:"请输入设备所在地址",disabled:3!=e.form.locationWay},model:{value:e.form.networkAddress,callback:function(t){e.$set(e.form,"networkAddress",t)},expression:"form.networkAddress"}})],1),a("el-form-item",{attrs:{label:"入网地址",prop:"networkIp"}},[a("el-input",{attrs:{placeholder:"设备入网IP",disabled:""},model:{value:e.form.networkIp,callback:function(t){e.$set(e.form,"networkIp",t)},expression:"form.networkIp"}})],1),a("el-form-item",{attrs:{label:"激活时间",prop:"activeTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"设备激活时间",disabled:""},model:{value:e.form.activeTime,callback:function(t){e.$set(e.form,"activeTime",t)},expression:"form.activeTime"}})],1),a("el-form-item",{attrs:{label:"设备信号",prop:"rssi"}},[a("el-input",{attrs:{placeholder:"设备信号强度",disabled:""},model:{value:e.form.rssi,callback:function(t){e.$set(e.form,"rssi",t)},expression:"form.rssi"}})],1),0!=e.form.deviceId?a("el-form-item",{attrs:{label:"其他信息",prop:"remark"}},[a("dict-tag",{staticStyle:{display:"inline-block","margin-right":"8px"},attrs:{options:e.dict.type.iot_device_status,value:e.form.status}}),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handleViewMqtt()}}},[e._v("认证信息")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.openCodeDialog()}}},[e._v("二维码")])],1):e._e()],1),0!=e.form.deviceId?a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("div",{staticStyle:{border:"1px solid #dfe4ed","border-radius":"5px",padding:"5px","text-align":"center","line-height":"400px"}},[a("div",{staticStyle:{height:"435px",width:"100%"},attrs:{id:"map"}},[e._v("地图展示区域，新增后显示")])])]):e._e()],1)],1),a("el-form",{staticStyle:{"margin-top":"50px"},attrs:{"label-width":"100px"}},[a("el-form-item",{staticStyle:{"text-align":"center","margin-left":"-100px","margin-top":"10px"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:edit"],expression:"['iot:device:edit']"},{name:"show",rawName:"v-show",value:0!=e.form.deviceId,expression:"form.deviceId != 0"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("修 改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"},{name:"show",rawName:"v-show",value:0==e.form.deviceId,expression:"form.deviceId == 0"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("新 增")])],1)],1),a("product-list",{ref:"productList",attrs:{productId:e.form.productId},on:{productEvent:function(t){return e.getProductData(t)}}}),a("sipid",{ref:"sipidGen",attrs:{product:e.form},on:{addGenEvent:function(t){return e.getSipIDData(t)}}})],1),a("el-tab-pane",{attrs:{name:"runningStatus",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("运行状态")]),e.isSubDev?a("real-time-status",{ref:"realTimeStatus",attrs:{device:e.form},on:{statusEvent:function(t){return e.getDeviceStatusData(t)}}}):a("running-status",{ref:"runningStatus",attrs:{device:e.form},on:{statusEvent:function(t){return e.getDeviceStatusData(t)}}})],1),e.isSubDev&&3!==e.form.deviceType?a("el-tab-pane",{attrs:{name:"deviceSub",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("子设备")]),a("device-sub",{ref:"deviceSub",attrs:{device:e.form}})],1):e._e(),3===e.form.deviceType?a("el-tab-pane",{attrs:{name:"sipChannel",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备通道")]),a("channel",{ref:"deviceChannel",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("timer")?a("el-tab-pane",{attrs:{name:"deviceTimer",disabled:0==e.form.deviceId,lazy:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备定时")]),a("device-timer",{ref:"deviceTimer",attrs:{device:e.form}})],1):e._e(),a("el-tab-pane",{attrs:{name:"deviceUser",disabled:0==e.form.deviceId,lazy:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备用户")]),a("device-user",{ref:"deviceUser",attrs:{device:e.form},on:{userEvent:function(t){return e.getUserData(t)}}})],1),a("el-tab-pane",{attrs:{name:"deviceLog",disabled:0==e.form.deviceId&&e.hasShrarePerm("log"),lazy:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("事件日志")]),a("device-log",{ref:"deviceLog",attrs:{device:e.form}})],1),3!==e.form.deviceType&&e.hasShrarePerm("log")?a("el-tab-pane",{attrs:{name:"deviceFuncLog",disabled:0==e.form.deviceId,lazy:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("指令日志")]),a("device-func",{ref:"deviceFuncLog",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("monitor")?a("el-tab-pane",{attrs:{name:"deviceMonitor",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("实时监测")]),a("device-monitor",{ref:"deviceMonitor",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("statistic")?a("el-tab-pane",{attrs:{name:"deviceStastic",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("监测统计")]),a("device-statistic",{ref:"deviceStatistic",attrs:{device:e.form}})],1):e._e(),a("el-tab-pane",{attrs:{disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-top":"200px"}})])]),a("el-tab-pane",{attrs:{name:"device05",disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(t){return e.goBack()}}},[e._v("返回列表")])],1)])],1),a("el-dialog",{attrs:{title:"摘要（设备上传的只读数据）",visible:e.openSummary,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openSummary=t}}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:14}},[a("div",{staticStyle:{border:"1px solid #ccc","margin-top":"-15px",height:"350px",width:"360px",overflow:"scroll"}},[a("json-viewer",{attrs:{value:e.summary,"expand-depth":10,copyable:""},scopedSlots:e._u([{key:"copy",fn:function(){return[e._v("复制")]},proxy:!0}])})],1)]),a("el-col",{attrs:{span:10}},[a("div",{staticStyle:{border:"1px solid #ccc",width:"200px","text-align":"center","margin-left":"20px","margin-top":"-10px"}},[a("vue-qr",{attrs:{text:e.qrText,size:200}}),a("div",{staticStyle:{"padding-bottom":"10px"}},[e._v("设备二维码")])],1)])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"info"},on:{click:e.closeSummaryDialog}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{visible:e.openCode,width:"300px","append-to-body":""},on:{"update:visible":function(t){e.openCode=t}}},[a("div",{staticStyle:{border:"1px solid #ccc",width:"220px","text-align":"center",margin:"0 auto","margin-top":"-15px"}},[a("vue-qr",{attrs:{text:e.qrText,size:200}}),a("div",{staticStyle:{"padding-bottom":"10px"}},[e._v("设备二维码")])],1)]),a("el-dialog",{attrs:{title:"Mqtt连接参数",visible:e.openViewMqtt,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openViewMqtt=t}}},[a("el-form",{ref:"listQuery",attrs:{model:e.listQuery,rules:e.rules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"clientId",prop:"clientId"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:""},model:{value:e.listQuery.clientId,callback:function(t){e.$set(e.listQuery,"clientId",t)},expression:"listQuery.clientId"}})],1),a("el-form-item",{attrs:{label:"username",prop:"username"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:""},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}})],1),a("el-form-item",{attrs:{label:"passwd",prop:"passwd"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{clearable:"",disabled:""},model:{value:e.listQuery.passwd,callback:function(t){e.$set(e.listQuery,"passwd",t)},expression:"listQuery.passwd"}})],1),a("el-form-item",{attrs:{label:"port",prop:"port"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{clearable:"",disabled:""},model:{value:e.listQuery.port,callback:function(t){e.$set(e.listQuery,"port",t)},expression:"listQuery.port"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"btns",attrs:{type:"primary"},on:{click:function(t){return e.doCopy(2)}}},[e._v("一键复制")]),a("el-button",{on:{click:e.closeSummaryDialog}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("c7eb"),s=a("1da1"),o=(a("d3b7"),a("25f0"),a("8a79"),a("14d9"),a("b0c0"),a("a9e3"),a("a434"),a("ac1f"),a("00b4"),a("e9c4"),a("d81d"),a("349e")),l=a.n(o),c=(a("0b22"),a("e51f")),d=a("7168"),u=a("b52e"),m=a("5f43"),p=a("dd50"),h=a("f14e"),f=a("7a72"),v=a("67dd"),y=a("9467"),b=a("1c4f"),g=a("9626"),w=a("5daf"),x=a("658f5"),_=a.n(x),I=a("09cb"),k=a("584f"),S=a("01ca"),M=(a("f5a7"),a("38da")),N={name:"DeviceEdit",dicts:["iot_device_status","iot_location_way"],components:{RealTimeStatus:w["default"],DeviceFunc:b["default"],deviceLog:d["default"],deviceUser:u["default"],deviceMonitor:p["default"],deviceStatistic:h["default"],runningStatus:m["default"],productList:c["default"],deviceTimer:f["default"],deviceFuncLog:b["default"],deviceSub:g["default"],JsonViewer:l.a,vueQr:_.a,channel:v["default"],sipid:y["default"]},watch:{activeName:function(e){"deviceStastic"==e&&this.$nextTick((function(){}))}},computed:{deviceStatus:{set:function(e){this.form.status=1==e?2:0==e?4:this.oldDeviceStatus},get:function(){return 2==this.form.status?1:0}}},data:function(){return{qrText:"fastbee",openSummary:!1,openCode:!1,openViewMqtt:!1,genDisabled:!1,activeName:"basic",mqttList:[],loading:!0,oldDeviceStatus:null,form:{productId:0,status:1,locationWay:1,firmwareVersion:1,serialNumber:"",deviceType:1,isSimulate:0},listQuery:{clientId:0,username:"",passwd:"",port:""},openTip:!1,isSubDev:!1,summary:[],baseUrl:"/prod-api",map:null,mk:null,latitude:"",longitude:"",rules:{deviceName:[{required:!0,message:"设备名称不能为空",trigger:"blur"},{min:2,max:32,message:"设备名称长度在 2 到 32 个字符",trigger:"blur"}],firmwareVersion:[{required:!0,message:"固件版本不能为空",trigger:"blur"}]},isMediaDevice:!1}},created:function(){var e=this.$route.query.activeName;null!=e&&""!=e&&(this.activeName=e),this.form.deviceId=this.$route.query&&this.$route.query.deviceId,0!=this.form.deviceId&&(this.connectMqtt(),this.getDevice(this.form.deviceId)),this.isSubDev=1==this.$route.query.isSubDev},activated:function(){var e=this.$route.query.activeName;null!=e&&""!=e&&(this.activeName=e)},destroyed:function(){this.mqttUnSubscribe(this.form)},methods:{connectMqtt:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.$mqttTool.client){t.next=3;break}return t.next=3,e.$mqttTool.connect(e.vuex_token);case 3:e.mqttCallback();case 4:case"end":return t.stop()}}),t)})))()},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);a=JSON.parse(a.toString()),a&&("status"!=n[3]&&"status"!=n[2]||(console.log("接收到【设备状态-详情】主题：",t),console.log("接收到【设备状态-详情】内容：",a),e.form.serialNumber==r&&(e.oldDeviceStatus=a.status,e.form.status=a.status,e.form.isShadow=a.isShadow,e.form.rssid=a.rssid)),e.isSubDev&&t.endsWith("ws/service")&&e.$busEvent.$emit("updateData",{serialNumber:n[2],productId:e.form.productId,data:a}),t.endsWith("ws/post/simulate")&&e.$busEvent.$emit("logData",{serialNumber:n[1],productId:e.form.productId,data:a}))}))},mqttSubscribe:function(e){var t="/"+e.productId+"/"+e.serialNumber+"/status/post",a=(e.productId,e.serialNumber,"/"+e.productId+"/"+e.serialNumber+"/function/post"),i="/"+e.productId+"/"+e.serialNumber+"/monitor/post",n="/"+e.productId+"/"+e.serialNumber+"/service/reply",r=[],s="/"+e.productId+"/"+e.serialNumber+"/ws/service";r.push(s),r.push(t),r.push(a),r.push(i),r.push(n),this.isSubDev,this.$mqttTool.subscribe(r)},mqttUnSubscribe:function(e){var t="/"+e.productId+"/"+e.serialNumber+"/status/post",a=(e.productId,e.serialNumber,"/"+e.productId+"/"+e.serialNumber+"/function/post"),i="/"+e.productId+"/"+e.serialNumber+"/monitor/post",n="/"+e.productId+"/"+e.serialNumber+"/service/reply",r=[],s="/"+e.productId+"/"+e.serialNumber+"/ws/service";r.push(s),r.push(t),r.push(a),r.push(i),r.push(n),this.isSubDev,this.$mqttTool.unsubscribe(r)},getDeviceStatusData:function(e){this.form.status=e},tabChange:function(e){var t=this;this.$nextTick((function(){"deviceStastic"==e.name&&t.$refs.deviceStatistic.getListHistory()}))},deviceSynchronization:function(){var e=this;Object(k["c"])(this.form.serialNumber).then(function(){var t=Object(s["a"])(Object(r["a"])().mark((function t(a){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getCacheThingsModdel(a.data.productId);case 2:return a.data.cacheThingsModel=t.sent,t.next=5,e.getDeviceStatus(e.form);case 5:a.data.thingsModels=t.sent,e.formatThingsModel(a.data),e.form=a.data,e.activeName="runningStatus",e.oldDeviceStatus=e.form.status,e.loadMap();case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getDevice:function(e){var t=this;Object(k["e"])(e).then(function(){var a=Object(s["a"])(Object(r["a"])().mark((function a(i){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i.data.userPerms=[],0==i.data.isOwner?getDeviceUser(e,t.$store.state.user.userId).then((function(e){i.data.userPerms=e.data.perms.split(","),t.getDeviceStatusWitchThingsModel(i)})):t.getDeviceStatusWitchThingsModel(i);case 2:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}())},hasShrarePerm:function(e){return 0!=this.form.isOwner||-1!=this.form.userPerms.indexOf(e)},getCacheThingsModdel:function(e){return new Promise((function(t,a){Object(S["b"])(e).then((function(e){t(JSON.parse(e.data))})).catch((function(e){a(e)}))}))},getDeviceStatus:function(e){var t={deviceId:e.deviceId,slaveId:e.slaveId};return new Promise((function(e,a){Object(k["g"])(t).then((function(t){e(t.data.thingsModels)})).catch((function(e){a(e)}))}))},formatThingsModel:function(e){e.chartList=[],e.monitorList=[],e.staticList=[];for(var t=0;t<e.thingsModels.length;t++)if("integer"!=e.thingsModels[t].datatype.type&&"decimal"!=e.thingsModels[t].datatype.type||(""==e.thingsModels[t].shadow?e.thingsModels[t].shadow=Number(e.thingsModels[t].datatype.min):e.thingsModels[t].shadow=Number(e.thingsModels[t].shadow)),"array"==e.thingsModels[t].datatype.type)if("object"==e.thingsModels[t].datatype.arrayType)for(var a=0;a<e.thingsModels[t].datatype.arrayParams.length;a++)for(var i=0;i<e.thingsModels[t].datatype.arrayParams[a].length;i++){var n=a>9?String(a):"0"+a,r="array_"+n+"_";e.thingsModels[t].datatype.arrayParams[a][i].id=r+e.thingsModels[t].datatype.arrayParams[a][i].id,1==e.thingsModels[t].datatype.arrayParams[a][i].isChart&&(e.thingsModels[t].datatype.arrayParams[a][i].name="["+e.thingsModels[t].name+(a+1)+"] "+e.thingsModels[t].datatype.arrayParams[a][i].name,e.thingsModels[t].datatype.arrayParams[a][i].datatype.arrayType="object",e.chartList.push(e.thingsModels[t].datatype.arrayParams[a][i]),1==e.thingsModels[t].datatype.arrayParams[a][i].isHistory&&e.staticList.push(e.thingsModels[t].datatype.arrayParams[a][i]),1==e.thingsModels[t].datatype.arrayParams[a][i].isMonitor&&e.monitorList.push(e.thingsModels[t].datatype.arrayParams[a][i]),e.thingsModels[t].datatype.arrayParams[a].splice(i--,1))}else for(var s=""!=e.thingsModels[t].value?e.thingsModels[t].value.split(","):[],o=""!=e.thingsModels[t].shadow?e.thingsModels[t].shadow.split(","):[],l=0;l<e.thingsModels[t].datatype.arrayCount;l++){e.thingsModels[t].datatype.arrayModel||(e.thingsModels[t].datatype.arrayModel=[]);var c=l>9?String(l):"0"+l,d="array_"+c+"_";e.thingsModels[t].datatype.arrayModel[l]={id:d+e.thingsModels[t].id,name:e.thingsModels[t].name,type:e.thingsModels[t].type,isReadonly:e.thingsModels[t].isReadonly,value:s[l]?s[l]:"",shadow:o[l]?o[l]:""}}else if("object"==e.thingsModels[t].datatype.type)for(var u=0;u<e.thingsModels[t].datatype.params.length;u++)1==e.thingsModels[t].datatype.params[u].isChart&&(e.thingsModels[t].datatype.params[u].name="["+e.thingsModels[t].name+"] "+e.thingsModels[t].datatype.params[u].name,e.chartList.push(e.thingsModels[t].datatype.params[u]),1==e.thingsModels[t].datatype.params[u].isHistory&&e.staticList.push(e.thingsModels[t].datatype.params[u]),1==e.thingsModels[t].datatype.params[u].isMonitor&&e.monitorList.push(e.thingsModels[t].datatype.params[u]),e.thingsModels[t].datatype.params.splice(u--,1));else 1==e.thingsModels[t].isChart&&(e.chartList.push(e.thingsModels[t]),1==e.thingsModels[t].isHistory&&e.staticList.push(e.thingsModels[t]),1==e.thingsModels[t].isMonitor&&e.monitorList.push(e.thingsModels[t]),e.thingsModels.splice(t--,1))},loadMap:function(){var e=this;this.$nextTick((function(){Object(I["a"])().then((function(){e.getmap()}))}))},goBack:function(){var e={path:"/iot/device",query:{t:Date.now(),pageNum:this.$route.query.pageNum}};this.$tab.closeOpenPage(e),this.reset()},reset:function(){this.form={deviceId:0,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:"",firmwareVersion:1,status:1,rssi:null,networkAddress:null,networkIp:null,longitude:null,latitude:null,activeTime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,locationWay:1,clientId:0},this.deviceStatus=0,this.resetForm("form")},submitForm:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.form.serialNumber&&0!=e.form.serialNumber){t.next=3;break}return e.$modal.alertError("设备编号不能为空"),t.abrupt("return");case 3:if(a=/^[0-9a-zA-Z]+$/,a.test(e.form.serialNumber)){t.next=7;break}return e.$modal.alertError("设备编号只能是字母和数字"),t.abrupt("return");case 7:if(null!=e.form.productId&&0!=e.form.productId){t.next=10;break}return e.$modal.alertError("所属产品不能为空"),t.abrupt("return");case 10:e.$refs["form"].validate((function(t){t&&(0!=e.form.deviceId?Object(k["o"])(e.form).then((function(t){0==t.data?e.$modal.alertError(t.msg):(e.$modal.alertSuccess("修改成功"),e.form=JSON.parse(JSON.stringify(e.form)),e.loadMap())})):Object(k["a"])(e.form).then(function(){var t=Object(s["a"])(Object(r["a"])().mark((function t(a){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDeviceStatusWitchThingsModel(a);case 2:null==e.form.deviceId||0==e.form.deviceId?e.$modal.alertError("设备编号已经存在，添加设备失败"):(2==e.form.status&&(e.deviceStatus=1),e.$modal.alertSuccess("添加设备成功"),e.loadMap());case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()))}));case 11:case"end":return t.stop()}}),t)})))()},getDeviceStatusWitchThingsModel:function(e){var t=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var i;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.getCacheThingsModdel(e.data.productId);case 2:return e.data.cacheThingsModel=a.sent,a.next=5,t.getDeviceStatus(e.data);case 5:if(e.data.thingsModels=a.sent,0==e.data.isOwner)for(i=0;i<e.data.thingsModels.length;i++)-1==e.data.userPerms.indexOf(e.data.thingsModels[i].id)&&e.data.thingsModels.splice(i--,1);t.formatThingsModel(e.data),t.form=e.data,null!=t.form.summary&&""!=t.form.summary&&(t.summary=JSON.parse(t.form.summary)),t.isSubDev=t.form.subDeviceList&&t.form.subDeviceList.length>0,t.oldDeviceStatus=t.form.status,t.loadMap(),t.connectMqtt(),t.mqttSubscribe(t.form);case 15:case"end":return a.stop()}}),a)})))()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},genSipID:function(){this.$refs.sipidGen.open=!0},getProductData:function(e){this.form.productId=e.productId,this.form.productName=e.productName,this.form.deviceType=e.deviceType,this.getDeviceTemp(),this.form.tenantId=e.tenantId,this.form.tenantName=e.tenantName},getSipIDData:function(e){this.form.serialNumber=e},getDeviceTemp:function(e){var t=this;Object(M["c"])(this.form).then((function(e){e.data&&2==t.form.deviceType?t.openTip=!0:t.openTip=!1}))},getUserData:function(e){},openSummaryDialog:function(){var e={type:1,deviceNumber:this.form.serialNumber,productId:this.form.productId};this.qrText=JSON.stringify(e),this.openSummary=!0},closeSummaryDialog:function(){this.openSummary=!1,this.openViewMqtt=!1},doCopy:function(e){if(2==e){var t=document.createElement("input");t.value="{clientId:"+this.listQuery.clientId+",username:"+this.listQuery.username+",passwd:"+this.listQuery.passwd+",port:"+this.listQuery.port+"}",document.body.appendChild(t),t.select(),document.execCommand("Copy"),document.body.removeChild(t),this.$message.success("复制成功")}},openCodeDialog:function(){var e={type:1};this.qrText=JSON.stringify(e),this.openCode=!0},getmap:function(){this.map=new BMap.Map("map");var e=null;e=null!=this.form.longitude&&""!=this.form.longitude&&null!=this.form.latitude&&""!=this.form.latitude?new BMap.Point(this.form.longitude,this.form.latitude):new BMap.Point(116.404,39.915),this.map.centerAndZoom(e,19),this.map.enableScrollWheelZoom(!0),this.map.addControl(new BMap.NavigationControl),this.mk=new BMap.Marker(e),this.map.addOverlay(this.mk),this.map.panTo(e)},generateNum:function(){var e=this;this.form.productId&&0!=this.form.productId?(this.genDisabled=!0,Object(k["d"])().then((function(t){e.form.serialNumber=t.data,e.genDisabled=!1}))):this.$modal.alertError("请先选择产品")},handleViewMqtt:function(){var e=this;this.openViewMqtt=!0,this.loading=!0;var t={deviceId:this.form.deviceId};Object(k["i"])(t).then((function(t){200==t.code&&(e.listQuery=t.data,e.loading=!1)}))}}},L=N,T=a("2877"),P=Object(T["a"])(L,i,n,!1,null,null,null);t["default"]=P.exports},ed08:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return l}));a("53ca"),a("ac1f"),a("5319"),a("14d9"),a("a15b"),a("d81d"),a("b64b"),a("d3b7"),a("159b"),a("fb6a"),a("d9e2"),a("a630"),a("3ca3"),a("6062"),a("ddb0"),a("25f0"),a("466d"),a("4d63"),a("c607"),a("2c3e"),a("00b4"),a("c38a");function i(e,t,a){var i,n,r,s,o,l=function l(){var c=+new Date-s;c<t&&c>0?i=setTimeout(l,t-c):(i=null,a||(o=e.apply(r,n),i||(r=n=null)))};return function(){for(var n=arguments.length,c=new Array(n),d=0;d<n;d++)c[d]=arguments[d];r=this,s=+new Date;var u=a&&!i;return i||(i=setTimeout(l,t)),u&&(o=e.apply(r,c),r=c=null),o}}function n(e,t){for(var a=Object.create(null),i=e.split(","),n=0;n<i.length;n++)a[i[n]]=!0;return t?function(e){return a[e.toLowerCase()]}:function(e){return a[e]}}var r="export default ",s={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function o(e){return e.replace(/( |^)[a-z]/g,(function(e){return e.toUpperCase()}))}function l(e){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(e)}},f14e:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"75px"}},[e.isSubDev?a("el-form-item",{attrs:{label:"请选择设备从机:","label-width":"120px"}},[a("el-select",{attrs:{placeholder:"请选择设备从机"},on:{change:e.selectSlave},model:{value:e.queryParams.slaveId,callback:function(t){e.$set(e.queryParams,"slaveId",t)},expression:"queryParams.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+" ("+e.slaveId+")",value:e.slaveId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.getListHistory}},[e._v("查询")])],1)],1)],1),a("el-col",{attrs:{span:23}},e._l(e.staticList,(function(t,i){return a("div",{key:i,staticStyle:{"margin-bottom":"30px"}},[a("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{shadow:"hover","body-style":{padding:"10px 0px",overflow:"auto"}}},[a("div",{ref:"statisticMap",refInFor:!0,staticStyle:{height:"300px",width:"1080px"}})])],1)})),0)],1)],1)},n=[],r=(a("4de4"),a("d3b7"),a("14d9"),a("b0c0"),a("a035")),s={name:"device-statistic",props:{device:{type:Object,default:null}},watch:{device:function(e,t){var a=this;this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.isSubDev=this.deviceInfo.subDeviceList&&this.deviceInfo.subDeviceList.length>0,this.queryParams.slaveId=this.deviceInfo.slaveId,this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.slaveList=e.subDeviceList,this.isSubDev?this.staticList=this.deviceInfo.cacheThingsModel["properties"].filter((function(e){return e.tempSlaveId==a.queryParams.slaveId})):this.staticList=this.deviceInfo.staticList,this.$nextTick((function(){this.getStatistic()})))}},data:function(){return{loading:!0,deviceInfo:{},staticList:[],chart:[],daterangeTime:[this.getTime(),this.getTime()],queryParams:{serialNumber:null,identity:"",slaveId:void 0},arrayData:[],slaveList:[],isSubDev:!1}},mounted:function(){},methods:{getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,t+"-"+a+"-"+i},getListHistory:function(){var e=this;this.loading=!0,this.queryParams.serialNumber=this.queryParams.slaveId?this.deviceInfo.serialNumber+"_"+this.queryParams.slaveId:this.deviceInfo.serialNumber,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]+" 23:59"),Object(r["b"])(this.queryParams).then((function(t){for(var a in t.data)for(var i=0;i<e.staticList.length;i++)if(a==e.staticList[i].id){for(var n=[],r=0;r<t.data[a].length;r++){var s=[];s[0]=t.data[a][r].time,s[1]=t.data[a][r].value,n.push(s)}e.chart[i].setOption({series:[{data:n}]})}e.loading=!1}))},getStatistic:function(){for(var e=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],t=0;t<this.staticList.length;t++){var a;this.$refs.statisticMap[t].style.width=document.documentElement.clientWidth-510+"px",this.chart[t]=this.$echarts.init(this.$refs.statisticMap[t]),a={animationDurationUpdate:3e3,tooltip:{trigger:"axis"},title:{left:"center",text:this.staticList[t].name+"统计 （单位 "+(this.staticList[t].datatype&&void 0!=this.staticList[t].datatype.unit?this.staticList[t].datatype.unit:"无")+"）"},grid:{top:"80px",left:"40px",right:"20px",bottom:"60px",containLabel:!0},toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{}}},xAxis:{type:"time"},yAxis:{type:"value"},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}],series:[{name:this.staticList[t].name,type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:t>9?e[0]:e[t]},areaStyle:{},data:[]}]},a&&this.chart[t].setOption(a)}},selectSlave:function(){var e=this;this.staticList=this.deviceInfo.cacheThingsModel["properties"].filter((function(t){return t.tempSlaveId==e.queryParams.slaveId})),this.$nextTick((function(){this.getStatistic(),this.getListHistory()}))}}},o=s,l=a("2877"),c=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=c.exports},f5a7:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return o}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function r(e){return Object(i["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function s(e,t,a){return Object(i["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:a})}function o(e,t,a){return Object(i["a"])({url:"/sip/ptz/scale/"+e+"/"+t,method:"post",data:a})}}}]);