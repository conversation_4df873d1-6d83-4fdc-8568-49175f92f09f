(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ad5bba6c","chunk-722c5e57","chunk-05c05b4c"],{"1ab6":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.$t("firmware.deviceList.index.222542-0"),visible:e.openDeviceList,width:"810px","append-to-body":""},on:{"update:visible":function(t){e.openDeviceList=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{prop:"deviceName"}},[r("el-input",{staticStyle:{width:"196px"},attrs:{placeholder:e.$t("firmware.deviceList.index.222542-12"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.query,callback:function(t){e.$set(e.queryParams,"query",t)},expression:"queryParams.query"}})],1),r("el-form-item",{attrs:{prop:"version"}},[r("el-input",{staticClass:"input-with-select",staticStyle:{"vertical-align":"middle",width:"318px"},attrs:{placeholder:e.$t("firmware.deviceList.index.222542-13"),clearable:"",size:"small",disabled:!e.versionType},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.version,callback:function(t){e.$set(e.queryParams,"version",t)},expression:"queryParams.version"}},[r("el-select",{staticStyle:{width:"126px"},attrs:{slot:"prepend",size:"small",placeholder:e.$t("firmware.deviceList.index.222542-14")},slot:"prepend",model:{value:e.versionType,callback:function(t){e.versionType=t},expression:"versionType"}},[r("el-option",{attrs:{label:e.$t("firmware.deviceList.index.222542-15"),value:1}}),r("el-option",{attrs:{label:e.$t("firmware.deviceList.index.222542-16"),value:2}})],1)],1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleTable",attrs:{data:e.deviceList,size:"small",border:!1},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-1"),align:"left",prop:"deviceName","min-width":"160"}}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-5"),align:"left",prop:"serialNumber","min-width":"140"}}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-15"),align:"center",prop:"firmwareVersion",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.firmwareVersion?r("span",[e._v("-")]):e._e(),r("span",[e._v("Version "+e._s(t.row.firmwareVersion))])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-16"),align:"center",prop:"wirelessVersion",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.wirelessVersion?r("span",[e._v("-")]):r("span",[e._v("Version "+e._s(t.row.wirelessVersion))])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-6"),align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?r("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("firmware.deviceList.index.222542-7")))]):r("el-tag",{attrs:{type:"primary"}},[e._v(e._s(e.$t("firmware.deviceList.index.222542-8")))])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-9"),align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleDeviceSelected}},[e._v(e._s(e.$t("confirm")))]),r("el-button",{on:{click:e.closeSelectDeviceList}},[e._v(e._s(e.$t("cancel")))])],1)],1)},a=[],n=(r("14d9"),r("a434"),r("d3b7"),r("159b"),r("584f")),o=r("ed08"),s={name:"device-list",dicts:["iot_device_status"],props:{upGrade:{type:Object,default:null}},data:function(){return{formUpGrade:{},loading:!0,ids:[],openDeviceList:!1,total:0,deviceList:[],queryParams:{query:"",pageNum:1,pageSize:10},versionType:null}},watch:{upGrade:{handler:function(e,t){e.flag&&(this.formUpGrade=e,this.queryParams.productId=this.formUpGrade.productId,this.queryParams.firmwareVersion=this.formUpGrade.firmwareVersion,this.ids=this.formUpGrade.deviceList)},immediate:!0,deep:!0},openDeviceList:function(e){e&&(this.queryParams.pageNum=1,this.resetQueryParamsHandle(),this.getList())}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0;var t=Object(o["c"])(this.queryParams);t.firmwareVersion=1===this.versionType?this.queryParams.version:null,t.wirelessVersion=2===this.versionType?this.queryParams.version:null,Object(n["o"])(t).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.forEach((function(t){e.$nextTick((function(){e.ids.some((function(e){return e===t.serialNumber}))&&e.$refs.multipleTable.toggleRowSelection(t,!0)}))}))}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleSelectionChange:function(e,t){var r=this.ids.indexOf(t.serialNumber),i=e.indexOf(t);-1==r&&-1!=i?this.ids.push(t.serialNumber):-1!=r&&-1==i&&this.ids.splice(r,1)},handleSelectionAll:function(e){for(var t=0;t<this.deviceList.length;t++){var r=this.ids.indexOf(this.deviceList[t].serialNumber),i=e.indexOf(this.deviceList[t]);-1==r&&-1!=i?this.ids.push(this.deviceList[t].serialNumber):-1!=r&&-1==i&&this.ids.splice(r,1)}},closeSelectDeviceList:function(){this.openDeviceList=!1,this.formUpGrade.flag=!1},handleDeviceSelected:function(){this.formUpGrade.deviceList=this.ids,this.formUpGrade.deviceAmount=this.ids.length,this.formUpGrade.flag=!1,this.$modal.msgSuccess(this.$t("firmware.deviceList.index.222542-10")),this.openDeviceList=!1},resetQueryParamsHandle:function(){this.queryParams.firmwareVersion=null,this.queryParams.query=null,this.versionType=null}}},l=s,u=r("2877"),c=Object(u["a"])(l,i,a,!1,null,null,null);t["default"]=c.exports},2065:function(e,t,r){"use strict";r.d(t,"e",(function(){return a})),r.d(t,"a",(function(){return n})),r.d(t,"b",(function(){return o})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return l})),r.d(t,"f",(function(){return u}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/firmware/task/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/task",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/firmware/task/"+e,method:"delete"})}function s(e){return Object(i["a"])({url:"/iot/firmware/task/deviceList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/firmware/task/deviceStatistic",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/iot/firmware/task/upgrade",method:"post",data:e})}},"584f":function(e,t,r){"use strict";r.d(t,"n",(function(){return a})),r.d(t,"t",(function(){return n})),r.d(t,"o",(function(){return o})),r.d(t,"p",(function(){return s})),r.d(t,"m",(function(){return l})),r.d(t,"f",(function(){return u})),r.d(t,"c",(function(){return c})),r.d(t,"g",(function(){return d})),r.d(t,"i",(function(){return m})),r.d(t,"d",(function(){return f})),r.d(t,"u",(function(){return p})),r.d(t,"q",(function(){return h})),r.d(t,"r",(function(){return v})),r.d(t,"h",(function(){return w})),r.d(t,"a",(function(){return g})),r.d(t,"v",(function(){return y})),r.d(t,"b",(function(){return b})),r.d(t,"e",(function(){return x})),r.d(t,"k",(function(){return $})),r.d(t,"l",(function(){return _})),r.d(t,"j",(function(){return L})),r.d(t,"s",(function(){return k}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/device/all",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function f(e,t){return Object(i["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function p(e,t){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function h(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function v(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function w(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function g(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function y(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function b(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function x(e){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:e})}function $(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function _(e){return Object(i["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function L(e){return Object(i["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function k(e){return Object(i["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},"5af9":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"iot-firmware"},[r("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"15px",width:"100%"}},[r("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-18px",padding:"3px 0 0 0"},attrs:{model:e.queryParams,inline:!0,"label-width":"78px"}},[r("el-form-item",{attrs:{prop:"firmwareName"}},[r("el-input",{attrs:{placeholder:e.$t("firmware.index.222541-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.firmwareName,callback:function(t){e.$set(e.queryParams,"firmwareName",t)},expression:"queryParams.firmwareName"}})],1),r("el-form-item",[r("el-select",{attrs:{placeholder:e.$t("firmware.index.222541-2"),clearable:"",filterable:""},on:{change:e.handleQuery},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productShortList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("div",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),r("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1)],1),r("el-card",[r("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:add"],expression:"['iot:firmware:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:e.handleAdd}},[e._v(e._s(e.$t("add")))])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:remove"],expression:"['iot:firmware:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(e._s(e.$t("del")))])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareList,border:!1},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",align:"center",width:"55"}}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-0"),align:"left",prop:"firmwareName","min-width":"210"}}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-48"),align:"center",prop:"firmwareType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.firmwareType?r("span",[e._v(e._s(e.$t("firmware.index.222541-49")))]):2==t.row.firmwareType?r("span",[e._v("HTTP")]):r("span",[e._v("-")])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-4"),align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v("Version")]),e._v(" "+e._s(t.row.version)+" ")]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-5"),align:"left",prop:"productName","min-width":"210"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{underline:!1,type:"primary"},on:{click:function(r){return e.handleViewProduct(t.row.productId)}}},[e._v(" "+e._s(t.row.productName)+" ")])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-6"),align:"left",prop:"filePath","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{href:e.getDownloadUrl(t.row.filePath),underline:!1,type:"primary"}},[e._v(" "+e._s(e.getDownloadUrl(t.row.filePath))+" ")])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-7"),align:"center",prop:"isLatest",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isLatest?r("el-tag",{attrs:{type:"primary"}},[e._v(e._s(e.$t("firmware.index.222541-8")))]):r("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("firmware.index.222541-9")))])]}}])}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-10"),align:"center",prop:"createTime",width:"185"}}),r("el-table-column",{attrs:{label:e.$t("firmware.index.222541-11"),align:"left",prop:"remark","min-width":"190"}}),r("el-table-column",{attrs:{fixed:"right",label:e.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"185"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleEdit(t.row)}}},[e._v(e._s(e.$t("update")))]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:query"],expression:"['iot:firmware:query']"}],attrs:{size:"small",type:"text",icon:"el-icon-document"},on:{click:function(r){return e.handleInfo(t.row)}}},[e._v(e._s(e.$t("detail")))]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:remove"],expression:"['iot:firmware:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("del")))])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"620px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-0"),prop:"firmwareName"}},[r("el-input",{staticStyle:{width:"400px",display:"block"},attrs:{placeholder:e.$t("firmware.index.222541-1")},model:{value:e.form.firmwareName,callback:function(t){e.$set(e.form,"firmwareName",t)},expression:"form.firmwareName"}}),r("span",{staticStyle:{"font-size":"10px",display:"block",color:"rgb(245, 108, 108)"}},[e._v(e._s(e.$t("firmware.index.222541-13")))])],1),r("el-form-item",{attrs:{label:"",prop:"productName"}},[r("template",{slot:"label"},[e._v(" "+e._s(e.$t("device.device-edit.148398-4"))+" ")]),r("el-input",{staticStyle:{width:"400px"},attrs:{readonly:"",placeholder:e.$t("device.device-edit.148398-5")},model:{value:e.form.productName,callback:function(t){e.$set(e.form,"productName",t)},expression:"form.productName"}},[r("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v(e._s(e.$t("device.device-edit.148398-6")))])],1)],2),r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-50"),prop:"firmwareType"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("firmware.index.222541-51"),disabled:""},model:{value:e.form.firmwareType,callback:function(t){e.$set(e.form,"firmwareType",t)},expression:"form.firmwareType"}},e._l(e.dict.type.iot_firmware_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-15"),prop:"version"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("firmware.index.222541-16")},on:{input:e.validateVersion},model:{value:e.form.version,callback:function(t){e.$set(e.form,"version",t)},expression:"form.version"}})],1),r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-17"),prop:"isLatest"}},[r("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.isLatest,callback:function(t){e.$set(e.form,"isLatest",t)},expression:"form.isLatest"}}),r("el-link",{staticStyle:{"font-size":"12px","margin-left":"15px"},attrs:{type:"info",underline:!1}},[e._v(e._s(e.$t("firmware.index.222541-18")))])],1),r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-19"),prop:"filePath"}},[r("fileUpload",{ref:"file-upload",staticStyle:{width:"400px"},attrs:{value:e.form.filePath,limit:1,fileSize:10,fileType:["bin","zip","pdf"]},on:{input:function(t){return e.getFilePath(t)}}})],1),r("el-form-item",{attrs:{label:e.$t("firmware.index.222541-11"),prop:"remark"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{type:"textarea",placeholder:e.$t("firmware.index.222541-21"),autosize:{minRows:3,maxRows:5}},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("save")))]),r("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1),r("deviceList",{ref:"deviceList",attrs:{upGrade:e.formUpGrade}}),r("product-list",{ref:"productList",attrs:{productId:e.form.productId,showSenior:!1},on:{productEvent:function(t){return e.getProductData(t)}}})],1)},a=[],n=r("5530"),o=(r("d81d"),r("14d9"),r("fb6a"),r("a434"),r("e9c4"),r("d3b7"),r("ac1f"),r("466d"),r("5319"),r("159b"),r("2a75")),s=r("1ab6"),l=r("814a"),u=r("2065"),c=r("584f"),d=r("5f87"),m=r("e51f"),f=r("9b9c"),p={name:"Firmware",dicts:["iot_yes_no","oat_update_limit","iot_firmware_type"],components:{fileUpload:o["a"],deviceList:s["default"],productList:m["default"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,firmwareList:[],productShortList:[],upGradeVersionList:[],group:{},title:"",open:!1,openUpGrade:!1,queryUpGradeVersion:{productId:null,version:0},queryDeviceByVersion:{productId:null,firmwareVersion:0},queryParams:{pageNum:1,pageSize:10,firmwareName:null,productId:null,tenantName:null,isSys:null},form:{version:1},rules:{firmwareName:[{required:!0,message:this.$t("firmware.index.222541-36"),trigger:"blur"}],firmwareType:[{required:!0,message:this.$t("firmware.index.222541-51"),trigger:"blur"}],productId:[{required:!0,message:this.$t("firmware.index.222541-37"),trigger:"blur"}],productName:[{required:!0,message:this.$t("firmware.index.222541-38"),trigger:"blur"}],version:[{required:!0,message:this.$t("firmware.index.222541-39"),trigger:"blur"}],filePath:[{required:!0,message:this.$t("firmware.index.222541-40"),trigger:"blur"}]},formUpGrade:{taskName:null,firmwareId:0,deviceAmount:0,bookTime:null,upgradeType:null,upType:null,deviceList:[],version:null,flag:!1},rulesUpGrade:{taskName:[{required:!0,message:this.$t("firmware.index.222541-41"),trigger:"blur"}]},upload:{isUploading:!1,headers:{Authorization:"Bearer "+Object(d["a"])()},url:"/prod-api/iot/tool/upload",fileList:[]}}},created:function(){this.getList(),this.getProductShortList()},methods:{handleViewProduct:function(e){this.$router.push({path:"/iot/product-edit",query:{t:Date.now(),productId:e}})},getProductShortList:function(){var e=this;Object(f["h"])().then((function(t){e.productShortList=t.data}))},getProductData:function(e){this.form.productId=e.productId,this.form.productName=e.productName,this.$set(this.form,"firmwareType",e.firmwareType)},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.firmwareList=t.rows,e.total=t.total,e.loading=!1}))},validateVersion:function(){var e=this.form.version.replace(/[^0-9.]/g,"");this.form.version=e;var t=(this.form.version.match(/\./g)||[]).length;t>2&&(this.form.version=this.form.version.slice(0,-1))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={firmwareId:null,firmwareName:null,productId:null,productName:null,tenantId:null,tenantName:null,isLatest:0,isSys:null,version:1,filePath:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.formUpGrade={taskName:null,firmwareId:0,deviceAmount:0,bookTime:null,upgradeType:null,deviceList:[],version:null,flag:!1},this.resetForm("form"),this.resetForm("formUpGrade")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.productId=null,this.resetForm("queryForm"),this.handleQuery()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.firmwareId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=this.$t("firmware.index.222541-42"),this.upload.fileList=[]},handleEdit:function(e){var t=this;this.reset();var r=e.firmwareId||this.ids;Object(l["c"])(r).then((function(e){t.form=e.data,t.open=!0,t.title=t.$t("firmware.index.222541-43"),t.upload.fileList=[{name:t.form.firmwareName,url:t.form.filePath}]}))},handleInfo:function(e){e&&(sessionStorage.setItem("firmwareTaskInfo",JSON.stringify(e)),this.$router.push({name:"FirmwareTask"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.firmwareId?Object(l["g"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess")),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("addSuccess")),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.firmwareId||this.ids;this.$modal.confirm(this.$t("firmware.index.222541-44",[r])).then((function(){return Object(l["b"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("delSuccess"))})).catch((function(){}))},handleExport:function(){this.download("iot/firmware/export",Object(n["a"])({},this.queryParams),"firmware_".concat((new Date).getTime(),".xlsx"))},changeUpgradeType:function(e){this.resetDeviceList(),2==e&&(this.formUpGrade.version=null)},changeUpType:function(e){var t=this;this.resetDeviceList(),"1"==e&&(this.queryDeviceByVersion.productId=this.formUpGrade.productId,this.queryDeviceByVersion.firmwareVersion=this.formUpGrade.version,Object(c["o"])(this.queryDeviceByVersion).then((function(e){e.rows.forEach((function(e){t.formUpGrade.deviceList.push(e.serialNumber)})),t.formUpGrade.deviceAmount=e.total})))},selectUpGradeVersion:function(e){this.resetDeviceList();for(var t=0;t<this.upGradeVersionList.length;t++)if(this.upGradeVersionList[t].firmwareId==e)return void(this.formUpGrade.version=this.upGradeVersionList[t].version)},resetDeviceList:function(){this.formUpGrade.upgradeType=this.formUpGrade.upgradeType,this.formUpGrade.deviceList=[],this.formUpGrade.deviceAmount=0},selectDeviceList:function(){this.formUpGrade.flag=!0,this.$refs.deviceList.openDeviceList=!0},handleClose:function(e){this.formUpGrade.deviceList.splice(this.formUpGrade.deviceList.indexOf(e),1),this.formUpGrade.deviceAmount=this.formUpGrade.deviceList.length},getFilePath:function(e){this.form.filePath=e},submitUpload:function(){this.$refs.upload.submit()},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.isUploading=!1,this.form.filePath=e.url,this.$modal.msgSuccess(e.msg)},handleDownload:function(e){window.open("/prod-api"+e.filePath)},submitFormUpGrade:function(){var e=this;this.$refs["formUpGrade"].validate((function(t){t&&(e.formUpGrade.deviceAmount>0?Object(u["a"])(e.formUpGrade).then((function(t){200==t.code?(e.$modal.msgSuccess(e.$t("updateSuccess")),e.openUpGrade=!1,e.reset()):e.$modal.msgError(res.data.message)})):e.$modal.msgError(e.$t("firmware.index.222541-47")))}))},canceUpGrade:function(){this.openUpGrade=!1,this.reset()},getUpGradeVersionList:function(){var e=this;this.queryUpGradeVersion.productId=this.form.productId,this.queryUpGradeVersion.version=this.form.version,Object(l["f"])(this.queryUpGradeVersion).then((function(t){e.upGradeVersionList=t.data}))}}},h=p,v=(r("f3ada"),r("2877")),w=Object(v["a"])(h,i,a,!1,null,"6ddf608a",null);t["default"]=w.exports},7811:function(e,t,r){},"814a":function(e,t,r){"use strict";r.d(t,"e",(function(){return a})),r.d(t,"f",(function(){return n})),r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return s})),r.d(t,"a",(function(){return l})),r.d(t,"g",(function(){return u})),r.d(t,"b",(function(){return c}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function o(e,t){return Object(i["a"])({url:"/iot/firmware/getLatest?deviceId="+e+"&firmwareType="+t,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},"9b9c":function(e,t,r){"use strict";r.d(t,"g",(function(){return a})),r.d(t,"h",(function(){return n})),r.d(t,"f",(function(){return o})),r.d(t,"a",(function(){return s})),r.d(t,"i",(function(){return l})),r.d(t,"e",(function(){return u})),r.d(t,"b",(function(){return c})),r.d(t,"d",(function(){return d})),r.d(t,"c",(function(){return m}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/product/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/product/shortList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/product/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/product",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/product",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/product/status",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/product/"+e,method:"delete"})}function m(e){return Object(i["a"])({url:"/iot/product/copy?productId="+e,method:"post"})}},e51f:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.$t("device.product-list.058448-0"),visible:e.open,width:"910px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:e.$t("device.product-list.058448-2"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.product-list.058448-3")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.product-list.058448-4")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":e.rowClick}},[r("el-table-column",{attrs:{label:e.$t("device.device-edit.148398-6"),width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-6"),align:"left",prop:"categoryName","min-width":"150"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-7"),align:"left",prop:"tenantName","min-width":"100"}}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-8"),align:"center",prop:"status",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isAuthorize?r("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("device.product-list.058448-9")))]):e._e(),0==t.row.isAuthorize?r("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("device.product-list.058448-10")))]):e._e()]}}])}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-11"),align:"center",prop:"status","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_vertificate_method,value:t.row.vertificateMethod}})]}}])}),r("el-table-column",{attrs:{label:e.$t("device.product-list.058448-12"),align:"center",prop:"networkMethod","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_network_method,value:t.row.networkMethod}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectProduct}},[e._v(e._s(e.$t("device.product-list.058448-14")))]),r("el-button",{attrs:{type:"info"},on:{click:e.closeDialog}},[e._v(e._s(e.$t("device.product-list.058448-15")))])],1)],1)},a=[],n=(r("a9e3"),r("9b9c")),o={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0},showSenior:{type:Boolean,default:!0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.showSenior=this.showSenior,Object(n["g"])(this.queryParams).then((function(t){for(var r=0;r<t.rows.length;r++)t.rows[r].isSelect=!1;e.productList=t.rows,e.total=t.total,0!=e.productId&&e.setRadioSelected(e.productId),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.productId),this.product=e)},setRadioSelected:function(e){for(var t=0;t<this.productList.length;t++)this.productList[t].productId==e?this.productList[t].isSelect=!0:this.productList[t].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},s=o,l=r("2877"),u=Object(l["a"])(s,i,a,!1,null,null,null);t["default"]=u.exports},f3ada:function(e,t,r){"use strict";r("7811")}}]);