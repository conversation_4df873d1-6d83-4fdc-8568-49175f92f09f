(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c4289"],{"3a49":function(e,t,r){"use strict";r.r(t);var l=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{inline:!0,model:e.queryParams,"label-width":"85px"}},[r("el-form-item",{attrs:{label:"第三方平台",prop:"platform"}},[r("el-select",{attrs:{clearable:"",placeholder:"请选择平台",size:"small"},model:{value:e.queryParams.platform,callback:function(t){e.$set(e.queryParams,"platform",t)},expression:"queryParams.platform"}},e._l(e.dict.type.iot_social_platform,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{clearable:"",placeholder:"请选择状态",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_social_platform_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:add"],expression:"['iot:platform:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:edit"],expression:"['iot:platform:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:remove"],expression:"['iot:platform:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:export"],expression:"['iot:platform:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.platformList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{align:"center",label:"平台名称",prop:"platform"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_social_platform,value:t.row.platform}})]}}])}),r("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_social_platform_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"平台申请ID",align:"center",prop:"clientId"}}),r("el-table-column",{attrs:{label:"跳转地址",align:"center",prop:"redirectUri",width:"180","show-overflow-tooltip":!0}}),r("el-table-column",{attrs:{align:"center",label:"绑定登录uri",prop:"bindUri","show-tooltip-when-overflow":!0,"render-header":function(t,r){return e.renderHeaderMethods(t,r,e.columnTips.bindId)}}}),r("el-table-column",{attrs:{align:"center",label:"跳转登录uri",prop:"redirectLoginUri","show-tooltip-when-overflow":!0,"render-header":function(t,r){return e.renderHeaderMethods(t,r,e.columnTips.redirectLogin)}}}),r("el-table-column",{attrs:{align:"center",label:"错误提示uri",prop:"errorMsgUri","show-tooltip-when-overflow":!0,"render-header":function(t,r){return e.renderHeaderMethods(t,r,e.columnTips.errorId)}}}),r("el-table-column",{attrs:{align:"center",label:"创建时间",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:edit"],expression:"['iot:platform:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:platform:remove"],expression:"['iot:platform:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"140px"}},[r("el-form-item",{attrs:{label:"第三方平台名称",prop:"platform"}},[r("el-select",{attrs:{placeholder:"请选择第三方平台"},model:{value:e.form.platform,callback:function(t){e.$set(e.form,"platform",t)},expression:"form.platform"}},e._l(e.dict.type.iot_social_platform,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"第三方平台状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.iot_social_platform_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"第三方平台申请ID",prop:"clientId"}},[r("el-input",{attrs:{placeholder:"请输入第三方平台申请Id"},model:{value:e.form.clientId,callback:function(t){e.$set(e.form,"clientId",t)},expression:"form.clientId"}})],1),r("el-form-item",{attrs:{label:"第三方平台密钥",prop:"secretKey"}},[r("el-input",{attrs:{placeholder:"请输入第三方平台密钥"},model:{value:e.form.secretKey,callback:function(t){e.$set(e.form,"secretKey",t)},expression:"form.secretKey"}})],1),r("el-form-item",{attrs:{label:"用户认证跳转地址",prop:"redirectUri"}},[r("el-input",{attrs:{placeholder:"请输入用户认证后跳转地址"},model:{value:e.form.redirectUri,callback:function(t){e.$set(e.form,"redirectUri",t)},expression:"form.redirectUri"}})],1),r("el-form-item",{attrs:{label:"绑定注册登录URI",prop:"bindUri"}},[r("el-input",{attrs:{placeholder:"请输入绑定注册登录uri,http://localhost/login?bindId="},model:{value:e.form.bindUri,callback:function(t){e.$set(e.form,"bindUri",t)},expression:"form.bindUri"}})],1),r("el-form-item",{attrs:{label:"跳转登录URI",prop:"redirectLoginUri"}},[r("el-input",{attrs:{placeholder:"请输入跳转登录uri,http://localhost/login?loginId="},model:{value:e.form.redirectLoginUri,callback:function(t){e.$set(e.form,"redirectLoginUri",t)},expression:"form.redirectLoginUri"}})],1),r("el-form-item",{attrs:{label:"错误提示URI",prop:"errorMsgUri"}},[r("el-input",{attrs:{placeholder:"请输入错误提示uri,http://localhost/login?errorId="},model:{value:e.form.errorMsgUri,callback:function(t){e.$set(e.form,"errorMsgUri",t)},expression:"form.errorMsgUri"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"请输入内容",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],o=r("5530"),i=(r("d81d"),r("b775"));function n(e){return Object(i["a"])({url:"/iot/platform/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/platform/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/platform",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/iot/platform",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/iot/platform/"+e,method:"delete"})}var p={name:"Platform",dicts:["iot_social_platform","iot_social_platform_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,platformList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,platform:null,status:null},columnTips:{bindId:"绑定登录uri, http://localhost/login?bindId=,域名换成对应域名即可，本地开发不需要更改",redirectLogin:"跳转登录uri,http://localhost/login?loginId=,域名换成对应域名即可，本地开发不需要更改",errorId:"错误提示获取uri,http://localhost/login?errorId=,域名换成对应域名即可，本地开发不需要更改"},form:{},rules:{platform:[{required:!0,message:"第三方平台不能为空",trigger:"change"}],status:[{required:!0,message:" 0:启用 ,1:禁用不能为空",trigger:"change"}],clientId:[{required:!0,message:"第三方平台申请Id不能为空",trigger:"blur"}],secretKey:[{required:!0,message:"第三方平台密钥不能为空",trigger:"blur"}],redirectUri:[{required:!0,message:"用户认证后跳转地址不能为空",trigger:"blur"}],bindUri:[{required:!0,message:"绑定注册登录uri,http://localhost/login?bindId=不能为空",trigger:"blur"}],redirectLoginUri:[{required:!0,message:"跳转登录uri,http://localhost/login?loginId=不能为空",trigger:"blur"}],errorMsgUri:[{required:!0,message:"错误提示uri,http://localhost/login?errorId=不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{renderHeaderMethods:function(e,t,r){var l=t.column;return e("div",[e("span",l.label),e("el-tooltip",{props:{effect:"dark",content:r,placement:"top"}},[e("i",{class:"el-icon-question",style:"color:#409EFF;margin-left:5px;"})])])},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.platformList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={socialPlatformId:null,platform:null,status:null,clientId:null,secretKey:null,redirectUri:null,createBy:null,createTime:null,updateTime:null,updateBy:null,remark:null,bindUri:null,redirectLoginUri:null,errorMsgUri:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.socialPlatformId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加第三方登录平台控制"},handleUpdate:function(e){var t=this;this.reset();var r=e.socialPlatformId||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改第三方登录平台控制"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.socialPlatformId?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.socialPlatformId||this.ids;this.$modal.confirm('是否确认删除第三方登录平台控制编号为"'+r+'"的数据项？').then((function(){return m(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/platform/export",Object(o["a"])({},this.queryParams),"platform_".concat((new Date).getTime(),".xlsx"))}}},d=p,f=r("2877"),h=Object(f["a"])(d,l,a,!1,null,null,null);t["default"]=h.exports}}]);