(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ff4ae72","chunk-63a1b6be"],{"165a":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"product-sub"},[a("el-row",{staticStyle:{"margin-bottom":"8px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:gateway:add"],expression:"['productModbus:gateway:add']"}],attrs:{plain:"",type:"primary",icon:"el-icon-plus",size:"small"},on:{click:t.handleAdd}},[t._v(t._s(t.$t("product.product-sub.3843945-0")))])],1),t.isSet?t._e():a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{plain:"",icon:"el-icon-edit",size:"small"},on:{click:t.setSubDeviceAddress}},[t._v(t._s(t.$t("product.product-sub.3843945-3")))])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:gateway:remove"],expression:"['productModbus:gateway:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:t.multiple},on:{click:t.handleDelete}},[t._v(" "+t._s(t.$t("product.product-sub.3843945-1"))+" ")])],1),a("el-col",{attrs:{span:1.5}},[a("span",{staticStyle:{"font-size":"12px","line-height":"32px",color:"#ffb032"}},[t._v(t._s(t.$t("product.product-sub.3843945-2")))])]),a("right-toolbar",{attrs:{showSearch:t.showSearch,search:!1},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}},[[t.isSet?a("div",{staticStyle:{"margin-right":"10px"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:gateway:edit"],expression:"['productModbus:gateway:edit']"}],attrs:{plain:"",type:"primary",size:"small"},on:{click:t.saveSetting}},[t._v(t._s(t.$t("save")))]),a("el-button",{attrs:{plain:"",type:"info",size:"small"},on:{click:t.cancelSetting}},[t._v(t._s(t.$t("cancel")))])],1):t._e()]],2)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.subProductList,border:!1},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:t.$t("product.product-sub.3843945-4"),align:"left",prop:"subProductName","min-width":"160"}}),a("el-table-column",{attrs:{label:t.$t("product.product-sub.3843945-5"),align:"center",prop:"slaveId",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-input",{staticStyle:{width:"100%","text-align":"center"},attrs:{disabled:!t.isSet,size:"small",placeholder:t.$t("product.product-sub.3843945-6")},model:{value:e.row.slaveId,callback:function(a){t.$set(e.row,"slaveId",a)},expression:"scope.row.slaveId"}})]}}])}),a("el-table-column",{attrs:{label:t.$t("creatTime"),align:"center",prop:"createTime",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t.parseTime(e.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{fixed:"right",label:t.$t("product.product-sub.3843945-7"),align:"center",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:gateway:remove"],expression:"['productModbus:gateway:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(t._s(t.$t("product.product-sub.3843945-8")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("subProductList",{ref:"subDeviceList",attrs:{gateway:t.gateway},on:{addSuccess:t.addSuccess}})],1)},r=[],i=(a("d81d"),a("5b52")),s=a("b77d"),o={name:"product-sub",props:{product:{type:Object,default:null}},components:{subProductList:s["default"]},dicts:["iot_device_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,subProductList:[],productInfo:{},title:"",open:!1,queryParams:{pageNum:1,pageSize:10},form:{},gateway:{},isSet:!1}},watch:{product:{handler:function(t){this.productInfo=t,this.productInfo&&0!=this.productInfo.productId&&(this.gateway.gwProductId=this.productInfo.productId,this.queryParams.gwProductId=this.productInfo.productId,this.getList())}}},mounted:function(){var t=this.product.productId;t&&(this.queryParams.gwProductId=this.product.productId,this.gateway.gwProductId=this.product.productId,this.getList())},methods:{getList:function(){var t=this;Object(i["g"])(this.queryParams).then((function(e){t.subProductList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,gwDeviceId:null,subDeviceId:null,slaveId:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},resetQuery:function(){this.resetForm("queryForm"),this.getList()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.id})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.$refs.subDeviceList.openDeviceList=!0,this.$refs.subDeviceList.getList()},handleDelete:function(t){var e=this,a=t.id||this.ids;this.$modal.confirm(this.$t("product.product-sub.3843945-9",[a])).then((function(){return Object(i["d"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess(e.$t("product.product-sub.3843945-10"))})).catch((function(){}))},setSubDeviceAddress:function(){var t=this;this.$confirm(this.$t("product.product-sub.3843945-11"),this.$t("product.product-sub.3843945-12"),{confirmButtonText:this.$t("product.product-sub.3843945-13"),cancelButtonText:this.$t("product.product-sub.3843945-14"),type:"warning"}).then((function(){t.isSet=!t.isSet})).catch((function(){t.$message({type:"info",message:t.$t("product.product-sub.3843945-15")})}))},saveSetting:function(){var t=this;this.isSet=!this.isSet,Object(i["f"])(this.subProductList).then((function(e){t.getList(),t.$modal.msgSuccess(t.$t("saveSuccess"))}))},cancelSetting:function(){this.isSet=!this.isSet},addSuccess:function(){this.getList()}}},u=o,c=(a("6bdf"),a("2877")),l=Object(c["a"])(u,n,r,!1,null,"d4fa4a80",null);e["default"]=l.exports},4426:function(t,e,a){},"5b52":function(t,e,a){"use strict";a.d(e,"h",(function(){return r})),a.d(e,"c",(function(){return i})),a.d(e,"i",(function(){return s})),a.d(e,"a",(function(){return o})),a.d(e,"e",(function(){return u})),a.d(e,"g",(function(){return c})),a.d(e,"b",(function(){return l})),a.d(e,"f",(function(){return d})),a.d(e,"d",(function(){return p}));var n=a("b775");function r(t){return Object(n["a"])({url:"/sub/gateway/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/sub/gateway/"+t,method:"delete"})}function s(t){return Object(n["a"])({url:"/sub/gateway/subDevice",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/sub/gateway/addBatch",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/sub/gateway/editBatch",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/productModbus/gateway/list",method:"get",params:t})}function l(t){return Object(n["a"])({url:"productModbus/gateway/addBatch",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/productModbus/gateway/editBatch",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/productModbus/gateway/"+t,method:"delete"})}},"6bdf":function(t,e,a){"use strict";a("4426")},"9b9c":function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"h",(function(){return i})),a.d(e,"f",(function(){return s})),a.d(e,"a",(function(){return o})),a.d(e,"i",(function(){return u})),a.d(e,"e",(function(){return c})),a.d(e,"b",(function(){return l})),a.d(e,"d",(function(){return d})),a.d(e,"c",(function(){return p}));var n=a("b775");function r(t){return Object(n["a"])({url:"/iot/product/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/iot/product/shortList",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/iot/product/"+t,method:"get"})}function o(t){return Object(n["a"])({url:"/iot/product",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/iot/product",method:"put",data:t})}function c(t){return Object(n["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function l(t){return Object(n["a"])({url:"/iot/product/status",method:"put",data:t})}function d(t){return Object(n["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(n["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}},b77d:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{title:t.$t("scene.index.670805-36"),visible:t.openDeviceList,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.openDeviceList=e}}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"48px"},nativeOn:{submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{prop:"productName"}},[a("el-input",{attrs:{size:"small",placeholder:"请输入子产品名称",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.gatewayList,size:"small",border:!1},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"ID",align:"left",prop:"productId",width:"120"}}),a("el-table-column",{attrs:{label:t.$t("device.device-edit.148398-1"),align:"left",prop:"productName"}})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.handleDeviceSelected}},[t._v(t._s(t.$t("confirm")))]),a("el-button",{on:{click:t.closeSelectDeviceList}},[t._v(t._s(t.$t("cancel")))])],1)],1)},r=[],i=(a("d81d"),a("5b52")),s=a("9b9c"),o={name:"sub-product-list",props:{gateway:{type:Object,default:null}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,gatewayList:[],title:"",open:!1,openDeviceList:!1,queryParams:{pageNum:1,pageSize:10,productName:null,deviceType:4}}},created:function(){},watch:{gateway:{handler:function(){this.queryParams.pageNum=1,this.getList()},immediate:!0}},methods:{getList:function(){var t=this;this.loading=!0,Object(s["g"])(this.queryParams).then((function(e){t.gatewayList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.resetForm("queryForm")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.reset(),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.productId})),this.single=1!==t.length,this.multiple=!t.length},closeSelectDeviceList:function(){this.openDeviceList=!1},handleDeviceSelected:function(){var t=this;this.gateway.subProductIds=this.ids,Object(i["b"])(this.gateway).then((function(e){t.$modal.msgSuccess(t.$t("device.sub-device-list.323213-4")),t.openDeviceList=!1,t.$emit("addSuccess")}))}}},u=o,c=a("2877"),l=Object(c["a"])(u,n,r,!1,null,null,null);e["default"]=l.exports}}]);