(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a5c06acc"],{"0538":function(e,t,r){"use strict";var n=r("e330"),o=r("59ed"),i=r("861d"),a=r("1a2d"),s=r("f36a"),u=r("40d5"),l=Function,c=n([].concat),f=n([].join),d={},h=function(e,t,r){if(!a(d,t)){for(var n=[],o=0;o<t;o++)n[o]="a["+o+"]";d[t]=l("C,a","return new C("+f(n,",")+")")}return d[t](e,r)};e.exports=u?l.bind:function(e){var t=o(this),r=t.prototype,n=s(arguments,1),a=function(){var r=c(n,s(arguments));return this instanceof a?h(t,r.length,r):t.apply(e,r)};return i(r)&&(a.prototype=r),a}},"36c6":function(e,t,r){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t)}r("3410"),r("1f68"),r("131a"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"3c96":function(e,t,r){function n(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}r("d9e2"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"4a4b":function(e,t,r){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,n(t,r)}r("1f68"),r("131a"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"4ae1":function(e,t,r){var n=r("23e7"),o=r("d066"),i=r("2ba4"),a=r("0538"),s=r("5087"),u=r("825a"),l=r("861d"),c=r("7c73"),f=r("d039"),d=o("Reflect","construct"),h=Object.prototype,p=[].push,m=f((function(){function e(){}return!(d((function(){}),[],e)instanceof e)})),g=!f((function(){d((function(){}))})),v=m||g;n({target:"Reflect",stat:!0,forced:v,sham:v},{construct:function(e,t){s(e),u(t);var r=arguments.length<3?e:s(arguments[2]);if(g&&!m)return d(e,t,r);if(e==r){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var n=[null];return i(p,n,t),new(i(a,e,n))}var o=r.prototype,f=c(l(o)?o:h),v=i(e,f,t);return l(v)?v:f}})},"4ec9":function(e,t,r){r("6f48")},"5bc3":function(e,t,r){var n=r("a395");function o(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,n(o.key),o)}}function i(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},"61e5":function(e,t,r){r("4ae1");var n=r("36c6"),o=r("6f8f"),i=r("6b58");function a(e,t,r){return t=n(t),i(e,o()?Reflect.construct(t,r||[],n(e).constructor):t.apply(e,r))}e.exports=a,e.exports.__esModule=!0,e.exports["default"]=e.exports},"6b58":function(e,t,r){r("d9e2");var n=r("7037")["default"],o=r("3c96");function i(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},"6f48":function(e,t,r){"use strict";var n=r("6d61"),o=r("6566");n("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),o)},"6f8f":function(e,t,r){function n(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(e.exports=n=function(){return!!t},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}r("4ae1"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"8b09":function(e,t,r){var n=r("74e8");n("Int16",(function(e){return function(t,r,n){return e(this,t,r,n)}}))},a128:function(e,t,r){r("d9e2"),r("4ec9"),r("d3b7"),r("3ca3"),r("ddb0");var n=r("36c6"),o=r("4a4b"),i=r("c5f7"),a=r("b17c");function s(t){var r="function"==typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!i(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return a(e,arguments,n(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),o(t,e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},b17c:function(e,t,r){r("14d9"),r("4ae1");var n=r("6f8f"),o=r("4a4b");function i(e,t,r){if(n())return Reflect.construct.apply(null,arguments);var i=[null];i.push.apply(i,t);var a=new(e.bind.apply(e,i));return r&&o(a,r.prototype),a}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},c5f7:function(e,t,r){function n(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}r("d3b7"),r("25f0"),e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},cfc3:function(e,t,r){var n=r("74e8");n("Float32",(function(e){return function(t,r,n){return e(this,t,r,n)}}))},ded3b:function(e,t,r){r("a4d3"),r("4de4"),r("14d9"),r("e439"),r("dbb4"),r("b64b"),r("d3b7"),r("159b");var n=r("9523");function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){n(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},ec97:function(e,t,r){"use strict";var n=r("ebb5"),o=r("8aa7"),i=n.aTypedArrayConstructor,a=n.exportTypedArrayStaticMethod;a("of",(function(){var e=0,t=arguments.length,r=new(i(this))(t);while(t>e)r[e]=arguments[e++];return r}),o)},ed6d:function(e,t,r){r("d9e2");var n=r("4a4b");function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},fbfe:function(e,t,r){var n,o,i=r("3c96").default,a=r("7ec2").default,s=r("c973").default,u=r("448a").default,l=r("a128").default,c=r("61e5").default,f=r("ed6d").default,d=r("ded3b").default,h=r("970b").default,p=r("5bc3").default,m=r("7037").default;r("d9e2"),r("99af"),r("d81d"),r("14d9"),r("fb6a"),r("c19f"),r("ace4"),r("6c57"),r("e9c4"),r("b680"),r("b64b"),r("d3b7"),r("ac1f"),r("25f0"),r("3ca3"),r("466d"),r("498a"),r("cfc3"),r("fd87"),r("8b09"),r("5cc6"),r("907a"),r("9a8c"),r("a975"),r("735e"),r("c1ac"),r("d139"),r("3a7b"),r("986a"),r("1d02"),r("d5d6"),r("82f8"),r("e91f"),r("60bd"),r("5f96"),r("3280"),r("3fcc"),r("ec97"),r("ca91"),r("25a1"),r("cd26"),r("3c5d"),r("2954"),r("649e"),r("219c"),r("170b"),r("b39a"),r("72f7"),r("1b3b"),r("3d71"),r("c6e3"),r("159b"),r("ddb0"),r("2b3d"),r("bf19"),r("9861"),function(i,a){"object"==m(t)&&"undefined"!=typeof e?e.exports=a():(n=a,o="function"===typeof n?n.call(t,r,t,e):n,void 0===o||(e.exports=o))}(0,(function(){"use strict";var e=function(){function e(){h(this,e)}return p(e,[{key:"on",value:function(e,t,r){var n=this.e||(this.e={});return(n[e]||(n[e]=[])).push({fn:t,ctx:r}),this}},{key:"once",value:function(e,t,r){var n=this;function o(){n.off(e,o);for(var i=arguments.length,a=new Array(i),s=0;s<i;s++)a[s]=arguments[s];t.apply(r,a)}return o._=t,this.on(e,o,r)}},{key:"emit",value:function(e){for(var t=((this.e||(this.e={}))[e]||[]).slice(),r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];for(var i=0;i<t.length;i+=1)t[i].fn.apply(t[i].ctx,n);return this}},{key:"off",value:function(e,t){var r=this.e||(this.e={});if(!e)return Object.keys(r).forEach((function(e){delete r[e]})),void delete this.e;var n=r[e],o=[];if(n&&t)for(var i=0,a=n.length;i<a;i+=1)n[i].fn!==t&&n[i].fn._!==t&&o.push(n[i]);return o.length?r[e]=o:delete r[e],this}}])}(),t="debug",r="warn",n="talkGetUserMediaSuccess",o="talkGetUserMediaFail",g="talkGetUserMediaTimeout",v="talkStreamStart",k="talkStreamOpen",b="talkStreamClose",y="talkStreamError",w="talkStreamInactive",_="talkFailedAndStop",T={talkStreamClose:b,talkStreamError:y,talkStreamInactive:w,talkGetUserMediaTimeout:g,talkFailedAndStop:_},S=d({playError:"playIsNotPauseOrUrlIsNull",fetchError:"fetchError",websocketError:"websocketError",webcodecsH265NotSupport:"webcodecsH265NotSupport",webcodecsDecodeError:"webcodecsDecodeError",webcodecsUnsupportedConfigurationError:"webcodecsUnsupportedConfigurationError",webcodecsDecodeConfigureError:"webcodecsDecodeConfigureError",mediaSourceH265NotSupport:"mediaSourceH265NotSupport",mediaSourceAudioG711NotSupport:"mediaSourceAudioG711NotSupport",mediaSourceAudioInitTimeout:"mediaSourceAudioInitTimeout",mediaSourceAudioNoDataTimeout:"mediaSourceAudioNoDataTimeout",mediaSourceDecoderConfigurationError:"mediaSourceDecoderConfigurationError",mediaSourceFull:"mseSourceBufferFull",mseSourceBufferError:"mseSourceBufferError",mseAddSourceBufferError:"mseAddSourceBufferError",mediaSourceAppendBufferError:"mediaSourceAppendBufferError",mediaSourceTsIsMaxDiff:"mediaSourceTsIsMaxDiff",mediaSourceUseCanvasRenderPlayFailed:"mediaSourceUseCanvasRenderPlayFailed",mediaSourceBufferedIsZeroError:"mediaSourceBufferedIsZeroError",wasmDecodeError:"wasmDecodeError",wasmUseVideoRenderError:"wasmUseVideoRenderError",hlsError:"hlsError",webrtcError:"webrtcError",webrtcClosed:"webrtcClosed",webrtcIceCandidateError:"webrtcIceCandidateError",webglAlignmentError:"webglAlignmentError",wasmWidthOrHeightChange:"wasmWidthOrHeightChange",mseWidthOrHeightChange:"mseWidthOrHeightChange",wcsWidthOrHeightChange:"wcsWidthOrHeightChange",widthOrHeightChange:"widthOrHeightChange",tallWebsocketClosedByError:"tallWebsocketClosedByError",flvDemuxBufferSizeTooLarge:"flvDemuxBufferSizeTooLarge",wasmDecodeVideoNoResponseError:"wasmDecodeVideoNoResponseError",audioChannelError:"audioChannelError",simdH264DecodeVideoWidthIsTooLarge:"simdH264DecodeVideoWidthIsTooLarge",simdDecodeError:"simdDecodeError",webglContextLostError:"webglContextLostError",videoElementPlayingFailed:"videoElementPlayingFailed",videoElementPlayingFailedForWebrtc:"videoElementPlayingFailedForWebrtc",decoderWorkerInitError:"decoderWorkerInitError",videoInfoError:"videoInfoError",videoCodecIdError:"videoCodecIdError",streamEnd:"streamEnd",websocket1006Error:"websocket1006Error",delayTimeout:"delayTimeout",loadingTimeout:"loadingTimeout",networkDelayTimeout:"networkDelayTimeout",aliyunRtcError:"aliyunRtcError"},{talkStreamError:y,talkStreamClose:b}),E="notConnect",M="open",A="close",x="error",G="g711a",U="g711u",B="pcm",O="opus",R=8,L=0,F=98,C="empty",P="rtp",N="tcp",W="open",I="close",D="error",j="message",z="worklet",q="script",H={encType:G,packetType:P,packetTcpSendType:N,rtpSsrc:"0000000000",numberChannels:1,sampleRate:8e3,sampleBitsWidth:16,sendInterval:20,debug:!1,debugLevel:r,testMicrophone:!1,saveToTempFile:!1,audioBufferLength:160,engine:z,checkGetUserMediaTimeout:!1,getUserMediaTimeout:1e4,audioConstraints:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1}};function J(){return(new Date).getTime()}function V(e){var t="";if("object"==m(e))try{t=JSON.stringify(e),t=JSON.parse(t)}catch(r){t=e}else t=e;return t}function Z(e){return!0===e||"true"===e}(function(e,t){return e(t={exports:{}},t.exports),t.exports})((function(e){!function(){var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,n=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,o=r.length,i={};n<o;n++)if((e=r[n])&&e[1]in t){for(n=0;n<e.length;n++)i[r[0][n]]=e[n];return i}return!1}(),o={change:n.fullscreenchange,error:n.fullscreenerror},i={request:function(e,r){return new Promise(function(o,i){var a=function(){this.off("change",a),o()}.bind(this);this.on("change",a);var s=(e=e||t.documentElement)[n.requestFullscreen](r);s instanceof Promise&&s.then(a).catch(i)}.bind(this))},exit:function(){return new Promise(function(e,r){if(this.isFullscreen){var o=function(){this.off("change",o),e()}.bind(this);this.on("change",o);var i=t[n.exitFullscreen]();i instanceof Promise&&i.then(o).catch(r)}else e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var n=o[e];n&&t.addEventListener(n,r,!1)},off:function(e,r){var n=o[e];n&&t.removeEventListener(n,r,!1)},raw:n};n?(Object.defineProperties(i,{isFullscreen:{get:function(){return Boolean(t[n.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[n.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[n.fullscreenEnabled])}}}),r?e.exports=i:window.screenfull=i):r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}}()})).isEnabled,function(){try{if("object"==("undefined"===typeof WebAssembly?"undefined":m(WebAssembly))&&"function"==typeof WebAssembly.instantiate){var t=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));if(t instanceof WebAssembly.Module)new WebAssembly.Instance(t),WebAssembly.Instance}}catch(e){}}();var $=function(){function r(e){h(this,r);var t=e.fromSampleRate,n=e.toSampleRate,o=e.channels,i=e.inputBufferSize;if(!t||!n||!o)throw new Error("Invalid settings specified for the resampler.");this.resampler=null,this.fromSampleRate=t,this.toSampleRate=n,this.channels=o||0,this.inputBufferSize=i,this.initialize()}return p(r,[{key:"initialize",value:function(){this.fromSampleRate==this.toSampleRate?(this.resampler=function(e){return e},this.ratioWeight=1):(this.fromSampleRate<this.toSampleRate?(this.linearInterpolation(),this.lastWeight=1):(this.multiTap(),this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate)}},{key:"bufferSlice",value:function(e){try{return this.outputBuffer.subarray(0,e)}catch(t){try{return this.outputBuffer.length=e,this.outputBuffer}catch(t){return this.outputBuffer.slice(0,e)}}}},{key:"initializeBuffers",value:function(){this.outputBufferSize=Math.ceil(this.inputBufferSize*this.toSampleRate/this.fromSampleRate/this.channels*1.0000004768371582)+this.channels+this.channels;try{this.outputBuffer=new Float32Array(this.outputBufferSize),this.lastOutput=new Float32Array(this.channels)}catch(e){this.outputBuffer=[],this.lastOutput=[]}}},{key:"linearInterpolation",value:function(){var e=this;this.resampler=function(t){var r,n,o,i,a,s,u,l,c,f=t.length,d=e.channels;if(f%d!=0)throw new Error("Buffer was of incorrect sample length.");if(f<=0)return[];for(r=e.outputBufferSize,n=e.ratioWeight,o=e.lastWeight,i=0,a=0,s=0,u=0,l=e.outputBuffer;o<1;o+=n)for(a=o%1,i=1-a,e.lastWeight=o%1,c=0;c<e.channels;++c)l[u++]=e.lastOutput[c]*i+t[c]*a;for(o-=1,f-=d,s=Math.floor(o)*d;u<r&&s<f;){for(a=o%1,i=1-a,c=0;c<e.channels;++c)l[u++]=t[s+(c>0?c:0)]*i+t[s+(d+c)]*a;o+=n,s=Math.floor(o)*d}for(c=0;c<d;++c)e.lastOutput[c]=t[s++];return e.bufferSlice(u)}}},{key:"multiTap",value:function(){var e=this;this.resampler=function(t){var r,n,o,i,a,s,u,l,c,f,d,h=t.length,p=e.channels;if(h%p!=0)throw new Error("Buffer was of incorrect sample length.");if(h<=0)return[];for(r=e.outputBufferSize,n=[],o=e.ratioWeight,i=0,s=0,u=0,l=!e.tailExists,e.tailExists=!1,c=e.outputBuffer,f=0,d=0,a=0;a<p;++a)n[a]=0;do{if(l)for(i=o,a=0;a<p;++a)n[a]=0;else{for(i=e.lastWeight,a=0;a<p;++a)n[a]=e.lastOutput[a];l=!0}for(;i>0&&s<h;){if(u=1+s-d,!(i>=u)){for(a=0;a<p;++a)n[a]+=t[s+(a>0?a:0)]*i;d+=i,i=0;break}for(a=0;a<p;++a)n[a]+=t[s++]*u;d=s,i-=u}if(0!==i){for(e.lastWeight=i,a=0;a<p;++a)e.lastOutput[a]=n[a];e.tailExists=!0;break}for(a=0;a<p;++a)c[f++]=n[a]/o}while(s<h&&f<r);return e.bufferSlice(f)}}},{key:"resample",value:function(e){return this.fromSampleRate==this.toSampleRate?this.ratioWeight=1:(this.fromSampleRate<this.toSampleRate?this.lastWeight=1:(this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate),this.resampler(e)}}])}(),K=[255,511,1023,2047,4095,8191,16383,32767];function Q(e,t,r){for(var n=0;n<r;n++)if(e<=t[n])return n;return r}function X(e){var t=[];return Array.prototype.slice.call(e).forEach((function(e,r){t[r]=function(e){var t,r,n;return e>=0?t=213:(t=85,(e=-e-1)<0&&(e=32767)),r=Q(e,K,8),r>=8?127^t:(n=r<<4,n|=r<2?e>>4&15:e>>r+3&15,n^t)}(e)})),t}function Y(e){var t=[];return Array.prototype.slice.call(e).forEach((function(e,r){t[r]=function(e){var t=0;e<0?(e=132-e,t=127):(e+=132,t=255);var r=Q(e,K,8);return r>=8?127^t:(r<<4|e>>r+3&15)^t}(e)})),t}var ee=p((function e(n){h(this,e),this.log=function(e){if(n._opt.debug&&n._opt.debugLevel==t){for(var r,o=n._opt.debugUuid?"[".concat(n._opt.debugUuid,"]"):"",i=arguments.length,a=new Array(i>1?i-1:0),s=1;s<i;s++)a[s-1]=arguments[s];(r=console).log.apply(r,["JbPro".concat(o,"[✅✅✅][").concat(e,"]")].concat(a))}},this.warn=function(e){if(n._opt.debug&&(n._opt.debugLevel==t||n._opt.debugLevel==r)){for(var o,i=n._opt.debugUuid?"[".concat(n._opt.debugUuid,"]"):"",a=arguments.length,s=new Array(a>1?a-1:0),u=1;u<a;u++)s[u-1]=arguments[u];(o=console).log.apply(o,["JbPro".concat(i,"[❗❗❗][").concat(e,"]")].concat(s))}},this.error=function(e){for(var t,r=n._opt.debugUuid?"[".concat(n._opt.debugUuid,"]"):"",o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];(t=console).error.apply(t,["JbPro".concat(r,"[❌❌❌][").concat(e,"]")].concat(i))}})),te=function(){function e(t){h(this,e),this.destroys=[],this.proxy=this.proxy.bind(this),this.master=t}return p(e,[{key:"proxy",value:function(e,t,r){var n=this,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(e){if(Array.isArray(t))return t.map((function(t){return n.proxy(e,t,r,o)}));e.addEventListener(t,r,o);var i=function(){"function"==typeof e.removeEventListener&&e.removeEventListener(t,r,o)};return this.destroys.push(i),i}}},{key:"destroy",value:function(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((function(e){return e()})),this.destroys=[]}}])}(),re=function(e){function t(e){var r;h(this,t);var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r=c(this,t),r._opt={},e&&(r.player=e),r.TAG_NAME="talk";var o=V(H);return r._opt=Object.assign({},o,n),r._opt.sampleRate=parseInt(r._opt.sampleRate,10),r._opt.sampleBitsWidth=parseInt(r._opt.sampleBitsWidth,10),r.audioContext=null,r.gainNode=null,r.recorder=null,r.workletRecorder=null,r.biquadFilter=null,r.userMediaStream=null,r.clearWorkletUrlTimeout=null,r.bufferSize=512,r._opt.audioBufferLength=r.calcAudioBufferLength(),r.audioBufferList=[],r.socket=null,r.socketStatus=E,r.mediaStreamSource=null,r.heartInterval=null,r.checkGetUserMediaTimeout=null,r.wsUrl=null,r.startTimestamp=0,r.sequenceId=0,r.tempTimestamp=null,r.tempG711BufferList=[],r.tempRtpBufferList=[],r.tempPcmBufferList=[],r.events=new te(r),r._initTalk(),r.player||(r.debug=new ee(r)),r._opt.encType!==G&&r._opt.encType!==U||8e3===r._opt.sampleRate&&16===r._opt.sampleBitsWidth||r.warn(r.TAG_NAME,"\n            encType is ".concat(r._opt.encType," and sampleBitsWidth is ").concat(r._opt.sampleBitsWidth,", set sampleBitsWidth to ").concat(r._opt.sampleBitsWidth,"。\n            ").concat(r._opt.encType," only support sampleRate 8000 and sampleBitsWidth 16")),r.log(r.TAG_NAME,"init",JSON.stringify(r._opt)),r}return f(t,e),p(t,[{key:"destroy",value:function(){this.clearWorkletUrlTimeout&&(clearTimeout(this.clearWorkletUrlTimeout),this.clearWorkletUrlTimeout=null),this.userMediaStream&&(this.userMediaStream.getTracks&&this.userMediaStream.getTracks().forEach((function(e){e.stop()})),this.userMediaStream=null),this.mediaStreamSource&&(this.mediaStreamSource.disconnect(),this.mediaStreamSource=null),this.recorder&&(this.recorder.disconnect(),this.recorder.onaudioprocess=null,this.recorder=null),this.biquadFilter&&(this.biquadFilter.disconnect(),this.biquadFilter=null),this.gainNode&&(this.gainNode.disconnect(),this.gainNode=null),this.workletRecorder&&(this.workletRecorder.disconnect(),this.workletRecorder=null),this.socket&&(this.socketStatus===M&&this._sendClose(),this.socket.close(),this.socket=null),this._stopHeartInterval(),this._stopCheckGetUserMediaTimeout(),this.audioContext=null,this.gainNode=null,this.recorder=null,this.audioBufferList=[],this.sequenceId=0,this.wsUrl=null,this.tempTimestamp=null,this.tempRtpBufferList=[],this.tempG711BufferList=[],this.tempPcmBufferList=[],this.startTimestamp=0,this.log(this.TAG_NAME,"destroy")}},{key:"addRtpToBuffer",value:function(e){var t=e.length+this.tempRtpBufferList.length,r=new Uint8Array(t);r.set(this.tempRtpBufferList,0),r.set(e,this.tempRtpBufferList.length),this.tempRtpBufferList=r}},{key:"addG711ToBuffer",value:function(e){var t=e.length+this.tempG711BufferList.length,r=new Uint8Array(t);r.set(this.tempG711BufferList,0),r.set(e,this.tempG711BufferList.length),this.tempG711BufferList=r}},{key:"addPcmToBuffer",value:function(e){var t=e.length+this.tempPcmBufferList.length,r=new Uint8Array(t);r.set(this.tempPcmBufferList,0),r.set(e,this.tempPcmBufferList.length),this.tempG711ButempPcmBufferListfferList=r}},{key:"downloadRtpFile",value:function(){this.debug.log(this.TAG_NAME,"downloadRtpFile");var e=new Blob([this.tempRtpBufferList]);try{var t=document.createElement("a");t.href=window.URL.createObjectURL(e),t.download=Date.now()+".rtp",t.click(),window.URL.revokeObjectURL(t.href)}catch(e){console.error("downloadRtpFile",e)}}},{key:"downloadG711File",value:function(){this.debug.log(this.TAG_NAME,"downloadG711File");var e=new Blob([this.tempG711BufferList]);try{var t=document.createElement("a");t.href=window.URL.createObjectURL(e),t.download=Date.now()+"."+this._opt.encType,t.click(),window.URL.revokeObjectURL(t.href)}catch(e){console.error("downloadG711File",e)}}},{key:"downloadPcmFile",value:function(){this.debug.log(this.TAG_NAME,"downloadPcmFile");var e=new Blob([this.tempPcmBufferList]);try{var t=document.createElement("a");t.href=window.URL.createObjectURL(e),t.download=Date.now()+"."+this._opt.encType,t.click(),window.URL.revokeObjectURL(t.href)}catch(e){console.error("downloadRtpFile",e)}}},{key:"downloadFile",value:function(){this._opt.packetType===P?this.downloadRtpFile():this._opt.encType===G||this._opt.encType===U?this.downloadG711File():this.downloadPcmFile()}},{key:"calcAudioBufferLength",value:function(){var e=this._opt,t=e.sampleRate;e.sampleBitsWidth;return 8*t*.02/8}},{key:"socketStatusOpen",get:function(){return this.socketStatus===M}},{key:"log",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];this._log.apply(this,["log"].concat(t))}},{key:"warn",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];this._log.apply(this,["warn"].concat(t))}},{key:"error",value:function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];this._log.apply(this,["error"].concat(t))}},{key:"_log",value:function(e){for(var t,r,n,o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];this.player?(t=this.player.debug)[e].apply(t,i):this.debug?(r=this.debug)[e].apply(r,i):(n=console)[e].apply(n,i)}},{key:"_getSequenceId",value:function(){return++this.sequenceId}},{key:"_createWebSocket",value:function(){var e=this;return new Promise((function(t,r){var n=e.events.proxy;e.socket=new WebSocket(e.wsUrl),e.socket.binaryType="arraybuffer",e.emit(v),n(e.socket,W,(function(){e.socketStatus=M,e.log(e.TAG_NAME,"websocket open -> do talk"),e.emit(k),t(),e._doTalk()})),n(e.socket,j,(function(t){e.log(e.TAG_NAME,"websocket message",t.data)})),n(e.socket,I,(function(t){e.socketStatus=A,e.warn(e.TAG_NAME,"websocket close -> reject",t),e.emit(b),r(t)})),n(e.socket,D,(function(t){e.socketStatus=x,e.error(e.TAG_NAME,"websocket error -> reject",t),e.emit(y,t),r(t)}))}))}},{key:"_sendClose",value:function(){}},{key:"_initTalk",value:function(){this._initMethods(),this._opt.engine===z?this._initWorklet():this._opt.engine===q&&this._initScriptProcessor(),this.log(this.TAG_NAME,"audioContext samplerate",this.audioContext.sampleRate)}},{key:"_initMethods",value:function(){this.audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:48e3}),this.gainNode=this.audioContext.createGain(),this.gainNode.gain.value=1,this.biquadFilter=this.audioContext.createBiquadFilter(),this.biquadFilter.type="lowpass",this.biquadFilter.frequency.value=3e3,this.resampler=new $({fromSampleRate:this.audioContext.sampleRate,toSampleRate:this._opt.sampleRate,channels:this._opt.numberChannels,inputBufferSize:this.bufferSize})}},{key:"_initScriptProcessor",value:function(){var e=this,t=this.audioContext.createScriptProcessor||this.audioContext.createJavaScriptNode;this.recorder=t.apply(this.audioContext,[this.bufferSize,this._opt.numberChannels,this._opt.numberChannels]),this.recorder.onaudioprocess=function(t){return e._onaudioprocess(t)}}},{key:"_initWorklet",value:function(){var e=this,t=function(e){var t=e.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],r=new Blob([t],{type:"application/javascript"});return URL.createObjectURL(r)}((function(){var e=function(e){function t(e){var r;return h(this,t),r=c(this,t),r._cursor=0,r._bufferSize=e.processorOptions.bufferSize,r._buffer=new Float32Array(r._bufferSize),r}return f(t,e),p(t,[{key:"process",value:function(e,t,r){if(!e.length||!e[0].length)return!0;for(var n=0;n<e[0][0].length;n++)this._cursor+=1,this._cursor===this._bufferSize&&(this._cursor=0,this.port.postMessage({eventType:"data",buffer:this._buffer})),this._buffer[this._cursor]=e[0][0][n];return!0}}])}(l(AudioWorkletProcessor));registerProcessor("talk-processor",e)}));this.audioContext.audioWorklet&&this.audioContext.audioWorklet.addModule(t).then((function(){var t=new AudioWorkletNode(e.audioContext,"talk-processor",{processorOptions:{bufferSize:e.bufferSize}});t.connect(e.gainNode),t.port.onmessage=function(t){"data"===t.data.eventType&&e._encodeAudioData(t.data.buffer)},e.workletRecorder=t})),this.clearWorkletUrlTimeout=setTimeout((function(){URL.revokeObjectURL(t),e.clearWorkletUrlTimeout=null}),1e4)}},{key:"_onaudioprocess",value:function(e){var t=e.inputBuffer.getChannelData(0);this._encodeAudioData(new Float32Array(t))}},{key:"_encodeAudioData",value:function(e){if(0!==e[0]||0!==e[1]){var t=this.resampler.resample(e),r=t;if(16===this._opt.sampleBitsWidth?r=function(e){for(var t=e.length,r=new Int16Array(t);t--;){var n=Math.max(-1,Math.min(1,e[t]));r[t]=n<0?32768*n:32767*n}return r}(t):8===this._opt.sampleBitsWidth&&(r=function(e){for(var t=e.length,r=new Int8Array(t);t--;){var n=Math.max(-1,Math.min(1,e[t])),o=n<0?32768*n:32767*n;r[t]=parseInt(255/(65535/(32768+o)),10)}return r}(t)),null!==r.buffer){var n=null;this._opt.encType===G?n=X(r):this._opt.encType===U?n=Y(r):this._opt.encType===B&&(n=r);for(var o=new Uint8Array(n),i=0;i<o.length;i++){var a=this.audioBufferList.length;this.audioBufferList[a++]=o[i],this.audioBufferList.length===this._opt.audioBufferLength&&(this._sendTalkMsg(new Uint8Array(this.audioBufferList)),this.audioBufferList=[])}}}else this.log(this.TAG_NAME,"empty audio data")}},{key:"_parseAudioMsg",value:function(e){var t=null;return this._opt.packetType!==P||this._opt.encType!==G&&this._opt.encType!==U?this._opt.packetType===C&&(t=e):t=this.rtpPacket(e),t}},{key:"rtpPacket",value:function(e){var t=[],r=0,n=0,o=0,i=this._opt.rtpSsrc,a=e.length;this._opt.encType===G?r=R:this._opt.encType===U?r=L:this._opt.encType===O&&(r=F),this.startTimestamp||(this.startTimestamp=J()),o=J()-this.startTimestamp,n=this._getSequenceId();var s=0;if(this._opt.packetTcpSendType===N){var l=a+12;t[s++]=255&l>>8,t[s++]=255&l>>0}t[s++]=128,t[s++]=128+r,t[s++]=n/256,t[s++]=n%256,t[s++]=o/65536/256,t[s++]=o/65536%256,t[s++]=o%65536/256,t[s++]=o%65536%256,t[s++]=i/65536/256,t[s++]=i/65536%256,t[s++]=i%65536/256,t[s++]=i%65536%256;for(var c=t.concat(u(e)),f=new Uint8Array(c.length),d=0;d<c.length;d++)f[d]=c[d];return f}},{key:"opusPacket",value:function(e){return e}},{key:"_sendTalkMsg",value:function(e){null===this.tempTimestamp&&(this.tempTimestamp=J());var t=J(),r=t-this.tempTimestamp,n=this._parseAudioMsg(e);this.log(this.TAG_NAME,"send talk msg and diff is ".concat(r," and byteLength is ").concat(n.byteLength," and length is ").concat(n.length,", and ").concat(this._opt.encType," length is ").concat(e.length)),Z(this._opt.saveToTempFile)&&Z(this._opt.debug)&&(this._opt.packetType===P?this.addRtpToBuffer(n):this._opt.encType===G||this._opt.encType===U?this.addG711ToBuffer(n):this.addPcmToBuffer(n)),n&&(this.socketStatusOpen?this.socket.send(n.buffer):this.emit(S.tallWebsocketClosedByError)),this.tempTimestamp=t}},{key:"_doTalk",value:function(){this._getUserMedia()}},{key:"_getUserMedia",value:function(){var e=this;this.log(this.TAG_NAME,"getUserMedia"),void 0===window.navigator.mediaDevices&&(window.navigator.mediaDevices={}),void 0===window.navigator.mediaDevices.getUserMedia&&(this.log(this.TAG_NAME,"window.navigator.mediaDevices.getUserMedia is undefined and init function"),window.navigator.mediaDevices.getUserMedia=function(e){var t=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return t?new Promise((function(r,n){t.call(navigator,e,r,n)})):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}),this._opt.checkGetUserMediaTimeout&&this._startCheckGetUserMediaTimeout(),window.navigator.mediaDevices.getUserMedia({audio:this._opt.audioConstraints,video:!1}).then((function(t){e.log(e.TAG_NAME,"getUserMedia success"),e.userMediaStream=t,e.mediaStreamSource=e.audioContext.createMediaStreamSource(t),e.mediaStreamSource.connect(e.biquadFilter),e.recorder?(e.biquadFilter.connect(e.recorder),e.recorder.connect(e.gainNode)):e.workletRecorder&&(e.biquadFilter.connect(e.workletRecorder),e.workletRecorder.connect(e.gainNode)),e.gainNode.connect(e.audioContext.destination),e.emit(n),null===t.oninactive&&(t.oninactive=function(t){e._handleStreamInactive(t)})})).catch((function(t){e.error(e.TAG_NAME,"getUserMedia error",t.toString()),e.emit(o,t.toString())})).finally((function(){e.log(e.TAG_NAME,"getUserMedia finally"),e._stopCheckGetUserMediaTimeout()}))}},{key:"_getUserMedia2",value:function(){var e=this;this.log(this.TAG_NAME,"getUserMedia"),navigator.mediaDevices?navigator.mediaDevices.getUserMedia({audio:!0}).then((function(t){e.log(e.TAG_NAME,"getUserMedia2 success")})):navigator.getUserMedia({audio:!0},this.log(this.TAG_NAME,"getUserMedia2 success"),this.log(this.TAG_NAME,"getUserMedia2 fail"))}},{key:"_getUserMedia3",value:function(){var e=s(a().mark((function e(){var t;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.log(this.TAG_NAME,"getUserMedia3"),e.prev=1,e.next=4,navigator.mediaDevices.getUserMedia({audio:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1},video:!1});case 4:t=e.sent,console.log("getUserMedia() got stream:",t),this.log(this.TAG_NAME,"getUserMedia3 success"),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](1),this.log(this.TAG_NAME,"getUserMedia3 fail");case 11:case"end":return e.stop()}}),e,this,[[1,8]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"_handleStreamInactive",value:function(e){this.userMediaStream&&(this.warn(this.TAG_NAME,"stream oninactive",e),this.emit(w))}},{key:"_startCheckGetUserMediaTimeout",value:function(){var e=this;this._stopCheckGetUserMediaTimeout(),this.checkGetUserMediaTimeout=setTimeout((function(){e.log(e.TAG_NAME,"check getUserMedia timeout"),e.emit(g)}),this._opt.getUserMediaTimeout)}},{key:"_stopCheckGetUserMediaTimeout",value:function(){this.checkGetUserMediaTimeout&&(this.log(this.TAG_NAME,"stop checkGetUserMediaTimeout"),clearTimeout(this.checkGetUserMediaTimeout),this.checkGetUserMediaTimeout=null)}},{key:"_startHeartInterval",value:function(){var e=this;this.heartInterval=setInterval((function(){e.log(e.TAG_NAME,"heart interval");var t=[35,36,0,0,0,0,0,0];t=new Uint8Array(t),e.socket.send(t.buffer)}),15e3)}},{key:"_stopHeartInterval",value:function(){this.heartInterval&&(this.log(this.TAG_NAME,"stop heart interval"),clearInterval(this.heartInterval),this.heartInterval=null)}},{key:"startTalk",value:function(e){var t=this;return new Promise((function(r,i){if(!function(){var e=!1,t=window.navigator;return t&&(e=!(!t.mediaDevices||!t.mediaDevices.getUserMedia),e||(e=!!(t.getUserMedia||t.webkitGetUserMedia||t.mozGetUserMedia||t.msGetUserMedia))),e}())return i("not support getUserMedia");if(t.wsUrl=e,t._opt.testMicrophone)t._doTalk();else{if(!t.wsUrl)return i("wsUrl is null");t._createWebSocket().catch((function(e){i(e)}))}t.once(o,(function(){i("getUserMedia fail")})),t.once(n,(function(){r()}))}))}},{key:"setVolume",value:function(e){var t,r,n;e=parseFloat(e).toFixed(2),isNaN(e)||(t=e,r=0,n=1,e=Math.max(Math.min(t,Math.max(r,n)),Math.min(r,n)),this.gainNode.gain.value=e)}},{key:"getOption",value:function(){return this._opt}},{key:"volume",get:function(){return this.gainNode?parseFloat(100*this.gainNode.gain.value).toFixed(0):null}}])}(e),ne=function(e){function t(){var e;h(this,t);var r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e=c(this,t),e.talk=null,e._opt=r,e.LOG_TAG="JbProTalk",e.debug=new ee(i(e)),e.debug.log(e.LOG_TAG,"init",JSON.stringify(r)),e}return f(t,e),p(t,[{key:"destroy",value:function(){this.debug.log(this.LOG_TAG,"destroy()"),this.off(),this.talk&&(this.talk.destroy(),this.talk=null),this.debug.log(this.LOG_TAG,"destroy")}},{key:"_initTalk",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.talk&&(this.debug.log(this.LOG_TAG,"_initTalk this.talk is not null and destroy"),this.talk.destroy(),this.talk=null);var t=Object.assign({},V(this._opt),e);this.talk=new re(null,t),this.debug.log(this.LOG_TAG,"_initTalk",this.talk.getOption()),this._bindTalkEvents()}},{key:"_bindTalkEvents",value:function(){var e=this;Object.keys(T).forEach((function(t){e.talk.on(T[t],(function(r){e.emit(t,r)}))}))}},{key:"startTalk",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,o){t.debug.log(t.LOG_TAG,"startTalk",e,JSON.stringify(r)),t._initTalk(r),t.talk.startTalk(e).then((function(){n(),t.talk.once(b,(function(){t.debug.warn(t.LOG_TAG,"talkStreamClose -> stopTalk"),t.stopTalk().catch((function(e){t.debug.warn(t.LOG_TAG,"talkStreamClose stopTalk",e)})).finally((function(){t.emit(_,b)}))})),t.talk.once(y,(function(e){t.debug.error(t.LOG_TAG,"talkStreamError -> stopTalk"),t.stopTalk().catch((function(e){t.debug.warn(t.LOG_TAG,"talkStreamError stopTalk",e)})).finally((function(){t.emit(_,y)}))})),t.talk.once(w,(function(){t.debug.warn(t.LOG_TAG,"talkStreamInactive -> stopTalk"),t.stopTalk().catch((function(e){t.debug.warn(t.LOG_TAG,"talkStreamInactive stopTalk",e)})).finally((function(){t.emit(_,w)}))})),t.talk.once(g,(function(){t.debug.warn(t.LOG_TAG,"talkGetUserMediaTimeout -> stopTalk"),t.stopTalk().catch((function(e){t.debug.warn(t.LOG_TAG,"talkGetUserMediaTimeout stopTalk",e)})).finally((function(){t.emit(_,g)}))}))})).catch((function(e){o(e)}))}))}},{key:"stopTalk",value:function(){var e=this;return new Promise((function(t,r){e.debug.log(e.LOG_TAG,"stopTalk()"),e.talk||r("talk is not init"),e.talk.destroy(),e.talk=null,t()}))}},{key:"getTalkVolume",value:function(){var e=this;return new Promise((function(t,r){e.talk||r("talk is not init"),t(e.talk.volume)}))}},{key:"setTalkVolume",value:function(e){var t=this;return new Promise((function(r,n){t.debug.log(t.LOG_TAG,"setTalkVolume",e),t.talk||n("talk is not init"),t.talk.setVolume(e/100),r()}))}},{key:"downloadTempRtpFile",value:function(){var e=this;return new Promise((function(t,r){e.talk?(e.talk.downloadRtpFile(),t()):r("talk is not init")}))}},{key:"downloadTempG711File",value:function(){var e=this;return new Promise((function(t,r){e.talk?(e.talk.downloadG711File(),t()):r("talk is not init")}))}},{key:"downloadTempPcmFile",value:function(){var e=this;return new Promise((function(t,r){e.talk?(e.talk.downloadPcmFile(),t()):r("talk is not init")}))}},{key:"downloadTempFile",value:function(){var e=this;return new Promise((function(t,r){e.talk?(e.talk.downloadFile(),t()):r("talk is not init")}))}}])}(e);return ne.EVENTS=T,window.JessibucaProTalk=ne,window.JbProTalk=ne,window.WebPlayerProTalk=ne,ne}))},fd87:function(e,t,r){var n=r("74e8");n("Int8",(function(e){return function(t,r,n){return e(this,t,r,n)}}))}}]);