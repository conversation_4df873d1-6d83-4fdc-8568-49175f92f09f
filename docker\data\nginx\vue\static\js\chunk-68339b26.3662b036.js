(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-68339b26"],{"0bc2":function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/runtime/service/invokeReply",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/iot/runtime/prop/get",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/runtime/service/invoke",method:"post",data:e})}},"15fd":function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));a("a4d3"),a("caad"),a("2532");function i(e,t){if(null==e)return{};var a={};for(var i in e)if({}.hasOwnProperty.call(e,i)){if(t.includes(i))continue;a[i]=e[i]}return a}function n(e,t){if(null==e)return{};var a,n,s=i(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(n=0;n<r.length;n++)a=r[n],t.includes(a)||{}.propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}},5170:function(e,t,a){},"5f43":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"running-status"},[a("el-row",{attrs:{gutter:90}},[a("el-col",{staticClass:"status-col",attrs:{xs:24,sm:24,md:24,lg:14,xl:10}},[a("el-descriptions",{attrs:{column:1,border:""}},[a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-menu"}),e._v(" "+e._s(e.$t("device.running-status.866086-0"))+" ")]),a("span",{staticClass:"title"},[e._v(e._s(e.title))])],2),a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("svg-icon",{attrs:{"icon-class":"ota"}}),e._v(" "+e._s(e.$t("device.running-status.866086-1"))+" ")],1),a("el-button",{attrs:{type:"primary",size:"mini",plain:!0},on:{click:function(t){return e.viewVersion()}}},[e._v(e._s(e.$t("device.running-status.866086-44")))])],2),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",{staticClass:"emum-wrap"},[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticClass:"btn",class:{"is-active-btn":i.value===t.shadow},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:e.$t("device.running-status.866086-3"),disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:t.datatype.unit?e.$t("device.running-status.866086-5",[t.datatype.unit]):e.$t("device.running-status.866086-4"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"2px 0 0 5px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"integer"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{staticStyle:{"margin-left":"10px"},attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"4px 0 0 10px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,class:{"is-active-btn":i.value===t.shadow},staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{size:"small",placeholder:e.$t("device.running-status.866086-3"),disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,class:{"is-active-btn":i.value===t.shadow},staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:e.$t("device.running-status.866086-3"),size:"small",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:e.$t("device.running-status.866086-6")},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2),1==e.deviceInfo.isShadow&&3!=e.deviceInfo.status?a("el-descriptions",{staticStyle:{"margin-top":"30px"},attrs:{column:1,border:""}},[e.deviceInfo.thingsModels.length>0?a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v(e._s(e.$t("device.running-status.866086-9")))])]):e._e(),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:{minWidth:"100px"}}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(t){return a("el-button",{key:t.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:""}},[e._v(e._s(t.text))])})),1):a("el-select",{attrs:{placeholder:e.$t("device.running-status.866086-3"),disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),disabled:""},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),disabled:""},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),disabled:""},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{size:"mini","active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:e.$t("device.running-status.866086-3"),disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:e.$t("device.running-status.866086-3"),disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:e.$t("device.running-status.866086-4"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:e.$t("device.running-status.866086-7"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:e.$t("device.running-status.866086-8"),disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2):e._e()],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:10,xl:14}},[e.deviceInfo.chartList.length>0?a("el-row",{attrs:{gutter:20}},e._l(e.deviceInfo.chartList,(function(e,t){return a("el-col",{key:t,attrs:{xs:24,sm:12,md:12,lg:24,xl:8}},[a("el-card",{staticStyle:{"border-radius":"8px","margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("div",{ref:"map",refInFor:!0,staticStyle:{height:"230px",width:"185px",margin:"0 auto","margin-bottom":"15px"}})])],1)})),1):e._e()],1)],1),a("el-dialog",{attrs:{title:e.$t("device.running-status.866086-10"),visible:e.openVersion,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.openVersion=t}}},[a("el-form",{ref:"firmwareForm",attrs:{"label-width":"100px",model:e.firmwareParams,inline:!0,rules:e.rules}},[a("el-form-item",{attrs:{label:e.$t("device.running-status.866086-38"),prop:"firmwareType"}},[a("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("firmware.index.222541-51"),disabled:""},on:{change:e.handleVersionInputChange},model:{value:e.deviceInfo.firmwareType,callback:function(t){e.$set(e.deviceInfo,"firmwareType",t)},expression:"deviceInfo.firmwareType"}},e._l(e.firmwareTypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:e.$t("device.running-status.866086-39"),prop:""}},[a("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("device.running-status.866086-40"),disabled:""},model:{value:e.deviceInfo.firmwareVersion,callback:function(t){e.$set(e.deviceInfo,"firmwareVersion",t)},expression:"deviceInfo.firmwareVersion"}},[a("template",{slot:"prepend"},[e._v("Version")])],2)],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-tooltip",{attrs:{effect:"dark",content:e.$t("device.running-status.866086-41"),placement:"top-start"}},[a("el-button",{attrs:{type:"primary",disabled:3!==e.device.status},on:{click:e.getLatestFirmware}},[e._v(e._s(e.$t("device.running-status.866086-42")))])],1),a("el-button",{on:{click:e.cancel1}},[e._v(e._s(e.$t("cancel")))])],1)],1),a("el-dialog",{attrs:{title:e.$t("device.running-status.866086-10"),visible:e.openFirmware,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openFirmware=t}}},[null==e.firmware?a("div",{staticStyle:{"text-align":"center","font-size":"16px"}},[a("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a"}}),e._v(" "+e._s(e.$t("device.running-status.866086-11"))+" ")]):e._e(),null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-descriptions",{attrs:{column:1,border:"",size:"large",labelStyle:{width:"150px","font-weight":"bold"}}},[a("template",{slot:"title"},[a("el-link",{attrs:{icon:"el-icon-success",type:"success",underline:!1}},[e._v(e._s(e.$t("device.running-status.866086-12")))])],1),a("el-descriptions-item",{attrs:{label:e.$t("device.running-status.866086-13")}},[e._v(e._s(e.firmware.firmwareName))]),a("el-descriptions-item",{attrs:{label:e.$t("device.device-edit.148398-4")}},[e._v(e._s(e.firmware.productName))]),a("el-descriptions-item",{attrs:{label:e.$t("device.device-edit.148398-12")}},[e._v("Version "+e._s(e.firmware.version))]),a("el-descriptions-item",{attrs:{label:e.$t("device.running-status.866086-16")}},[a("el-link",{attrs:{href:e.getDownloadUrl(e.firmware.filePath),underline:!1,type:"primary"}},[e._v(e._s(e.getDownloadUrl(e.firmware.filePath)))])],1),a("el-descriptions-item",{attrs:{label:e.$t("device.running-status.866086-17")}},[e._v(e._s(e.firmware.remark))])],2):e._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-button",{attrs:{type:"success"},on:{click:e.otaUpgrade}},[e._v(e._s(e.$t("device.running-status.866086-18")))]):e._e(),a("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1)],1)},n=[],s=a("c7eb"),r=a("1da1"),o=a("5530"),l=a("15fd"),d=(a("d81d"),a("4e82"),a("b0c0"),a("a9e3"),a("814a")),c=a("0bc2"),u=a("67fa"),p=["firmwareVersion","wirelessVersion","firmwareType"],v={name:"running-status",props:{device:{type:Object,default:null}},watch:{device:{handler:function(e){e&&0!=e.deviceId&&e&&0!=e.deviceId&&(this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.$nextTick((function(){this.MonitorChart()})),this.deviceInfo.thingsModels&&this.deviceInfo.thingsModels.length>0&&(this.deviceInfo.thingsModels=this.device.thingsModels.sort((function(e,t){return t.order-e.order}))),this.deviceInfo.chartList&&this.deviceInfo.chartList.length>0&&(this.deviceInfo.chartList=this.deviceInfo.chartList.sort((function(e,t){return t.order-e.order}))))}}},data:function(){return{title:"设备控制 ",shadowUnEnable:!1,statusColor:{background:"#67C23A",color:"#fff",minWidth:"100px"},firmware:{},openFirmware:!1,loading:!0,deviceInfo:{boolList:[],enumList:[],stringList:[],integerList:[],decimalList:[],arrayList:[],thingsModels:[],chartList:[]},firmwareParams:{firmwareType:"",versionInput:""},monitorChart:[{chart:{},data:{id:"",name:"",value:""}}],openVersion:!1,firmwareTypeList:[{label:this.$t("firmware.index.222541-52"),value:1},{label:"HTTP",value:2}],remoteCommand:{},rules:{firmwareType:[{required:!0,message:this.$t("device.running-status.866086-43"),trigger:"blur"}]}}},mounted:function(){this.handleDeviceChange(this.device);var e=this.device,t=e.deviceId;e.serialNumber;t&&(this.initDataStatus(),this.initData())},methods:{handleDeviceChange:function(e){var t=this;if(e&&0!=e.deviceId&&this.device.thingsModels){var a=e.firmwareVersion,i=e.wirelessVersion,n=e.firmwareType,s=Object(l["a"])(e,p),r=Object(o["a"])({version:1===n?a:i,firmwareType:n},s);this.deviceInfo=r,this.updateDeviceStatus(this.deviceInfo),this.$nextTick((function(){t.MonitorChart()})),this.deviceInfo.thingsModels=this.deviceInfo.thingsModels.sort((function(e,t){return t.order-e.order})),this.deviceInfo.chartList=this.deviceInfo.chartList.sort((function(e,t){return t.order-e.order}))}},initData:function(){var e=this;this.$busEvent.$on("updateData",(function(t){e.updateParam(t)}))},initDataStatus:function(){var e=this;this.$busEvent.$on("updateStatus",(function(t){e.updateStatus(t)}))},updateStatus:function(e){var t=e.serialNumber,a=(e.productId,e.data);a&&this.deviceInfo.serialNumber==t&&(this.deviceInfo.status=a.status,this.deviceInfo.isShadow=a.isShadow,this.deviceInfo.rssi=a.rssi,this.updateDeviceStatus(this.deviceInfo))},updateParam:function(e){e.serialNumber,e.productId;var t=e.data,a=!1;if(t=t.message,t)for(var i=0;i<t.length;i++){for(var n=0;n<this.deviceInfo.thingsModels.length&&!a;n++){if(this.deviceInfo.thingsModels[n].id==t[i].id){var s=this.deviceInfo.thingsModels[n];"decimal"==this.deviceInfo.thingsModels[n].datatype.type||"integer"==this.deviceInfo.thingsModels[n].datatype.type?s.shadow=Number(t[i].value):s.shadow=t[i].value}if("object"==this.deviceInfo.thingsModels[n].datatype.type){for(var r=0;r<this.deviceInfo.thingsModels[n].datatype.params.length;r++)if(this.deviceInfo.thingsModels[n].datatype.params[r].id==t[i].id){this.deviceInfo.thingsModels[n].datatype.params[r].shadow=t[i].value,a=!0;break}}else if("array"==this.deviceInfo.thingsModels[n].datatype.type)if("object"==this.deviceInfo.thingsModels[n].datatype.arrayType)if(0==String(t[i].id).indexOf("array_"))for(var o=0;o<this.deviceInfo.thingsModels[n].datatype.arrayParams.length;o++){for(var l=0;l<this.deviceInfo.thingsModels[n].datatype.arrayParams[o].length;l++)if(this.deviceInfo.thingsModels[n].datatype.arrayParams[o][l].id==t[i].id){this.deviceInfo.thingsModels[n].datatype.arrayParams[o][l].shadow=t[i].value,a=!0;break}if(a)break}else for(var d=0;d<this.deviceInfo.thingsModels[n].datatype.arrayParams.length;d++)for(var c=0;c<this.deviceInfo.thingsModels[n].datatype.arrayParams[d].length;c++){var u=d>9?String(d):"0"+n,p="array_"+u+"_";this.deviceInfo.thingsModels[n].datatype.arrayParams[d][c].id==p+t[i].id&&(this.deviceInfo.thingsModels[n].datatype.arrayParams[d][c].shadow=t[i].value)}else for(var v=0;v<this.deviceInfo.thingsModels[n].datatype.arrayModel.length;v++)if(this.deviceInfo.thingsModels[n].datatype.arrayModel[v].id==t[i].id){this.deviceInfo.thingsModels[n].datatype.arrayModel[v].shadow=t[i].value;break}}for(var h=0;h<this.deviceInfo.chartList.length;h++){if(0==this.deviceInfo.chartList[h].id.indexOf("array_")){if(this.deviceInfo.chartList[h].id==t[i].id){this.deviceInfo.chartList[h].shadow=t[i].value;for(var m=0;m<this.monitorChart.length;m++)if(t[i].id==this.monitorChart[m].data.id){var f=[{value:this.deviceInfo.chartList[h].shadow,name:this.monitorChart[m].data.name}];this.monitorChart[m].chart.setOption({series:[{data:f}]});break}}}else if(this.deviceInfo.chartList[h].id==t[i].id){this.deviceInfo.chartList[h].shadow=t[i].value;for(var y=0;y<this.monitorChart.length;y++)if(t[i].id==this.monitorChart[y].data.id){var b=[{value:this.deviceInfo.chartList[h].shadow,name:this.monitorChart[y].data.name}];this.monitorChart[y].chart.setOption({series:[{data:b}]});break}}if(a)break}}},mqttPublish:function(e,t){var a=this;return Object(r["a"])(Object(s["a"])().mark((function i(){var n,r,o,l;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n={},n[t.id]=t.shadow,r={deviceId:e.deviceId,modelId:t.modelId},i.next=5,Object(u["d"])(r);case 5:if(o=i.sent,200==o.code){i.next=9;break}return a.$message({type:"warning",message:o.msg}),i.abrupt("return");case 9:if(l={serialNumber:e.serialNumber,productId:e.productId,remoteCommand:n,identifier:t.id,modelName:t.name,isShadow:3!=e.status,type:t.type},3===a.device.status||1===a.device.isShadow){i.next=14;break}return 1===a.device.status?title=a.$t("device.device-variable.930930-0"):2===a.device.status?title=a.$t("device.device-variable.930930-1"):title=a.$t("device.device-variable.930930-2"),a.$message({type:"warning",message:title}),i.abrupt("return");case 14:if("MODBUS-TCP"!==a.deviceInfo.protocolCode&&"MODBUS-RTU"!==a.deviceInfo.protocolCode||3!==a.device.status){i.next=19;break}return i.next=17,Object(c["c"])(l).then((function(e){200===e.code?a.$message({type:"success",message:a.$t("device.running-status.866086-25")}):a.$message.error(e.msg)}));case 17:i.next=21;break;case 19:return i.next=21,Object(c["b"])(l).then((function(e){200===e.code?a.$message({type:"success",message:a.$t("device.running-status.866086-25")}):a.$message.error(e.msg)}));case 21:case"end":return i.stop()}}),i)})))()},enumButtonClick:function(e,t,a){t.shadow=a,this.mqttPublish(e,t)},viewVersion:function(){this.openVersion=!0,this.firmwareParams.firmwareType=1,this.firmwareParams.versionInput="",this.handleVersionInputChange()},handleVersionInputChange:function(){1==this.firmwareParams.firmwareType?this.firmwareParams.versionInput="Version"+this.device.firmwareVersion:this.firmwareParams.versionInput="Version"+this.device.wirelessVersion},cancel1:function(){this.openVersion=!1},updateDeviceStatus:function(e){3==e.status?(this.statusColor.background="#12d09f",this.title=this.$t("device.running-status.866086-26"),this.shadowUnEnable=!1):1==e.isShadow?(this.statusColor.background="#486FF2",this.title=this.$t("device.running-status.866086-27"),this.shadowUnEnable=!1):(this.statusColor.background="#909399",this.title=this.$t("device.running-status.866086-28"),this.shadowUnEnable=!0),this.$emit("statusEvent",this.deviceInfo.status)},arrayItemChange:function(e,t){for(var a="",i=0;i<t.datatype.arrayCount;i++)a+=t.datatype.arrayModel[i].shadow+",";a=a.substring(0,a.length-1),t.shadow=a},arrayInputChange:function(e,t){var a=e.split(",");if(a.length!=t.datatype.arrayCount)this.$modal.alertWarning(this.$t("device.running-status.866086-29")+t.datatype.arrayCount+this.$t("device.running-status.866086-30"));else for(var i=0;i<t.datatype.arrayCount;i++)t.datatype.arrayModel[i].shadow=a[i]},otaUpgrade:function(){var e=this,t="/"+this.deviceInfo.productId+"/"+this.deviceInfo.serialNumber+"/ota/get",a='{"version":'+this.firmware.version+',"downloadUrl":"'+this.getDownloadUrl(this.firmware.filePath)+'"}';this.$mqttTool.publish(t,a,this.$t("device.running-status.866086-31")).then((function(t){e.$modal.notifySuccess(t)})).catch((function(t){e.$modal.notifyError(t)})),this.openFirmware=!1},getLatestFirmware:function(){var e=this,t=this.deviceInfo,a=t.deviceId,i=t.firmwareType;Object(d["d"])(a,i).then((function(t){200===t.code&&(e.firmware=t.data,e.openFirmware=!0)}))},cancel:function(){this.openFirmware=!1},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},MonitorChart:function(){for(var e=0;e<this.deviceInfo.chartList.length;e++){var t;this.monitorChart[e]={chart:this.$echarts.init(this.$refs.map[e]),data:{id:this.deviceInfo.chartList[e].id,name:this.deviceInfo.chartList[e].name,value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min}},t={tooltip:{formatter:" {b} <br/> {c}"+this.deviceInfo.chartList[e].datatype.unit},series:[{name:this.deviceInfo.chartList[e].datatype.type,type:"gauge",min:this.deviceInfo.chartList[e].datatype.min,max:this.deviceInfo.chartList[e].datatype.max,colorBy:"data",splitNumber:10,radius:"100%",splitLine:{distance:4},axisLabel:{fontSize:10,distance:10},axisTick:{distance:4},axisLine:{lineStyle:{width:8,color:[[.2,"#409EFF"],[.8,"#12d09f"],[1,"#F56C6C"]],opacity:.3}},pointer:{icon:"triangle",length:"60%",width:7},progress:{show:!0,width:8},detail:{valueAnimation:!0,formatter:"{value} "+this.deviceInfo.chartList[e].datatype.unit,offsetCenter:[0,"80%"],fontSize:20},data:[{value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min,name:this.deviceInfo.chartList[e].name}],title:{offsetCenter:[0,"115%"],fontSize:16}}]},t&&this.monitorChart[e].chart.setOption(t)}}}},h=v,m=(a("d854"),a("2877")),f=Object(m["a"])(h,i,n,!1,null,"53fe5367",null);t["default"]=f.exports},"67fa":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"a",(function(){return r})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"d",(function(){return d}));var i=a("b775");function n(e){return Object(i["a"])({url:"/order/control/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/order/control/"+e,method:"get"})}function r(e){return Object(i["a"])({url:"/order/control",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/order/control",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/order/control/"+e,method:"delete"})}function d(e){return Object(i["a"])({url:"/order/control/get",method:"get",params:e})}},"814a":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"f",(function(){return s})),a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return d})),a.d(t,"b",(function(){return c}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function r(e,t){return Object(i["a"])({url:"/iot/firmware/getLatest?deviceId="+e+"&firmwareType="+t,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},d854:function(e,t,a){"use strict";a("5170")}}]);