(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ee8ac7c"],{"01ca":function(e,t,i){"use strict";i.d(t,"h",(function(){return n})),i.d(t,"d",(function(){return s})),i.d(t,"i",(function(){return a})),i.d(t,"a",(function(){return o})),i.d(t,"g",(function(){return c})),i.d(t,"k",(function(){return d})),i.d(t,"c",(function(){return l})),i.d(t,"b",(function(){return u})),i.d(t,"f",(function(){return m})),i.d(t,"e",(function(){return v})),i.d(t,"j",(function(){return h}));var r=i("b775");function n(e){return Object(r["a"])({url:"/iot/model/list",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/iot/model/"+e,method:"get"})}function a(e){return Object(r["a"])({url:"/iot/model/permList/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/iot/model",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/iot/model/import",method:"post",data:e})}function d(e){return Object(r["a"])({url:"/iot/model",method:"put",data:e})}function l(e){return Object(r["a"])({url:"/iot/model/"+e,method:"delete"})}function u(e){return Object(r["a"])({url:"/iot/model/cache/"+e,method:"get"})}function m(e){return Object(r["a"])({url:"/iot/model/listModbus",method:"get",params:e})}function v(e){return Object(r["a"])({url:"/iot/model/write",method:"get",params:e})}function h(e){return Object(r["a"])({url:"/iot/model/refresh?productId="+e,method:"post"})}},b52e:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("right-toolbar",{attrs:{showSearch:e.showSearch,search:!1},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceUserList,border:!1},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-1"),align:"center",prop:"userId",width:"100"}}),i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-2"),align:"left",prop:"userName","min-width":"140"}}),i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-3"),align:"center",prop:"phonenumber",width:"150"}}),i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-4"),align:"center",prop:"isOwner",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isOwner?i("el-tag",{attrs:{type:"primary"}},[e._v(e._s(e.$t("device.device-user.037521-5")))]):i("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("device.device-user.037521-6")))])]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-7"),align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),i("el-table-column",{attrs:{label:e.$t("device.device-user.037521-8"),align:"left",prop:"remark","header-align":"center","min-width":"150"}}),i("el-table-column",{attrs:{fixed:"right",label:e.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner&&1==e.deviceInfo.isOwner?i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:query"],expression:"['iot:device:user:query']"}],attrs:{size:"small",type:"text",icon:"el-icon-view"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v(" "+e._s(e.$t("device.device-user.037521-10"))+" ")]):e._e(),0==t.row.isOwner&&1==e.deviceInfo.isOwner?i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:remove"],expression:"['iot:device:user:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v(" "+e._s(e.$t("device.device-user.037521-11"))+" ")]):e._e()]}}])})],1),i("el-dialog",{attrs:{title:e.$t("device.device-user.037521-12"),visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[i("div",{staticStyle:{"margin-top":"-50px"}},[i("el-divider")],1),1==e.type?i("el-form",{ref:"queryForm",attrs:{model:e.permParams,rules:e.rules,inline:!0,"label-width":"80px"}},[i("el-form-item",{attrs:{label:e.$t("device.device-user.037521-3"),prop:"phonenumber"}},[i("el-input",{staticStyle:{width:"240px"},attrs:{type:"text",placeholder:e.$t("device.device-user.037521-13"),minlength:"10",clearable:"",size:"small","show-word-limit":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.permParams.phonenumber,callback:function(t){e.$set(e.permParams,"phonenumber",t)},expression:"permParams.phonenumber"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.userQuery}},[e._v(e._s(e.$t("device.device-user.037521-14")))])],1)],1):e._e(),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.permsLoading,expression:"permsLoading"}],staticStyle:{"background-color":"#f8f8f9","line-height":"28px"}},[e.message?i("div",{staticStyle:{padding:"20px"}},[e._v(e._s(e.message))]):e._e(),e.form.userId?i("div",{staticStyle:{padding:"15px"}},[i("div",{staticStyle:{"font-weight":"bold","line-height":"28px"}},[e._v(e._s(e.$t("device.device-user.037521-15")))]),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v(e._s(e.$t("device.device-user.037521-16")))]),i("span",[e._v(e._s(e.form.userId))]),i("br"),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v(e._s(e.$t("device.device-user.037521-3"))+"：")]),i("span",[e._v(e._s(e.form.phonenumber))]),i("br"),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v(e._s(e.$t("device.device-user.037521-2"))+"：")]),i("span",[e._v(e._s(e.form.userName))]),i("br"),i("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v(e._s(e.$t("device.device-user.037521-19")))]),i("el-table",{ref:"multipleTable",attrs:{data:e.sharePermissionList,"highlight-current-row":"",size:"mini"},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{key:"modelName",attrs:{label:e.$t("device.device-user.037521-20"),align:"center",prop:"modelName"}}),i("el-table-column",{key:"identifier",attrs:{label:e.$t("device.device-user.037521-21"),align:"center",prop:"identifier"}}),i("el-table-column",{key:"remark",attrs:{label:e.$t("device.device-edit.148398-17"),align:"left","min-width":"100","header-align":"center",prop:"remark"}})],1),i("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v(e._s(e.$t("device.device-edit.148398-17")))]),i("el-input",{attrs:{type:"textarea",placeholder:e.$t("plzInput"),rows:"2"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1):e._e()]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:edit"],expression:"['iot:device:user:edit']"}],attrs:{type:"primary",disabled:!e.form.userId||!e.deviceInfo.deviceId},on:{click:e.submitForm}},[e._v(e._s(e.$t("device.device-user.037521-24")))]),i("el-button",{on:{click:e.closeSelectUser}},[e._v(e._s(e.$t("device.device-user.037521-25")))])],1)],1)],1)},n=[],s=i("c7eb"),a=i("1da1"),o=(i("99af"),i("a15b"),i("d81d"),i("01ca")),c=i("b775");function d(e){return Object(c["a"])({url:"/iot/share/list",method:"get",params:e})}function l(e){return Object(c["a"])({url:"/iot/share/shareUser",method:"get",params:e})}function u(e,t){return Object(c["a"])({url:"/iot/share/detail?deviceId="+e+"&userId="+t,method:"get"})}function m(e){return Object(c["a"])({url:"/iot/share",method:"post",data:e})}function v(e){return Object(c["a"])({url:"/iot/share",method:"put",data:e})}function h(e){return Object(c["a"])({url:"/iot/share",method:"delete",data:e})}var p={name:"device-user",dicts:["iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:{deep:!0,handler:function(e,t){e.deviceId&&e.deviceId!==t.deviceId&&(this.deviceInfo=e,this.queryParams.deviceId=e.deviceId,this.getList())}}},data:function(){return{type:1,message:"",permsLoading:!1,showSearch:!0,sharePermissionList:[],open:!1,permParams:{userName:void 0,phonenumber:void 0,deviceId:null},rules:{phonenumber:[{required:!0,message:this.$t("device.device-user.037521-26"),trigger:"blur"},{min:11,max:11,message:this.$t("device.device-user.037521-27"),trigger:"blur"}]},loading:!0,total:0,deviceUserList:[],deviceInfo:{},queryParams:{pageNum:1,pageSize:10,deviceName:null,userName:null,userId:null,tenantName:null,isOwner:null},form:{}}},created:function(){this.queryParams.deviceId=this.device.deviceId,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,d(this.queryParams).then((function(t){e.deviceUserList=t.rows,e.total=t.total,e.loading=!1}))},reset:function(){this.form={deviceId:null,userId:null,deviceName:null,userName:null,perms:null,phonenumber:null,remark:null},this.sharePermissionList=[],this.message="",this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset(),this.type=2,u(e.deviceId,e.userId).then((function(e){t.form=e.data,t.getPermissionList(),t.open=!0}))},shareDevice:function(){this.type=1,this.open=!0,this.form={}},handleDelete:function(e){var t=this,i={deviceId:e.deviceId,userId:e.userId};this.$modal.confirm(this.$t("device.device-user.037521-28")).then((function(){return h(i)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("device.device-user.037521-29"))})).catch((function(){}))},userQuery:function(){var e=this;this.$refs["queryForm"].validate((function(t){t&&(e.reset(),e.getShareUser())}))},getShareUser:function(){var e=this;this.permsLoading=!0,this.deviceInfo.deviceId?(this.permParams.deviceId=this.deviceInfo.deviceId,l(this.permParams).then((function(t){t.data?(e.form=t.data,e.getPermissionList()):(e.permsLoading=!1,e.message=e.$t("device.device-user.037521-31"))}))):this.$modal.alert(this.$t("device.device-user.037521-30"))},getPermissionList:function(){var e=this;return Object(a["a"])(Object(s["a"])().mark((function t(){var i;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=[],e.form.perms&&(i=e.form.perms.split(",")),Object(o["i"])(e.deviceInfo.productId).then((function(t){if(e.sharePermissionList=[{identifier:"ota",modelName:e.$t("device.device-user.037521-32"),remark:e.$t("device.device-user.037521-33")},{identifier:"timer",modelName:e.$t("device.device-user.037521-34"),remark:e.$t("device.device-user.037521-35")},{identifier:"log",modelName:e.$t("device.device-user.037521-36"),remark:e.$t("device.device-user.037521-37")},{identifier:"monitor",modelName:e.$t("device.device-user.037521-38"),remark:e.$t("device.device-user.037521-39")},{identifier:"statistic",modelName:e.$t("device.device-user.037521-40"),remark:e.$t("device.device-user.037521-41")}],e.sharePermissionList=e.sharePermissionList.concat(t.data),i.length>0)for(var r=function(t){for(var r=0;r<i.length;r++)if(e.sharePermissionList[t].identifier==i[r]){e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(e.sharePermissionList[t],!0)}));break}},n=0;n<e.sharePermissionList.length;n++)r(n);e.permsLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},resetUserQuery:function(){this.resetForm("queryForm"),this.reset()},closeSelectUser:function(){this.open=!1,this.resetUserQuery()},handleSelectionChange:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},handleSelectionAll:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},submitForm:function(){var e=this;2==this.type?v(this.form).then((function(t){e.$modal.msgSuccess(e.$t("device.device-user.037521-42")),e.resetUserQuery(),e.open=!1,e.getList()})):1==this.type&&(this.form.deviceId=this.deviceInfo.deviceId,this.form.deviceName=this.deviceInfo.deviceName,m(this.form).then((function(t){e.$modal.msgSuccess(e.$t("device.device-user.037521-43")),e.resetUserQuery(),e.open=!1,e.getList()})))}}},f=p,g=i("2877"),b=Object(g["a"])(f,r,n,!1,null,null,null);t["default"]=b.exports}}]);