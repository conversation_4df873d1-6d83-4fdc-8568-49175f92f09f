(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b0162818","chunk-264209f0","chunk-722c5e57"],{"0e0c":function(t,e,r){},"1e36":function(t,e,r){"use strict";r.d(e,"d",(function(){return i})),r.d(e,"e",(function(){return a})),r.d(e,"c",(function(){return s})),r.d(e,"a",(function(){return c})),r.d(e,"f",(function(){return n})),r.d(e,"b",(function(){return l}));var o=r("b775");function i(t){return Object(o["a"])({url:"/iot/category/list",method:"get",params:t})}function a(t){return Object(o["a"])({url:"/iot/category/shortlist",method:"get",params:t})}function s(t){return Object(o["a"])({url:"/iot/category/"+t,method:"get"})}function c(t){return Object(o["a"])({url:"/iot/category",method:"post",data:t})}function n(t){return Object(o["a"])({url:"/iot/category",method:"put",data:t})}function l(t){return Object(o["a"])({url:"/iot/category/"+t,method:"delete"})}},3021:function(t,e,r){"use strict";r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return a})),r.d(e,"d",(function(){return s})),r.d(e,"b",(function(){return c}));var o=r("b775");function i(t){return Object(o["a"])({url:"/sip/sipconfig/"+t,method:"get"})}function a(t){return Object(o["a"])({url:"/sip/sipconfig",method:"post",data:t})}function s(t){return Object(o["a"])({url:"/sip/sipconfig",method:"put",data:t})}function c(t){return Object(o["a"])({url:"/sip/sipconfig/product/"+t,method:"delete"})}},"4efc":function(t,e){t.exports="data:image/png;base64,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"},"52bb":function(t,e){t.exports="data:image/png;base64,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"},"7b77":function(t,e,r){"use strict";r("0e0c")},"98bc":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"iot-product"},[o("el-card",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"card-search"},[o("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"}},[o("el-form-item",{attrs:{prop:"productName"}},[o("el-input",{attrs:{placeholder:t.$t("product.index.091251-1"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),o("el-form-item",{attrs:{prop:"categoryName"}},[o("el-input",{attrs:{placeholder:t.$t("product.index.091251-3"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.categoryName,callback:function(e){t.$set(t.queryParams,"categoryName",e)},expression:"queryParams.categoryName"}})],1),o("el-form-item",{attrs:{prop:"status"}},[o("el-select",{attrs:{placeholder:t.$t("product.index.091251-5"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.dict.type.iot_product_status,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),o("div",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),o("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))])],1)],1)],1),o("el-card",{staticClass:"product-card"},[o("el-row",{staticClass:"product-header",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:add"],expression:"['iot:product:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:t.handleAddProduct}},[t._v(t._s(t.$t("add")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:edit"],expression:"['iot:product:edit']"}],attrs:{plain:"",icon:"el-icon-document-copy",size:"small"},on:{click:t.selectProduct}},[t._v(t._s(t.$t("product.product-things-model.142341-92")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:add"],expression:"['iot:product:add']"}],attrs:{plain:"",icon:"el-icon-upload2",size:"small"},on:{click:t.handleImport}},[t._v(t._s(t.$t("import")))])],1),o("el-col",{staticClass:"product-checkbox",attrs:{span:1.5}},[o("el-checkbox",{on:{change:t.handleQuery},model:{value:t.queryParams.showSenior,callback:function(e){t.$set(t.queryParams,"showSenior",e)},expression:"queryParams.showSenior"}},[o("div",{staticClass:"el-checkbox__label"},[t._v(t._s(t.$t("product.index.091251-8")))])]),o("el-tooltip",{attrs:{content:t.$t("product.index.091251-9"),placement:"top"}},[o("i",{staticClass:"el-icon-question"})])],1),o("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),o("el-row",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{gutter:20}},t._l(t.productList,(function(e,i){return o("el-col",{key:i,staticClass:"product-item",attrs:{xs:24,sm:12,md:12,lg:8,xl:6}},[o("el-card",{staticClass:"card-item",staticStyle:{"border-radius":"8px"},attrs:{"body-style":{padding:"0px"},shadow:"always"}},[o("div",{staticClass:"item-title"},[o("div",[null!=e.imgUrl&&""!=e.imgUrl?o("el-image",{staticClass:"img",attrs:{lazy:"","preview-src-list":[t.baseUrl+e.imgUrl],src:t.baseUrl+e.imgUrl,fit:"cover"}}):2==e.deviceType?o("el-image",{staticClass:"img",attrs:{"preview-src-list":[r("4efc")],src:r("4efc"),fit:"cover"}}):3==e.deviceType?o("el-image",{staticClass:"img",attrs:{"preview-src-list":[r("c59e")],src:r("c59e"),fit:"cover"}}):o("el-image",{staticClass:"img",attrs:{"preview-src-list":[r("52bb")],src:r("52bb"),fit:"cover"}})],1),o("div",{staticClass:"title"},[o("div",{staticClass:"name",on:{click:function(r){return t.handleEditProduct(e)}}},[t._v(t._s(e.productName))]),o("div",{staticClass:"tag"},[1==e.isSys?o("el-tag",{attrs:{type:"info",size:"mini"}},[t._v(t._s(t.$t("product.index.091251-11")))]):o("el-tag",{attrs:{type:"info",size:"mini"}},[t._v(t._s(e.tenantName))])],1)]),o("div",{staticStyle:{width:"45px"}}),o("div",{staticClass:"status"},[2==e.status?o("el-tooltip",{attrs:{effect:"dark",content:t.$t("product.index.091251-12"),placement:"top-start"}},[o("el-button",{staticClass:"btn-published",attrs:{plain:"",size:"mini",disabled:0===e.isOwner},on:{click:function(r){return t.changeProductStatus(e.productId,1,e.deviceType)}}},[o("svg-icon",{attrs:{"icon-class":"check-circle-solid"}}),t._v(" "+t._s(t.$t("product.index.091251-13"))+" ")],1)],1):t._e(),1==e.status?o("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:t.$t("product.index.091251-14"),placement:"top-start"}},[o("el-button",{staticClass:"btn-unpublished",attrs:{type:"info",size:"mini",disabled:0===e.isOwner},on:{click:function(r){return t.changeProductStatus(e.productId,2,e.deviceType)}}},[o("svg-icon",{attrs:{"icon-class":"exclamation-circle-solid"}}),t._v(" "+t._s(t.$t("product.index.091251-15"))+" ")],1)],1):t._e()],1)]),o("el-row",{attrs:{gutter:10}},[o("el-col",{staticClass:"card-item-desc",staticStyle:{"padding-left":"24px"},attrs:{span:12}},[o("el-descriptions",{staticClass:"card-item-desc-item",attrs:{column:1,size:"small"}},[o("el-descriptions-item",{attrs:{label:t.$t("product.index.091251-16")}},[o("el-link",{staticClass:"product-category",attrs:{type:"primary",underline:!1}},[t._v(" "+t._s(e.categoryName)+" ")])],1)],1)],1),o("el-col",{staticClass:"card-item-desc",staticStyle:{"padding-right":"24px"},attrs:{span:12}},[o("el-descriptions",{staticClass:"card-item-desc-item",attrs:{column:1,size:"small"}},[o("el-descriptions-item",{attrs:{label:t.$t("product.index.091251-18")}},[o("dict-tag",{attrs:{options:t.dict.type.iot_network_method,value:e.networkMethod}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",{staticStyle:{"padding-left":"24px"},attrs:{span:12}},[o("el-descriptions",{staticClass:"card-item-desc-item",attrs:{column:1,size:"small"}},[o("el-descriptions-item",{attrs:{label:t.$t("product.index.091251-17")}},[o("dict-tag",{attrs:{options:t.dict.type.iot_device_type,value:e.deviceType}})],1)],1)],1),o("el-col",{staticStyle:{"padding-right":"24px"},attrs:{span:12}},[o("el-descriptions",{staticClass:"card-item-desc-item",attrs:{column:1,size:"small"}},[o("el-descriptions-item",{attrs:{label:t.$t("product.index.091251-19")}},[1==e.isAuthorize?o("el-tag",{staticClass:"tag-item-success",attrs:{size:"mini"}},[t._v(" "+t._s(t.$t("product.index.091251-20"))+" ")]):o("el-tag",{staticClass:"tag-item-error",staticStyle:{color:"#c0c4cc","border-radius":"2px",border:"1px solid #c0c4cc","background-color":"#ffffff"},attrs:{size:"mini"}},[t._v(" "+t._s(t.$t("product.index.091251-21"))+" ")])],1)],1)],1)],1),o("el-divider",{staticClass:"divider"}),o("div",{staticClass:"card-item-btns"},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:query"],expression:"['iot:product:query']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleEditProduct(e)}}},[t._v(t._s(t.$t("product.index.091251-22")))]),o("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:query"],expression:"['iot:product:query']"}],staticClass:"btn-item-line"},[t._v("|")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleViewDevice(e.productId)}}},[t._v(t._s(t.$t("product.index.091251-24")))]),3!==e.deviceType&&1==t.isShowScada?o("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["scada:center:share"],expression:"['scada:center:share']"}],staticClass:"btn-item-line"},[t._v("|")]):t._e(),3!==e.deviceType&&1==t.isShowScada?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["scada:center:share"],expression:"['scada:center:share']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleScadaShare(e)}}},[t._v(" "+t._s(t.$t("product.index.091251-42"))+" ")]):t._e(),3!==e.deviceType&&1==t.isShowScada?o("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["scada:center:edit"],expression:"['scada:center:edit']"}],staticClass:"btn-item-line"},[t._v("|")]):t._e(),3!==e.deviceType&&1==t.isShowScada?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["scada:center:edit"],expression:"['scada:center:edit']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleGoToScada(e)}}},[t._v(" "+t._s(t.$t("product.index.091251-40"))+" ")]):t._e(),1==e.status&&0!=e.isOwner?o("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:remove"],expression:"['iot:product:remove']"}],staticClass:"btn-item-line"},[t._v("|")]):t._e(),1==e.status&&0!=e.isOwner?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:remove"],expression:"['iot:product:remove']"}],attrs:{size:"mini",type:"text"},on:{click:function(r){return t.handleDelete(e)}}},[t._v(" "+t._s(t.$t("del"))+" ")]):t._e()],1)],1)],1)})),1),0==t.total?o("el-empty",{attrs:{description:t.$t("product.index.091251-25")}}):t._e(),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 20px 0"},attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize,pageSizes:[12,24,36,60]},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1),o("el-dialog",{attrs:{title:t.title,visible:t.open,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[o("el-link",{staticStyle:{"padding-left":"10px"},attrs:{type:"danger",underline:!1}},[t._v(t._s(t.$t("product.index.091251-26")))]),o("el-form",{attrs:{"label-width":"80px"}},[o("el-form-item",{attrs:{label:t.$t("product.index.091251-27")}},[o("el-radio-group",{model:{value:t.form.datatype,callback:function(e){t.$set(t.form,"datatype",e)},expression:"form.datatype"}},t._l(t.dict.type.iot_device_chip,(function(e){return o("el-radio",{key:e.value,staticStyle:{"margin-top":"15px",width:"160px"},attrs:{label:e.value}},[t._v(t._s(e.label))])})),1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary",disabled:""},on:{click:t.downloadSdk}},[t._v(t._s(t.$t("product.index.091251-28")))]),o("el-button",{on:{click:t.cancel}},[t._v(t._s(t.$t("product.index.091251-29")))])],1)],1),o("el-dialog",{attrs:{title:t.uploadImport.title,visible:t.uploadImport.open,width:"500px","append-to-body":""},on:{"update:visible":function(e){return t.$set(t.uploadImport,"open",e)}}},[o("el-upload",{ref:"uploadImport",attrs:{limit:1,accept:".json",headers:t.uploadImport.headers,action:t.uploadImport.url,disabled:t.uploadImport.isUploading,"on-progress":t.handleFileUploadProgress,"on-success":t.handleFileSuccess,"before-upload":t.handleBeforeUpload,"auto-upload":!1,drag:""}},[o("i",{staticClass:"el-icon-upload"}),o("div",{staticClass:"el-upload__text"},[t._v(" "+t._s(t.$t("dragFileTips"))+" "),o("em",[t._v(t._s(t.$t("clickFileTips")))])]),o("div",{staticClass:"el-upload__tip",staticStyle:{color:"red"},attrs:{slot:"tip"},slot:"tip"},[t._v(t._s(t.$t("scada.topoMain.320129-1")))])]),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitFileForm}},[t._v(t._s(t.$t("confirm")))]),o("el-button",{on:{click:function(e){t.uploadImport.open=!1}}},[t._v(t._s(t.$t("cancel")))])],1)],1),o("product-add",{ref:"productAdd"}),o("product-list",{ref:"productList",attrs:{productId:t.form.productId,showSenior:t.queryParams.showSenior},on:{productEvent:function(e){return t.getProductData(e)}}})],1)},i=[],a=r("c7eb"),s=r("1da1"),c=(r("14d9"),r("b0c0"),r("a9e3"),r("d3b7"),r("9b9c")),n=r("3021"),l=r("e350"),d=r("83d6"),u=r("e51f"),p=r("b1cc"),m=r("5f87"),f={name:"Product",components:{productList:u["default"],productAdd:p["default"]},dicts:["iot_yes_no","iot_product_status","iot_device_type","iot_network_method","iot_vertificate_method","iot_device_chip"],data:function(){return{loading:!0,total:0,productList:[],title:"",open:!1,showSearch:!0,isShowScada:d["a"].isShowScada,queryParams:{pageNum:1,pageSize:12,showSenior:!0,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:null,deviceType:null,networkMethod:null},uploadImport:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(m["a"])()},url:"/prod-api/iot/product/importJson"},fileType:["json"],form:{},baseUrl:"/prod-api",isScadaShare:!1,productId:null,scadaGuid:""}},created:function(){this.getList()},activated:function(){var t=this.$route.query.t;null!=t&&t!=this.uniqueId&&(this.uniqueId=t,this.queryParams.pageNum=Number(this.$route.query.pageNum),this.getList())},methods:{getList:function(){var t=this;this.loading=!0,Object(c["g"])(this.queryParams).then((function(e){t.productList=e.rows,t.total=e.total,t.loading=!1}))},handleImport:function(){this.uploadImport.title="导入产品",this.uploadImport.open=!0},handleBeforeUpload:function(t){if(this.fileType){var e=t.name.split("."),r=e[e.length-1],o=this.fileType.indexOf(r)>=0;return!!o||(this.$modal.msgError("文件格式不正确，请上传json文件！"),!1)}},handleFileUploadProgress:function(){this.uploadImport.isUploading=!0},handleFileSuccess:function(t,e,r){this.uploadImport.open=!1,this.uploadImport.isUploading=!1,this.$refs.uploadImport.clearFiles(),this.$alert(t.msg,"导入结果",{dangerouslyUseHTMLString:!0}),this.importLoading=!1,this.getList()},submitFileForm:function(){this.$refs.uploadImport.submit(),this.importLoading=!0},handleAddProduct:function(){this.$refs.productAdd.open=!0,this.$refs.productAdd.reset()},selectProduct:function(){this.$refs.productList.queryParams.status="",this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(t){var e=t.productId;this.handleCopy(e)},handleCopy:function(t){var e=this;Object(c["c"])(t).then((function(t){e.$message.success(t.msg)})).catch((function(){})),setTimeout((function(){e.getList()}),2e3)},getDeviceCountByProductId:function(t){return new Promise((function(e,r){Object(c["e"])(t).then((function(t){e(t)})).catch((function(t){r(t)}))}))},changeProductStatus:function(t,e,r){var o=this;return Object(s["a"])(Object(a["a"])().mark((function i(){var s,n,d,u;return Object(a["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(s=o.$t("product.index.091251-30"),2!=e){i.next=9;break}if(n=Object(l["a"])(["iot:product:add"]),n){i.next=6;break}return o.$modal.alertError(o.$t("product.index.091251-31")),i.abrupt("return");case 6:s=o.$t("product.index.091251-32"),i.next=18;break;case 9:if(1!=e){i.next=18;break}if(d=Object(l["a"])(["iot:product:edit"]),d){i.next=14;break}return o.$modal.alertError(o.$t("product.index.091251-31")),i.abrupt("return");case 14:return i.next=16,o.getDeviceCountByProductId(t);case 16:u=i.sent,u.data>0&&(s=o.$t("product.index.091251-33",[u.data]));case 18:o.$confirm(s,o.$t("product.index.091251-34"),{confirmButtonText:o.$t("product.index.091251-35"),cancelButtonText:o.$t("product.index.091251-29"),type:"warning"}).then((function(){var i={};i.productId=t,i.status=e,i.deviceType=r,Object(c["b"])(i).then((function(t){o.getList(),o.$modal.alertSuccess(t.msg)})).catch((function(){}))})).catch((function(){}));case 19:case"end":return i.stop()}}),i)})))()},handleViewDevice:function(t){this.$router.push({path:"/iot/device",query:{t:Date.now(),productId:t}})},handleGoToScada:function(t){var e=t.scadaId,r=t.guid,o=t.productId;if(r){var i=this.$router.resolve({path:"/scada/topo/editor",query:{id:e,guid:r,type:1}});window.open(i.href,"_blank")}else this.$router.push({path:"/scada/center/temp",query:{productId:o}})},handleScadaShare:function(t){t.guid?(this.isScadaShare=!0,this.scadaGuid=t.guid,this.productId=t.productId):this.$message({type:"warning",message:this.$t("product.index.091251-41")})},cancel:function(){this.open=!1,this.reset()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleGeneratorSDK:function(t){this.title=this.$t("product.index.091251-38"),this.open=!0},downloadSdk:function(){this.$download.zip("/iot/tool/genSdk?deviceChip=1","fastbee-sdk")},handleDelete:function(t){var e=this,r=t.productId||this.ids,o="";this.$modal.confirm(this.$t("product.index.091251-39",[r])).then((function(){return Object(n["b"])(r).then((function(t){})),Object(c["d"])(r).then((function(t){o=t.msg}))})).then((function(){e.getList(),e.$modal.msgSuccess(o)})).catch((function(){}))},handleEditProduct:function(t){var e=0;0!=t&&(e=t.productId||this.ids),this.$router.push({path:"/iot/product-edit",query:{productId:e,pageNum:this.queryParams.pageNum}})}}},h=f,g=(r("7b77"),r("2877")),v=Object(g["a"])(h,o,i,!1,null,"24f6b87c",null);e["default"]=v.exports},"9b9c":function(t,e,r){"use strict";r.d(e,"g",(function(){return i})),r.d(e,"h",(function(){return a})),r.d(e,"f",(function(){return s})),r.d(e,"a",(function(){return c})),r.d(e,"i",(function(){return n})),r.d(e,"e",(function(){return l})),r.d(e,"b",(function(){return d})),r.d(e,"d",(function(){return u})),r.d(e,"c",(function(){return p}));var o=r("b775");function i(t){return Object(o["a"])({url:"/iot/product/list",method:"get",params:t})}function a(t){return Object(o["a"])({url:"/iot/product/shortList",method:"get",params:t})}function s(t){return Object(o["a"])({url:"/iot/product/"+t,method:"get"})}function c(t){return Object(o["a"])({url:"/iot/product",method:"post",data:t})}function n(t){return Object(o["a"])({url:"/iot/product",method:"put",data:t})}function l(t){return Object(o["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function d(t){return Object(o["a"])({url:"/iot/product/status",method:"put",data:t})}function u(t){return Object(o["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(o["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}},b1cc:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.$t("product.product-edit.473153-88"),visible:t.open,width:"900px"},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-1"),prop:"productName"}},[r("el-input",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-2")},model:{value:t.form.productName,callback:function(e){t.$set(t.form,"productName",e)},expression:"form.productName"}})],1),r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-3"),prop:"categoryId"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-4"),filterable:!0,clearable:""},on:{change:t.selectCategory},model:{value:t.form.categoryId,callback:function(e){t.$set(t.form,"categoryId",e)},expression:"form.categoryId"}},t._l(t.categoryShortList,(function(t){return r("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1),r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-78"),prop:"deviceType"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-13"),filterable:!0,clearable:""},on:{change:t.handleDeviceTypeChange},model:{value:t.form.deviceType,callback:function(e){t.$set(t.form,"deviceType",e)},expression:"form.deviceType"}},t._l(t.dict.type.iot_device_type,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:parseInt(t.value)}})})),1)],1),r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-91"),prop:"firmwareType"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-13"),filterable:!0,clearable:""},model:{value:t.form.firmwareType,callback:function(e){t.$set(t.form,"firmwareType",e)},expression:"form.firmwareType"}},t._l(t.dict.type.iot_firmware_type,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:parseInt(t.value)}})})),1)],1),r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-16"),prop:"networkMethod"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-17"),clearable:""},model:{value:t.form.networkMethod,callback:function(e){t.$set(t.form,"networkMethod",e)},expression:"form.networkMethod"}},t._l(t.networkOptions,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:parseInt(t.value)}})})),1)],1),r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-18")}},[r("template",{slot:"label"},[r("span",[t._v(t._s(t.$t("product.product-edit.473153-18")))]),r("el-tooltip",{staticStyle:{cursor:"pointer","margin-left":"5px"},attrs:{effect:"light",placement:"bottom"}},[r("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("product.product-edit.473153-19"))+" "),r("br")]),r("i",{staticClass:"el-icon-question"})])],1),r("el-radio-group",{model:{value:t.form.isSys,callback:function(e){t.$set(t.form,"isSys",e)},expression:"form.isSys"}},[r("el-radio",{attrs:{label:1}},[t._v(t._s(t.$t("product.product-edit.473153-89")))]),r("el-radio",{attrs:{label:0}},[t._v(t._s(t.$t("product.product-edit.473153-90")))])],1)],2)],1),r("el-col",{attrs:{span:12}},[3!==t.form.deviceType?r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-81"),prop:"protocolCode"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-82"),filterable:!0,clearable:""},on:{change:t.handleProductCodeChange},model:{value:t.form.protocolCode,callback:function(e){t.$set(t.form,"protocolCode",e)},expression:"form.protocolCode"}},t._l(t.protocolList,(function(t){return r("el-option",{key:t.protocolCode,attrs:{label:t.protocolName,value:t.protocolCode}})})),1)],1):t._e(),4!==t.form.deviceType?r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-14"),prop:"transport"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-15"),clearable:""},model:{value:t.form.transport,callback:function(e){t.$set(t.form,"transport",e)},expression:"form.transport"}},t._l(t.dict.type.iot_transport_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value,disabled:3===t.form.deviceType&&"GB28181"!==e.value||"MODBUS-TCP"===t.form.protocolCode&&"TCP"!==e.value}})})),1)],1):t._e(),"MQTT"===t.form.transport?r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-21"),prop:"vertificateMethod"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-22"),clearable:""},model:{value:t.form.vertificateMethod,callback:function(e){t.$set(t.form,"vertificateMethod",e)},expression:"form.vertificateMethod"}},t._l(t.dict.type.iot_vertificate_method,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:parseInt(t.value)}})})),1)],1):t._e(),4!=t.form.deviceType?r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-23"),prop:"locationWay"}},[r("el-select",{staticStyle:{width:"300px"},attrs:{placeholder:t.$t("product.product-edit.473153-24"),clearable:""},model:{value:t.form.locationWay,callback:function(e){t.$set(t.form,"locationWay",e)},expression:"form.locationWay"}},t._l(t.dict.type.iot_location_way,(function(t){return r("el-option",{key:t.value,attrs:{label:t.label,value:Number(t.value)}})})),1)],1):t._e(),"MQTT"===t.form.transport?r("el-form-item",{attrs:{label:t.$t("product.product-edit.473153-20"),prop:"networkMethod"}},[r("el-switch",{attrs:{"active-value":1,"inactive-value":0},on:{change:function(e){return t.changeIsAuthorize(t.form.isAuthorize)}},model:{value:t.form.isAuthorize,callback:function(e){t.$set(t.form,"isAuthorize",e)},expression:"form.isAuthorize"}})],1):t._e()],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.$t("device.product-list.058448-14")))]),r("el-button",{on:{click:t.closeDialog}},[t._v(t._s(t.$t("device.product-list.058448-15")))])],1)],1)},i=[],a=(r("b0c0"),r("b213")),s=r("1e36"),c=r("9b9c"),n={name:"ProductAdd",dicts:["iot_device_type","iot_network_method","iot_vertificate_method","iot_transport_type","data_collect_type","iot_location_way","sub_gateway_type","iot_firmware_type"],data:function(){return{open:!1,protocolList:[],form:{},categoryShortList:[],rules:{productName:[{required:!0,message:this.$t("product.product-edit.473153-58"),trigger:"blur"}],categoryId:[{required:!0,message:this.$t("product.product-edit.473153-59"),trigger:"blur"}],deviceType:[{required:!0,message:this.$t("product.product-edit.473153-13"),trigger:"blur"}],firmwareType:[{required:!0,message:this.$t("product.product-edit.473153-92"),trigger:"blur"}],protocolCode:[{required:!0,message:this.$t("product.product-edit.473153-60"),trigger:"blur"}],transport:[{required:!0,message:this.$t("product.product-edit.473153-61"),trigger:"blur"}],isSys:[{required:!0,message:this.$t("product.product-edit.473153-61"),trigger:"blur"}]}}},created:function(){this.getProtocol(),this.getShortCategory()},computed:{networkOptions:function(){return 4==this.form.deviceType?this.dict.type.sub_gateway_type:this.dict.type.iot_network_method}},methods:{getShortCategory:function(){var t=this,e={pageSize:999};Object(s["e"])(e).then((function(e){t.categoryShortList=e.data}))},getProtocol:function(){var t=this,e={protocolStatus:1,pageSize:99,display:1};Object(a["d"])(e).then((function(e){t.protocolList=e.rows}))},handleProductCodeChange:function(t){"MODBUS-TCP"==t&&(this.form.transport="TCP")},handleDeviceTypeChange:function(t){3===t?(this.form.transport="GB28181",this.form.locationWay=3):this.form.transport=4===t?"":"MQTT"},reset:function(){this.form={productId:null,productName:"",categoryId:null,categoryName:"",status:1,tslJson:null,isAuthorize:0,deviceType:1,transport:"MQTT",networkMethod:1,vertificateMethod:3,mqttAccount:null,mqttPassword:null,mqttSecret:null,remark:null,imgUrl:"",locationWay:1,isSys:0},this.resetForm("form")},selectCategory:function(t){for(var e=0;e<this.categoryShortList.length;e++)if(this.categoryShortList[e].id==t)return void(this.form.categoryName=this.categoryShortList[e].name)},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&Object(c["a"])(t.form).then((function(e){200===e.code&&(t.$modal.msgSuccess(t.$t("product.product-edit.473153-64")),t.$parent.getList()),t.open=!1}))}))},closeDialog:function(){this.open=!1}}},l=n,d=r("2877"),u=Object(d["a"])(l,o,i,!1,null,null,null);e["default"]=u.exports},b213:function(t,e,r){"use strict";r.d(e,"d",(function(){return i})),r.d(e,"c",(function(){return a})),r.d(e,"a",(function(){return s})),r.d(e,"e",(function(){return c})),r.d(e,"b",(function(){return n}));var o=r("b775");function i(t){return Object(o["a"])({url:"/iot/protocol/list",method:"get",params:t})}function a(t){return Object(o["a"])({url:"/iot/protocol/"+t,method:"get"})}function s(t){return Object(o["a"])({url:"/iot/protocol",method:"post",data:t})}function c(t){return Object(o["a"])({url:"/iot/protocol",method:"put",data:t})}function n(t){return Object(o["a"])({url:"/iot/protocol/"+t,method:"delete"})}},c59e:function(t,e){t.exports="data:image/png;base64,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"},e350:function(t,e,r){"use strict";r.d(e,"a",(function(){return i}));r("caad"),r("d3b7"),r("2532");var o=r("4360");function i(t){if(t&&t instanceof Array&&t.length>0){var e=o["a"].getters&&o["a"].getters.permissions,r=t,i="*:*:*",a=e.some((function(t){return i===t||r.includes(t)}));return!!a}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}},e51f:function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.$t("device.product-list.058448-0"),visible:t.open,width:"910px"},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:t.$t("device.product-list.058448-2"),clearable:"",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("device.product-list.058448-3")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("device.product-list.058448-4")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":t.rowClick}},[r("el-table-column",{attrs:{label:t.$t("device.device-edit.148398-6"),width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:t.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:t.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-6"),align:"left",prop:"categoryName","min-width":"150"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-7"),align:"left",prop:"tenantName","min-width":"100"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-8"),align:"center",prop:"status",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.isAuthorize?r("el-tag",{attrs:{type:"success"}},[t._v(t._s(t.$t("device.product-list.058448-9")))]):t._e(),0==e.row.isAuthorize?r("el-tag",{attrs:{type:"info"}},[t._v(t._s(t.$t("device.product-list.058448-10")))]):t._e()]}}])}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-11"),align:"center",prop:"status","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.iot_vertificate_method,value:e.row.vertificateMethod}})]}}])}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-12"),align:"center",prop:"networkMethod","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.iot_network_method,value:e.row.networkMethod}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectProduct}},[t._v(t._s(t.$t("device.product-list.058448-14")))]),r("el-button",{attrs:{type:"info"},on:{click:t.closeDialog}},[t._v(t._s(t.$t("device.product-list.058448-15")))])],1)],1)},i=[],a=(r("a9e3"),r("9b9c")),s={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0},showSenior:{type:Boolean,default:!0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.showSenior=this.showSenior,Object(a["g"])(this.queryParams).then((function(e){for(var r=0;r<e.rows.length;r++)e.rows[r].isSelect=!1;t.productList=e.rows,t.total=e.total,0!=t.productId&&t.setRadioSelected(t.productId),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(t){null!=t&&(this.setRadioSelected(t.productId),this.product=t)},setRadioSelected:function(t){for(var e=0;e<this.productList.length;e++)this.productList[e].productId==t?this.productList[e].isSelect=!0:this.productList[e].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},c=s,n=r("2877"),l=Object(n["a"])(c,o,i,!1,null,null,null);e["default"]=l.exports}}]);