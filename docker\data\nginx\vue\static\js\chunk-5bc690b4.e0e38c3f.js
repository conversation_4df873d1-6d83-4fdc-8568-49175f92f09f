(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bc690b4"],{"5cfa":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"system-dept"},[s("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-card"},[s("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[s("el-form-item",{attrs:{prop:"deptName"}},[s("el-input",{attrs:{placeholder:e.$t("system.dept.780956-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deptName,callback:function(t){e.$set(e.queryParams,"deptName",t)},expression:"queryParams.deptName"}})],1),s("el-form-item",{attrs:{prop:"status"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:e.$t("system.dept.780956-2"),clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("div",{staticStyle:{float:"right"}},[s("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),s("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1)],1),s("el-card",[s("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[s("el-col",{attrs:{span:1.5}},[s("el-button",{attrs:{plain:"",icon:"el-icon-sort",size:"small"},on:{click:e.toggleExpandAll}},[e._v(e._s(e.$t("role.index.094567-18")))])],1),s("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deptList,border:!1,"row-key":"deptId","default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"}}},[s("el-table-column",{attrs:{prop:"deptId",label:e.$t("role.index.094567-38"),"min-width":"130",align:"left"}}),s("el-table-column",{attrs:{prop:"deptName",label:e.$t("role.index.094567-7"),"min-width":"350",align:"left"}}),s("el-table-column",{attrs:{prop:"deptType",label:e.$t("system.dept.780956-3"),"min-width":"150",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("dict-tag",{attrs:{options:e.dict.type.department_type,value:t.row.deptType}})]}}],null,!1,170071197)}),s("el-table-column",{attrs:{prop:"status",label:e.$t("status"),width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}],null,!1,2802338569)}),s("el-table-column",{attrs:{prop:"leader",label:e.$t("system.dept.780956-4"),"min-width":"150",align:"left"}}),s("el-table-column",{attrs:{label:e.$t("creatTime"),align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}),s("el-table-column",{attrs:{fixed:"right",label:"操作",align:"center","class-name":"small-padding fixed-width",width:"220"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:add"],expression:"['system:dept:add']"}],attrs:{size:"small",type:"text",icon:"el-icon-plus"},on:{click:function(s){return e.handleAdd(t.row)}}},[e._v(e._s(e.$t("add")))]),s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:edit"],expression:"['system:dept:edit']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(s){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("edit")))]),s("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"small"},on:{command:function(s){return e.handleCommand(s,t.row)}}},[s("el-button",{attrs:{size:"small",type:"text",icon:"el-icon-d-arrow-right"}},[e._v(e._s(e.$t("system.dept.780956-7")))]),s("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[s("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleUserManage",icon:"el-icon-user"}},[e._v(e._s(e.$t("system.dept.780956-8")))]),s("el-dropdown-item",{attrs:{command:"handleRoleManage",icon:"el-icon-circle-check"}},[e._v(e._s(e.$t("system.dept.780956-9")))]),0!=t.row.parentId?s("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:remove"],expression:"['system:dept:remove']"}],attrs:{command:"handleDelete",size:"small",type:"text",icon:"el-icon-delete"}},[e._v(" "+e._s(e.$t("del"))+" ")]):e._e()],1)],1)]}}],null,!1,2277172155)})],1):e._e(),s("el-dialog",{attrs:{title:e.title,visible:e.open,width:"620px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[s("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[s("el-row",[0!==e.form.parentId?s("el-col",{attrs:{span:24}},[s("el-form-item",{attrs:{label:e.$t("system.dept.780956-10"),prop:"parentId"}},[s("treeselect",{staticStyle:{width:"400px"},attrs:{options:e.deptOptions,normalizer:e.normalizer,disabled:"",placeholder:e.$t("system.dept.780956-11")},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1)],1):e._e()],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-0"),prop:"deptName"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-1")},model:{value:e.form.deptName,callback:function(t){e.$set(e.form,"deptName",t)},expression:"form.deptName"}})],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-3"),prop:"deptType"}},[s("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-12"),clearable:"",size:"small",disabled:null!=this.form.deptId},model:{value:e.form.deptType,callback:function(t){e.$set(e.form,"deptType",t)},expression:"form.deptType"}},e._l(e.deptTypeList,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-4"),prop:"leader"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-13"),maxlength:"20"},model:{value:e.form.leader,callback:function(t){e.$set(e.form,"leader",t)},expression:"form.leader"}})],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-2")}},[s("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return s("el-radio",{key:t.value,attrs:{label:Number(t.value)}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-35"),prop:"logoName"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-36")},model:{value:e.form.logoName,callback:function(t){e.$set(e.form,"logoName",t)},expression:"form.logoName"}})],1),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-37"),prop:"deptLogo"}},[s("imageUpload",{ref:"image-upload",attrs:{limit:1,fileSize:1,fileType:["jpg","jpeg","png","gif"]},on:{input:function(t){return e.getImagePath(t)}},model:{value:e.form.deptLogo,callback:function(t){e.$set(e.form,"deptLogo",t)},expression:"form.deptLogo"}})],1),s("div",{staticClass:"title-wrap"},[e._v(e._s(e.$t("system.dept.780956-14")))]),s("el-form-item",{attrs:{prop:"userName"}},[s("span",{attrs:{slot:"label"},slot:"label"},[s("span",{staticClass:"span-box"},[s("span",[e._v(e._s(e.$t("system.dept.780956-15")))]),s("el-tooltip",{attrs:{content:e.$t("system.dept.780956-16"),placement:"top"}},[s("i",{staticClass:"el-icon-warning-outline"})])],1)]),s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-17"),disabled:null!=this.form.deptId},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1),null==this.form.deptId?s("el-form-item",{attrs:{label:e.$t("system.dept.780956-18"),prop:"password"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-19"),type:"password","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1):e._e(),null==this.form.deptId?s("el-form-item",{attrs:{label:e.$t("system.dept.780956-20"),prop:"confirmPassword"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-19"),type:"password","show-password":""},model:{value:e.form.confirmPassword,callback:function(t){e.$set(e.form,"confirmPassword",t)},expression:"form.confirmPassword"}})],1):e._e(),s("el-form-item",{attrs:{label:e.$t("system.dept.780956-21"),prop:"phone"}},[s("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("system.dept.780956-22"),maxlength:"11",disabled:null!=this.form.deptId},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("confirm")))]),s("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1)],1)],1)},r=[],l=(s("d9e2"),s("d81d"),s("14d9"),s("ac1f"),s("00b4"),s("fcb7")),o=s("ca17"),n=s.n(o),i=(s("542c"),{name:"Dept",dicts:["sys_normal_disable","department_type"],components:{Treeselect:n.a},data:function(){var e=this,t=function(t,s,a){e.form.password!==s?a(new Error(e.$t("system.dept.780956-23"))):a()};return{loading:!0,showSearch:!0,deptList:[],deptOptions:[],deptType:"",title:"",open:!1,isExpandAll:!0,refreshTable:!0,deptTypeList:[],multiple:!0,queryParams:{deptName:void 0,status:void 0},form:{},rules:{parentId:[{required:!0,message:this.$t("system.dept.780956-24"),trigger:"blur"}],deptName:[{required:!0,message:this.$t("system.dept.780956-25"),trigger:"blur"}],deptType:[{required:!0,message:this.$t("system.dept.780956-26"),trigger:"blur"}],leader:[{required:!0,message:this.$t("system.dept.780956-27"),trigger:"blur"}],userName:[{required:!0,message:this.$t("system.dept.780956-28"),trigger:"blur"}],password:[{required:!0,message:this.$t("system.dept.780956-29"),trigger:"blur"},{min:5,max:20,message:this.$t("user.index.098976-35"),trigger:"blur"},{trigger:"blur",validator:function(t,s,a){var r=/(?![A-Z]*$)(?![a-z]*$)(?![0-9]*$)(?![^a-zA-Z0-9]*$)/;r.test(s)?a():a(new Error(e.$t("system.dept.780956-30")))}}],confirmPassword:[{required:!0,trigger:"blur",message:this.$t("system.dept.780956-31")},{required:!0,validator:t,trigger:"blur"}],phone:[{required:!0,message:this.$t("system.dept.780956-32"),trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:this.$t("user.index.098976-39"),trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.deptList=e.handleTree(t.data,"deptId"),e.loading=!1}))},normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.deptId,label:e.deptName,children:e.children}},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deptId:null,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:0,deptType:0,showOwner:!1},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(e){var t=this;this.reset(),this.form.deptType=null,void 0!=e&&(this.form.parentId=e.deptId),this.open=!0,this.title=this.$t("system.dept.780956-33"),null==e.deptType?(e.deptType="",this.deptType=e.deptType,this.getDeptType()):(this.deptType=e.deptType,this.getDeptType()),Object(l["e"])().then((function(e){t.deptOptions=t.handleTree(e.data,"deptId")}))},getDeptType:function(){var e=this;Object(l["d"])(this.deptType,this.form.showOwner).then((function(t){e.deptTypeList=t.data.map((function(e){return{value:e.deptType,label:e.deptTypeName}}))}))},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this;this.reset(),Object(l["c"])(e.deptId).then((function(s){t.form=s.data,t.open=!0,t.title=t.$t("system.dept.780956-34"),t.form.showOwner=!0,null==e.deptType?(e.deptType="",t.deptType=e.deptType,t.getDeptType()):(t.deptType=e.deptType,t.getDeptType()),Object(l["f"])(e.deptId).then((function(e){if(t.deptOptions=t.handleTree(e.data,"deptId"),0==t.deptOptions.length){var s={deptId:t.form.parentId,deptName:t.form.parentName,children:[]};t.deptOptions.push(s)}}))}))},handleCommand:function(e,t){switch(e){case"handleUserManage":this.handleUserManage(t);break;case"handleRoleManage":this.handleRoleManage(t);break;case"handleDelete":this.handleDelete(t);break;default:break}},handleUserManage:function(e){var t=e.deptId;this.$router.push("/system/user-manage/user/"+t)},handleRoleManage:function(e){var t=e.deptId;this.$router.push("/system/role-manage/role/"+t)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deptId?Object(l["g"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess")),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("addSuccess")),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm(this.$t("system.menu.034890-42",[e.deptName])).then((function(){return Object(l["b"])(e.deptId)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("delSuccess"))})).catch((function(){}))}}}),d=i,p=(s("9149"),s("2877")),m=Object(p["a"])(d,a,r,!1,null,"746da944",null);t["default"]=m.exports},9149:function(e,t,s){"use strict";s("f502")},f502:function(e,t,s){}}]);