(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bbd9e87","chunk-722c5e57"],{"584f":function(t,e,r){"use strict";r.d(e,"n",(function(){return o})),r.d(e,"t",(function(){return n})),r.d(e,"o",(function(){return a})),r.d(e,"p",(function(){return c})),r.d(e,"m",(function(){return u})),r.d(e,"f",(function(){return l})),r.d(e,"c",(function(){return s})),r.d(e,"g",(function(){return d})),r.d(e,"i",(function(){return p})),r.d(e,"d",(function(){return m})),r.d(e,"u",(function(){return f})),r.d(e,"q",(function(){return h})),r.d(e,"r",(function(){return g})),r.d(e,"h",(function(){return b})),r.d(e,"a",(function(){return v})),r.d(e,"v",(function(){return y})),r.d(e,"b",(function(){return w})),r.d(e,"e",(function(){return O})),r.d(e,"k",(function(){return j})),r.d(e,"l",(function(){return $})),r.d(e,"j",(function(){return S})),r.d(e,"s",(function(){return _}));var i=r("b775");function o(t){return Object(i["a"])({url:"/iot/device/list",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:t})}function u(t){return Object(i["a"])({url:"/iot/device/all",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/iot/device/"+t,method:"get"})}function s(t){return Object(i["a"])({url:"/iot/device/synchronization/"+t,method:"get"})}function d(t){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+t,method:"get"})}function p(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function m(t,e){return Object(i["a"])({url:"/iot/device/assignment?deptId="+t+"&deviceIds="+e,method:"post"})}function f(t,e){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+t+"&recoveryDeptId="+e,method:"post"})}function h(t){return Object(i["a"])({url:"/iot/record/list",method:"get",params:t})}function g(t){return Object(i["a"])({url:"/iot/record/list",method:"get",params:t})}function b(t){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:t})}function v(t){return Object(i["a"])({url:"/iot/device",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/iot/device",method:"put",data:t})}function w(t){return Object(i["a"])({url:"/iot/device/"+t,method:"delete"})}function O(t){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:t})}function j(t){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:t})}function $(t){return Object(i["a"])({url:"/sip/sipconfig/auth/"+t,method:"get"})}function S(t){return Object(i["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:t})}function _(t){return Object(i["a"])({url:"/iot/device/listThingsModel",method:"get",params:t})}},"6b87":function(t,e,r){},7539:function(t,e,r){"use strict";r("6b87")},"9b9c":function(t,e,r){"use strict";r.d(e,"g",(function(){return o})),r.d(e,"h",(function(){return n})),r.d(e,"f",(function(){return a})),r.d(e,"a",(function(){return c})),r.d(e,"i",(function(){return u})),r.d(e,"e",(function(){return l})),r.d(e,"b",(function(){return s})),r.d(e,"d",(function(){return d})),r.d(e,"c",(function(){return p}));var i=r("b775");function o(t){return Object(i["a"])({url:"/iot/product/list",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/iot/product/shortList",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/iot/product/"+t,method:"get"})}function c(t){return Object(i["a"])({url:"/iot/product",method:"post",data:t})}function u(t){return Object(i["a"])({url:"/iot/product",method:"put",data:t})}function l(t){return Object(i["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function s(t){return Object(i["a"])({url:"/iot/product/status",method:"put",data:t})}function d(t){return Object(i["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(i["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}},c0c7:function(t,e,r){"use strict";r.d(e,"l",(function(){return n})),r.d(e,"o",(function(){return a})),r.d(e,"j",(function(){return c})),r.d(e,"i",(function(){return u})),r.d(e,"a",(function(){return l})),r.d(e,"q",(function(){return s})),r.d(e,"c",(function(){return d})),r.d(e,"m",(function(){return p})),r.d(e,"b",(function(){return m})),r.d(e,"h",(function(){return f})),r.d(e,"n",(function(){return h})),r.d(e,"k",(function(){return g})),r.d(e,"r",(function(){return b})),r.d(e,"s",(function(){return v})),r.d(e,"t",(function(){return y})),r.d(e,"f",(function(){return w})),r.d(e,"p",(function(){return O})),r.d(e,"d",(function(){return j})),r.d(e,"e",(function(){return $})),r.d(e,"g",(function(){return S}));var i=r("b775"),o=r("c38a");function n(t){return Object(i["a"])({url:"/system/user/list",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/system/user/listTerminal",method:"get",params:t})}function c(t){return Object(i["a"])({url:"/system/user/"+Object(o["f"])(t),method:"get"})}function u(t){return Object(i["a"])({url:"/system/dept/getRole?deptId="+t,method:"get"})}function l(t){return Object(i["a"])({url:"/system/user",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/system/user",method:"put",data:t})}function d(t){return Object(i["a"])({url:"/system/user/"+t,method:"delete"})}function p(t,e){var r={userId:t,password:e};return Object(i["a"])({url:"/system/user/resetPwd",method:"put",data:r})}function m(t,e){var r={userId:t,status:e};return Object(i["a"])({url:"/system/user/changeStatus",method:"put",data:r})}function f(){return Object(i["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(t){return Object(i["a"])({url:"/wechat/cancelBind",method:"post",data:t})}function g(){return Object(i["a"])({url:"/system/user/profile",method:"get"})}function b(t){return Object(i["a"])({url:"/system/user/profile",method:"put",data:t})}function v(t,e){var r={oldPassword:t,newPassword:e};return Object(i["a"])({url:"/system/user/profile/updatePwd",method:"put",params:r})}function y(t){return Object(i["a"])({url:"/system/user/profile/avatar",method:"post",data:t})}function w(t){return Object(i["a"])({url:"/system/user/authRole/"+t,method:"get"})}function O(t){return Object(i["a"])({url:"/system/user/authRole",method:"put",params:t})}function j(){return Object(i["a"])({url:"/system/user/deptTree",method:"get"})}function $(t){return Object(i["a"])({url:"/system/user/deptTree?showOwner="+t,method:"get"})}function S(t){return Object(i["a"])({url:"/system/user/getByDeptId",method:"get",params:t})}},e000:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"select-allot-wrap"},[r("el-card",{staticClass:"top-card",attrs:{"body-style":{padding:"26px 20px"}}},[r("div",{staticClass:"title-wrap"},[r("el-button",{staticClass:"top-button",attrs:{type:"info",size:"small"},on:{click:function(e){return t.goBack()}}},[r("i",{staticClass:"el-icon-arrow-left"}),t._v(" "+t._s(t.$t("product.product-edit.473153-44"))+" ")]),r("span",{staticClass:"info-item"},[t._v(t._s(t.$t("device.device-select-allot.903153-0")))])],1)]),r("el-card",{staticClass:"main-card"},[r("el-row",[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0}},[r("el-form-item",{attrs:{label:"",prop:"productName"}},[r("el-input",{attrs:{readonly:"",placeholder:t.$t("device.allot-import-dialog.060657-1")},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}},[r("el-button",{attrs:{slot:"append"},on:{click:function(e){return t.selectProduct()}},slot:"append"},[t._v(t._s(t.$t("device.device-edit.148398-6")))])],1)],1),r("el-form-item",{attrs:{prop:"deviceName"}},[r("el-input",{attrs:{placeholder:t.$t("device.device-edit.148398-2"),clearable:""},model:{value:t.queryParams.deviceName,callback:function(e){t.$set(t.queryParams,"deviceName",e)},expression:"queryParams.deviceName"}})],1),r("el-form-item",{attrs:{prop:"serialNumber"}},[r("el-input",{attrs:{placeholder:t.$t("device.device-edit.148398-8"),clearable:""},model:{value:t.queryParams.serialNumber,callback:function(e){t.$set(t.queryParams,"serialNumber",e)},expression:"queryParams.serialNumber"}})],1),r("div",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("device.device-select-allot.903153-4")))]),r("el-button",{staticStyle:{"margin-left":"5px"},attrs:{icon:"el-icon-refresh-left"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("device.device-select-allot.903153-5")))])],1)],1)],1),r("div",{staticClass:"general"},[r("div",{staticClass:"topLeft"},[r("div",{staticStyle:{padding:"15px 10px","background-color":"#f4f4f5"}},[r("span",{staticStyle:{"font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.$t("device.device-select-allot.903153-6")))]),r("span",{staticStyle:{"font-size":"15px","font-weight":"bold",float:"right"}},[t._v(t._s(t.selectedCount)+"/"+t._s(this.count))])]),r("el-row",[r("el-table",{ref:"leftTableData",staticStyle:{width:"100%"},attrs:{data:t.menuTableData,"max-height":"400",border:!1},on:{"selection-change":t.changeCheckBoxValueLeft}},[r("template",{slot:"empty"},[r("el-empty",{staticStyle:{height:"400px"},attrs:{"image-size":100,description:t.$t("device.device-select-allot.903153-7")}})],1),r("el-table-column",{attrs:{type:"selection",align:"center",width:"50",selectable:t.checkSelectable}}),r("el-table-column",{attrs:{prop:"productName",label:t.$t("device.allot-record.155854-2"),"show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"serialNumber",label:t.$t("device.device-edit.148398-7"),"show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"deviceName",label:t.$t("device.device-edit.148398-1"),"show-overflow-tooltip":""}})],2)],1)],1),r("div",{staticClass:"topCenter"},[r("el-button",{attrs:{type:"primary",disabled:t.add},on:{click:t.rightAdd}},[r("i",{staticClass:"el-icon-arrow-right el-icon--right"})]),r("el-button",{attrs:{type:"primary",disabled:t.del},on:{click:t.leftDelete}},[r("i",{staticClass:"el-icon-arrow-left el-icon--left"})])],1),r("div",{staticClass:"topRight"},[r("div",{staticStyle:{padding:"15px 10px","background-color":"#f4f4f5"}},[r("span",{staticStyle:{"font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.$t("device.device-select-allot.903153-11")))]),r("span",{staticStyle:{"font-size":"15px","font-weight":"bold",float:"right"}},[t._v(t._s(t.selectedCount1)+"/500")])]),r("el-row",[r("el-table",{ref:"rightTableData",staticStyle:{width:"100%"},attrs:{data:t.rightTableData,"max-height":"400",border:!1},on:{"selection-change":t.changeCheckBoxValueRight}},[r("template",{slot:"empty"},[r("el-empty",{staticStyle:{height:"400px"},attrs:{"image-size":100,description:t.$t("device.device-select-allot.903153-7")}})],1),r("el-table-column",{attrs:{type:"selection",align:"center",width:"50"}}),r("el-table-column",{attrs:{prop:"productName",label:t.$t("device.allot-record.155854-2"),"show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"serialNumber",label:t.$t("device.device-edit.148398-7"),"show-overflow-tooltip":""}}),r("el-table-column",{attrs:{prop:"deviceName",label:t.$t("device.device-edit.148398-1"),"show-overflow-tooltip":""}})],2)],1)],1)]),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,"pager-count":5,limit:t.queryParams.pageSize,pageSizes:[10,20,30,40]},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),r("div",{staticStyle:{width:"100%"}},[r("el-form",{ref:"allotForm",attrs:{"label-position":"top",model:t.allotForm,rules:t.allotRules}},[r("div",{staticStyle:{width:"45%",margin:"60px 0"}},[r("el-form-item",{attrs:{label:t.$t("device.allot-import-dialog.060657-2"),prop:"deptId"}},[r("treeselect",{attrs:{options:t.deptOptions,"show-count":!0,placeholder:t.$t("device.allot-import-dialog.060657-3"),appendToBody:!0,"z-index":"9000"},model:{value:t.allotForm.deptId,callback:function(e){t.$set(t.allotForm,"deptId",e)},expression:"allotForm.deptId"}})],1)],1)])],1),r("div",{staticClass:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirmDistribution}},[t._v(t._s(t.$t("device.device-select-allot.903153-14")))])],1)],1),r("product-list",{ref:"productList",attrs:{productId:t.queryParams.productId},on:{productEvent:function(e){return t.getProductData(e)}}})],1)},o=[],n=(r("99af"),r("4de4"),r("a15b"),r("d81d"),r("14d9"),r("4e82"),r("e9c4"),r("b64b"),r("d3b7"),r("159b"),r("ca17")),a=r.n(n),c=(r("542c"),r("c0c7")),u=r("584f"),l=r("e51f"),s={components:{Treeselect:a.a,productList:l["default"]},data:function(){return{total:0,productList:[],queryParams:{productId:null,deviceName:"",pageNum:1,pageSize:10,productName:null,serialNumber:null,showChild:!1},count:0,allotForm:{productId:0,deptId:0},deviceIds:{},selectedCount:0,deptOptions:[],selectedRow:null,add:!0,del:!0,leftTableData:[],rightTableData:[],selectedListLeft:[],selectedListRight:[],menuTableData:[],allotRules:{deptId:[{required:!0,message:this.$t("device.allot-import-dialog.060657-15"),trigger:"change"}]}}},created:function(){this.getDeptTree(),this.getList()},computed:{selectedCount1:function(){return this.rightTableData.length}},watch:{selectedListLeft:function(t){t.length?this.add=!1:this.add=!0},selectedListRight:function(t){t.length?this.del=!1:this.del=!0}},methods:{getList:function(){var t=this;this.loading=!0,Object(u["n"])(this.queryParams).then((function(e){t.menuTableData=e.rows,t.menuTableData.map((function(e){t.leftTableData.push(Object.assign(e,{isSelect:0}))})),0!=t.rightTableData.length&&t.menuTableData.forEach((function(e,r){t.rightTableData.forEach((function(t){t.deviceId==e.deviceId&&(e.isSelect=1)}))})),t.total=e.total,0===t.count&&(t.count=t.total),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.rightTableData=[],this.queryParams.productId=null,this.resetForm("queryForm"),this.handleQuery()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(t){this.queryParams.productId=t.productId,this.queryParams.productName=t.productName},getDeptTree:function(){var t=this;this.allotForm.deptId=null,Object(c["d"])().then((function(e){t.deptOptions=e.data}))},goBack:function(){this.$router.go(-1)},rightAdd:function(){var t=this,e=JSON.parse(JSON.stringify(this.menuTableData));e.forEach((function(e,r){t.selectedListLeft.forEach((function(r){e.deviceId==r.deviceId&&(t.rightTableData=t.rightTableData.concat(e).sort((function(t,e){return t.deviceId-e.deviceId})),e.isSelect=1)}))})),0!=this.selectedCount1&&(this.count=this.count-this.selectedListLeft.length),e=e.filter((function(t){return t})),this.menuTableData=e,this.selectedListLeft=[]},leftDelete:function(){var t=this,e=JSON.parse(JSON.stringify(this.rightTableData));e.forEach((function(r,i){t.selectedListRight.forEach((function(o){t.menuTableData.forEach((function(t){t.deviceId==o.deviceId&&(t.isSelect=0),o.deviceId==r.deviceId&&delete e[i]}))}))})),0!=this.selectedCount1&&(this.count=this.count+this.selectedListRight.length,this.getList()),e=e.filter((function(t){return t})),this.rightTableData=e,this.selectedListRight=[]},checkSelectable:function(t){var e=!0;return e=0===t.isSelect,e},changeCheckBoxValueLeft:function(t){this.selectedListLeft=t,this.selectedCount=t.length},changeCheckBoxValueRight:function(t){this.selectedListRight=t},confirmDistribution:function(){var t=this;this.$refs["allotForm"].validate((function(e){if(e){t.deviceIds=t.rightTableData.map((function(t){return t.deviceId}));var r=t.deviceIds.join(","),i=t.allotForm.deptId;Object(u["d"])(i,r).then((function(e){200==e.code?(t.$modal.msgSuccess(e.msg),t.resetQuery()):t.$modal.msgError(e.msg)}))}}))}}},d=s,p=(r("7539"),r("2877")),m=Object(p["a"])(d,i,o,!1,null,"8646d80a",null);e["default"]=m.exports},e51f:function(t,e,r){"use strict";r.r(e);var i=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.$t("device.product-list.058448-0"),visible:t.open,width:"910px"},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(t){t.preventDefault()}}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:t.$t("device.product-list.058448-2"),clearable:"",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("device.product-list.058448-3")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("device.product-list.058448-4")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":t.rowClick}},[r("el-table-column",{attrs:{label:t.$t("device.device-edit.148398-6"),width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:t.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:t.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-6"),align:"left",prop:"categoryName","min-width":"150"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-7"),align:"left",prop:"tenantName","min-width":"100"}}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-8"),align:"center",prop:"status",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.isAuthorize?r("el-tag",{attrs:{type:"success"}},[t._v(t._s(t.$t("device.product-list.058448-9")))]):t._e(),0==e.row.isAuthorize?r("el-tag",{attrs:{type:"info"}},[t._v(t._s(t.$t("device.product-list.058448-10")))]):t._e()]}}])}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-11"),align:"center",prop:"status","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.iot_vertificate_method,value:e.row.vertificateMethod}})]}}])}),r("el-table-column",{attrs:{label:t.$t("device.product-list.058448-12"),align:"center",prop:"networkMethod","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.iot_network_method,value:e.row.networkMethod}})]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectProduct}},[t._v(t._s(t.$t("device.product-list.058448-14")))]),r("el-button",{attrs:{type:"info"},on:{click:t.closeDialog}},[t._v(t._s(t.$t("device.product-list.058448-15")))])],1)],1)},o=[],n=(r("a9e3"),r("9b9c")),a={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0},showSenior:{type:Boolean,default:!0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.showSenior=this.showSenior,Object(n["g"])(this.queryParams).then((function(e){for(var r=0;r<e.rows.length;r++)e.rows[r].isSelect=!1;t.productList=e.rows,t.total=e.total,0!=t.productId&&t.setRadioSelected(t.productId),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(t){null!=t&&(this.setRadioSelected(t.productId),this.product=t)},setRadioSelected:function(t){for(var e=0;e<this.productList.length;e++)this.productList[e].productId==t?this.productList[e].isSelect=!0:this.productList[e].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},c=a,u=r("2877"),l=Object(u["a"])(c,i,o,!1,null,null,null);e["default"]=l.exports}}]);