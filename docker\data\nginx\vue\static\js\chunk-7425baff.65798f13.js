(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7425baff","chunk-09e59946"],{"01ca":function(e,t,a){"use strict";a.d(t,"f",(function(){return s})),a.d(t,"d",(function(){return i})),a.d(t,"g",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return n})),a.d(t,"i",(function(){return c})),a.d(t,"c",(function(){return m})),a.d(t,"b",(function(){return p})),a.d(t,"h",(function(){return d}));var r=a("b775");function s(e){return Object(r["a"])({url:"/iot/model/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/iot/model/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/iot/model/permList/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/iot/model",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/iot/model/import",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/iot/model",method:"put",data:e})}function m(e){return Object(r["a"])({url:"/iot/model/"+e,method:"delete"})}function p(e){return Object(r["a"])({url:"/iot/model/cache/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/iot/model/synchron",method:"post",data:e})}},"09a1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"6px"}},[a("el-dialog",{attrs:{title:"编辑参数",visible:e.openEdit,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.openEdit=t}}},[a("div",{staticStyle:{margin:"-30px 0 30px","background-color":"#ddd",height:"1px"}}),a("el-row",[a("el-col",{staticStyle:{border:"1px solid #ddd","border-radius":"5px",padding:"10px","background-color":"#eee"},attrs:{span:12}},[a("el-form",{attrs:{model:e.queryParams,inline:!0,"label-width":"48px",size:"small"}},[a("el-form-item",{attrs:{label:"",prop:"templateName"}},[a("el-input",{staticStyle:{width:"160px"},attrs:{placeholder:"请输入物模型名称",clearable:"",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.templateName,callback:function(t){e.$set(e.queryParams,"templateName",t)},expression:"queryParams.templateName"}})],1),a("el-form-item",[a("el-button",{staticStyle:{padding:"5px"},attrs:{type:"info",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")])],1),a("el-form-item",[a("el-link",{staticStyle:{"margin-left":"20px"},attrs:{underline:!1,icon:"el-icon-info",type:"primary"}},[e._v("单击应用模板")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.templateList,size:"mini","highlight-current-row":"",border:!1,"show-header":!1,"row-style":{backgroundColor:"#eee"}},on:{"row-click":e.rowClick}},[a("el-table-column",{attrs:{label:"选择",width:"30",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("input",{attrs:{type:"radio",disabled:"array"==e.row.datatype||"object"==e.row.datatype,name:"template"},domProps:{checked:e.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:"名称",align:"left",prop:"templateName"}}),a("el-table-column",{attrs:{label:"标识符",align:"left",prop:"identifier"}}),a("el-table-column",{attrs:{label:"数据类型",align:"center",prop:"datatype",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_data_type,value:t.row.datatype}})]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 10px","background-color":"#eee"},attrs:{small:"",layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"参数名称",prop:"name"}},[a("el-input",{staticStyle:{width:"270px"},attrs:{placeholder:"例如：温度",size:"small"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"参数标识",prop:"id"}},[a("el-input",{staticStyle:{width:"270px"},attrs:{placeholder:"例如：temperature",size:"small"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1),a("el-form-item",{attrs:{label:"参数排序",prop:"order"}},[a("el-input-number",{staticStyle:{width:"270px"},attrs:{"controls-position":"right",placeholder:"请输入排序",type:"number",size:"small"},model:{value:e.form.order,callback:function(t){e.$set(e.form,"order",t)},expression:"form.order"}})],1),a("el-form-item",{attrs:{label:"参数特性",prop:"property"}},[a("el-checkbox",{attrs:{name:"isChart",label:"图表展示","true-label":1,"false-label":0},on:{change:e.isChartChange},model:{value:e.form.isChart,callback:function(t){e.$set(e.form,"isChart",t)},expression:"form.isChart"}}),a("el-checkbox",{attrs:{name:"isMonitor",label:"实时监测","true-label":1,"false-label":0},on:{change:e.isMonitorChange},model:{value:e.form.isMonitor,callback:function(t){e.$set(e.form,"isMonitor",t)},expression:"form.isMonitor"}}),a("el-checkbox",{attrs:{name:"isReadonly",label:"只读数据","true-label":1,"false-label":0},on:{change:e.isReadonlyChange},model:{value:e.form.isReadonly,callback:function(t){e.$set(e.form,"isReadonly",t)},expression:"form.isReadonly"}}),a("el-checkbox",{attrs:{name:"isHistory",label:"历史存储","true-label":1,"false-label":0},model:{value:e.form.isHistory,callback:function(t){e.$set(e.form,"isHistory",t)},expression:"form.isHistory"}}),a("el-checkbox",{attrs:{name:"isSharePerm",label:"分享权限","true-label":1,"false-label":0},model:{value:e.form.isSharePerm,callback:function(t){e.$set(e.form,"isSharePerm",t)},expression:"form.isSharePerm"}})],1),a("div",{staticStyle:{"margin-bottom":"20px","background-color":"#ddd",height:"1px"}}),a("el-form-item",{attrs:{label:"数据类型",prop:"datatype"}},[a("el-select",{staticStyle:{width:"125px"},attrs:{placeholder:"请选择数据类型",size:"small"},model:{value:e.form.datatype,callback:function(t){e.$set(e.form,"datatype",t)},expression:"form.datatype"}},[a("el-option",{key:"integer",attrs:{label:"整数",value:"integer"}}),a("el-option",{key:"decimal",attrs:{label:"小数",value:"decimal"}}),a("el-option",{key:"bool",attrs:{label:"布尔",value:"bool",disabled:1==e.form.isChart}}),a("el-option",{key:"enum",attrs:{label:"枚举",value:"enum",disabled:1==e.form.isChart}}),a("el-option",{key:"string",attrs:{label:"字符串",value:"string",disabled:1==e.form.isChart}})],1)],1),"integer"==e.form.datatype||"decimal"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"取值范围"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"最小值",type:"number",size:"small"},model:{value:e.form.specs.min,callback:function(t){e.$set(e.form.specs,"min",t)},expression:"form.specs.min"}})],1),a("el-col",{attrs:{span:4,align:"center"}},[e._v("到")]),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"最大值",type:"number",size:"small"},model:{value:e.form.specs.max,callback:function(t){e.$set(e.form.specs,"max",t)},expression:"form.specs.max"}})],1)],1)],1),a("el-form-item",{attrs:{label:"单位"}},[a("el-input",{staticStyle:{width:"308px"},attrs:{placeholder:"例如：℃",size:"small"},model:{value:e.form.specs.unit,callback:function(t){e.$set(e.form.specs,"unit",t)},expression:"form.specs.unit"}})],1),a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{staticStyle:{width:"308px"},attrs:{"controls-position":"right",placeholder:"例如：1",type:"number",size:"small"},model:{value:e.form.specs.step,callback:function(t){e.$set(e.form.specs,"step",t)},expression:"form.specs.step"}})],1)],1):e._e(),"bool"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"布尔值",prop:""}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：关闭",size:"small"},model:{value:e.form.specs.falseText,callback:function(t){e.$set(e.form.specs,"falseText",t)},expression:"form.specs.falseText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（0 值对应文本）")])],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：打开",size:"small"},model:{value:e.form.specs.trueText,callback:function(t){e.$set(e.form.specs,"trueText",t)},expression:"form.specs.trueText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（1 值对应文本）")])],1)],1)],1):e._e(),"enum"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"展示方式"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择展示方式"},model:{value:e.form.specs.showWay,callback:function(t){e.$set(e.form.specs,"showWay",t)},expression:"form.specs.showWay"}},[a("el-option",{key:"select",attrs:{label:"下拉框",value:"select"}}),a("el-option",{key:"button",attrs:{label:"按钮",value:"button"}})],1)],1),a("el-form-item",{attrs:{label:"枚举项",prop:""}},[e._l(e.form.specs.enumList,(function(t,r){return a("el-row",{key:"enum"+r,staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"例如：0",size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-input",{attrs:{placeholder:"例如：中速挡位",size:"small"},model:{value:t.text,callback:function(a){e.$set(t,"text",a)},expression:"item.text"}})],1),0!=r?a("el-col",{attrs:{span:3,offset:1}},[a("a",{staticStyle:{color:"#f56c6c"},on:{click:function(t){return e.removeEnumItem(r)}}},[e._v("删除")])]):e._e()],1)})),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addEnumItem()}}},[e._v("添加枚举项")])])],2)],1):e._e(),"string"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"最大长度",prop:""}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：1024",type:"number",size:"small"},model:{value:e.form.specs.maxLength,callback:function(t){e.$set(e.form.specs,"maxLength",t)},expression:"form.specs.maxLength"}})],1)],1)],1)],1):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],i=(a("14d9"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("cec4")),l={name:"things_parameter",dicts:["iot_things_type","iot_data_type","iot_yes_no"],props:{data:{type:Object,default:null}},watch:{data:function(e,t){this.index=e.index,e&&e.parameter.name&&""!=e.parameter.name&&(this.form.name=e.parameter.name,this.form.id=e.parameter.id,this.form.order=e.parameter.order,this.form.isChart=e.parameter.isChart?e.parameter.isChart:0,this.form.isHistory=e.parameter.isHistory?e.parameter.isHistory:1,this.form.isSharePerm=e.parameter.isSharePerm?e.parameter.isSharePerm:0,this.form.isMonitor=e.parameter.isMonitor?e.parameter.isMonitor:0,this.form.isReadonly=e.parameter.isReadonly?e.parameter.isReadonly:0,this.form.specs=e.parameter.datatype,this.form.datatype=this.form.specs.type,this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer")),this.openEdit=!0,this.getList()}},data:function(){return{loading:!0,total:0,templateList:[],openEdit:!1,queryParams:{pageNum:1,pageSize:10,name:null,type:null},index:-1,form:{},rules:{name:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],id:[{required:!0,message:"参数标识符不能为空",trigger:"blur"}],order:[{required:!0,message:"模型排序不能为空",trigger:"blur"}],datatype:[{required:!0,message:"数据类型不能为空",trigger:"change"}]}}},created:function(){this.getList(),this.reset()},methods:{getList:function(){var e=this;this.loading=!0,Object(i["e"])(this.queryParams).then((function(t){for(var a=0;a<t.rows.length;a++)t.rows[a].isSelect=!1;e.templateList=t.rows,e.total=t.total,e.setRadioSelected(e.productId),e.loading=!1}))},rowClick:function(e){null!=e&&"array"!=e.datatype&&"object"!=e.datatype&&(this.form.name=e.templateName,this.form.id=e.identifier,this.form.order=e.modelOrder,this.form.isChart=e.isChart?e.isChart:0,this.form.isHistory=e.isHistory?e.isHistory:1,this.form.isSharePerm=e.isSharePerm?e.isSharePerm:0,this.form.isReadonly=e.isReadonly?e.isReadonly:0,this.form.isMonitor=e.isMonitor?e.isMonitor:0,this.form.datatype=e.datatype,this.form.specs=JSON.parse(e.specs),this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer"),this.setRadioSelected(e.templateId))},setRadioSelected:function(e){for(var t=0;t<this.templateList.length;t++)this.templateList[t].templateId==e?this.templateList[t].isSelect=!0:this.templateList[t].isSelect=!1},cancel:function(){this.openEdit=!1,this.reset()},reset:function(){this.index=-1,this.form={name:null,id:null,order:0,datatype:"integer",isChart:0,isHistory:1,isSharePerm:0,isMonitor:0,isReadonly:0,specs:{enumList:[{value:"",text:""}],showWay:"select"}},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.form.datatype=e.formatThingsSpecs(),delete e.form.specs,e.openEdit=!1;var a={parameter:JSON.parse(JSON.stringify(e.form)),index:e.index};console.log("data",a),e.$emit("dataEvent",a),e.reset()}}))},isChartChange:function(){1==this.form.isChart?this.form.isReadonly=1:this.form.isMonitor=0},isMonitorChange:function(){1==this.form.isMonitor&&(this.form.isReadonly=1,this.form.isChart=1)},isReadonlyChange:function(){0==this.form.isReadonly&&(this.form.isMonitor=0,this.form.isChart=0)},formatThingsSpecs:function(){var e={};return e.type=this.form.datatype,"integer"==this.form.datatype||"decimal"==this.form.datatype?(e.min=Number(this.form.specs.min?this.form.specs.min:0),e.max=Number(this.form.specs.max?this.form.specs.max:100),e.unit=this.form.specs.unit?this.form.specs.unit:"",e.step=Number(this.form.specs.step?this.form.specs.step:1)):"string"==this.form.datatype?e.maxLength=Number(this.form.specs.maxLength?this.form.specs.maxLength:1024):"bool"==this.form.datatype?(e.falseText=this.form.specs.falseText?this.form.specs.falseText:"关闭",e.trueText=this.form.specs.trueText?this.form.specs.trueText:"打开"):"array"==this.form.datatype?e.arrayType=this.form.specs.arrayType:"enum"==this.form.datatype&&(e.showWay=this.form.specs.showWay,this.form.specs.enumList&&""!=this.form.specs.enumList[0].text?e.enumList=this.form.specs.enumList:(e.showWay="select",e.enumList=[{value:"0",text:"低"},{value:"1",text:"高"}])),e},addEnumItem:function(){this.form.specs.enumList.push({value:"",text:""})},removeEnumItem:function(e){this.form.specs.enumList.splice(e,1)}}},o=l,n=(a("4df4f"),a("2877")),c=Object(n["a"])(o,r,s,!1,null,null,null);t["default"]=c.exports},"4df4f":function(e,t,a){"use strict";a("c23a")},"78f7":function(e,t,a){},"9b9c":function(e,t,a){"use strict";a.d(t,"f",(function(){return s})),a.d(t,"g",(function(){return i})),a.d(t,"e",(function(){return l})),a.d(t,"a",(function(){return o})),a.d(t,"i",(function(){return n})),a.d(t,"d",(function(){return c})),a.d(t,"b",(function(){return m})),a.d(t,"c",(function(){return p})),a.d(t,"h",(function(){return d}));var r=a("b775");function s(e){return Object(r["a"])({url:"/iot/product/list",method:"get",params:e})}function i(){return Object(r["a"])({url:"/iot/product/shortList",method:"get"})}function l(e){return Object(r["a"])({url:"/iot/product/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/iot/product",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/iot/product",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function m(e){return Object(r["a"])({url:"/iot/product/status/",method:"put",data:e})}function p(e){return Object(r["a"])({url:"/iot/product/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:e})}},"9fb1":function(e,t,a){"use strict";a("78f7")},a66a:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"6px"}},[a("el-card",{staticStyle:{"margin-bottom":"10px"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",{staticStyle:{"font-weight":"bold"}},[e._v("变量模板")]),a("el-button",{staticStyle:{float:"right",padding:"5px 15px"},attrs:{size:"mini",type:"info"},on:{click:e.goBack}},[e._v("返回")])],1),a("el-form",{ref:"form",staticStyle:{"margin-bottom":"-20px"},attrs:{inline:"",model:e.content,"label-width":"150px",align:"left"}},[a("el-form-item",{attrs:{label:"模板名称: ",size:"mini"}},[a("span",[e._v(e._s(e.content.templateName))])]),a("el-form-item",{attrs:{label:"采集方式: ",size:"mini"}},[a("dict-tag",{staticStyle:{display:"inline-block"},attrs:{options:e.dict.type.data_collect_type,value:e.content.pollingMethod,size:"small"}})],1)],1)],1),a("el-card",{staticStyle:{padding:"-10px","padding-bottom":"30px"}},[a("el-row",{attrs:{gutter:0}},[a("el-col",{attrs:{span:8}},[a("el-form",{attrs:{inline:!0}},[a("el-form-item",[a("el-button",{staticStyle:{"margin-top":"13px"},attrs:{size:"mini",type:"primary"},on:{click:e.refresh}},[e._v("刷新")])],1)],1),a("div",{staticStyle:{border:"1px solid #e6ebf5"}},[a("div",{staticStyle:{padding:"20px 20px 30px 20px","background-color":"#f8f8f9"}},[a("span",{staticStyle:{"font-size":"15px","font-weight":"bold"}},[e._v("从机列表")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:add"],expression:"['iot:salve:add']"}],staticStyle:{float:"right"},attrs:{type:"primary",size:"mini"},on:{click:e.addSalve}},[e._v("添加从机")])],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%",height:"500px"},attrs:{data:e.salveList,size:"mini","highlight-current-row":""},on:{"row-click":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("input",{attrs:{type:"radio"},domProps:{checked:e.row.isSelect}})]}}])}),a("el-table-column",{attrs:{prop:"slaveName",label:"从机名称","header-align":"center",align:"left","min-width":"120"}}),a("el-table-column",{attrs:{prop:"timer","header-align":"center",align:"left",label:"采集频率"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_modbus_poll_time,value:t.row.timer}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:slave:edit"],expression:"['iot:slave:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdateSlave(t.row)}}},[e._v("编辑 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:slave:remove"],expression:"['iot:slave:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDeleteSalve(t.row)}}},[e._v("删除 ")])]}}])})],1)],1)],1),a("el-col",{attrs:{span:16}},[a("div",{staticClass:"container-02"},[a("el-form",{attrs:{inline:!0,model:e.queryParams}},[a("el-form-item",[a("el-input",{attrs:{size:"mini",placeholder:"请输入变量名称"},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:e.queryByName}},[e._v("查询")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"warning",icon:"el-icon-upload2",plain:"",size:"mini"},on:{click:e.handleImport}},[e._v("导入变量 ")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.addValTemp}},[e._v("添加变量")]),a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.synchronizaToProduct}},[e._v("同步至产品")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.sLoadoing,expression:"sLoadoing"}],attrs:{data:e.pointList,size:"mini"}},[a("el-table-column",{attrs:{label:"序号",prop:"templateId",width:"50"}}),a("el-table-column",{attrs:{prop:"templateName",label:"物模型名称",width:"150"}}),a("el-table-column",{attrs:{prop:"regAddr",label:"寄存器地址",width:"95"}}),a("el-table-column",{attrs:{label:"物模型类别",align:"center",prop:"type",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_things_type,value:t.row.type}})]}}])}),a("el-table-column",{attrs:{prop:"datatype",label:"数值类型",width:"100px"}}),a("el-table-column",{attrs:{prop:"isReadonly",label:"读写类型",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_data_read_write,value:t.row.isReadonly}})]}}])}),a("el-table-column",{attrs:{label:"历史存储",align:"center",prop:"",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isHistory}})]}}])}),a("el-table-column",{attrs:{prop:"formula",label:"计算公式",width:"95"}}),a("el-table-column",{attrs:{label:"数据定义",align:"left","header-align":"center",prop:"specs","min-width":"150","class-name":"specsColor"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatSpecsDisplay(t.row.specs))}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:edit"],expression:"['iot:temp:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("编辑 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:temp:remove"],expression:"['iot:temp:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)])],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.openViewVarTemp,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openViewVarTemp=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"模型名称",prop:"templateName"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入物模型名称，例如：温度"},model:{value:e.form.templateName,callback:function(t){e.$set(e.form,"templateName",t)},expression:"form.templateName"}})],1),a("el-form-item",{attrs:{label:"功能码",prop:"code",size:"small"}},[a("el-select",{staticStyle:{width:"200px",display:"inline-block","padding-right":"10px"},attrs:{placeholder:"请选择"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}},e._l(e.dict.type.iot_modbus_status_code,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"寄存器/线圈",prop:"regStr"}},[a("el-input",{staticStyle:{width:"200px",display:"inline-block","padding-right":"10px"},attrs:{placeholder:"请输入地址(10进制)"},model:{value:e.form.regStr,callback:function(t){e.$set(e.form,"regStr",t)},expression:"form.regStr"}})],1),a("el-form-item",{attrs:{label:"读取个数",prop:"quantity",size:"small"}},[a("template",{slot:"label"},[a("span",[e._v("读取个数")]),a("el-tooltip",{staticStyle:{cursor:"pointer"},attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" 单个数据点读取寄存器或线圈的个数"),a("br")]),a("i",{staticClass:"el-icon-question"})])],1),a("el-input",{staticStyle:{width:"200px",display:"inline-block","padding-right":"10px"},attrs:{placeholder:"单个数据点读取寄存器或线圈的个数"},model:{value:e.form.quantity,callback:function(t){e.$set(e.form,"quantity",t)},expression:"form.quantity"}})],2),a("el-form-item",{attrs:{label:"解析类型",prop:"parseType",size:"small"}},[a("el-select",{staticStyle:{width:"200px",display:"inline-block","padding-right":"10px"},attrs:{placeholder:"Modbus解析类型"},model:{value:e.form.parseType,callback:function(t){e.$set(e.form,"parseType",t)},expression:"form.parseType"}},e._l(e.dict.type.iot_modbus_data_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"模型排序",prop:"modelOrder"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入排序",type:"number"},model:{value:e.form.modelOrder,callback:function(t){e.$set(e.form,"modelOrder",t)},expression:"form.modelOrder"}})],1),a("el-form-item",{attrs:{label:"模型类别",prop:"type"}},[a("el-radio-group",{on:{change:function(t){return e.typeChange(e.form.type)}},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("属性")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("功能")]),a("el-radio-button",{attrs:{label:"3"}},[e._v("事件")])],1)],1),a("el-form-item",{attrs:{label:"模型特性",prop:"property"}},[a("template",{slot:"label"},[a("span",[e._v("模型特性")]),a("el-tooltip",{staticStyle:{cursor:"pointer"},attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" 图标展示: 在运行状态以图标形式展示"),a("br"),e._v(" 只读数据: 只读数据点位/读写数据点位"),a("br"),e._v(" 历史存储：勾选后在历史数据记录,历史数据处展示"),a("br")]),a("i",{staticClass:"el-icon-question"})])],1),a("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:1==e.form.type,expression:"form.type == 1"}],attrs:{name:"isChart",label:"图表展示","true-label":1,"false-label":0},on:{change:e.isChartChange},model:{value:e.form.isChart,callback:function(t){e.$set(e.form,"isChart",t)},expression:"form.isChart"}}),a("el-checkbox",{attrs:{name:"isReadonly",label:"只读数据",disabled:3==e.form.type,"true-label":1,"false-label":0},on:{change:e.isReadonlyChange},model:{value:e.form.isReadonly,callback:function(t){e.$set(e.form,"isReadonly",t)},expression:"form.isReadonly"}}),a("el-checkbox",{attrs:{name:"isHistory",label:"历史存储","true-label":1,"false-label":0},model:{value:e.form.isHistory,callback:function(t){e.$set(e.form,"isHistory",t)},expression:"form.isHistory"}})],2),a("el-divider"),a("el-form-item",{attrs:{label:"数据类型",prop:"datatype"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择数据类型"},on:{change:e.dataTypeChange},model:{value:e.form.datatype,callback:function(t){e.$set(e.form,"datatype",t)},expression:"form.datatype"}},[a("el-option",{key:"integer",attrs:{label:"整数",value:"integer"}}),a("el-option",{key:"decimal",attrs:{label:"小数",value:"decimal"}}),a("el-option",{key:"bool",attrs:{label:"布尔",value:"bool",disabled:1==e.form.isChart}}),a("el-option",{key:"enum",attrs:{label:"枚举",value:"enum",disabled:1==e.form.isChart}}),a("el-option",{key:"string",attrs:{label:"字符串",value:"string",disabled:1==e.form.isChart}}),a("el-option",{key:"array",attrs:{label:"数组",value:"array",disabled:1==e.form.isChart}})],1)],1),"integer"==e.form.datatype||"decimal"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"取值范围"}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"最小值",type:"number"},model:{value:e.form.specs.min,callback:function(t){e.$set(e.form.specs,"min",t)},expression:"form.specs.min"}})],1),a("el-col",{attrs:{span:2,align:"center"}},[e._v("到")]),a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"最大值",type:"number"},model:{value:e.form.specs.max,callback:function(t){e.$set(e.form.specs,"max",t)},expression:"form.specs.max"}})],1)],1)],1),a("el-form-item",{attrs:{label:"单位"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入单位，例如：℃"},model:{value:e.form.specs.unit,callback:function(t){e.$set(e.form.specs,"unit",t)},expression:"form.specs.unit"}})],1)],1):e._e(),"bool"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"布尔值",prop:""}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：关闭"},model:{value:e.form.specs.falseText,callback:function(t){e.$set(e.form.specs,"falseText",t)},expression:"form.specs.falseText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v(" （0 值对应文本）")])],1),a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：打开"},model:{value:e.form.specs.trueText,callback:function(t){e.$set(e.form.specs,"trueText",t)},expression:"form.specs.trueText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v(" （1 值对应文本）")])],1)],1)],1):e._e(),"enum"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"展示方式"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择展示方式"},model:{value:e.form.specs.showWay,callback:function(t){e.$set(e.form.specs,"showWay",t)},expression:"form.specs.showWay"}},[a("el-option",{key:"select",attrs:{label:"下拉框",value:"select"}}),a("el-option",{key:"button",attrs:{label:"按钮",value:"button"}})],1)],1),a("el-form-item",{attrs:{label:"枚举项",prop:""}},[e._l(e.form.specs.enumList,(function(t,r){return a("el-row",{key:"enum"+r,staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"参数值，例如：0"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-input",{attrs:{placeholder:"参数描述，例如：中速档位"},model:{value:t.text,callback:function(a){e.$set(t,"text",a)},expression:"item.text"}})],1),0!=r?a("el-col",{attrs:{span:2,offset:1}},[a("a",{staticStyle:{color:"#F56C6C"},on:{click:function(t){return e.removeEnumItem(r)}}},[e._v("删除")])]):e._e()],1)})),a("div",[e._v("+ "),a("a",{staticStyle:{color:"#409EFF"},on:{click:function(t){return e.addEnumItem()}}},[e._v("添加枚举项")])])],2)],1):e._e(),"string"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"最大长度",prop:""}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：1024",type:"number"},model:{value:e.form.specs.maxLength,callback:function(t){e.$set(e.form.specs,"maxLength",t)},expression:"form.specs.maxLength"}})],1),a("el-col",{attrs:{span:14,offset:1}},[e._v("（字符串的最大长度）")])],1)],1)],1):e._e(),"array"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"元素个数",prop:""}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：5",type:"number"},model:{value:e.form.specs.arrayCount,callback:function(t){e.$set(e.form.specs,"arrayCount",t)},expression:"form.specs.arrayCount"}})],1)],1)],1),a("el-form-item",{attrs:{label:"数组类型",prop:""}},[a("el-radio-group",{model:{value:e.form.specs.arrayType,callback:function(t){e.$set(e.form.specs,"arrayType",t)},expression:"form.specs.arrayType"}},[a("el-radio",{attrs:{label:"integer"}},[e._v("整数")]),a("el-radio",{attrs:{label:"decimal"}},[e._v("小数")]),a("el-radio",{attrs:{label:"string"}},[e._v("字符串")])],1)],1),"object"==e.form.specs.arrayType?a("el-form-item",{attrs:{label:"对象参数"}},[a("div",{staticStyle:{"background-color":"#f8f8f8","border-radius":"5px"}},e._l(e.form.specs.params,(function(t,r){return a("el-row",{key:r,staticStyle:{padding:"0 10px 5px"}},[0==r?a("div",{staticStyle:{"margin-top":"5px"}}):e._e(),a("el-col",{attrs:{span:18}},[a("el-input",{staticStyle:{"margin-top":"3px"},attrs:{readonly:"",size:"mini",placeholder:"请选择设备"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}},[a("template",{slot:"prepend"},[a("el-tag",{staticStyle:{"margin-left":"-21px",height:"26px","line-height":"26px"},attrs:{size:"mini",effect:"dark"}},[e._v(" "+e._s(t.order)+" ")]),e._v(" "+e._s(e.form.identifier+"_"+t.id)+" ")],1),a("el-button",{attrs:{slot:"append",size:"small"},on:{click:function(a){return e.editParameter(t,r)}},slot:"append"},[e._v("编辑")])],2)],1),a("el-col",{attrs:{span:2,offset:2}},[a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",plain:"",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.removeParameter(r)}}},[e._v("删除 ")])],1)],1)})),1),a("div",[e._v("+ "),a("a",{staticStyle:{color:"#409EFF"},on:{click:function(t){return e.addParameter()}}},[e._v("添加参数")])])]):e._e()],1):e._e(),"object"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"对象参数",prop:""}},[a("div",{staticStyle:{"background-color":"#f8f8f8","border-radius":"5px"}},e._l(e.form.specs.params,(function(t,r){return a("el-row",{key:r,staticStyle:{padding:"0 10px 5px"}},[0==r?a("div",{staticStyle:{"margin-top":"5px"}}):e._e(),a("el-col",{attrs:{span:18}},[a("el-input",{staticStyle:{"margin-top":"3px"},attrs:{readonly:"",size:"mini",placeholder:"请选择设备"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}},[a("template",{slot:"prepend"},[a("el-tag",{staticStyle:{"margin-left":"-21px",height:"26px","line-height":"26px"},attrs:{size:"mini",effect:"dark"}},[e._v(" "+e._s(t.order)+" ")]),e._v(" "+e._s(e.form.identifier+"_"+t.id)+" ")],1),a("el-button",{attrs:{slot:"append",size:"small"},on:{click:function(a){return e.editParameter(t,r)}},slot:"append"},[e._v("编辑")])],2)],1),a("el-col",{attrs:{span:2,offset:2}},[a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",plain:"",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.removeParameter(r)}}},[e._v("删除 ")])],1)],1)})),1),a("div",[e._v("+ "),a("a",{staticStyle:{color:"#409EFF"},on:{click:function(t){return e.addParameter()}}},[e._v("添加参数")])])])],1):e._e(),a("el-form-item",{attrs:{label:"高级选项"}},[a("el-switch",{on:{change:e.selectOpenAction},model:{value:e.isAdvance,callback:function(t){e.isAdvance=t},expression:"isAdvance"}})],1),e.selectOpen?a("el-form-item",{attrs:{label:"计算公式"}},[a("template",{slot:"label"},[a("span",[e._v("计算公式")]),a("el-tooltip",{staticStyle:{cursor:"pointer"},attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" 设备上行数据经计算公式计算后显示 。"),a("br"),e._v(" 公式中的%s为占位符，是固定字段。"),a("br"),e._v(" 如："),a("br"),e._v(" 加：%s+10"),a("br"),e._v(" 减：%s-10"),a("br"),e._v(" 乘：%s*10"),a("br"),e._v(" 除：%s/10"),a("br"),e._v(" 除(保留小数)：%s%10.00"),a("br")]),a("i",{staticClass:"el-icon-question"})])],1),a("el-input",{attrs:{placeholder:"计算公式"},model:{value:e.form.formula,callback:function(t){e.$set(e.form,"formula",t)},expression:"form.formula"}})],2):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("things-parameter",{attrs:{data:e.paramData},on:{dataEvent:function(t){return e.getParamData(t)}}}),a("el-dialog",{attrs:{title:e.title,visible:e.openViewSalve,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.openViewSalve=t}}},[a("el-form",{ref:"addSavleFrom",staticStyle:{width:"500px"},attrs:{model:e.addSavleFrom,rules:e.salveRules,"label-width":"130px",size:"mini"}},[a("el-form-item",{attrs:{label:"从机名称",prop:"slaveName",size:"small"}},[a("el-input",{attrs:{placeholder:"请输入从机名称"},model:{value:e.addSavleFrom.slaveName,callback:function(t){e.$set(e.addSavleFrom,"slaveName",t)},expression:"addSavleFrom.slaveName"}})],1),a("el-form-item",{attrs:{label:"从机地址",prop:"slaveAddr",size:"small"}},[a("el-input",{attrs:{placeholder:"请输入从机地址"},model:{value:e.addSavleFrom.slaveAddr,callback:function(t){e.$set(e.addSavleFrom,"slaveAddr",t)},expression:"addSavleFrom.slaveAddr"}})],1),a("el-form-item",{attrs:{label:"寄存器起始地址",prop:"addrStart",size:"small"}},[a("el-input",{attrs:{placeholder:"寄存器起始地址(10进制),例如 0x000A 填写 10 "},model:{value:e.addSavleFrom.addrStart,callback:function(t){e.$set(e.addSavleFrom,"addrStart",t)},expression:"addSavleFrom.addrStart"}})],1),a("el-form-item",{attrs:{label:"寄存器结束地址",prop:"addrEnd",size:"small"}},[a("el-input",{attrs:{placeholder:"寄存器结束地址(10进制),例如 0x00AA 填写 170"},model:{value:e.addSavleFrom.addrEnd,callback:function(t){e.$set(e.addSavleFrom,"addrEnd",t)},expression:"addSavleFrom.addrEnd"}})],1),a("el-form-item",{attrs:{label:"批量采集频率",prop:"timer",size:"small"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.addSavleFrom.timer,callback:function(t){e.$set(e.addSavleFrom,"timer",t)},expression:"addSavleFrom.timer"}},e._l(e.dict.type.iot_modbus_poll_time,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"高级选项"}},[a("el-switch",{on:{change:e.selectOpenAction},model:{value:e.isAdvance,callback:function(t){e.isAdvance=t},expression:"isAdvance"}})],1),e.selectOpen?a("el-form-item",{attrs:{label:"组包寄存器最大数量",prop:"packetLength","label-width":"170px",type:"number"}},[a("template",{slot:"label"},[a("span",[e._v("组包寄存器最大数量")]),a("el-tooltip",{staticStyle:{cursor:"pointer"},attrs:{effect:"light",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v("云端采集的协议支持配置组包寄存器最大的数量; "),a("br"),a("div",{staticStyle:{height:"10px"}}),a("b",[e._v("不填写: ")]),e._v("每次随机轮询32~64个寄存器（2 Byte）"),a("br"),e._v("Modbus协议回复包没有起始地址，可以解决网络延迟"),a("br"),e._v("或回复超时解析变量错位问题；"),a("br"),a("div",{staticStyle:{height:"10px"}}),a("b",[e._v("填写: ")]),e._v("每次按填写的数值轮询寄存器，通过云端的智"),a("br"),e._v("能化算法，可以降低网络延迟或回复超时解析变量错"),a("br"),e._v("位问题的概率; ")]),a("i",{staticClass:"el-icon-question"})])],1),a("el-input",{attrs:{prop:"packetLength",placeholder:"批量读取寄存器的个数(32-64)"},model:{value:e.addSavleFrom.packetLength,callback:function(t){e.$set(e.addSavleFrom,"packetLength",t)},expression:"addSavleFrom.packetLength"}})],2):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitFormSlave}},[e._v("确 定")])],1)],1),a("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url,disabled:e.upload.isUploading,data:{tempSlaveId:this.queryParams.tempSlaveId},"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("span",[e._v("仅允许导入xls、xlsx格式文件。")]),a("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[e._v("下载模板 ")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.openViewProduct,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.openViewProduct=t}}},[a("div",{staticStyle:{color:"red"}},[e._v("采集点模板同步到产品后,产品物模型以及缓存物模型将更新,请谨慎更新产品采集点模板数据")]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%",height:"500px"},attrs:{data:e.productList,size:"mini","highlight-current-row":""},on:{"selection-change":e.selectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"productId",label:"产品id","header-align":"center",align:"left","min-width":"60"}}),a("el-table-column",{attrs:{prop:"productName",label:"产品名称","header-align":"center",align:"left","min-width":"120"}}),a("el-table-column",{attrs:{prop:"transport",label:"传输协议","header-align":"center",align:"left","min-width":"60"}}),a("el-table-column",{attrs:{prop:"protocolCode",label:"设备协议","header-align":"center",align:"left","min-width":"80"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitSyncToProduct}},[e._v("确定同步")])],1)],1)],1)},s=[],i=(a("d81d"),a("14d9"),a("4e82"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("cec4")),l=a("a824"),o=(a("61f7"),a("5f87")),n=a("09a1"),c=a("9b9c"),m=a("01ca"),p={name:"Point",dicts:["data_collect_type","iot_modbus_status_code","iot_modbus_data_type","iot_modbus_poll_time","iot_data_read_write","iot_yes_no","iot_things_type"],components:{thingsParameter:n["default"]},data:function(){return{isAdvance:!1,loading:!0,sLoadoing:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,pointList:[],salveList:[],paramData:{index:-1,parameter:{}},title:"",openViewVarTemp:!1,openViewSalve:!1,openViewProduct:!1,selectOpen:!1,selectRowData:null,queryParams:{pageNum:1,pageSize:10,tempSlaveId:null,name:null,identifier:null,weight:null,deviceTempId:null},productList:[],productIds:[],upload:{open:!1,title:"",headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/iot/template/importData"},form:{quantity:1,regType:3,code:3,parseType:"ushort"},rules:{templateName:[{required:!0,message:"物模型名称不能为空",trigger:"blur"}],modelOrder:[{required:!0,message:"模型排序不能为空",trigger:"blur"}],type:[{required:!0,message:"模型类别不能为空",trigger:"change"}],datatype:[{required:!0,message:"数据类型不能为空",trigger:"change"}],specs:[{required:!0,message:"数据定义不能为空",trigger:"blur"}],regStr:[{required:!0,message:"寄存器地址不能为空",trigger:"blur"}],regType:[{required:!0,message:"功能码不能为空",trigger:"blur"}],quantity:[{required:!0,message:"数量不能为空",trigger:"blur"}]},content:{pollingMethod:0},addSavleFrom:{code:3},salveRules:{slaveName:[{required:!0,message:"从机名称不能为空",trigger:"blur"}],addrStart:[{required:!0,message:"寄存器起始地址不能为空",trigger:"blur"}],addrEnd:[{required:!0,message:"寄存器结束地址不能为空",trigger:"blur"}],slaveAddr:[{required:!0,message:"从机地址不能为空",trigger:"blur"}],packetLength:[{required:!0,message:"数量不能为空",trigger:"blur"}]}}},created:function(){this.content.templateName=this.$route.query&&this.$route.query.templateName,this.content.pollingMethod=this.$route.query&&this.$route.query.pollingMethod,this.content.share=this.$route.query&&this.$route.query.share,this.content.templateId=this.$route.query&&this.$route.query.templateId,this.addSavleFrom.pollingMethod=this.$route.query&&this.$route.query.pollingMethod;var e=this.$route.query&&this.$route.query.isOpen;1===e&&(this.openViewSalve=!0),this.queryParams.deviceTempId=this.content.templateId,this.getSalveList()},methods:{getList:function(){var e=this;this.sLoadoing=!0,Object(i["e"])(this.queryParams).then((function(t){e.pointList=t.rows,e.total=t.total,e.sLoadoing=!1}))},getSalveList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){for(var a=0;a<t.rows.length;a++)t.rows[a].isSelect=!1,e.selectRowData&&e.selectRowData.id==t.rows[a].id&&(t.rows[a].isSelect=!0);if(e.salveList=t.rows,e.total=t.total,e.loading=!1,e.salveList.length>0){var r=e.salveList[0];e.handleSelectionChange(r),e.queryParams.tempSlaveId=r.deviceTempId+"#"+r.slaveAddr,e.getList()}}))},cancel:function(){this.openViewVarTemp=!1,this.openViewSalve=!1,this.openViewProduct=!1,this.reset()},reset:function(){this.form={templateId:null,templateName:null,userId:null,userName:null,tenantId:null,tenantName:null,identifier:null,modelOrder:0,type:1,datatype:"integer",isSys:null,isChart:0,isHistory:0,isMonitor:0,isReadonly:1,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,regType:"3",code:"3",quantity:1,parseType:"ushort",specs:{enumList:[{value:"",text:""}],arrayType:"integer",arrayCount:5,showWay:"select",params:[]}},this.addSavleFrom={deviceTempId:null,slaveAddr:null,slaveIndex:null,code:null,slaveIp:null,slaveName:null,slavePort:null,status:0,createTime:null,createBy:null,updateTime:null,updateBy:null,remark:null},this.resetForm("form"),this.resetForm("addSavleFrom")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.setRadioSelected(e),null!=e&&(this.selectRowData=e),this.queryParams.tempSlaveId=e.deviceTempId+"#"+e.slaveAddr,this.getList()},selectionChange:function(e){this.productIds=e.map((function(e){return e.productId}))},setRadioSelected:function(e){for(var t=0;t<this.salveList.length;t++)this.salveList[t].id==e.id?this.salveList[t].isSelect=!0:this.salveList[t].isSelect=!1},handleAdd:function(){this.reset(),this.openViewVarTemp=!0,this.title="添加变量模板从机采集点"},handleUpdate:function(e){var t=this;this.reset();var a=e.templateId||this.ids;Object(i["d"])(a).then((function(e){var a=e.data;if(t.openViewVarTemp=!0,t.title="修改物模型",a.specs=JSON.parse(a.specs),a.specs.enumList||(a.specs.showWay="select",a.specs.enumList=[{value:"",text:""}]),a.specs.arrayType||(a.specs.arrayType="integer"),a.specs.arrayCount||(a.specs.arrayCount=5),a.specs.params||(a.specs.params=[]),"array"==a.specs.type&&"object"==a.specs.arrayType||"object"==a.specs.type)for(var r=0;r<a.specs.params.length;r++)a.specs.params[r].id=String(a.specs.params[r].id).substring(String(a.identifier).length+1);t.form=a,t.form.regType="3",t.form.regStr=t.form.regAddr+""}))},handleUpdateSlave:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(l["c"])(a).then((function(e){t.addSavleFrom=e.data,t.openViewSalve=!0,t.addSavleFrom.code=t.addSavleFrom.code+"",t.addSavleFrom.timer=t.addSavleFrom.timer+"",t.title="修改设备从机"}))},handleDeleteSalve:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除变量模板设备从机编号为"'+a+'"的数据项？').then((function(){Object(l["b"])(a)})).then((function(){t.getSalveList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){if(("object"==e.form.datatype||"array"==e.form.datatype&&"object"==e.form.specs.arrayType)&&(!e.form.specs.params||0==e.form.specs.params))return void e.$modal.msgError("对象的参数不能为空");if(e.form.specs.params&&e.form.specs.params.length>0)for(var a=e.form.specs.params.map((function(e){return e.id})).sort(),r=0;r<a.length;r++)if(a[r]==a[r+1])return void e.$modal.msgError("参数标识 "+a[r]+" 重复");if(e.form.deviceTempId=e.content.templateId,e.form.tempSlaveId=e.form.deviceTempId+"#"+e.selectRowData.slaveAddr,null!=e.form.templateId){var s=JSON.parse(JSON.stringify(e.form));s.specs=e.formatThingsSpecs(),(2==e.form.type||3==e.form.type)&&(s.isMonitor=0,s.isChart=0),Object(i["f"])(s).then((function(t){e.$modal.msgSuccess("修改成功"),e.openViewVarTemp=!1,e.getList()}))}else{var l=JSON.parse(JSON.stringify(e.form));l.specs=e.formatThingsSpecs(),2==e.form.type?l.isMonitor=0:3==e.form.type&&(l.isMonitor=0,l.isChart=0),Object(i["a"])(l).then((function(t){e.$modal.msgSuccess("新增成功"),e.openViewVarTemp=!1,e.getList()}))}}}))},submitFormSlave:function(){var e=this;this.addSavleFrom.pollingMethod=this.content.pollingMethod,this.$refs["addSavleFrom"].validate((function(t){t&&(e.addSavleFrom.deviceTempId=e.content.templateId,null!=e.addSavleFrom.id?Object(l["f"])(e.addSavleFrom).then((function(t){e.$modal.msgSuccess("修改成功"),e.openViewSalve=!1,e.getSalveList()})):Object(l["a"])(e.addSavleFrom).then((function(t){e.$modal.msgSuccess("新增成功"),e.openViewSalve=!1,e.getSalveList()})))}))},queryByName:function(){this.selectRowData||this.$modal.alert("请选选择或添加子设备")},addSalve:function(){this.reset(),this.openViewSalve=!0,this.title="添加从机"},addValTemp:function(){this.selectRowData?(this.reset(),this.openViewVarTemp=!0,this.title="添加变量"):this.$modal.alert("请选选择或添加子设备")},synchronizaToProduct:function(){var e=this,t={templateId:this.content.templateId};Object(c["h"])(t).then((function(t){e.productList=t.data,e.openViewProduct=!0,e.title="同步采集点到产品"}))},submitSyncToProduct:function(){var e=this,t={productIds:this.productIds,templateId:this.content.templateId};Object(m["h"])(t).then((function(t){200==t.code?e.$modal.msgSuccess("同步成功"):e.$modal.msgError(t.msg),e.openViewProduct=!1,e.reset()}))},selectOpenAction:function(){this.selectOpen=!this.selectOpen},handleDelete:function(e){var t=this,a=e.templateId||this.ids;this.$modal.confirm('是否确认删除变量模板从机采集点编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},importTemplate:function(){this.download("iot/template/temp",{},"采集点_".concat((new Date).getTime(),".xlsx"))},submitFileForm:function(){this.$refs.upload.submit()},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},handleImport:function(){this.selectRowData?(this.upload.title="采集点导入",this.upload.open=!0):this.$modal.alert("请选选择或添加子设备")},dataTypeChange:function(e){},addEnumItem:function(){this.form.specs.enumList.push({value:"",text:""})},removeEnumItem:function(e){this.form.specs.enumList.splice(e,1)},typeChange:function(e){1==e?(this.form.isChart=1,this.form.isHistory=1,this.form.isMonitor=1,this.form.isReadonly=1,this.form.datatype="integer"):2==e?(this.form.isChart=0,this.form.isHistory=1,this.form.isMonitor=0,this.form.isReadonly=0):3==e&&(this.form.isChart=0,this.form.isHistory=1,this.form.isMonitor=0,this.form.isReadonly=1)},isChartChange:function(){1==this.form.isChart?this.form.isReadonly=1:this.form.isMonitor=0},isMonitorChange:function(){1==this.form.isMonitor&&(this.form.isReadonly=1,this.form.isChart=1)},isReadonlyChange:function(){0==this.form.isReadonly&&(this.form.isMonitor=0,this.form.isChart=0)},formatThingsSpecs:function(){var e={};if(e.type=this.form.datatype,"integer"==this.form.datatype||"decimal"==this.form.datatype)e.min=Number(this.form.specs.min?this.form.specs.min:0),e.max=Number(this.form.specs.max?this.form.specs.max:100),e.unit=this.form.specs.unit?this.form.specs.unit:"",e.step=Number(this.form.specs.step?this.form.specs.step:1);else if("string"==this.form.datatype)e.maxLength=Number(this.form.specs.maxLength?this.form.specs.maxLength:1024);else if("bool"==this.form.datatype)e.falseText=this.form.specs.falseText?this.form.specs.falseText:"关闭",e.trueText=this.form.specs.trueText?this.form.specs.trueText:"打开";else if("array"==this.form.datatype)e.arrayType=this.form.specs.arrayType;else if("enum"==this.form.datatype)e.showWay=this.form.specs.showWay,this.form.specs.enumList&&""!=this.form.specs.enumList[0].text?e.enumList=this.form.specs.enumList:(e.showWay="select",e.enumList=[{value:"0",text:"低"},{value:"1",text:"高"}]);else if("array"==this.form.datatype){if(e.arrayType=this.form.specs.arrayType,e.arrayCount=this.form.specs.arrayCount?this.form.specs.arrayCount:5,"object"==e.arrayType){e.params=this.form.specs.params;for(var t=0;t<e.params.length;t++)e.params[t].id=this.form.identifier+"_"+e.params[t].id}}else if("object"==this.form.datatype){e.params=this.form.specs.params;for(var a=0;a<e.params.length;a++)e.params[a].id=this.form.identifier+"_"+e.params[a].id}return JSON.stringify(e)},formatSpecsDisplay:function(e){var t=JSON.parse(e);if("integer"===t.type||"decimal"===t.type||"INT16"===t.type||"INT"===t.type)return"<span style='width:50%;display:inline-block;'>最大值：<span style=\"color:#F56C6C\">"+t.max+'</span></span>最小值：<span style="color:#F56C6C">'+t.min+"</span><br /><span style='width:50%;display:inline-block;'>步长：<span style=\"color:#F56C6C\">"+t.step+'</span></span>单位：<span style="color:#F56C6C">'+t.unit;if("string"===t.type)return'最大长度：<span style="color:#F56C6C">'+t.maxLength+"</span>";if("array"===t.type)return"<span style='width:50%;display:inline-block;'>数组类型：<span style=\"color:#F56C6C\">"+t.arrayType+'</span></span>元素个数：<span style="color:#F56C6C">'+t.arrayCount;if("enum"===t.type){for(var a="",r=0;r<t.enumList.length;r++)a=a+"<span style='width:50%;display:inline-block;'>"+t.enumList[r].value+"：<span style='color:#F56C6C'>"+t.enumList[r].text+"</span></span>",r>0&&r%2!=0&&(a+="<br />");return a}if("bool"===t.type)return"<span style='width:50%;display:inline-block;'>0：<span style=\"color:#F56C6C\">"+t.falseText+'</span></span>1：<span style="color:#F56C6C">'+t.trueText;if("object"===t.type){for(var s="",i=0;i<t.params.length;i++)s=s+"<span style='width:50%;display:inline-block;'>"+t.params[i].name+"：<span style='color:#F56C6C'>"+t.params[i].datatype.type+"</span></span>",i>0&&i%2!=0&&(s+="<br />");return s}},addParameter:function(){this.paramData={index:-1,parameter:{}}},editParameter:function(e,t){this.paramData=null,this.paramData={index:t,parameter:e}},getParamData:function(e){-1==e.index?this.form.specs.params.push(e.parameter):(this.form.specs.params[e.index]=e.parameter,this.$set(this.form.specs.params,e.index,this.form.specs.params[e.index]))},goBack:function(){var e={path:"/template/template"};this.$tab.closeOpenPage(e),this.reset()},refresh:function(){this.getList(),this.getSalveList()}}},d=p,u=(a("9fb1"),a("2877")),f=Object(u["a"])(d,r,s,!1,null,null,null);t["default"]=f.exports},a824:function(e,t,a){"use strict";a.d(t,"e",(function(){return s})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return n})),a.d(t,"d",(function(){return c}));var r=a("b775");function s(e){return Object(r["a"])({url:"/iot/salve/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/iot/salve/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/iot/salve",method:"post",data:e})}function o(e){return Object(r["a"])({url:"/iot/salve",method:"put",data:e})}function n(e){return Object(r["a"])({url:"/iot/salve/"+e,method:"delete"})}function c(e){return Object(r["a"])({url:"/iot/salve/listByPId",method:"get",params:e})}},c23a:function(e,t,a){},cec4:function(e,t,a){"use strict";a.d(t,"e",(function(){return s})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return c}));var r=a("b775");function s(e){return Object(r["a"])({url:"/iot/template/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/iot/template/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/iot/template",method:"post",data:e})}function o(e){return Object(r["a"])({url:"/iot/template",method:"put",data:e})}function n(e){return Object(r["a"])({url:"/iot/template/"+e,method:"delete"})}function c(e){return Object(r["a"])({url:"/iot/template/getPoints",method:"get",params:e})}}}]);