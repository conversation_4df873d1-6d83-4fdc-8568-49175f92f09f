(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7c76c388"],{"8b84":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"关联的模板id",prop:"deviceTempId"}},[a("el-input",{attrs:{placeholder:"请输入关联的模板id",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceTempId,callback:function(t){e.$set(e.queryParams,"deviceTempId",t)},expression:"queryParams.deviceTempId"}})],1),a("el-form-item",{attrs:{label:"从机编号",prop:"slaveAddr"}},[a("el-input",{attrs:{placeholder:"请输入从机编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.slaveAddr,callback:function(t){e.$set(e.queryParams,"slaveAddr",t)},expression:"queryParams.slaveAddr"}})],1),a("el-form-item",{attrs:{label:"${comment}",prop:"slaveIndex"}},[a("el-input",{attrs:{placeholder:"请输入${comment}",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.slaveIndex,callback:function(t){e.$set(e.queryParams,"slaveIndex",t)},expression:"queryParams.slaveIndex"}})],1),a("el-form-item",{attrs:{label:"从机ip地址",prop:"slaveIp"}},[a("el-input",{attrs:{placeholder:"请输入从机ip地址",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.slaveIp,callback:function(t){e.$set(e.queryParams,"slaveIp",t)},expression:"queryParams.slaveIp"}})],1),a("el-form-item",{attrs:{label:"从机名称",prop:"slaveName"}},[a("el-input",{attrs:{placeholder:"请输入从机名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.slaveName,callback:function(t){e.$set(e.queryParams,"slaveName",t)},expression:"queryParams.slaveName"}})],1),a("el-form-item",{attrs:{label:"从机端口",prop:"slavePort"}},[a("el-input",{attrs:{placeholder:"请输入从机端口",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.slavePort,callback:function(t){e.$set(e.queryParams,"slavePort",t)},expression:"queryParams.slavePort"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:add"],expression:"['iot:salve:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:edit"],expression:"['iot:salve:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:remove"],expression:"['iot:salve:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:export"],expression:"['iot:salve:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.salveList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"主键id",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"关联的模板id",align:"center",prop:"deviceTempId"}}),a("el-table-column",{attrs:{label:"从机编号",align:"center",prop:"slaveAddr"}}),a("el-table-column",{attrs:{label:"${comment}",align:"center",prop:"slaveIndex"}}),a("el-table-column",{attrs:{label:"从机ip地址",align:"center",prop:"slaveIp"}}),a("el-table-column",{attrs:{label:"从机名称",align:"center",prop:"slaveName"}}),a("el-table-column",{attrs:{label:"从机端口",align:"center",prop:"slavePort"}}),a("el-table-column",{attrs:{label:"状态 0-启动 1-失效",align:"center",prop:"status"}}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:edit"],expression:"['iot:salve:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:salve:remove"],expression:"['iot:salve:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"关联的模板id",prop:"deviceTempId"}},[a("el-input",{attrs:{placeholder:"请输入关联的模板id"},model:{value:e.form.deviceTempId,callback:function(t){e.$set(e.form,"deviceTempId",t)},expression:"form.deviceTempId"}})],1),a("el-form-item",{attrs:{label:"从机编号",prop:"slaveAddr"}},[a("el-input",{attrs:{placeholder:"请输入从机编号"},model:{value:e.form.slaveAddr,callback:function(t){e.$set(e.form,"slaveAddr",t)},expression:"form.slaveAddr"}})],1),a("el-form-item",{attrs:{label:"${comment}",prop:"slaveIndex"}},[a("el-input",{attrs:{placeholder:"请输入${comment}"},model:{value:e.form.slaveIndex,callback:function(t){e.$set(e.form,"slaveIndex",t)},expression:"form.slaveIndex"}})],1),a("el-form-item",{attrs:{label:"从机ip地址",prop:"slaveIp"}},[a("el-input",{attrs:{placeholder:"请输入从机ip地址"},model:{value:e.form.slaveIp,callback:function(t){e.$set(e.form,"slaveIp",t)},expression:"form.slaveIp"}})],1),a("el-form-item",{attrs:{label:"从机名称",prop:"slaveName"}},[a("el-input",{attrs:{placeholder:"请输入从机名称"},model:{value:e.form.slaveName,callback:function(t){e.$set(e.form,"slaveName",t)},expression:"form.slaveName"}})],1),a("el-form-item",{attrs:{label:"从机端口",prop:"slavePort"}},[a("el-input",{attrs:{placeholder:"请输入从机端口"},model:{value:e.form.slavePort,callback:function(t){e.$set(e.form,"slavePort",t)},expression:"form.slavePort"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),s=(a("d81d"),a("a824")),i={name:"Salve",data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,salveList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deviceTempId:null,slaveAddr:null,slaveIndex:null,slaveIp:null,slaveName:null,slavePort:null,status:null},form:{},rules:{deviceTempId:[{required:!0,message:"关联的模板id不能为空",trigger:"blur"}],slaveAddr:[{required:!0,message:"从机编号不能为空",trigger:"blur"}],status:[{required:!0,message:"状态 0-启动 1-失效不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["d"])(this.queryParams).then((function(t){e.salveList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,deviceTempId:null,slaveAddr:null,slaveIndex:null,slaveIp:null,slaveName:null,slavePort:null,status:0,createTime:null,createBy:null,updateTime:null,updateBy:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加变量模板设备从机"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改变量模板设备从机"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(s["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除变量模板设备从机编号为"'+a+'"的数据项？').then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/salve/export",Object(n["a"])({},this.queryParams),"salve_".concat((new Date).getTime(),".xlsx"))}}},o=i,u=a("2877"),c=Object(u["a"])(o,l,r,!1,null,null,null);t["default"]=c.exports},a824:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return o}));var l=a("b775");function r(e){return Object(l["a"])({url:"/iot/salve/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/iot/salve/"+e,method:"get"})}function s(e){return Object(l["a"])({url:"/iot/salve",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/iot/salve",method:"put",data:e})}function o(e){return Object(l["a"])({url:"/iot/salve/"+e,method:"delete"})}}}]);