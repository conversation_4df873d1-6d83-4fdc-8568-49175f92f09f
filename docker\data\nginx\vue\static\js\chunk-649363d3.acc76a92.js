(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-649363d3"],{"57a9":function(e,t,s){},b06d0:function(e,t,s){"use strict";s("57a9")},dee6:function(e,t,s){e.exports=s.p+"static/img/logo-no-word-blue.a4ad8541.png"},ff2b:function(e,t,s){e.exports=s.p+"static/img/cover.791b154f.png"},ffd2:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"license-wrap"},[e._m(0),s("pre",{staticClass:"introduce-text"},[e._v("FastBee物联网平台授权认证")]),e._m(1),s("div",{staticClass:"box-wrap"},[s("div",{staticClass:"form-box"},[e._m(2),e.visible&&!e.isUpload?s("div",{staticClass:"fixed-message",class:e.messageClass},[e._v("提示: "+e._s(e.message))]):e._e(),e.visible&&e.isUpload?s("div",{staticClass:"fixed-message",class:e.messageClass},[e._v("提示: "+e._s(e.installMessage))]):e._e(),e.visible&&e.isUpload?s("div",{staticClass:"fixed-message",staticStyle:{color:"#000","margin-top":"80px","font-size":"14px"}},[e._v("服务器信息: "+e._s(e.serverList))]):e._e(),s("el-form",{ref:"form",attrs:{model:e.form,"label-width":"0px",rules:e.rules}},[e.isUpload?e._e():s("el-row",{staticStyle:{"margin-top":"100px",display:"flex"}},[s("el-form-item",{attrs:{label:"",prop:"fileName"}},[s("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:"请上传.lic文件",disabled:"",readonly:""},model:{value:e.form.fileName,callback:function(t){e.$set(e.form,"fileName",t)},expression:"form.fileName"}})],1),s("el-form-item",{attrs:{label:""}},[s("el-upload",{attrs:{action:e.uploadImgUrl,"on-change":e.handleChange,"on-success":e.handleSuccess,"on-error":e.handleError,"show-file-list":!1,"before-upload":e.handleBeforeUpload,accept:".lic"}},[s("el-button",{staticStyle:{"margin-left":"10px"},attrs:{size:"small",type:"primary",icon:"el-icon-plus"}},[e._v("上传许可文件")])],1)],1)],1)],1),e.isUpload?e._e():s("el-button",{staticStyle:{"margin-top":"20px"},attrs:{size:"small",type:"primary",plain:!0,disabled:e.isShowNextBtn},on:{click:e.nextStep}},[e._v(" 下一步 "),s("i",{staticClass:"el-icon-right"})]),e.isUpload?s("el-form",{ref:"installForm",staticClass:"form-wrap",attrs:{model:e.installForm,"label-width":"0px",rules:e.installRules}},[s("el-form-item",{attrs:{label:"",prop:"type"}},[s("el-select",{staticStyle:{width:"280px"},attrs:{placeholder:"请选择授权类型",clearable:!0},on:{change:e.handleTypeChange},model:{value:e.installForm.type,callback:function(t){e.$set(e.installForm,"type",t)},expression:"installForm.type"}},e._l(e.options,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{attrs:{label:"",prop:"subject"}},[s("el-input",{staticStyle:{width:"280px"},attrs:{placeholder:"请输入证书名称"},model:{value:e.installForm.subject,callback:function(t){e.$set(e.installForm,"subject",t)},expression:"installForm.subject"}})],1),2===e.installForm.type?s("el-form-item",{attrs:{label:"",prop:"company"}},[s("el-input",{staticStyle:{width:"280px"},attrs:{placeholder:"请输入公司名称"},model:{value:e.installForm.company,callback:function(t){e.$set(e.installForm,"company",t)},expression:"installForm.company"}})],1):e._e(),s("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[s("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.startInstallation}},[e._v("开始安装")]),s("el-button",{attrs:{size:"small",type:"primary",plain:!0},on:{click:e.returnStep}},[e._v("上一步")])],1)],1):e._e()],1),s("el-dialog",{attrs:{title:"提示",visible:e.dialogVisible,"append-to-body":"",width:"300px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[s("div",[e._v("安装成功，三秒后跳转登录页...")]),s("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.redirectToLogin}},[e._v("确认")])],1)])],1)])},l=[function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"logo-wrap"},[i("img",{staticClass:"icon",attrs:{src:s("dee6")}}),i("span",{staticClass:"text"},[e._v("蜂信物联")])])},function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"img-wrap"},[i("img",{staticStyle:{width:"100%",height:"100%"},attrs:{src:s("ff2b")}})])},function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"title-wrap"},[s("div",{staticClass:"name"},[e._v("授权认证")])])}],a=(s("14d9"),s("b0c0"),s("a9e3"),s("b64b"),s("b775"));function r(){return Object(a["a"])({url:"/license/validate",method:"get"})}function n(e){return Object(a["a"])({url:"/license/install",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/license/getServerInfo",method:"get",params:e})}var c={name:"Lincense",data:function(){return{isUpload:!1,form:{fileName:""},fileType:["lic"],isShowNextBtn:!1,visible:!0,message:"认证过期或者未认证，请上传文件进行认证！",serverList:"选择授权类型后将显示服务器信息...",installMessage:"请点击安装",messageClass:"error",file:null,uploadImgUrl:"/prod-api/license/upload",options:[{value:2,label:"公司"},{value:3,label:"个人"},{value:4,label:"试用"}],installForm:{subject:"",type:"",region:"",company:""},dialogVisible:!1,rules:{fileName:[{required:!0,message:"请上传文件",trigger:"blur"}]},installRules:{subject:[{required:!0,message:"请输入证书名称",trigger:"blur"}],type:[{required:!0,message:"请选择授权类型",trigger:"blur"}],region:[{required:!0,message:"请输入地域编码",trigger:"blur"}],company:[{required:!0,message:"请输入公司名称",trigger:"blur"}]}}},mounted:function(){this.getLicense()},methods:{getLicense:function(){var e=this;r().then((function(t){200===t.code&&!0===t.data&&e.$router.push("/login")})).catch((function(){}))},nextStep:function(){var e=this;this.$refs.form.validate((function(t){e.installMessage="请填写授权信息并点击安装！",e.messageClass="success",t&&(e.isUpload=!0,e.reset())}))},handleBeforeUpload:function(e){if(this.fileType){var t=e.name.split("."),s=t[t.length-1],i=this.fileType.indexOf(s)>=0;return i?(this.isShowNextBtn=!1,!0):(this.$modal.msgError("文件格式不正确，请上传.lic文件！"),this.isShowNextBtn=!0,!1)}},getServerMsg:function(e){var t=this,s={type:Number(e)};o(s).then((function(s){if(200===s.code){var i=JSON.parse(s.msg);2===e?t.serverList="ip："+i.ip+" ; 城市："+i.city+" ; 城市编码："+i.cityCode:3!==e&&4!==e||(t.serverList="ip："+i.ip+" ; mac地址："+i.mac)}else t.serverList="暂无服务器信息"})).catch((function(e){}))},validateForm:function(){var e=this.installForm,t=e.subject,s=e.type,i=e.region,l=e.company;if(2===s){if(!t||!i||!l)return!1}else if(3===s&&!t)return!1;return!0},handleTypeChange:function(e){this.installForm.company=2===e?this.installForm.company:"",this.installForm.region=2===e?this.installForm.region:"",this.installForm.subject="",this.getServerMsg(e)},returnStep:function(){this.isUpload=!1,this.message="请上传文件进行认证！",this.messageClass="success",this.serverList="选择授权类型后将显示服务器信息..."},handleChange:function(e,t){this.form.fileName=e.name},handleSuccess:function(e,t){200===e.code?(this.visible=!0,this.message="上传成功，可以点击下一步进行安装了",this.messageClass="success"):(this.visible=!0,this.handleError(e))},handleError:function(e){this.message=e.msg||"上传失败",this.messageClass="error"},startInstallation:function(){var e=this;this.visible=!0,this.$refs.installForm.validate((function(t){t&&e.install()}))},install:function(){var e=this;n(this.installForm).then((function(t){200===t.code?(e.installMessage=t.msg||"安装成功，请登录",e.messageClass="success",e.dialogVisible=!0,setTimeout((function(){e.dialogVisible=!1,e.redirectToLogin()}),3e3)):(e.installMessage=t.msg||"安装失败，请重试",e.messageClass="error")})).catch((function(t){e.installMessage=t.message||"证书安装失败",e.messageClass="error"}))},redirectToLogin:function(){this.$router.push("/login")},reset:function(){this.installForm={type:"",subject:"",region:"",company:""}},handleClose:function(){this.visible=!1}}},m=c,u=(s("b06d0"),s("2877")),p=Object(u["a"])(m,i,l,!1,null,"1c77cadb",null);t["default"]=p.exports}}]);