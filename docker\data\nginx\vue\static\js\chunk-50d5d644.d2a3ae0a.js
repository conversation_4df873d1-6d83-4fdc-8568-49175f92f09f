(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-50d5d644"],{"0bc2":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/runtime/service/invoke",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/runtime/funcLog",method:"get",params:e})}},"5f43":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{attrs:{gutter:120}},[a("el-col",{staticStyle:{"margin-bottom":"50px"},attrs:{xs:24,sm:24,md:24,lg:14,xl:10}},[a("el-descriptions",{staticStyle:{"margin-bottom":"50px"},attrs:{column:1,border:""}},[a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-menu"}),e._v(" 设备模式 ")]),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v(e._s(e.title))])],2),e.hasShrarePerm("ota")?a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("svg-icon",{attrs:{"icon-class":"ota"}}),e._v(" OTA升级 ")],1),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v("Version "+e._s(e.deviceInfo.firmwareVersion))]),a("el-button",{staticStyle:{float:"right"},attrs:{type:"success",size:"mini",disabled:3!=e.deviceInfo.status},on:{click:function(t){return e.getLatestFirmware(e.deviceInfo.deviceId)}}},[e._v("检查更新")])],2):e._e(),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串 "+(t.datatype.unit?"，单位："+t.datatype.unit:""),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"2px 0 0 5px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"integer"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"4px 0 0 10px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{size:"small",placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",size:"small",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2),1==e.deviceInfo.isShadow&&3!=e.deviceInfo.status?a("el-descriptions",{attrs:{column:1,border:"",size:"mini"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("设备离线时状态")])]),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(t){return a("el-button",{key:t.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:""}},[e._v(e._s(t.text))])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{size:"mini","active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2):e._e()],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:10,xl:14}},[e.deviceInfo.chartList.length>0?a("el-row",{staticStyle:{"background-color":"#f5f7fa",padding:"20px 10px 20px 10px","border-radius":"15px","margin-right":"5px"},attrs:{gutter:20}},e._l(e.deviceInfo.chartList,(function(e,t){return a("el-col",{key:t,attrs:{xs:24,sm:12,md:12,lg:24,xl:8}},[a("el-card",{staticStyle:{"border-radius":"30px","margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("div",{ref:"map",refInFor:!0,staticStyle:{height:"230px",width:"185px",margin:"0 auto"}})])],1)})),1):e._e()],1)],1),a("el-dialog",{attrs:{title:"设备固件升级",visible:e.openFirmware,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openFirmware=t}}},[null==e.firmware?a("div",{staticStyle:{"text-align":"center","font-size":"16px"}},[a("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a"}}),e._v(" 已经是最新版本，不需要升级 ")]):e._e(),null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-descriptions",{attrs:{column:1,border:"",size:"large",labelStyle:{width:"150px","font-weight":"bold"}}},[a("template",{slot:"title"},[a("el-link",{attrs:{icon:"el-icon-success",type:"success",underline:!1}},[e._v("可以升级到以下版本")])],1),a("el-descriptions-item",{attrs:{label:"固件名称"}},[e._v(e._s(e.firmware.firmwareName))]),a("el-descriptions-item",{attrs:{label:"所属产品"}},[e._v(e._s(e.firmware.productName))]),a("el-descriptions-item",{attrs:{label:"固件版本"}},[e._v("Version "+e._s(e.firmware.version))]),a("el-descriptions-item",{attrs:{label:"下载地址"}},[a("el-link",{attrs:{href:e.getDownloadUrl(e.firmware.filePath),underline:!1,type:"primary"}},[e._v(e._s(e.getDownloadUrl(e.firmware.filePath)))])],1),a("el-descriptions-item",{attrs:{label:"固件描述"}},[e._v(e._s(e.firmware.remark))])],2):e._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-button",{attrs:{type:"success"},on:{click:e.otaUpgrade}},[e._v("升 级")]):e._e(),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],o=(a("d81d"),a("b0c0"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("8a79"),a("814a")),s=a("0bc2"),l={name:"running-status",props:{device:{type:Object,default:null}},watch:{device:function(e,t){e&&0!=e.deviceId&&(this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.$nextTick((function(){this.MonitorChart()})),this.mqttCallback())}},data:function(){return{title:"设备控制 ",shadowUnEnable:!1,statusColor:{background:"#67C23A",color:"#fff",minWidth:"100px"},firmware:{},openFirmware:!1,loading:!0,deviceInfo:{boolList:[],enumList:[],stringList:[],integerList:[],decimalList:[],arrayList:[],thingsModels:[],chartList:[]},monitorChart:[{chart:{},data:{id:"",name:"",value:""}}],remoteCommand:{}}},created:function(){},methods:{mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),o=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态-运行】主题：",t),console.log("接收到【设备状态-运行】内容：",a),e.deviceInfo.serialNumber==o&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi,e.updateDeviceStatus(e.deviceInfo))),"reply"==n[4]&&e.$modal.notifySuccess(a),t.endsWith("ws/service")&&(console.log("接收到【物模型】主题1：",t),console.log("接收到【物模型】内容：",a),e.deviceInfo.serialNumber==o)))for(var s=0;s<a.length;s++){for(var l=!1,r=0;r<e.deviceInfo.thingsModels.length&&!l;r++){if(e.deviceInfo.thingsModels[r].id==a[s].id){"decimal"==e.deviceInfo.thingsModels[r].datatype.type||"integer"==e.deviceInfo.thingsModels[r].datatype.type?e.deviceInfo.thingsModels[r].shadow=Number(a[s].value):e.deviceInfo.thingsModels[r].shadow=a[s].value,l=!0;break}if("object"==e.deviceInfo.thingsModels[r].datatype.type){for(var d=0;d<e.deviceInfo.thingsModels[r].datatype.params.length;d++)if(e.deviceInfo.thingsModels[r].datatype.params[d].id==a[s].id){e.deviceInfo.thingsModels[r].datatype.params[d].shadow=a[s].value,l=!0;break}}else if("array"==e.deviceInfo.thingsModels[r].datatype.type)if("object"==e.deviceInfo.thingsModels[r].datatype.arrayType)if(0==String(a[s].id).indexOf("array_"))for(var c=0;c<e.deviceInfo.thingsModels[r].datatype.arrayParams.length;c++){for(var u=0;u<e.deviceInfo.thingsModels[r].datatype.arrayParams[c].length;u++)if(e.deviceInfo.thingsModels[r].datatype.arrayParams[c][u].id==a[s].id){e.deviceInfo.thingsModels[r].datatype.arrayParams[c][u].shadow=a[s].value,l=!0;break}if(l)break}else for(var p=0;p<e.deviceInfo.thingsModels[r].datatype.arrayParams.length;p++){for(var m=0;m<e.deviceInfo.thingsModels[r].datatype.arrayParams[p].length;m++){var h=p>9?String(p):"0"+r,v="array_"+h+"_";e.deviceInfo.thingsModels[r].datatype.arrayParams[p][m].id==v+a[s].id&&(e.deviceInfo.thingsModels[r].datatype.arrayParams[p][m].shadow=a[s].value,l=!0)}if(l)break}else for(var f=0;f<e.deviceInfo.thingsModels[r].datatype.arrayModel.length;f++)if(e.deviceInfo.thingsModels[r].datatype.arrayModel[f].id==a[s].id){e.deviceInfo.thingsModels[r].datatype.arrayModel[f].shadow=a[s].value,l=!0;break}}for(var y=0;y<e.deviceInfo.chartList.length;y++){if(0==e.deviceInfo.chartList[y].id.indexOf("array_")){if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var b=0;b<e.monitorChart.length;b++)if(a[s].id==e.monitorChart[b].data.id){var w=[{value:a[s].value,name:e.monitorChart[b].data.name}];e.monitorChart[b].chart.setOption({series:[{data:w}]});break}}}else if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var g=0;g<e.monitorChart.length;g++)if(a[s].id==e.monitorChart[g].data.id){l=!0;var x=[{value:a[s].value,name:e.monitorChart[g].data.name}];e.monitorChart[g].chart.setOption({series:[{data:x}]});break}}if(l)break}}}))},mqttPublish:function(e,t){var a=this,i={};i[t.id]=t.shadow;var n={serialNumber:e.serialNumber,productId:e.productId,remoteCommand:i,identifier:t.id,modelName:t.name,isShadow:3!=e.status,type:t.type};Object(s["b"])(n).then((function(e){200===e.code&&a.$message({type:"success",message:"服务调用成功!"})}))},enumButtonClick:function(e,t,a){t.shadow=a,this.mqttPublish(e,t)},updateDeviceStatus:function(e){3==e.status?(this.statusColor.background="#12d09f",this.title="在线模式"):1==e.isShadow?(this.statusColor.background="#409EFF",this.title="影子模式"):(this.statusColor.background="#909399",this.title="离线模式",this.shadowUnEnable=!0),this.$emit("statusEvent",this.deviceInfo.status)},arrayItemChange:function(e,t){for(var a="",i=0;i<t.datatype.arrayCount;i++)a+=t.datatype.arrayModel[i].shadow+",";a=a.substring(0,a.length-1),t.shadow=a},arrayInputChange:function(e,t){var a=e.split(",");if(a.length!=t.datatype.arrayCount)this.$modal.alertWarning("元素个数不匹配，数组元素个数为"+t.datatype.arrayCount+"个，以英文逗号分隔。");else for(var i=0;i<t.datatype.arrayCount;i++)t.datatype.arrayModel[i].shadow=a[i]},hasShrarePerm:function(e){return 0!=this.deviceInfo.isOwner||-1!=this.deviceInfo.userPerms.indexOf(e)},otaUpgrade:function(){var e=this,t="/"+this.deviceInfo.productId+"/"+this.deviceInfo.serialNumber+"/ota/get",a='{"version":'+this.firmware.version+',"downloadUrl":"'+this.getDownloadUrl(this.firmware.filePath)+'"}';this.$mqttTool.publish(t,a,"设备升级").then((function(t){e.$modal.notifySuccess(t)})).catch((function(t){e.$modal.notifyError(t)})),this.openFirmware=!1},getLatestFirmware:function(e){var t=this;Object(o["d"])(e).then((function(e){t.firmware=e.data,t.openFirmware=!0}))},cancel:function(){this.openFirmware=!1},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},MonitorChart:function(){for(var e=0;e<this.deviceInfo.chartList.length;e++){var t;this.monitorChart[e]={chart:this.$echarts.init(this.$refs.map[e]),data:{id:this.deviceInfo.chartList[e].id,name:this.deviceInfo.chartList[e].name,value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min}},t={tooltip:{formatter:" {b} <br/> {c}"+this.deviceInfo.chartList[e].datatype.unit},series:[{name:this.deviceInfo.chartList[e].datatype.type,type:"gauge",min:this.deviceInfo.chartList[e].datatype.min,max:this.deviceInfo.chartList[e].datatype.max,colorBy:"data",splitNumber:10,radius:"100%",splitLine:{distance:4},axisLabel:{fontSize:10,distance:10},axisTick:{distance:4},axisLine:{lineStyle:{width:8,color:[[.2,"#409EFF"],[.8,"#12d09f"],[1,"#F56C6C"]],opacity:.3}},pointer:{icon:"triangle",length:"60%",width:7},progress:{show:!0,width:8},detail:{valueAnimation:!0,formatter:"{value} "+this.deviceInfo.chartList[e].datatype.unit,offsetCenter:[0,"80%"],fontSize:20},data:[{value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min,name:this.deviceInfo.chartList[e].name}],title:{offsetCenter:[0,"115%"],fontSize:16}}]},t&&this.monitorChart[e].chart.setOption(t)}}}},r=l,d=(a("6db0"),a("2877")),c=Object(d["a"])(r,i,n,!1,null,null,null);t["default"]=c.exports},"6ca8":function(e,t,a){},"6db0":function(e,t,a){"use strict";a("6ca8")},"814a":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return r})),a.d(t,"g",(function(){return d})),a.d(t,"b",(function(){return c}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/getLatest/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function r(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},"8a79":function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),o=a("06cf").f,s=a("50c4"),l=a("577e"),r=a("5a34"),d=a("1d80"),c=a("ab13"),u=a("c430"),p=n("".endsWith),m=n("".slice),h=Math.min,v=c("endsWith"),f=!u&&!v&&!!function(){var e=o(String.prototype,"endsWith");return e&&!e.writable}();i({target:"String",proto:!0,forced:!f&&!v},{endsWith:function(e){var t=l(d(this));r(e);var a=arguments.length>1?arguments[1]:void 0,i=t.length,n=void 0===a?i:h(s(a),i),o=l(e);return p?p(t,o,n):m(t,n-o.length,n)===o}})}}]);