(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20955d"],{a92a:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",[l("el-dialog",e._g(e._b({attrs:{width:"500px","close-on-click-modal":!1,"modal-append-to-body":!1},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[l("el-row",{attrs:{gutter:15}},[l("el-form",{ref:"elForm",attrs:{model:e.formData,rules:e.rules,size:"medium","label-width":"100px"}},[l("el-col",{attrs:{span:24}},[l("el-form-item",{attrs:{label:e.$t("build.index.2090840-12"),prop:"type"}},[l("el-radio-group",{model:{value:e.formData.type,callback:function(t){e.$set(e.formData,"type",t)},expression:"formData.type"}},e._l(e.typeOptions,(function(t,a){return l("el-radio-button",{key:a,attrs:{label:t.value,disabled:t.disabled}},[e._v(" "+e._s(t.label)+" ")])})),1)],1),e.showFileName?l("el-form-item",{attrs:{label:e.$t("build.index.2090840-13"),prop:"fileName"}},[l("el-input",{attrs:{placeholder:e.$t("build.index.2090840-14"),clearable:""},model:{value:e.formData.fileName,callback:function(t){e.$set(e.formData,"fileName",t)},expression:"formData.fileName"}})],1):e._e()],1)],1)],1),l("div",{attrs:{slot:"footer"},slot:"footer"},[l("el-button",{on:{click:e.close}},[e._v(" "+e._s(e.$t("cancel"))+" ")]),l("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v(" "+e._s(e.$t("confirm"))+" ")])],1)],1)],1)},i=[],o=l("5530"),n={inheritAttrs:!1,props:["showFileName"],data:function(){return{formData:{fileName:void 0,type:"file"},rules:{fileName:[{required:!0,message:this.$t("build.index.2090840-14"),trigger:"blur"}],type:[{required:!0,message:this.$t("build.index.2090840-15"),trigger:"change"}]},typeOptions:[{label:this.$t("build.index.2090840-16"),value:"file"},{label:this.$t("build.index.2090840-17"),value:"dialog"}]}},computed:{},watch:{},mounted:function(){},methods:{onOpen:function(){this.showFileName&&(this.formData.fileName="".concat(+new Date,".vue"))},onClose:function(){},close:function(e){this.$emit("update:visible",!1)},handleConfirm:function(){var e=this;this.$refs.elForm.validate((function(t){t&&(e.$emit("confirm",Object(o["a"])({},e.formData)),e.close())}))}}},r=n,s=l("2877"),d=Object(s["a"])(r,a,i,!1,null,null,null);t["default"]=d.exports}}]);