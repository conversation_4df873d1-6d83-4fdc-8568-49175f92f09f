(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-51799542"],{8586:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-form",{ref:"genInfoForm",staticClass:"gen-gen-info-form",attrs:{model:e.info,rules:e.rules,"label-width":"150px"}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"tplCategory"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(e._s(e.$t("gen.genInfoForm.432422-0")))]),n("el-select",{on:{change:e.tplSelectChange},model:{value:e.info.tplCategory,callback:function(t){e.$set(e.info,"tplCategory",t)},expression:"info.tplCategory"}},[n("el-option",{attrs:{label:e.$t("gen.genInfoForm.432422-1"),value:"crud"}}),n("el-option",{attrs:{label:e.$t("gen.genInfoForm.432422-2"),value:"tree"}}),n("el-option",{attrs:{label:e.$t("gen.genInfoForm.432422-3"),value:"sub"}})],1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"packageName"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-4"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-5"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-input",{model:{value:e.info.packageName,callback:function(t){e.$set(e.info,"packageName",t)},expression:"info.packageName"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"moduleName"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-6"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-7"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-input",{model:{value:e.info.moduleName,callback:function(t){e.$set(e.info,"moduleName",t)},expression:"info.moduleName"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"businessName"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-8"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-9"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-input",{model:{value:e.info.businessName,callback:function(t){e.$set(e.info,"businessName",t)},expression:"info.businessName"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"functionName"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-10"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-11"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-input",{model:{value:e.info.functionName,callback:function(t){e.$set(e.info,"functionName",t)},expression:"info.functionName"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-12"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-13"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("treeselect",{attrs:{"append-to-body":!0,options:e.menus,normalizer:e.normalizer,"show-count":!0,placeholder:e.$t("gen.genInfoForm.432422-14")},model:{value:e.info.parentMenuId,callback:function(t){e.$set(e.info,"parentMenuId",t)},expression:"info.parentMenuId"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"genType"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-15"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-16"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-radio",{attrs:{label:"0"},model:{value:e.info.genType,callback:function(t){e.$set(e.info,"genType",t)},expression:"info.genType"}},[e._v(e._s(e.$t("gen.genInfoForm.432422-17")))]),n("el-radio",{attrs:{label:"1"},model:{value:e.info.genType,callback:function(t){e.$set(e.info,"genType",t)},expression:"info.genType"}},[e._v(e._s(e.$t("gen.genInfoForm.432422-18")))])],1)],1),"1"==e.info.genType?n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{prop:"genPath"}},[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-19"))+" "),n("el-tooltip",{attrs:{"：content":"$t('gen.genInfoForm.432422-20')",placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-input",{model:{value:e.info.genPath,callback:function(t){e.$set(e.info,"genPath",t)},expression:"info.genPath"}},[n("el-dropdown",{attrs:{slot:"append"},slot:"append"},[n("el-button",{attrs:{type:"primary"}},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-21"))+" "),n("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{nativeOn:{click:function(t){e.info.genPath="/"}}},[e._v(e._s(e.$t("gen.genInfoForm.432422-22")))])],1)],1)],1)],1)],1):e._e()],1),n("el-row",{directives:[{name:"show",rawName:"v-show",value:"tree"==e.info.tplCategory,expression:"info.tplCategory == 'tree'"}]},[n("h4",{staticClass:"form-header"},[e._v(e._s(e.$t("gen.genInfoForm.432422-23")))]),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-24"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-25"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.info.treeCode,callback:function(t){e.$set(e.info,"treeCode",t)},expression:"info.treeCode"}},e._l(e.info.columns,(function(e,t){return n("el-option",{key:t,attrs:{label:e.columnName+"："+e.columnComment,value:e.columnName}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-26"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-27"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.info.treeParentCode,callback:function(t){e.$set(e.info,"treeParentCode",t)},expression:"info.treeParentCode"}},e._l(e.info.columns,(function(e,t){return n("el-option",{key:t,attrs:{label:e.columnName+"："+e.columnComment,value:e.columnName}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-28"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-29"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.info.treeName,callback:function(t){e.$set(e.info,"treeName",t)},expression:"info.treeName"}},e._l(e.info.columns,(function(e,t){return n("el-option",{key:t,attrs:{label:e.columnName+"："+e.columnComment,value:e.columnName}})})),1)],1)],1)],1),n("el-row",{directives:[{name:"show",rawName:"v-show",value:"sub"==e.info.tplCategory,expression:"info.tplCategory == 'sub'"}]},[n("h4",{staticClass:"form-header"},[e._v(e._s(e.$t("gen.genInfoForm.432422-30")))]),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-31"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-32"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},on:{change:e.subSelectChange},model:{value:e.info.subTableName,callback:function(t){e.$set(e.info,"subTableName",t)},expression:"info.subTableName"}},e._l(e.tables,(function(e,t){return n("el-option",{key:t,attrs:{label:e.tableName+"："+e.tableComment,value:e.tableName}})})),1)],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[e._v(" "+e._s(e.$t("gen.genInfoForm.432422-33"))+" "),n("el-tooltip",{attrs:{content:e.$t("gen.genInfoForm.432422-34"),placement:"top"}},[n("i",{staticClass:"el-icon-question"})])],1),n("el-select",{attrs:{placeholder:e.$t("pleaseSelect")},model:{value:e.info.subTableFkName,callback:function(t){e.$set(e.info,"subTableFkName",t)},expression:"info.subTableFkName"}},e._l(e.subColumns,(function(e,t){return n("el-option",{key:t,attrs:{label:e.columnName+"："+e.columnComment,value:e.columnName}})})),1)],1)],1)],1)],1)},l=[],a=n("ca17"),s=n.n(a),r=(n("542c"),{components:{Treeselect:s.a},props:{info:{type:Object,default:null},tables:{type:Array,default:null},menus:{type:Array,default:[]}},data:function(){return{subColumns:[],rules:{tplCategory:[{required:!0,message:this.$t("gen.genInfoForm.432422-35"),trigger:"blur"}],packageName:[{required:!0,message:this.$t("gen.genInfoForm.432422-36"),trigger:"blur"}],moduleName:[{required:!0,message:this.$t("gen.genInfoForm.432422-37"),trigger:"blur"}],businessName:[{required:!0,message:this.$t("gen.genInfoForm.432422-38"),trigger:"blur"}],functionName:[{required:!0,message:this.$t("gen.genInfoForm.432422-39"),trigger:"blur"}]}}},created:function(){},watch:{"info.subTableName":function(e){this.setSubTableColumns(e)}},methods:{normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.menuId,label:e.menuName,children:e.children}},subSelectChange:function(e){this.info.subTableFkName=""},tplSelectChange:function(e){"sub"!==e&&(this.info.subTableName="",this.info.subTableFkName="")},setSubTableColumns:function(e){for(var t in this.tables){var n=this.tables[t].tableName;if(e===n){this.subColumns=this.tables[t].columns;break}}}}}),i=r,c=(n("b74c"),n("2877")),m=Object(c["a"])(i,o,l,!1,null,"8ed910c0",null);t["default"]=m.exports},b74c:function(e,t,n){"use strict";n("e901")},e901:function(e,t,n){}}]);