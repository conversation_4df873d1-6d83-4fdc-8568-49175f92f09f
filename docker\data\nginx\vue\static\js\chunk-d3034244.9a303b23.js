(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d3034244"],{"203d":function(e,a,t){"use strict";t("4d1e")},2065:function(e,a,t){"use strict";t.d(a,"d",(function(){return s})),t.d(a,"a",(function(){return r})),t.d(a,"b",(function(){return n})),t.d(a,"c",(function(){return l}));var i=t("b775");function s(e){return Object(i["a"])({url:"/iot/firmware/task/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/firmware/task",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/task/deviceList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/firmware/task/deviceStatistic",method:"get",params:e})}},"4d1e":function(e,a,t){},be4a:function(e,a,t){"use strict";t.r(a);var i=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"firmware-task"},[t("el-card",[t("el-descriptions",{attrs:{title:"固件信息"}},e._l(e.firmwareInfo,(function(a,i){return t("el-descriptions-item",{key:i,attrs:{label:a.name,"label-class-name":"feint"}},[e._v(e._s(a.value))])})),1)],1),t("el-card",[t("div",{staticClass:"firmwareDeviceHeader"},[t("h1",[e._v("固件升级设备统计")]),t("i",{staticClass:"el-icon-refresh icon",on:{click:function(a){return e.updateFirmwareDevice()}}})]),t("div",{staticClass:"firmwareDevice"},e._l(e.firmwareDevice,(function(a,i){return t("div",{key:i,staticClass:"box"},[t("span",{staticClass:"title"},[e._v(e._s(a.title))]),t("span",{staticClass:"num"},[e._v(e._s(a.num))])])})),0)]),t("el-card",[t("div",{staticClass:"firmwareDeviceHeader"},[t("h1",[e._v("任务管理")])]),t("div",{staticClass:"task"},[t("div",{staticClass:"taskHeader"},[t("div",{staticClass:"taskTitle"},[t("span",{class:{active:0===e.changeIndex,noRight:1===e.changeIndex},on:{click:function(a){return e.handleTask(0)}}},[e._v("任务明细")]),t("span",{class:{active:1===e.changeIndex,noLeft:0===e.changeIndex},on:{click:function(a){return e.handleTask(1)}}},[e._v("设备明细")])]),t("div",{staticClass:"taskInput"},[t("el-input",{directives:[{name:"show",rawName:"v-show",value:0===e.changeIndex,expression:"changeIndex === 0"}],staticClass:"searchInput",attrs:{placeholder:"请输入任务ID",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.taskQuery()}},model:{value:e.taskId,callback:function(a){e.taskId=a},expression:"taskId"}}),t("el-input",{directives:[{name:"show",rawName:"v-show",value:1===e.changeIndex,expression:"changeIndex === 1"}],staticClass:"searchInput input2",attrs:{placeholder:"请输入任务ID",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.deviceQuery(a)}},model:{value:e.deviceId,callback:function(a){e.deviceId=a},expression:"deviceId"}}),t("el-input",{directives:[{name:"show",rawName:"v-show",value:1===e.changeIndex,expression:"changeIndex === 1"}],staticClass:"searchInput input2",attrs:{placeholder:"请输入设备序列号",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.deviceQuery(a)}},model:{value:e.serialNumber,callback:function(a){e.serialNumber=a},expression:"serialNumber"}}),t("el-input",{directives:[{name:"show",rawName:"v-show",value:1===e.changeIndex,expression:"changeIndex === 1"}],staticClass:"searchInput",attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.deviceQuery(a)}},model:{value:e.deviceName,callback:function(a){e.deviceName=a},expression:"deviceName"}})],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:0===e.changeIndex,expression:"changeIndex === 0"}],staticClass:"taskBody"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareTaskList}},[t("el-table-column",{attrs:{label:"任务ID",align:"center",prop:"id",width:"80"}}),t("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"taskName"}}),t("el-table-column",{attrs:{label:"任务类型",align:"center",prop:"upgradeType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[1==a.row.upgradeType?t("el-tag",{attrs:{type:"warning"}},[e._v("版本升级")]):t("el-tag",[e._v("指定设备")])]}}])}),t("el-table-column",{attrs:{label:"设备数量",align:"center",prop:"deviceAmount",width:"80"}}),t("el-table-column",{attrs:{label:"预定时间",align:"center",prop:"bookTime",width:"160"}}),t("el-table-column",{attrs:{label:"任务描述",align:"center",prop:"taskDesc"}}),t("el-table-column",{attrs:{label:"添加时间",align:"center",prop:"createTime",width:"160"}}),t("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.taskDetailClick(a.row)}}},[e._v("查看详情")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.taskTotal>0,expression:"taskTotal>0"}],attrs:{total:e.taskTotal,page:e.taskParams.pageNum,limit:e.taskParams.pageSize},on:{"update:page":function(a){return e.$set(e.taskParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.taskParams,"pageSize",a)},pagination:e.getTaskList}})],1),t("div",{directives:[{name:"show",rawName:"v-show",value:1===e.changeIndex,expression:"changeIndex === 1"}],staticClass:"deviceBody"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareDeviceList}},[t("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),t("el-table-column",{attrs:{label:"序列号",align:"center",prop:"serialNumber"}}),t("el-table-column",{attrs:{label:"任务ID",align:"center",prop:"taskId",width:"80"}}),t("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"taskName"}}),t("el-table-column",{attrs:{label:"当前版本号",align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("Version "+e._s(a.row.version))])]}}])}),t("el-table-column",{attrs:{label:"消息ID",align:"center",prop:"messageId",width:"120"}}),t("el-table-column",{attrs:{label:"升级状态",align:"center",prop:"upgradeStatus",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[0==a.row.upgradeStatus?t("el-tag",{attrs:{type:"info"}},[e._v("等待升级")]):1==a.row.upgradeStatus?t("el-tag",{attrs:{type:""}},[e._v("已发送设备")]):2==a.row.upgradeStatus?t("el-tag",{attrs:{type:"warning"}},[e._v("设备收到")]):3==a.row.upgradeStatus?t("el-tag",{attrs:{type:"success"}},[e._v("升级成功")]):4==a.row.upgradeStatus?t("el-tag",{attrs:{type:"danger"}},[e._v("升级失败")]):5==a.row.upgradeStatus?t("el-tag",{attrs:{type:"info"}},[e._v("停止")]):t("el-tag",{attrs:{type:"info"}},[e._v("未知")])]}}])}),t("el-table-column",{attrs:{label:"状态详情",align:"center",prop:"detailMsg",width:"120"}}),t("el-table-column",{attrs:{label:"状态更新时间",align:"center",prop:"updateTime",width:"160"}})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.deviceTotal>0,expression:"deviceTotal>0"}],attrs:{total:e.deviceTotal,page:e.deviceParams.pageNum,limit:e.deviceParams.pageSize},on:{"update:page":function(a){return e.$set(e.deviceParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.deviceParams,"pageSize",a)},pagination:e.getDeviceList}})],1)])]),t("el-dialog",{attrs:{title:"任务详情",width:"80%",visible:e.taskDialogVisible},on:{"update:visible":function(a){e.taskDialogVisible=a}}},[t("div",{staticClass:"dialogBox"},[t("div",[t("el-card",[t("el-descriptions",{attrs:{title:"任务信息",column:1}},e._l(e.taskDialogData,(function(a,i){return t("el-descriptions-item",{key:i,attrs:{label:a.name,"label-class-name":"feint"}},[e._v(e._s(a.value))])})),1)],1)],1),t("div",[t("el-card",[t("el-descriptions",{attrs:{title:"任务统计"}},[t("template",{slot:"extra"},[t("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-refresh"},on:{click:e.chartRefresh}})],1)],2),t("div",{ref:"taskChart",staticClass:"chart"})],1)],1)]),t("el-card",[t("div",{staticClass:"firmwareDeviceHeader"},[t("h1",[e._v("设备详情")])]),t("div",{staticClass:"deviceDialogSearch"},[t("div",{staticClass:"left",attrs:{id:"left"}},[t("span",{class:{active:"all"===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick("all")}}},[e._v("全部设备（"+e._s(e.deviceDialogSearchStatus[0])+"）")]),t("span",{class:{active:3===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick(3)}}},[e._v("升级成功（"+e._s(e.deviceDialogSearchStatus[1])+"）")]),t("span",{class:{active:0===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick(0)}}},[e._v("待推送（"+e._s(e.deviceDialogSearchStatus[2])+"）")]),t("span",{class:{active:1===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick(1)}}},[e._v("升级中（"+e._s(e.deviceDialogSearchStatus[3])+"）")]),t("span",{class:{active:4===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick(4)}}},[e._v("升级失败（"+e._s(e.deviceDialogSearchStatus[5])+"）")]),t("span",{class:{active:5===e.deviceStatus},on:{click:function(a){return e.deviceStatusClick(5)}}},[e._v("停止（"+e._s(e.deviceDialogSearchStatus[6])+"）")])]),t("div",{staticClass:"right"},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.deviceInfoQuery}},[e._v("刷新")]),t("el-input",{staticClass:"input3",attrs:{placeholder:"请输入设备序列号",clearable:"",size:"small"},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.deviceInfoQuery(a)}},model:{value:e.deviceSerialNumber,callback:function(a){e.deviceSerialNumber=a},expression:"deviceSerialNumber"}})],1)]),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceDialogList}},[t("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),t("el-table-column",{attrs:{label:"序列号",align:"center",prop:"serialNumber"}}),t("el-table-column",{attrs:{label:"任务ID",align:"center",prop:"taskId",width:"80"}}),t("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"taskName"}}),t("el-table-column",{attrs:{label:"当前版本号",align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("span",[e._v("Version "+e._s(a.row.version))])]}}])}),t("el-table-column",{attrs:{label:"消息ID",align:"center",prop:"messageId",width:"120"}}),t("el-table-column",{attrs:{label:"升级状态",align:"center",prop:"upgradeStatus",width:"120"},scopedSlots:e._u([{key:"default",fn:function(a){return[0==a.row.upgradeStatus?t("el-tag",{attrs:{type:"info"}},[e._v("等待升级")]):1==a.row.upgradeStatus?t("el-tag",{attrs:{type:""}},[e._v("已发送设备")]):2==a.row.upgradeStatus?t("el-tag",{attrs:{type:"warning"}},[e._v("设备收到")]):3==a.row.upgradeStatus?t("el-tag",{attrs:{type:"success"}},[e._v("升级成功")]):4==a.row.upgradeStatus?t("el-tag",{attrs:{type:"danger"}},[e._v("升级失败")]):5==a.row.upgradeStatus?t("el-tag",{attrs:{type:"info"}},[e._v("停止")]):t("el-tag",{attrs:{type:"info"}},[e._v("未知")])]}}])}),t("el-table-column",{attrs:{label:"状态详情",align:"center",prop:"detailMsg",width:"120"}}),t("el-table-column",{attrs:{label:"状态更新时间",align:"center",prop:"updateTime",width:"160"}})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.deviceInfoTotal>0,expression:"deviceInfoTotal>0"}],attrs:{total:e.deviceInfoTotal,page:e.deviceInfoParams.pageNum,limit:e.deviceInfoParams.pageSize},on:{"update:page":function(a){return e.$set(e.deviceInfoParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.deviceInfoParams,"pageSize",a)},pagination:e.getDeviceInfoList}})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.handleDialogCancel}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogCancel}},[e._v("确 定")])],1)],1)],1)},s=[],r=(t("d81d"),t("14d9"),t("2065")),n=t("313e"),l={name:"firmware-task",data:function(){return{firmwareId:"",firmwareInfo:[{name:"固件名称",value:""},{name:"所属产品",value:""},{name:"是否最新",value:""},{name:"固件版本号",value:""},{name:"添加时间",value:""},{name:"固件描述",value:""}],firmwareDevice:[{title:"固件升级设备总数",num:0},{title:"升级成功",num:0},{title:"正在升级",num:0},{title:"升级失败",num:0}],changeIndex:0,loading:!1,taskParams:{pageNum:1,pageSize:10,firmwareId:"",id:""},taskTotal:0,firmwareTaskList:[],taskId:"",deviceParams:{pageNum:1,pageSize:10,firmwareId:"",taskId:"",serialNumber:"",deviceName:""},deviceTotal:0,firmwareDeviceList:[],deviceId:"",deviceName:"",serialNumber:"",taskDialogVisible:!1,taskDialogData:[{name:"任务ID",value:""},{name:"任务名称",value:""},{name:"任务类型",value:""},{name:"设备数量",value:""},{name:"预定时间",value:""},{name:"任务描述",value:""},{name:"添加时间",value:""}],myChart:null,option:null,chartData:[],chartParam:{},deviceDialogList:[],deviceInfoParams:{pageNum:1,pageSize:10,firmwareId:"",taskId:"",upgradeStatus:"",serialNumber:""},deviceSerialNumber:"",deviceInfoTotal:0,deviceDialogSearchStatus:[0,0,0,0,0,0],deviceStatus:"all"}},created:function(){var e=JSON.parse(sessionStorage.getItem("firmwareTaskInfo"));this.firmwareId=e.firmwareId,this.taskParams.firmwareId=e.firmwareId,this.deviceParams.firmwareId=e.firmwareId,this.deviceInfoParams.firmwareId=e.firmwareId,this.firmwareInfo[0].value=e.firmwareName,this.firmwareInfo[1].value=e.productName,this.firmwareInfo[2].value=1==e.isLatest?"最新":"默认",this.firmwareInfo[3].value="Version "+e.version,this.firmwareInfo[4].value=e.createTime,this.firmwareInfo[5].value=e.remark,this.getDeviceStatistic(),this.getTaskList(),this.getDeviceList()},watch:{chartData:{immediate:!0,deep:!0,handler:function(e,a){var t=this;e.length>0&&this.$nextTick((function(){t.myChart=n["init"](t.$refs.taskChart),t.option={grid:{top:0,bottom:0,containLabel:!0},tooltip:{trigger:"item",formatter:"{b}:{c}"},series:[{name:"任务统计",type:"pie",radius:["45%","70%"],label:{show:!0,formatter:"{b}:{c}\n占比:{d}%"},data:e}]},t.myChart.setOption(t.option)}))}}},methods:{getDeviceStatistic:function(){var e=this;Object(r["c"])({firmwareId:this.firmwareId}).then((function(a){if(200==a.code){var t=a.data,i=0,s=0,r=0,n=0;t.map((function(e){n+=e.deviceCount,0==e.upgradeStatus||1==e.upgradeStatus||2==e.upgradeStatus?i+=e.deviceCount:4==e.upgradeStatus||5==e.upgradeStatus?r+=e.deviceCount:s+=e.deviceCount})),e.firmwareDevice[0].num=n,e.firmwareDevice[1].num=s,e.firmwareDevice[2].num=i,e.firmwareDevice[3].num=r}}))},updateFirmwareDevice:function(){this.getDeviceStatistic()},getTaskList:function(){var e=this;Object(r["d"])(this.taskParams).then((function(a){e.firmwareTaskList=a.rows,e.taskTotal=a.total}))},getDeviceList:function(){var e=this;Object(r["b"])(this.deviceParams).then((function(a){e.firmwareDeviceList=a.rows,e.deviceTotal=a.total}))},taskQuery:function(){this.taskParams.id=this.taskId,this.getTaskList()},deviceQuery:function(){this.deviceParams.taskId=this.deviceId,this.deviceParams.deviceName=this.deviceName,this.deviceParams.serialNumber=this.serialNumber,this.getDeviceList()},taskDetailClick:function(e){this.taskDialogData[0].value=e.id,this.taskDialogData[1].value=e.taskName,this.taskDialogData[2].value=1==e.upgradeType?"版本升级":"指定设备",this.taskDialogData[3].value=e.deviceAmount,this.taskDialogData[4].value=e.bookTime,this.taskDialogData[5].value=e.taskDesc,this.taskDialogData[6].value=e.createTime,this.deviceDialogSearchStatus[0]=e.deviceAmount,this.taskDialogVisible=!0,this.chartParam={taskId:e.id,firmwareId:e.firmwareId},this.deviceInfoParams.taskId=e.id,this.getChartData(),this.getDeviceInfoList()},getChartData:function(){var e=this;Object(r["c"])(this.chartParam).then((function(a){var t=a.data,i=[],s=[e.deviceDialogSearchStatus[0],0,0,0,0,0];t.map((function(e){switch(e.upgradeStatus){case 0:i.push({name:"待推送",value:e.deviceCount}),s[2]=e.deviceCount;break;case 1:i.push({name:"升级中",value:e.deviceCount}),s[3]=e.deviceCount;break;case 3:i.push({name:"升级成功",value:e.deviceCount}),s[1]=e.deviceCount;break;case 4:i.push({name:"升级失败",value:e.deviceCount}),s[5]=e.deviceCount;break;case 5:i.push({name:"停止",value:e.deviceCount}),s[6]=e.deviceCount;break}})),e.chartData=i,e.deviceDialogSearchStatus=s}))},chartRefresh:function(){this.getChartData()},getDeviceInfoList:function(){var e=this;Object(r["b"])(this.deviceInfoParams).then((function(a){e.deviceDialogList=a.rows,e.deviceInfoTotal=a.total}))},deviceInfoQuery:function(){this.deviceInfoParams.serialNumber=this.deviceSerialNumber,this.getDeviceInfoList()},deviceStatusClick:function(e){this.deviceStatus=e,this.deviceInfoParams.upgradeStatus="all"===e?"":e,this.getDeviceInfoList()},handleTask:function(e){this.changeIndex=e},handleDialogCancel:function(){this.taskDialogVisible=!1,this.deviceStatus="all",this.deviceSerialNumber="",this.deviceInfoParams.upgradeStatus="",this.deviceInfoParams.serialNumber=""}}},c=l,o=(t("203d"),t("2877")),u=Object(o["a"])(c,i,s,!1,null,"50ad873e",null);a["default"]=u.exports}}]);