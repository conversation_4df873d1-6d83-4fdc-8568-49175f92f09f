(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9c90ac72","chunk-a990e6d8","chunk-06d82310"],{"01ca":function(t,e,o){"use strict";o.d(e,"h",(function(){return a})),o.d(e,"d",(function(){return s})),o.d(e,"i",(function(){return l})),o.d(e,"a",(function(){return r})),o.d(e,"g",(function(){return n})),o.d(e,"k",(function(){return d})),o.d(e,"c",(function(){return u})),o.d(e,"b",(function(){return c})),o.d(e,"f",(function(){return p})),o.d(e,"e",(function(){return m})),o.d(e,"j",(function(){return f}));var i=o("b775");function a(t){return Object(i["a"])({url:"/iot/model/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/iot/model/"+t,method:"get"})}function l(t){return Object(i["a"])({url:"/iot/model/permList/"+t,method:"get"})}function r(t){return Object(i["a"])({url:"/iot/model",method:"post",data:t})}function n(t){return Object(i["a"])({url:"/iot/model/import",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/iot/model",method:"put",data:t})}function u(t){return Object(i["a"])({url:"/iot/model/"+t,method:"delete"})}function c(t){return Object(i["a"])({url:"/iot/model/cache/"+t,method:"get"})}function p(t){return Object(i["a"])({url:"/iot/model/listModbus",method:"get",params:t})}function m(t){return Object(i["a"])({url:"/iot/model/write",method:"get",params:t})}function f(t){return Object(i["a"])({url:"/iot/model/refresh?productId="+t,method:"post"})}},"41d7":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{title:t.upload.title,visible:t.upload.importDeviceDialog,width:"500px","append-to-body":""},on:{"update:visible":function(e){return t.$set(t.upload,"importDeviceDialog",e)}}},[o("el-form",{ref:"importForm",attrs:{"label-position":"top",model:t.importForm,rules:t.importRules}},[o("el-form-item",{attrs:{label:t.$t("uploadFile"),prop:"fileList"}},[o("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:t.upload.headers,action:t.upload.url+"?productId="+t.productId+"&type="+("isSelectData"==t.justiceSelect?2:1),disabled:t.upload.isUploading,"on-progress":t.handleFileUploadProgress,"on-error":t.handleError,"on-success":t.handleFileSuccess,"auto-upload":!1,"on-change":t.handleChange,"on-remove":t.handleRemove,drag:""},model:{value:t.importForm.fileList,callback:function(e){t.$set(t.importForm,"fileList",e)},expression:"importForm.fileList"}},[o("i",{staticClass:"el-icon-upload"}),o("div",{staticClass:"el-upload__text"},[t._v(" "+t._s(t.$t("dragFileTips"))+" "),o("em",[t._v(t._s(t.$t("clickFileTips")))])]),o("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[o("div",{staticStyle:{"margin-top":"10px"}},[o("span",[t._v(t._s(t.$t("device.batch-import-dialog.850870-5")))])])])]),o("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:t.importTemplate}},[o("i",{staticClass:"el-icon-download"}),t._v(" "+t._s(t.$t("device.batch-import-dialog.850870-6"))+" ")])],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitFileForm}},[t._v(t._s(t.$t("confirm")))]),o("el-button",{on:{click:function(e){t.upload.importDeviceDialog=!1}}},[t._v(t._s(t.$t("cancel")))])],1)],1)},a=[],s=(o("99af"),o("a9e3"),o("5f87")),l=o("7aa2"),r={name:"batchImport",props:{productId:{type:Number,default:0},justiceSelect:{type:String,default:"isSelectData"}},data:function(){return{type:1,importForm:{productId:null,fileList:[]},file:null,configList:[],upload:{importDeviceDialog:!1,title:this.$t("batchImport"),isUploading:!1,headers:{Authorization:"Bearer "+Object(s["a"])()},url:"/prod-api/modbus/config/importModbus"},importRules:{fileList:[{required:!0,message:this.$t("plzUploadFile"),trigger:"change"}]},loading:!1}},methods:{importTemplate:function(){var t="isSelectData"==this.justiceSelect?2:1,e="isSelectData"==this.justiceSelect?this.$t("product.components.batchImportModbus.745343-1"):this.$t("product.components.batchImportModbus.745343-0");this.download("/modbus/config/modbusTemplate?type="+t,{},"".concat(e,"_").concat((new Date).getTime(),".xlsx"))},handleChange:function(t,e){this.importForm.fileList=e,this.importForm.fileList&&this.$refs.importForm.clearValidate("fileList")},handleRemove:function(t,e){this.importForm.fileList=e,this.$refs.importForm.validateField("fileList")},handleFileUploadProgress:function(t,e,o){this.upload.isUploading=!0},handleError:function(t,e,o){this.upload.importDeviceDialog=!1,this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0})},handleFileSuccess:function(t,e,o){this.upload.importDeviceDialog=!1,this.upload.isUploading=!1,this.loading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0}),this.$parent.getIOList(),this.$parent.getDataList()},submitFileForm:function(){var t=this;this.$refs["importForm"].validate((function(e){e&&(t.upload.isUploading=!0,t.$refs.upload.submit(),t.getIOList(),setTimeout((function(){t.$emit("data-imported",t.configList)}),500))}))},getIOList:function(){var t=this,e={pageNum:1,pageSize:10,type:1,productId:this.productId};Object(l["b"])(e).then((function(e){t.configList=e.rows,t.total=e.total}))}}},n=r,d=o("2877"),u=Object(d["a"])(n,i,a,!1,null,null,null);e["default"]=u.exports},"4dd3":function(t,e,o){},"5e6c":function(t,e,o){"use strict";o.d(e,"g",(function(){return a})),o.d(e,"e",(function(){return s})),o.d(e,"a",(function(){return l})),o.d(e,"i",(function(){return r})),o.d(e,"c",(function(){return n})),o.d(e,"h",(function(){return d})),o.d(e,"b",(function(){return u})),o.d(e,"j",(function(){return c})),o.d(e,"d",(function(){return p})),o.d(e,"f",(function(){return m}));var i=o("b775");function a(t){return Object(i["a"])({url:"/modbus/job/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/modbus/job/"+t,method:"get"})}function l(t){return Object(i["a"])({url:"/modbus/job",method:"post",data:t})}function r(t,e){var o={taskId:t,status:e};return Object(i["a"])({url:"/modbus/job",method:"put",data:o})}function n(t){return Object(i["a"])({url:"/modbus/job/del",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/productModbus/job/list",method:"get",params:t})}function u(t){return Object(i["a"])({url:"/productModbus/job",method:"post",data:t})}function c(t,e){var o={taskId:t,status:e};return Object(i["a"])({url:"/productModbus/job",method:"put",data:o})}function p(t){return Object(i["a"])({url:"/productModbus/job/"+t,method:"delete"})}function m(t,e){return Object(i["a"])({url:"/productModbus/job/getSlaveId?productId="+t+"&deviceId="+e,method:"get"})}},"7aa2":function(t,e,o){"use strict";o.d(e,"b",(function(){return a})),o.d(e,"a",(function(){return s}));var i=o("b775");function a(t){return Object(i["a"])({url:"/modbus/config/list",method:"get",params:t})}function s(t){return Object(i["a"])({url:"/modbus/config/addBatch",method:"post",data:t})}},9718:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"modbus-wrap"},[o("div",{staticClass:"card-wrap"},[o("div",{staticClass:"title-wrap"},[o("div",{staticClass:"title"},[o("div",[t._v(t._s(t.$t("product.product-modbus.562372-0")))]),o("el-tooltip",{attrs:{effect:"dark",content:t.$t("product.product-modbus.562372-1"),placement:"top"}},[o("i",{staticClass:"el-icon-question",staticStyle:{"margin-left":"6px"}})])],1),o("div",{staticClass:"btn-group"},[t.enableSetSlave||1!=t.productInfo.status?t._e():o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-edit",size:"small"},on:{click:t.setSlave}},[t._v(t._s(t.$t("product.product-modbus.562372-2")))]),t.enableSetSlave?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:edit"],expression:"['modbus:config:edit']"}],attrs:{type:"primary",plain:"",icon:"el-icon-close",size:"small"},on:{click:t.saveSlave}},[t._v(t._s(t.$t("product.product-modbus.562372-3")))]):t._e(),t.enableSetSlave?o("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-edit",size:"small"},on:{click:t.cancelSlave}},[t._v(t._s(t.$t("product.product-modbus.562372-4")))]):t._e()],1)]),o("el-form",{ref:"form",staticClass:"form-wrap",attrs:{model:t.form,"label-width":"180px",rules:t.rules}},[o("el-row",[o("el-col",{attrs:{span:10}},[o("el-form-item",{attrs:{label:""}},[o("el-tooltip",{attrs:{placement:"top"}},[o("div",{staticClass:"tips_div",attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("product.product-modbus.562372-6"))+" "),o("br"),t._v(" "+t._s(t.$t("product.product-modbus.562372-7"))+" "),o("br"),t._v(" "+t._s(t.$t("product.product-modbus.562372-8"))+" ")]),o("i",{staticClass:"el-icon-question"})]),o("span",{staticStyle:{margin:"0 15px 0 6px"}},[t._v(t._s(t.$t("product.product-modbus.562372-5")))]),o("el-radio-group",{attrs:{disabled:!t.enableSetSlave},model:{value:t.form.statusDeter,callback:function(e){t.$set(t.form,"statusDeter",e)},expression:"form.statusDeter"}},t._l(t.dict.type.device_status_deter,(function(e){return o("el-radio",{key:e.value,attrs:{label:Number(e.value)}},[t._v(" "+t._s(e.label)+" ")])})),1)],1)],1),o("el-col",{attrs:{span:14}},["1"==t.form.statusDeter?o("el-form-item",{attrs:{label:""}},[o("span",[o("el-tooltip",{attrs:{placement:"top"}},[o("div",{staticClass:"tips_div",attrs:{slot:"content"},slot:"content"},[t._v(t._s(t.$t("product.product-modbus.562372-10"))+","+t._s(t.$t("product.product-modbus.562372-12")))]),o("i",{staticClass:"el-icon-question"})])],1),o("span",{staticStyle:{margin:"0 15px 0 6px"}},[t._v(t._s(t.$t("product.product-modbus.562372-9")))]),o("el-select",{staticStyle:{width:"230px"},attrs:{disabled:!t.enableSetSlave,placeholder:t.$t("product.product-modbus.562372-11")},model:{value:t.form.deterTimer,callback:function(e){t.$set(t.form,"deterTimer",e)},expression:"form.deterTimer"}},t._l(t.dict.type.iot_modbus_poll_time,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e()],1)],1),o("el-row",[o("el-col",{attrs:{span:10}},[o("el-form-item",{attrs:{label:""}},[o("span",{staticStyle:{margin:"0 15px 0 20px"}},[t._v(t._s(t.$t("product.product-modbus.562372-13")))]),o("el-radio-group",{attrs:{disabled:!t.enableSetSlave},on:{change:t.changePollType},model:{value:t.form.pollType,callback:function(e){t.$set(t.form,"pollType",e)},expression:"form.pollType"}},t._l(t.dict.type.data_collect_type,(function(e){return o("el-radio",{key:e.value,attrs:{label:Number(e.value)}},[t._v(t._s(e.label))])})),1)],1)],1),o("el-col",{attrs:{span:14}},[o("el-form-item",{attrs:{label:"",prop:"slaveId"}},[o("span",[o("el-tooltip",{attrs:{placement:"top"}},[o("div",{staticClass:"tips_div",attrs:{slot:"content"},slot:"content"},[t._v(t._s(t.$t("product.product-modbus.562372-16")))]),o("i",{staticClass:"el-icon-question"})])],1),o("span",{staticStyle:{margin:"0 15px 0 6px"}},[t._v(t._s(t.$t("product.product-modbus.562372-14")))]),o("el-input",{staticStyle:{width:"230px"},attrs:{disabled:!t.enableSetSlave,label:t.$t("product.product-modbus.562372-15"),type:"number"},model:{value:t.form.slaveId,callback:function(e){t.$set(t.form,"slaveId",e)},expression:"form.slaveId"}})],1)],1)],1)],1)],1),o("el-divider"),o("div",{staticClass:"card-wrap"},[o("div",{staticClass:"title-wrap"},[o("div",{staticClass:"title"},[o("div",[t._v(t._s(t.$t("product.product-modbus.562372-17")))]),o("el-tooltip",{attrs:{effect:"dark",content:t.$t("product.product-modbus.562372-18"),placement:"top"}},[o("i",{staticClass:"el-icon-question",staticStyle:{"margin-left":"6px"}})])],1),o("div",{staticClass:"btn-group"},[t.enableEditIO||1!=t.productInfo.status?t._e():o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-edit",size:"small"},on:{click:t.editIOModbus}},[t._v(t._s(t.$t("product.product-modbus.562372-19")))]),t.enableEditIO?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:edit"],expression:"['modbus:config:edit']"}],attrs:{type:"primary",plain:"",icon:"el-icon-check",size:"small"},on:{click:t.submitFormIO}},[t._v(t._s(t.$t("product.product-modbus.562372-22")))]):t._e(),t.enableEditIO?o("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-close",size:"small"},on:{click:t.handleCancelIO}},[t._v(t._s(t.$t("product.product-modbus.562372-23")))]):t._e(),1==t.productInfo.status?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:import"],expression:"['modbus:config:import']"}],attrs:{plain:"",icon:"el-icon-upload2",size:"small"},on:{click:function(e){return e.stopPropagation(),t.batchImport("isSelectIo")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-20"))+" ")]):t._e(),1==t.productInfo.status?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:export"],expression:"['modbus:config:export']"}],attrs:{plain:"",icon:"el-icon-download",size:"small"},on:{click:function(e){return e.stopPropagation(),t.exportModbus("isSelectIo")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-21"))+" ")]):t._e()],1)]),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingIO,expression:"loadingIO"}],key:t.configTableKey,ref:"IOTable",staticClass:"table-wrap",attrs:{data:t.configList,"data-key":"id",border:!1}},[o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-24"),align:"center",prop:"sort",width:"50"}}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-25"),align:"center",prop:"identifier",width:"280px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-select",{ref:"selectIo"+e.$index,staticStyle:{width:"100%"},attrs:{filterable:"",disabled:!t.enableEditIO,placeholder:t.$t("product.product-modbus.562372-26")},on:{change:function(o){return t.updateSelectThingsModel({newVal:o,oldVal:t.$refs["selectIo"+e.$index].value,justiceSelect:"isSelectIo"})}},model:{value:e.row.identifier,callback:function(o){t.$set(e.row,"identifier",o)},expression:"scope.row.identifier"}},[o("el-option",{key:"0",staticStyle:{width:"300px"},attrs:{label:"",value:"",disabled:""}},[o("span",{staticStyle:{float:"left"}},[t._v(t._s(t.$t("product.product-modbus.562372-27")))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(t.$t("product.product-modbus.562372-28")))])]),t._l(t.thingsModelList,(function(e){return o("el-option",{key:e.identifier,staticStyle:{width:"300px"},attrs:{label:e.modelName+" ("+e.identifier+")",value:e.identifier,disabled:!e.isSelectIo}},[o("span",{staticStyle:{float:"left"}},[t._v(t._s(e.modelName))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.identifier))])])}))],2)]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-30"),align:"center",prop:"address",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!t.enableEditIO,min:0,max:4e5,label:t.$t("product.product-modbus.562372-30")},model:{value:e.row.address,callback:function(o){t.$set(e.row,"address",o)},expression:"scope.row.address"}})]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-31"),align:"center",prop:"isReadonly",width:"260px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-radio-group",{attrs:{disabled:!t.enableEditIO},model:{value:e.row.isReadonly,callback:function(o){t.$set(e.row,"isReadonly",o)},expression:"scope.row.isReadonly"}},[o("el-radio-button",{attrs:{label:1}},[t._v(t._s(t.$t("product.product-modbus.562372-32")))]),o("el-radio-button",{attrs:{label:0}},[t._v(t._s(t.$t("product.product-modbus.562372-33")))])],1)]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-311"),align:"center",prop:"bitOrder",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!t.enableEditIO,min:0,max:15,label:t.$t("product.product-modbus.562372-311")},model:{value:e.row.bitOrder,callback:function(o){t.$set(e.row,"bitOrder",o)},expression:"scope.row.bitOrder"}})]}}])}),t.enableEditIO?o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-34"),align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:config:remove"],expression:"['iot:config:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(o){return t.handleDelete(e.row,e.$index,t.configList,"isSelectIo")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-35"))+" ")])]}}],null,!1,398356832)}):t._e()],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParamsIO.pageNum,limit:t.queryParamsIO.pageSize},on:{"update:page":function(e){return t.$set(t.queryParamsIO,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParamsIO,"pageSize",e)},pagination:t.getIOList}}),o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[t.enableEditIO?o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:t.handleAdd}},[t._v(t._s(t.$t("product.product-modbus.562372-36")))]):t._e()],1),o("el-col",{attrs:{span:1.5}},[t.enableEditIO?o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:function(e){return t.handleAddBatch("isSelectIo")}}},[t._v(t._s(t.$t("product.product-modbus.562372-37")))]):t._e()],1)],1)],1),o("el-divider"),o("div",{staticClass:"card-wrap"},[o("div",{staticClass:"title-wrap"},[o("div",{staticClass:"title"},[o("div",[t._v(t._s(t.$t("product.product-modbus.562372-38")))]),o("el-tooltip",{attrs:{effect:"dark",content:t.$t("product.product-modbus.562372-39"),placement:"top"}},[o("i",{staticClass:"el-icon-question",staticStyle:{"margin-left":"6px"}})])],1),o("div",{staticClass:"btn-group"},[t.enableEditData||1!=t.productInfo.status?t._e():o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-edit",size:"small"},on:{click:t.editDataModbus}},[t._v(t._s(t.$t("product.product-modbus.562372-19")))]),t.enableEditData?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:edit"],expression:"['modbus:config:edit']"}],attrs:{type:"primary",plain:"",icon:"el-icon-check",size:"small"},on:{click:t.submitFormData}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-22"))+" ")]):t._e(),t.enableEditData?o("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-close",size:"small"},on:{click:t.handleCancelData}},[t._v(t._s(t.$t("product.product-modbus.562372-23")))]):t._e(),1==t.productInfo.status?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:import"],expression:"['modbus:config:import']"}],attrs:{plain:"",icon:"el-icon-upload2",size:"small"},on:{click:function(e){return e.stopPropagation(),t.batchImport("isSelectData")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-20"))+" ")]):t._e(),1==t.productInfo.status?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:config:export"],expression:"['modbus:config:export']"}],attrs:{plain:"",icon:"el-icon-download",size:"small"},on:{click:function(e){return e.stopPropagation(),t.exportModbus("isSelectIo")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-41"))+" ")]):t._e()],1)]),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loadingData,expression:"loadingData"}],key:t.dataTableKey,ref:"Dataable",staticClass:"table-wrap",attrs:{data:t.dataModbusList,border:!1}},[o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-24"),align:"center",prop:"sort",width:"50"}}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-25"),align:"center",prop:"identifier",width:"280px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-select",{ref:"selectData"+e.$index,staticStyle:{width:"100%"},attrs:{filterable:"",disabled:!t.enableEditData,placeholder:t.$t("product.product-modbus.562372-26")},on:{change:function(o){return t.updateSelectThingsModel({newVal:o,oldVal:t.$refs["selectData"+e.$index].value,justiceSelect:"isSelectData"})}},model:{value:e.row.identifier,callback:function(o){t.$set(e.row,"identifier",o)},expression:"scope.row.identifier"}},[o("el-option",{key:"0",staticStyle:{width:"300px"},attrs:{label:"",value:"",disabled:""}},[o("span",{staticStyle:{float:"left"}},[t._v(t._s(t.$t("product.product-modbus.562372-27")))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(t.$t("product.product-modbus.562372-28")))])]),t._l(t.thingsModelList,(function(e){return o("el-option",{key:e.identifier,staticStyle:{width:"300px"},attrs:{label:e.modelName+" ("+e.identifier+")",value:e.identifier,disabled:!e.isSelectData}},[o("span",{staticStyle:{float:"left"}},[t._v(t._s(e.modelName))]),o("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[t._v(t._s(e.identifier))])])}))],2)]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-30"),align:"center",prop:"address",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!t.enableEditData,min:0,max:4e5,label:t.$t("product.product-modbus.562372-30")},model:{value:e.row.address,callback:function(o){t.$set(e.row,"address",o)},expression:"scope.row.address"}})]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-31"),align:"center",prop:"isReadonly",width:"260px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-radio-group",{attrs:{disabled:!t.enableEditData},model:{value:e.row.isReadonly,callback:function(o){t.$set(e.row,"isReadonly",o)},expression:"scope.row.isReadonly"}},[o("el-radio-button",{attrs:{label:1}},[t._v(t._s(t.$t("product.product-modbus.562372-317")))]),o("el-radio-button",{attrs:{label:0}},[t._v(t._s(t.$t("product.product-modbus.562372-318")))])],1)]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-43"),align:"center",prop:"quantity",width:"200px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{disabled:!t.enableEditData,min:1,max:256,label:t.$t("product.product-modbus.562372-43")},model:{value:e.row.quantity,callback:function(o){t.$set(e.row,"quantity",o)},expression:"scope.row.quantity"}})]}}])}),o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-42"),align:"center",prop:"dataType",width:"230px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-select",{staticStyle:{display:"inline-block","padding-right":"10px"},attrs:{disabled:!t.enableEditData,placeholder:t.$t("product.product-modbus.562372-42")},model:{value:e.row.dataType,callback:function(o){t.$set(e.row,"dataType",o)},expression:"scope.row.dataType"}},t._l(t.dict.type.iot_modbus_data_type,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)]}}])}),t.enableEditData?o("el-table-column",{attrs:{label:t.$t("product.product-modbus.562372-34"),align:"center","class-name":"small-padding fixed-width",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.enableEditData?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:config:remove"],expression:"['iot:config:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(o){return t.handleDelete(e.row,e.$index,t.dataModbusList,"isSelectData")}}},[t._v(" "+t._s(t.$t("product.product-modbus.562372-35"))+" ")]):t._e()]}}],null,!1,3103350844)}):t._e()],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.dataTotal>0,expression:"dataTotal > 0"}],attrs:{total:t.dataTotal,page:t.queryParamsData.pageNum,limit:t.queryParamsData.pageSize},on:{"update:page":function(e){return t.$set(t.queryParamsData,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParamsData,"pageSize",e)},pagination:t.getDataList}}),o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[t.enableEditData?o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:t.handleAddData}},[t._v(t._s(t.$t("product.product-modbus.562372-44")))]):t._e()],1),o("el-col",{attrs:{span:1.5}},[t.enableEditData?o("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:function(e){return t.handleAddBatch("isSelectData")}}},[t._v(t._s(t.$t("product.product-modbus.562372-45")))]):t._e()],1)],1)],1),o("things-list",{ref:"thingsListRef",attrs:{productId:t.productId,justiceSelect:t.justiceSelect},on:{productEvent:function(e){return t.getThingsData(e)}}}),o("import-batch",{ref:"importBatchRef",attrs:{productId:t.productId,justiceSelect:t.justiceSelect},on:{"data-imported":t.handleDataImport}})],1)},a=[],s=(o("99af"),o("c740"),o("d81d"),o("14d9"),o("4e82"),o("a434"),o("d3b7"),o("159b"),o("7aa2")),l=o("01ca"),r=o("cc6f"),n=o("41d7"),d=o("aa47"),u=o("b775");function c(t){return Object(u["a"])({url:"/modbus/params/addOrUpdate",method:"post",data:t})}function p(t){return Object(u["a"])({url:"/modbus/params/getByProductId",method:"get",params:t})}var m=o("5e6c"),f={name:"product-modbus-copy",dicts:["iot_modbus_data_type","iot_yes_no","data_collect_type","device_status_deter","iot_modbus_poll_time"],props:{product:{type:Object,default:null}},components:{thingsList:r["default"],importBatch:n["default"]},watch:{product:function(t,e){this.productInfo=t,this.productInfo&&0!=this.productInfo.productId&&(this.thingsModelParams.productId=this.productInfo.productId,this.queryParamsIO.productId=this.productInfo.productId,this.queryParamsData.productId=this.productInfo.productId,this.productId=this.productInfo.productId,this.getIOList(),this.getDataList(),this.getThingsModelList(),this.getParams(),this.sendPollType())},enableEditIO:function(t,e){this.sortableIo&&this.sortableIo.option("disabled",!t),t||(this.getIOList(),this.getThingsModelList()),this.delIoIds=[],this.getThingsModelList()},enableEditData:function(t,e){this.sortableData&&this.sortableData.option("disabled",!t),t||(this.getDataList(),this.getThingsModelList()),this.delDataIds=[]}},mounted:function(){var t=this.product.productId;this.productInfo.status=this.product.status,t&&(this.rowDropIo(),this.rowDropData(),this.getTaskList(t),this.sendPollType(),this.thingsModelParams.productId=t,this.queryParamsIO.productId=t,this.queryParamsData.productId=t,this.productId=t,this.getIOList(),this.getDataList(),this.getThingsModelList(),this.getParams(),this.sendPollType())},data:function(){return{loadingIO:!1,loadingData:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,configList:[],enableEditIO:!1,enableSetSlave:!1,title:"",open:!1,queryParamsIO:{pageNum:1,pageSize:10,identifier:null,address:null,isReadonly:null,dataType:null,quantity:null,type:1,productId:0},queryParamsData:{pageNum:1,pageSize:10,identifier:null,address:null,isReadonly:null,dataType:null,quantity:null,type:2,productId:0},productId:0,dataModbusList:[],enableEditData:!1,dataTotal:0,dataLoading:!0,thingsModelList:[],thingsLoading:!0,thingsModelParams:{pageNum:1,pageSize:1e3,productId:0},thingsTotal:0,form:{statusDeter:1,slaveId:null,pollType:0,deterTimer:"300"},productInfo:{},rules:{slaveId:[{required:!0,message:this.$t("product.product-modbus.562372-46"),trigger:"blur"}],address:[{required:!0,message:this.$t("product.product-modbus.562372-47"),trigger:"blur"}],isReadonly:[{required:!0,message:this.$t("product.product-modbus.562372-48"),trigger:"blur"}],dataType:[{required:!0,message:this.$t("product.product-modbus.562372-49"),trigger:"change"}],quantity:[{required:!0,message:this.$t("product.product-modbus.562372-50"),trigger:"blur"}],type:[{required:!0,message:this.$t("product.product-modbus.562372-51"),trigger:"change"}]},sortableIo:null,configTableKey:0,delIoIds:[],sortableData:null,dataTableKey:1e3,delDataIds:[],justiceSelect:"isSelectData",jobTotal:0}},methods:{getIOList:function(){var t=this;this.loadingIO=!0,Object(s["b"])(this.queryParamsIO).then((function(e){t.configList=e.rows,t.total=e.total,t.loadingIO=!1}))},sendPollType:function(){this.$emit("sendPollType",this.form.pollType)},handleDataImport:function(t){this.configList=t},getDataList:function(){var t=this;this.loadingData=!0,Object(s["b"])(this.queryParamsData).then((function(e){t.dataModbusList=e.rows,t.dataTotal=e.total,t.loadingData=!1})),this.$nextTick((function(){t.$refs.Dataable.bodyWrapper.scrollTop=0}))},getThingsModelList:function(){var t=this;this.thingsLoading=!0,Object(l["f"])(this.thingsModelParams).then((function(e){t.$refs.thingsListRef.modelList=e.rows,t.thingsModelList=e.rows,t.thingsTotal=e.total,t.thingsLoading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={statusDeter:1,slaveId:null,deterTime:"300"},this.resetForm("form")},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.id})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){var t=this,e={identifier:"",slave:1,address:1,isReadonly:1,type:1,quantity:1,sort:this.configList.length+1};this.configList.push(e),setTimeout((function(){t.$refs.IOTable.setCurrentRow(e)}),10)},handleAddData:function(){var t=this,e={identifier:"",slave:1,address:1,isReadonly:1,dataType:"ushort",quantity:1,type:2,sort:this.dataModbusList.length+1};this.dataModbusList.push(e),setTimeout((function(){t.$refs.Dataable.setCurrentRow(e)}),10)},handleAddBatch:function(t){this.justiceSelect=t,this.$refs.thingsListRef.open=!0,this.$refs.thingsListRef.selectedList="isSelectData"==t?this.dataModbusList:this.configList,this.$refs.thingsListRef.getList()},editIOModbus:function(){this.enableEditIO=!this.enableEditIO},handleCancelIO:function(){this.enableEditIO=!this.enableEditIO},handleCancelData:function(){this.enableEditData=!this.enableEditData},editDataModbus:function(){this.enableEditData=!this.enableEditData},submitFormIO:function(){var t=this,e=[];this.configList.forEach((function(t,o){t.identifier&&(t.sort=e.length+1,e.push(t))})),this.loadingIO=!0;var o={productId:this.productId,configList:e,delIds:this.delIoIds};Object(s["a"])(o).then((function(e){t.$modal.msgSuccess("保存成功"),t.getIOList(),t.open=!1,t.loadingIO=!1,t.enableEditIO=!1})).catch((function(e){t.loadingIO=!1}))},submitFormData:function(){var t=this,e=[];this.dataModbusList.forEach((function(t,o){t.identifier&&(t.sort=e.length+1,e.push(t))})),this.loadingData=!0;var o={productId:this.productId,configList:e,delIds:this.delDataIds};Object(s["a"])(o).then((function(e){t.$modal.msgSuccess(t.$t("product.product-modbus.562372-52")),t.open=!1,t.enableEditData=!1,t.loadingData=!1})).catch((function(e){t.loadingData=!1}))},handleDelete:function(t,e,o,i){var a=o.splice(e,1)[0];"isSelectData"==i&&t.id&&this.delDataIds.push(t.id),"isSelectIo"==i&&t.id&&this.delIoIds.push(t.id),this.updateSelectThingsModel({justiceSelect:i,oldVal:a.identifier})},getThingsData:function(t){var e=this,o="isSelectData"==this.justiceSelect?this.dataModbusList:this.configList;t.forEach((function(t,i){var a=o.findIndex((function(e){return e.identifier==t}));-1==a&&(o.push({identifier:t,slave:1,address:1,isReadonly:1,dataType:"ushort",quantity:1,type:"isSelectData"==e.justiceSelect?2:1,sort:o.length+1}),e.updateSelectThingsModel({justiceSelect:e.justiceSelect,newVal:t}))}))},rowDropIo:function(){var t=this,e=this.$refs.IOTable.$el.children[2].children[0].children[1];this.sortableIo=new d["default"](e,{disabled:!0,onEnd:function(e){var o=e.newIndex,i=e.oldIndex;e.to;t.dealDrop(t.configList,o,i),t.configTableKey++,t.sortableIo.destroy(),t.$nextTick((function(){t.rowDropIo(),t.sortableIo.option("disabled",!1)}))}})},rowDropData:function(){var t=this,e=this.$refs.Dataable.$el.children[2].children[0].children[1];this.sortableData=new d["default"](e,{disabled:!0,onEnd:function(e){var o=e.newIndex,i=e.oldIndex;e.to;t.dealDrop(t.dataModbusList,o,i),t.dataTableKey++,t.sortableData.destroy(),t.$nextTick((function(){t.rowDropData(),t.sortableData.option("disabled",!1)}))}})},dealDrop:function(t,e,o){if(o!=e){var i=t.splice(o,1)[0];t.splice(e,0,i)}},updateSelectThingsModel:function(t){var e=t.oldVal,o=t.newVal,i=t.justiceSelect,a=e?this.thingsModelList.findIndex((function(t){return t.identifier==e})):-1,s=o?this.thingsModelList.findIndex((function(t){return t.identifier==o})):-1;-1!=a&&(this.thingsModelList[a][i]=!0),-1!=s&&(this.thingsModelList[s][i]=!1)},batchImport:function(t){this.justiceSelect=t,this.$refs.importBatchRef.upload.importDeviceDialog=!0},exportModbus:function(t){var e="isSelectData"==t?2:1,o="isSelectData"==t?this.$t("product.product-modbus.562372-38"):this.$t("product.product-modbus.562372-17");this.download("/modbus/config/exportModbus?type="+e,{},"".concat(o,"_").concat((new Date).getTime(),".xlsx"))},getParams:function(){var t=this,e={productId:this.productId};p(e).then((function(e){e.data&&(t.form=e.data,t.sendPollType())}))},setSlave:function(){this.enableSetSlave=!this.enableSetSlave},saveSlave:function(){var t=this;this.$refs["form"].validate((function(e){e&&(t.enableSetSlave=!t.enableSetSlave,t.form.productId=t.productId,c(t.form).then((function(e){t.$modal.msgSuccess(t.$t("product.product-modbus.562372-53"))})))})),this.sendPollType()},changePollType:function(){var t=this;this.$nextTick((function(){t.getTaskList(t.product.productId)})),setTimeout((function(){1===t.form.pollType&&t.jobTotal>0&&t.$confirm(t.$t("product.product-modbus.562372-312"),t.$t("product.product-modbus.562372-313"),{confirmButtonText:t.$t("product.product-modbus.562372-314"),cancelButtonText:t.$t("product.product-modbus.562372-315"),type:"warning"}).then((function(){})).catch((function(){t.form.pollType=0,t.$message({type:"info",message:t.$t("product.product-modbus.562372-316")})}))}),500)},getTaskList:function(t){var e=this;this.loading=!0;var o={pageNum:1,pageSize:10,productId:t};Object(m["h"])(o).then((function(t){e.jobList=t.rows,e.jobTotal=t.total,e.loading=!1}))},cancelSlave:function(){this.enableSetSlave=!this.enableSetSlave}}},b=f,h=(o("eae5"),o("2877")),g=Object(h["a"])(b,i,a,!1,null,"a669b3de",null);e["default"]=g.exports},cc6f:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-dialog",{attrs:{title:t.$t("product.thimgs-mopdel-list.738493-0"),visible:t.open,width:"700px"},on:{"update:visible":function(e){t.open=e}}},[o("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"}},[o("el-form-item",{attrs:{prop:"productName"}},[o("el-input",{attrs:{placeholder:t.$t("product.thimgs-mopdel-list.738493-2"),clearable:"",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.modelName,callback:function(e){t.$set(t.queryParams,"modelName",e)},expression:"queryParams.modelName"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("product.thimgs-mopdel-list.738493-3")))]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("product.thimgs-mopdel-list.738493-4")))])],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"thingsModelTable",attrs:{data:t.modelList,"highlight-current-row":"",height:"50vh",size:"small",border:!1},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55",align:"center",selectable:t.selectable}}),o("el-table-column",{attrs:{label:t.$t("product.thimgs-mopdel-list.738493-5"),align:"left",prop:"modelName","min-width":"160"}}),o("el-table-column",{attrs:{label:t.$t("product.thimgs-mopdel-list.738493-6"),align:"left",prop:"identifier","min-width":"120"}})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectProduct}},[t._v(t._s(t.$t("product.thimgs-mopdel-list.738493-7")))]),o("el-button",{attrs:{type:"info"},on:{click:t.closeDialog}},[t._v(t._s(t.$t("product.thimgs-mopdel-list.738493-8")))])],1)],1)},a=[],s=(o("c740"),o("d81d"),o("a9e3"),o("d3b7"),o("159b"),o("01ca")),l={name:"ThingsModelList",props:{productId:{type:Number,default:0},justiceSelect:{type:String,default:"isSelectData"}},data:function(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,modelList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20},form:{},selectedList:[]}},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.productId=this.productId,Object(s["f"])(this.queryParams).then((function(e){t.modelList=e.rows,t.total=e.total,t.loading=!1,t.$nextTick((function(){t.selectedList.forEach((function(e){var o=t.modelList.findIndex((function(t){return t.identifier==e.identifier}));if(-1!=o){var i=t.modelList[o];i.isSelectData=!1,i.isSelectIo=!1,t.$refs.thingsModelTable.toggleRowSelection(i,!0)}}))}))}))},cancel:function(){this.open=!1},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.identifier})),this.single=1!==t.length,this.multiple=!t.length},confirmSelectProduct:function(){this.$emit("productEvent",this.ids),this.open=!1},closeDialog:function(){this.open=!1},selectable:function(t){return t[this.justiceSelect]}}},r=l,n=o("2877"),d=Object(n["a"])(r,i,a,!1,null,null,null);e["default"]=d.exports},eae5:function(t,e,o){"use strict";o("4dd3")}}]);