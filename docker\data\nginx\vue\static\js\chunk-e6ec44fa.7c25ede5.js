(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e6ec44fa"],{c3be:function(t,e,a){"use strict";a("fd93")},da6d:function(t,e,a){"use strict";a.r(e);var c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"product-scada-wrap"},[t.isScada?a("div",{staticClass:"scada",style:{height:t.contentHeight+"px"}},[a(t.scadaComp,{tag:"component",attrs:{fullScreemTip:!1,isContextmenu:!1}})],1):a("div",[a("el-empty",{attrs:{description:t.$t("product.product-scada.638785-0")}}),a("div",{staticStyle:{"text-align":"center"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:product:scada"],expression:"['iot:product:scada']"}],attrs:{size:"mini",type:"primary"},on:{click:function(e){return t.handleGoToScada()}}},[t._v(t._s(t.$t("product.product-scada.034908-0")))])],1)],1)])},i=[],n=a("5530"),o=(a("14d9"),{name:"ProductScada",props:{product:{type:Object,default:null}},watch:{product:{deep:!0,immediate:!0,handler:function(t,e){this.productInfo=t,t.guid?(this.getScadaComp(t),this.isScada=!0):this.isScada=!1}}},data:function(){return{isScada:!1,contentHeight:window.innerHeight,scadaComp:null,productInfo:{}}},methods:{calculateContentHeight:function(){var t=document.getElementById("productDetailTab").offsetHeight;this.contentHeight=parseFloat(t)},handleGoToScada:function(){this.$router.push({path:"/scada/center/temp",query:{productId:this.productInfo.productId}})},getScadaComp:function(t){this.$router.push({query:Object(n["a"])(Object(n["a"])({},this.$route.query),{},{guid:this.productInfo.guid,type:1})})}}}),d=o,s=(a("c3be"),a("2877")),r=Object(s["a"])(d,c,i,!1,null,"1cf363e2",null);e["default"]=r.exports},fd93:function(t,e,a){}}]);