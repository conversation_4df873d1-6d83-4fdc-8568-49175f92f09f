(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6012"],{7168:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"日志类型",prop:"logType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.dict.type.iot_event_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identity"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identity,callback:function(t){e.$set(e.queryParams,"identity",t)},expression:"queryParams.identity"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceLogList,size:"mini"}},[a("el-table-column",{attrs:{label:"类型",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_event_type,value:t.row.logType}})]}}])}),a("el-table-column",{attrs:{label:"模式",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.mode?a("el-tag",{attrs:{type:"primary"}},[e._v("影子模式")]):2==t.row.mode?a("el-tag",{attrs:{type:"success"}},[e._v("在线模式")]):a("el-tag",{attrs:{type:"info"}},[e._v("其他信息")])]}}])}),a("el-table-column",{attrs:{label:"时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createTime))])]}}])}),a("el-table-column",{attrs:{label:"标识符",align:"center",prop:"identity"}}),a("el-table-column",{attrs:{label:"动作",align:"left","header-align":"center",prop:"logValue"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatValueDisplay(t.row))}})]}}])}),a("el-table-column",{attrs:{label:"备注","header-align":"center",align:"left",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null==t.row.remark?"无":t.row.remark)+" ")]}}])})],1),a("div",{staticStyle:{height:"40px"}},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1)},n=[],r=a("5530"),s=(a("b0c0"),a("a9e3"),a("b775"));function o(e){return Object(s["a"])({url:"/iot/event/list",method:"get",params:e})}var l={name:"DeviceLog",dicts:["iot_event_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList(),this.thingsModel=this.deviceInfo.cacheThingsModel)}},data:function(){return{thingsModel:{},loading:!0,showSearch:!0,total:0,deviceLogList:[],queryParams:{pageNum:1,pageSize:10,logType:null,logValue:null,deviceId:null,serialNumber:null,deviceName:null,identity:null,isMonitor:null},daterangeTime:[]}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]),o(this.queryParams).then((function(t){e.deviceLogList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleExport:function(){this.download("iot/event/export",Object(r["a"])({},this.queryParams),"eventLog_".concat((new Date).getTime(),".xlsx"))},formatValueDisplay:function(e){if(1==e.logType){var t=this.getThingsModelItem(1,e.identity);if(""!=t)return(t.parentName?"["+t.parentName+(t.arrayIndex?t.arrayIndex:"")+"] ":"")+t.name+'： <span style="color:#409EFF;">'+this.getThingsModelItemValue(t,e.logValue)+" "+(void 0!=t.datatype.unit?t.datatype.unit:"")+"</span>"}else if(2==e.logType){var a=this.getThingsModelItem(2,e.identity);if(""!=a)return(a.parentName?"["+a.parentName+(a.arrayIndex?a.arrayIndex:"")+"] ":"")+a.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(a,e.logValue)+" "+(void 0!=a.datatype.unit?a.datatype.unit:"")+"</span>"}else if(3==e.logType){var i=this.getThingsModelItem(3,e.identity);if(""!=i)return(i.parentName?"["+i.parentName+(i.arrayIndex?i.arrayIndex:"")+"] ":"")+i.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(i,e.logValue)+" "+(void 0!=i.datatype.unit?i.datatype.unit:"")+"</span>"}else{if(4==e.logType)return'<span style="font-weight:bold">设备升级</span>';if(5==e.logType)return'<span style="font-weight:bold">设备上线</span>';if(6==e.logType)return'<span style="font-weight:bold">设备离线</span>'}return""},getThingsModelItemValue:function(e,t){if("bool"==e.datatype.type){if("0"==t)return e.datatype.falseText;if("1"==t)return e.datatype.trueText}else if("enum"==e.datatype.type)for(var a=0;a<e.datatype.enumList.length;a++)if(t==e.datatype.enumList[a].value)return e.datatype.enumList[a].text;return t},getThingsModelItem:function(e,t){if(1==e&&this.thingsModel.properties)for(var a=0;a<this.thingsModel.properties.length;a++){if(this.thingsModel.properties[a].id==t)return this.thingsModel.properties[a];if("object"==this.thingsModel.properties[a].datatype.type)for(var i=0;i<this.thingsModel.properties[a].datatype.params.length;i++)if(this.thingsModel.properties[a].datatype.params[i].id==t)return this.thingsModel.properties[a].datatype.params[i].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[i];if("array"==this.thingsModel.properties[a].datatype.type&&this.thingsModel.properties[a].datatype.arrayType)if("object"==this.thingsModel.properties[a].datatype.arrayType){var n=t,r=0;t.indexOf("array_")>-1&&(r=t.substring(6,8),n=t.substring(9));for(var s=0;s<this.thingsModel.properties[a].datatype.params.length;s++)if(this.thingsModel.properties[a].datatype.params[s].id==n)return this.thingsModel.properties[a].datatype.params[s].arrayIndex=Number(r)+1,this.thingsModel.properties[a].datatype.params[s].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[s]}else for(var o=0;o<this.thingsModel.properties[a].datatype.arrayCount.length;o++)if(this.thingsModel.properties[a].id==realIdentity)return this.thingsModel.properties[a].arrayIndex=Number(arrayIndex)+1,this.thingsModel.properties[a].parentName="元素",this.thingsModel.properties[a]}else if(2==e&&this.thingsModel.functions)for(var l=0;l<this.thingsModel.functions.length;l++){if(this.thingsModel.functions[l].id==t)return this.thingsModel.functions[l];if("object"==this.thingsModel.functions[l].datatype.type)for(var d=0;d<this.thingsModel.functions[l].datatype.params.length;d++)if(this.thingsModel.functions[l].datatype.params[d].id==t)return this.thingsModel.functions[l].datatype.params[d].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[d];if("array"==this.thingsModel.functions[l].datatype.type&&this.thingsModel.functions[l].datatype.arrayType){var p=t,u=0;if(t.indexOf("array_")>-1&&(u=t.substring(6,8),p=t.substring(9)),"object"==this.thingsModel.functions[l].datatype.arrayType){for(var h=0;h<this.thingsModel.functions[l].datatype.params.length;h++)if(this.thingsModel.functions[l].datatype.params[h].id==p)return this.thingsModel.functions[l].datatype.params[h].arrayIndex=Number(u)+1,this.thingsModel.functions[l].datatype.params[h].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[h]}else for(var y=0;y<this.thingsModel.functions[l].datatype.arrayCount.length;y++)if(this.thingsModel.functions[l].id==p)return this.thingsModel.functions[l].arrayIndex=Number(u)+1,this.thingsModel.functions[l].parentName="元素",this.thingsModel.functions[l]}}else if(3==e&&this.thingsModel.events)for(var g=0;g<this.thingsModel.events.length;g++)if(this.thingsModel.events[g].id==t)return this.thingsModel.events[g];return""}}},d=l,p=a("2877"),u=Object(p["a"])(d,i,n,!1,null,null,null);t["default"]=u.exports}}]);