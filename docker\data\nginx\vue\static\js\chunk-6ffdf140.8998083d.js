(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ffdf140"],{"584f":function(e,t,i){"use strict";i.d(t,"k",(function(){return r})),i.d(t,"n",(function(){return a})),i.d(t,"l",(function(){return o})),i.d(t,"m",(function(){return l})),i.d(t,"j",(function(){return u})),i.d(t,"e",(function(){return c})),i.d(t,"c",(function(){return s})),i.d(t,"f",(function(){return d})),i.d(t,"h",(function(){return m})),i.d(t,"g",(function(){return p})),i.d(t,"a",(function(){return f})),i.d(t,"o",(function(){return v})),i.d(t,"b",(function(){return h})),i.d(t,"d",(function(){return b})),i.d(t,"i",(function(){return g}));var n=i("b775");function r(e){return Object(n["a"])({url:"/iot/device/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:e})}function u(){return Object(n["a"])({url:"/iot/device/all",method:"get"})}function c(e){return Object(n["a"])({url:"/iot/device/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function p(e){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/iot/device",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/iot/device",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/iot/device/"+e,method:"delete"})}function b(e){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:e})}function g(e){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},ed76:function(e,t,i){"use strict";i.r(t);var n=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"选择设备",visible:e.openDeviceList,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.openDeviceList=t}}},[i("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[i("el-input",{attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),i("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[i("el-input",{attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList,"highlight-current-row":"",size:"mini"},on:{"row-click":e.rowClick}},[i("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[i("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),i("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),i("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),i("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName"}}),i("el-table-column",{attrs:{label:"设备类型",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?i("el-tag",{attrs:{type:"success"}},[e._v("分享")]):i("el-tag",{attrs:{type:"primary"}},[e._v("拥有")])]}}])}),i("el-table-column",{attrs:{label:"定位方式",align:"center",prop:"locationWay",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_location_way,value:t.row.locationWay}})]}}])}),i("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectDevice}},[e._v("确 定")]),i("el-button",{on:{click:e.closeSelectDeviceList}},[e._v("取 消")])],1)],1)},r=[],a=i("584f"),o={name:"device-list",dicts:["iot_device_status","iot_location_way"],data:function(){return{loading:!0,ids:[],openDeviceList:!1,total:0,deviceList:[],selectDevice:{},queryParams:{pageNum:1,pageSize:10,deviceName:null,productId:null,groupId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,status:null,networkAddress:null,activeTime:null}}},created:function(){},methods:{getList:function(e){var t=this;this.deviceList=[],this.total=0,this.loading=!0,Object(a["m"])(this.queryParams).then((function(i){for(var n=0;n<i.rows.length;n++)i.rows[n].isSelect=!1;t.deviceList=i.rows,t.total=i.total,0!=e&&t.setRadioSelected(e),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.deviceId),this.selectDevice=e)},setRadioSelected:function(e){for(var t=0;t<this.deviceList.length;t++)this.deviceList[t].deviceId==e?this.deviceList[t].isSelect=!0:this.deviceList[t].isSelect=!1},closeSelectDeviceList:function(){this.openDeviceList=!1},confirmSelectDevice:function(){this.$emit("deviceEvent",this.selectDevice),this.openDeviceList=!1}}},l=o,u=i("2877"),c=Object(u["a"])(l,n,r,!1,null,null,null);t["default"]=c.exports}}]);