(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-36d17c5d","chunk-5d005370","chunk-376d6212","chunk-350db0a8","chunk-39413ce8"],{"04e0":function(e,t,s){"use strict";s("8ceb")},"0d20":function(e,t,s){},"1e8b":function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"user-info"},[s("el-descriptions",{attrs:{column:3}},[s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-1")}},[e._v(e._s(e.userInfo.userName))]),s("el-descriptions-item",{attrs:{label:e.$t("user.index.098976-13")}},[e._v(e._s(e.userInfo.phonenumber))]),s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-2")}},[e._v(e._s(e.userInfo.email))]),s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-3")}},[e._v(" "+e._s(e.posts?(e.userInfo.dept?e.userInfo.dept.deptName:"")+" / "+e.posts:""+(e.userInfo.dept?e.userInfo.dept.deptName:""))+" ")]),s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-4")}},[e._v(e._s(e.roles))]),s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-5")}},[e._v(e._s(e.userInfo.createTime))]),s("el-descriptions-item",{attrs:{label:e.$t("user.profile.index.894502-6")}},[e.wxBind?s("el-button",{attrs:{type:"warning",size:"mini"},on:{click:e.handleUnBindWeChat}},[e._v(e._s(e.$t("user.profile.index.894502-7")))]):s("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handleBindWeChat}},[e._v(e._s(e.$t("user.profile.index.894502-8")))])],1)],1),s("el-dialog",{attrs:{title:"绑定微信",visible:e.isBindWeChat,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.isBindWeChat=t}}},[s("div",{staticClass:"bindWeChatDialog"},[s("div",{staticClass:"dec"},[e._v("请通过微信扫一扫，进行微信绑定。")]),s("div",{staticClass:"weChat"},[s("div",{staticStyle:{height:"200px"},attrs:{id:"weChatLogin"}})])]),s("div",{attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){e.isBindWeChat=!1}}},[e._v(e._s(e.$t("close")))])],1)]),s("el-dialog",{attrs:{title:e.$t("user.profile.index.894502-11"),visible:e.isUnBindWeChat,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.isUnBindWeChat=t}}},[s("div",{staticClass:"unBindWeChatDialog"},[s("el-form",{attrs:{"label-width":"150px"}},[s("el-form-item",{attrs:{label:e.$t("user.profile.index.894502-12"),prop:"pasaward"}},[s("el-input",{staticStyle:{width:"80%"},attrs:{type:e.pwdtype},model:{value:e.unBindWeChat.password,callback:function(t){e.$set(e.unBindWeChat,"password",t)},expression:"unBindWeChat.password"}},[s("template",{slot:"suffix"},[s("span",{staticClass:"el-icon-view",staticStyle:{"margin-right":"8px"},on:{click:function(t){return e.pwdTypeChange()}}})])],2)],1)],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{size:"small"},on:{click:function(t){e.isUnBindWeChat=!1}}},[e._v(e._s(e.$t("close")))]),s("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.confirmUnBindWeChat}},[e._v(e._s(e.$t("confirm")))])],1)])],1)},i=[],n=s("5530"),a=s("c0c7"),o=s("7ded"),u={props:{user:{type:Object,default:{}},postGroup:{type:String,default:""},roleGroup:{type:String,default:""},wxbind:{type:Boolean,default:!1}},data:function(){return{isBindWeChat:!1,isUnBindWeChat:!1,pwdtype:"password",wxBind:this.wxbind,userInfo:this.user,posts:this.postGroup,roles:this.roleGroup,unBindWeChat:{password:"",verifyType:1}}},mounted:function(){this.getUser();var e=this.$route.query.wxBindMsgId;e?this.getWeChatBindMsg():console.log("此时没有进行绑定操作！");var t=document.createElement("script");t.type="text/javascript",t.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js",document.body.appendChild(t)},methods:{handleBindWeChat:function(){Object(a["h"])().then((function(e){var t=document.createElement("script");t.type="text/javascript",t.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js";var s=document.body.appendChild(t);s.onload=function(){new WxLogin({self_redirect:!1,id:"weChatLogin",appid:e.data.appid,scope:e.data.scope,redirect_uri:e.data.redirectUri,state:e.data.state,style:"black",href:"data:text/css;base64,LmltcG93ZXJCb3ggLnRpdGxlIHsKIGRpc3BsYXk6IG5vbmU7Cn0KLmltcG93ZXJCb3ggLnN0YXR1cy5zdGF0dXNfYnJvd3NlciB7CiBkaXNwbGF5OiBub25lOwp9Ci5pbXBvd2VyQm94IC5xcmNvZGUgewogYm9yZGVyOm5vbmU7CiB3aWR0aDogMjAwcHg7CiBoZWlnaHQ6IDIwMHB4OwogbWFyZ2luOjAgYXV0bzsKfQouaW1wb3dlckJveCAuc3RhdHVzewogZGlzcGxheTogbm9uZQp9"})}})),this.isBindWeChat=!0},handleUnBindWeChat:function(){this.isUnBindWeChat=!0},pwdTypeChange:function(){"password"==this.pwdtype?this.pwdtype="text":this.pwdtype="password"},confirmUnBindWeChat:function(){var e=this,t=Object(n["a"])({},this.unBindWeChat);Object(a["n"])(t).then((function(t){200===t.code?(e.getUser(),e.$modal.msgSuccess(t.msg)):e.$modal.msgError(t.msg),e.isUnBindWeChat=!1}))},getUser:function(){var e=this;Object(a["k"])().then((function(t){200===t.code&&(e.userInfo=t.data,e.wxBind=t.wxBind,e.roles=t.roleGroup,e.posts=t.postGroup)}))},getWeChatBindMsg:function(){var e=this,t=this.$route.query.wxBindMsgId;Object(o["l"])(t).then((function(t){200===t.code?e.$modal.msgSuccess(t.msg):e.$modal.msgError(t.msg)}))}}},l=u,c=(s("d960"),s("2877")),d=Object(c["a"])(l,r,i,!1,null,"3c69c49e",null);t["default"]=d.exports},"343e":function(e,t,s){},"4c1b":function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"user-profile"},[s("el-card",{attrs:{"body-style":{padding:"0px"}}},[s("div",{staticClass:"card-body"},[s("div",{staticClass:"left-wrap"},[s("userAvatar",{attrs:{user:e.user}}),s("div",{staticClass:"user-name"},[e._v(e._s(e.user.userName))]),s("ul",{staticClass:"ul-wrap"},[s("li",{class:{"li-wrap":!0,"li-active":0===e.tabIndex},on:{click:function(t){return e.handleTabClick(0)}}},[s("i",{staticClass:"el-icon-user icon"}),s("span",{staticClass:"title"},[e._v(e._s(e.$t("user.index.098976-50")))])]),s("li",{class:{"li-wrap":!0,"li-active":1===e.tabIndex},on:{click:function(t){return e.handleTabClick(1)}}},[s("i",{staticClass:"el-icon-postcard icon"}),s("span",{staticClass:"title"},[e._v(e._s(e.$t("user.index.098976-51")))])]),s("li",{class:{"li-wrap":!0,"li-active":2===e.tabIndex},on:{click:function(t){return e.handleTabClick(2)}}},[s("svg-icon",{staticClass:"icon",attrs:{"icon-class":"password"}}),s("span",{staticClass:"title"},[e._v(e._s(e.$t("user.index.098976-52")))])],1)])],1),s("div",{staticClass:"right-wrap"},[s("div",{directives:[{name:"show",rawName:"v-show",value:0===e.tabIndex,expression:"tabIndex === 0"}],staticClass:"body-wrap"},[s("div",{staticClass:"header"},[e._v(e._s(e.$t("user.index.098976-50")))]),s("div",{staticClass:"content"},[s("userInfo",{attrs:{user:e.user,postGroup:e.postGroup,roleGroup:e.roleGroup,wxbind:e.wxbind}})],1)]),s("div",{directives:[{name:"show",rawName:"v-show",value:1===e.tabIndex,expression:"tabIndex === 1"}],staticClass:"body-wrap"},[s("div",{staticClass:"header"},[e._v(e._s(e.$t("user.index.098976-51")))]),s("div",{staticClass:"content"},[s("baseInfo",{attrs:{user:e.user}})],1)]),s("div",{directives:[{name:"show",rawName:"v-show",value:2===e.tabIndex,expression:"tabIndex === 2"}],staticClass:"body-wrap"},[s("div",{staticClass:"header"},[e._v(e._s(e.$t("user.index.098976-52")))]),s("div",{staticClass:"content"},[s("resetPwd")],1)])])])])],1)},i=[],n=s("9429"),a=s("1e8b"),o=s("5124"),u=s("ee46"),l=s("c0c7"),c={name:"Profile",components:{userAvatar:n["default"],userInfo:a["default"],baseInfo:o["default"],resetPwd:u["default"]},data:function(){return{tabIndex:0,user:{},roleGroup:"",postGroup:"",wxbind:""}},mounted:function(){this.getUser()},methods:{handleTabClick:function(e){this.tabIndex=e},getUser:function(){var e=this;Object(l["k"])().then((function(t){e.user=t.data,e.wxbind=t.wxBind,e.roleGroup=t.roleGroup,e.postGroup=t.postGroup}))}}},d=c,p=(s("04e0"),s("2877")),f=Object(p["a"])(d,r,i,!1,null,"d6302ac6",null);t["default"]=f.exports},5124:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-form",{ref:"form",attrs:{model:e.user,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:e.$t("user.index.098976-11"),prop:"nickName"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{maxlength:"30"},model:{value:e.user.nickName,callback:function(t){e.$set(e.user,"nickName",t)},expression:"user.nickName"}})],1),s("el-form-item",{attrs:{label:e.$t("user.index.098976-13"),prop:"phonenumber"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{maxlength:"11"},model:{value:e.user.phonenumber,callback:function(t){e.$set(e.user,"phonenumber",t)},expression:"user.phonenumber"}})],1),s("el-form-item",{attrs:{label:e.$t("user.index.098976-19"),prop:"email"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{maxlength:"50"},model:{value:e.user.email,callback:function(t){e.$set(e.user,"email",t)},expression:"user.email"}})],1),s("el-form-item",{attrs:{label:e.$t("user.userInfo.560923-1")}},[s("el-radio-group",{model:{value:e.user.sex,callback:function(t){e.$set(e.user,"sex",t)},expression:"user.sex"}},[s("el-radio",{attrs:{label:"0"}},[e._v(e._s(e.$t("user.userInfo.560923-2")))]),s("el-radio",{attrs:{label:"1"}},[e._v(e._s(e.$t("user.userInfo.560923-3")))])],1)],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.submit}},[e._v(e._s(e.$t("save")))]),s("el-button",{attrs:{type:"danger",size:"mini"},on:{click:e.close}},[e._v(e._s(e.$t("close")))])],1)],1)},i=[],n=s("c0c7"),a={props:{user:{type:Object}},data:function(){return{rules:{nickName:[{required:!0,message:this.$t("user.index.098976-33"),trigger:"blur"}],email:[{required:!0,message:this.$t("user.userInfo.560923-0"),trigger:"blur"},{type:"email",message:this.$t("user.index.098976-37"),trigger:["blur","change"]}],phonenumber:[{required:!0,message:this.$t("user.index.098976-38"),trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:this.$t("user.index.098976-39"),trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(n["r"])(e.user).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess"))}))}))},close:function(){this.$tab.closePage()}}},o=a,u=s("2877"),l=Object(u["a"])(o,r,i,!1,null,null,null);t["default"]=l.exports},"8ceb":function(e,t,s){},9429:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",{staticClass:"user-info-head",on:{click:function(t){return e.editCropper()}}},[s("img",{staticClass:"img-circle img-lg",attrs:{src:e.options.img,title:e.$t("user.resetPwd.450986-11")}})]),s("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t},opened:e.modalOpened,close:e.closeDialog}},[s("el-row",[s("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[e.visible?s("vue-cropper",{ref:"cropper",attrs:{img:e.options.img,info:!0,autoCrop:e.options.autoCrop,autoCropWidth:e.options.autoCropWidth,autoCropHeight:e.options.autoCropHeight,fixedBox:e.options.fixedBox,outputType:e.options.outputType},on:{realTime:e.realTime}}):e._e()],1),s("el-col",{style:{height:"350px"},attrs:{xs:24,md:12}},[s("div",{staticClass:"avatar-upload-preview"},[s("img",{style:e.previews.img,attrs:{src:e.previews.url}})])])],1),s("br"),s("el-row",[s("el-col",{attrs:{lg:2,sm:3,xs:3}},[s("el-upload",{attrs:{action:"#","http-request":e.requestUpload,"show-file-list":!1,"before-upload":e.beforeUpload}},[s("el-button",{attrs:{size:"small"}},[e._v(" "+e._s(e.$t("select"))+" "),s("i",{staticClass:"el-icon-upload el-icon--right"})])],1)],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-plus",size:"small"},on:{click:function(t){return e.changeScale(1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-minus",size:"small"},on:{click:function(t){return e.changeScale(-1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-left",size:"small"},on:{click:function(t){return e.rotateLeft()}}})],1),s("el-col",{attrs:{lg:{span:1,offset:1},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-right",size:"small"},on:{click:function(t){return e.rotateRight()}}})],1),s("el-col",{attrs:{lg:{span:2,offset:6},sm:2,xs:2}},[s("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.uploadImg()}}},[e._v(e._s(e.$t("submit")))])],1)],1)],1)],1)},i=[],n=s("4360"),a=s("7e79"),o=s("c0c7"),u=s("ed08"),l={components:{VueCropper:a["VueCropper"]},props:{user:{type:Object}},data:function(){return{open:!1,visible:!1,title:this.$t("user.resetPwd.450986-12"),options:{img:n["a"].getters.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png"},previews:{},resizeHandler:null}},methods:{editCropper:function(){this.open=!0},modalOpened:function(){var e=this;this.visible=!0,this.resizeHandler||(this.resizeHandler=Object(u["b"])((function(){e.refresh()}),100)),window.addEventListener("resize",this.resizeHandler)},refresh:function(){this.$refs.cropper.refresh()},requestUpload:function(){},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},changeScale:function(e){e=e||1,this.$refs.cropper.changeScale(e)},beforeUpload:function(e){var t=this;if(-1==e.type.indexOf("image/"))this.$modal.msgError(this.$t("user.resetPwd.450986-13"));else{var s=new FileReader;s.readAsDataURL(e),s.onload=function(){t.options.img=s.result}}},uploadImg:function(){var e=this;this.$refs.cropper.getCropBlob((function(t){var s=new FormData;s.append("avatarfile",t),Object(o["t"])(s).then((function(t){e.open=!1,e.options.img="/prod-api"+t.imgUrl,n["a"].commit("SET_AVATAR",e.options.img),e.$modal.msgSuccess(e.$t("updateSuccess")),e.visible=!1}))}))},realTime:function(e){this.previews=e},closeDialog:function(){this.options.img=n["a"].getters.avatar,this.visible=!1,window.removeEventListener("resize",this.resizeHandler)}}},c=l,d=(s("bd54"),s("2877")),p=Object(d["a"])(c,r,i,!1,null,"dd6cc878",null);t["default"]=p.exports},bd54:function(e,t,s){"use strict";s("0d20")},c0c7:function(e,t,s){"use strict";s.d(t,"l",(function(){return n})),s.d(t,"o",(function(){return a})),s.d(t,"j",(function(){return o})),s.d(t,"i",(function(){return u})),s.d(t,"a",(function(){return l})),s.d(t,"q",(function(){return c})),s.d(t,"c",(function(){return d})),s.d(t,"m",(function(){return p})),s.d(t,"b",(function(){return f})),s.d(t,"h",(function(){return m})),s.d(t,"n",(function(){return h})),s.d(t,"k",(function(){return b})),s.d(t,"r",(function(){return w})),s.d(t,"s",(function(){return g})),s.d(t,"t",(function(){return v})),s.d(t,"f",(function(){return x})),s.d(t,"p",(function(){return C})),s.d(t,"d",(function(){return y})),s.d(t,"e",(function(){return $})),s.d(t,"g",(function(){return _}));var r=s("b775"),i=s("c38a");function n(e){return Object(r["a"])({url:"/system/user/list",method:"get",params:e})}function a(e){return Object(r["a"])({url:"/system/user/listTerminal",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/user/"+Object(i["f"])(e),method:"get"})}function u(e){return Object(r["a"])({url:"/system/dept/getRole?deptId="+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/user",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/system/user",method:"put",data:e})}function d(e){return Object(r["a"])({url:"/system/user/"+e,method:"delete"})}function p(e,t){var s={userId:e,password:t};return Object(r["a"])({url:"/system/user/resetPwd",method:"put",data:s})}function f(e,t){var s={userId:e,status:t};return Object(r["a"])({url:"/system/user/changeStatus",method:"put",data:s})}function m(){return Object(r["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(e){return Object(r["a"])({url:"/wechat/cancelBind",method:"post",data:e})}function b(){return Object(r["a"])({url:"/system/user/profile",method:"get"})}function w(e){return Object(r["a"])({url:"/system/user/profile",method:"put",data:e})}function g(e,t){var s={oldPassword:e,newPassword:t};return Object(r["a"])({url:"/system/user/profile/updatePwd",method:"put",params:s})}function v(e){return Object(r["a"])({url:"/system/user/profile/avatar",method:"post",data:e})}function x(e){return Object(r["a"])({url:"/system/user/authRole/"+e,method:"get"})}function C(e){return Object(r["a"])({url:"/system/user/authRole",method:"put",params:e})}function y(){return Object(r["a"])({url:"/system/user/deptTree",method:"get"})}function $(e){return Object(r["a"])({url:"/system/user/deptTree?showOwner="+e,method:"get"})}function _(e){return Object(r["a"])({url:"/system/user/getByDeptId",method:"get",params:e})}},d960:function(e,t,s){"use strict";s("343e")},ee46:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-form",{ref:"form",attrs:{model:e.user,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:e.$t("user.resetPwd.450986-0"),prop:"oldPassword"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:e.$t("user.resetPwd.450986-1"),type:"password","show-password":""},model:{value:e.user.oldPassword,callback:function(t){e.$set(e.user,"oldPassword",t)},expression:"user.oldPassword"}})],1),s("el-form-item",{attrs:{label:e.$t("user.resetPwd.450986-2"),prop:"newPassword"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:e.$t("user.resetPwd.450986-3"),type:"password","show-password":""},model:{value:e.user.newPassword,callback:function(t){e.$set(e.user,"newPassword",t)},expression:"user.newPassword"}})],1),s("el-form-item",{attrs:{label:e.$t("user.resetPwd.450986-4"),prop:"confirmPassword"}},[s("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:e.$t("user.resetPwd.450986-5"),type:"password","show-password":""},model:{value:e.user.confirmPassword,callback:function(t){e.$set(e.user,"confirmPassword",t)},expression:"user.confirmPassword"}})],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.submit}},[e._v(e._s(e.$t("save")))]),s("el-button",{attrs:{type:"danger",size:"mini"},on:{click:e.close}},[e._v(e._s(e.$t("close")))])],1)],1)},i=[],n=(s("d9e2"),s("ac1f"),s("00b4"),s("c0c7")),a={data:function(){var e=this,t=function(t,s,r){e.user.newPassword!==s?r(new Error(e.$t("user.resetPwd.450986-6"))):r()};return{user:{oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},rules:{oldPassword:[{required:!0,message:this.$t("user.resetPwd.450986-7"),trigger:"blur"}],newPassword:[{required:!0,message:this.$t("user.resetPwd.450986-8"),trigger:"blur"},{min:6,max:20,message:this.$t("user.resetPwd.450986-9"),trigger:"blur"},{trigger:"blur",validator:function(t,s,r){var i=/(?![A-Z]*$)(?![a-z]*$)(?![0-9]*$)(?![^a-zA-Z0-9]*$)/;i.test(s)?r():r(new Error(e.$t("system.dept.780956-30")))}}],confirmPassword:[{required:!0,message:this.$t("user.resetPwd.450986-10"),trigger:"blur"},{required:!0,validator:t,trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(n["s"])(e.user.oldPassword,e.user.newPassword).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess"))}))}))},close:function(){this.$tab.closePage()}}},o=a,u=s("2877"),l=Object(u["a"])(o,r,i,!1,null,null,null);t["default"]=l.exports}}]);