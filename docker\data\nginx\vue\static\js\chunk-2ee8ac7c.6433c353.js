(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2ee8ac7c"],{"01ca":function(e,t,i){"use strict";i.d(t,"f",(function(){return n})),i.d(t,"d",(function(){return a})),i.d(t,"g",(function(){return s})),i.d(t,"a",(function(){return o})),i.d(t,"e",(function(){return l})),i.d(t,"i",(function(){return c})),i.d(t,"c",(function(){return d})),i.d(t,"b",(function(){return u})),i.d(t,"h",(function(){return m}));var r=i("b775");function n(e){return Object(r["a"])({url:"/iot/model/list",method:"get",params:e})}function a(e){return Object(r["a"])({url:"/iot/model/"+e,method:"get"})}function s(e){return Object(r["a"])({url:"/iot/model/permList/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/iot/model",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/iot/model/import",method:"post",data:e})}function c(e){return Object(r["a"])({url:"/iot/model",method:"put",data:e})}function d(e){return Object(r["a"])({url:"/iot/model/"+e,method:"delete"})}function u(e){return Object(r["a"])({url:"/iot/model/cache/"+e,method:"get"})}function m(e){return Object(r["a"])({url:"/iot/model/synchron",method:"post",data:e})}},b52e:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"padding-left":"20px"}},[i("el-row",{staticClass:"mb8",attrs:{gutter:10}},[i("el-col",{attrs:{span:1.5}},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:share"],expression:"['iot:device:user:share']"}],attrs:{type:"primary",plain:"",icon:"el-icon-share",size:"mini",disabled:0==e.deviceInfo.isOwner||null==e.deviceInfo.isOwner},on:{click:e.shareDevice}},[e._v("分享设备")])],1),i("el-col",{attrs:{span:1.5}},[i("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceUserList,size:"mini"},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{label:"用户编号",align:"center",prop:"userId",width:"100"}}),i("el-table-column",{attrs:{label:"用户名称",align:"center",prop:"userName"}}),i("el-table-column",{attrs:{label:"手机号码",align:"center",prop:"phonenumber",width:"150"}}),i("el-table-column",{attrs:{label:"用户类型",align:"center",prop:"isOwner",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isOwner?i("el-tag",{attrs:{type:"primary"}},[e._v("拥有者")]):i("el-tag",{attrs:{type:"success"}},[e._v("分享")])]}}])}),i("el-table-column",{attrs:{label:"分享时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),i("el-table-column",{attrs:{label:"备注",align:"left",prop:"remark","header-align":"center","min-width":"150"}}),i("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner&&1==e.deviceInfo.isOwner?i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:query"],expression:"['iot:device:user:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(i){return e.handleUpdate(t.row)}}},[e._v("查看")]):e._e(),0==t.row.isOwner&&1==e.deviceInfo.isOwner?i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:remove"],expression:"['iot:device:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v("取消分享")]):e._e()]}}])})],1),i("el-dialog",{attrs:{title:"设备分享",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[i("div",{staticStyle:{"margin-top":"-50px"}},[i("el-divider")],1),1==e.type?i("el-form",{ref:"queryForm",attrs:{model:e.permParams,rules:e.rules,inline:!0,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[i("el-input",{staticStyle:{width:"240px"},attrs:{type:"text",placeholder:"请输入用户手机号码",minlength:"10",clearable:"",size:"small","show-word-limit":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.permParams.phonenumber,callback:function(t){e.$set(e.permParams,"phonenumber",t)},expression:"permParams.phonenumber"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.userQuery}},[e._v("查询用户")])],1)],1):e._e(),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.permsLoading,expression:"permsLoading"}],staticStyle:{"background-color":"#f8f8f9","line-height":"28px"}},[e.message?i("div",{staticStyle:{padding:"20px"}},[e._v(e._s(e.message))]):e._e(),e.form.userId?i("div",{staticStyle:{padding:"15px"}},[i("div",{staticStyle:{"font-weight":"bold","line-height":"28px"}},[e._v("用户信息")]),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户ID：")]),i("span",[e._v(e._s(e.form.userId))]),i("br"),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("手机号码：")]),i("span",[e._v(e._s(e.form.phonenumber))]),i("br"),i("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户名称：")]),i("span",[e._v(e._s(e.form.userName))]),i("br"),i("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("设置用户权限")]),i("el-table",{ref:"multipleTable",attrs:{data:e.sharePermissionList,"highlight-current-row":"",size:"mini"},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[i("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),i("el-table-column",{key:"modelName",attrs:{label:"权限名称",align:"center",prop:"modelName"}}),i("el-table-column",{key:"identifier",attrs:{label:"权限标识",align:"center",prop:"identifier"}}),i("el-table-column",{key:"remark",attrs:{label:"备注信息",align:"left","min-width":"100","header-align":"center",prop:"remark"}})],1),i("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("备注信息")]),i("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",rows:"2"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1):e._e()]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:edit"],expression:"['iot:device:user:edit']"}],attrs:{type:"primary",disabled:!e.form.userId||!e.deviceInfo.deviceId},on:{click:e.submitForm}},[e._v("修改")]),i("el-button",{on:{click:e.closeSelectUser}},[e._v("关 闭")])],1)],1)],1)},n=[],a=i("c7eb"),s=i("1da1"),o=(i("99af"),i("a15b"),i("d81d"),i("01ca")),l=i("b775");function c(e){return Object(l["a"])({url:"/iot/share/list",method:"get",params:e})}function d(e){return Object(l["a"])({url:"/iot/share/shareUser",method:"get",params:e})}function u(e,t){return Object(l["a"])({url:"/iot/share/detail?deviceId="+e+"&userId="+t,method:"get"})}function m(e){return Object(l["a"])({url:"/iot/share",method:"post",data:e})}function h(e){return Object(l["a"])({url:"/iot/share",method:"put",data:e})}function p(e){return Object(l["a"])({url:"/iot/share",method:"delete",data:e})}var f={name:"device-user",dicts:["iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:{deep:!0,immediate:!0,handler:function(e){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceId=this.deviceInfo.deviceId,this.getList())}}},data:function(){return{type:1,message:"",permsLoading:!1,sharePermissionList:[],open:!1,permParams:{userName:void 0,phonenumber:void 0,deviceId:null},rules:{phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{min:11,max:11,message:"手机号码长度为11位",trigger:"blur"}]},loading:!0,total:0,deviceUserList:[],deviceInfo:{},queryParams:{pageNum:1,pageSize:10,deviceName:null,userName:null,userId:null,tenantName:null,isOwner:null},form:{}}},created:function(){this.queryParams.deviceId=this.device.deviceId,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,c(this.queryParams).then((function(t){e.deviceUserList=t.rows,e.total=t.total,e.loading=!1}))},reset:function(){this.form={deviceId:null,userId:null,deviceName:null,userName:null,perms:null,phonenumber:null,remark:null},this.sharePermissionList=[],this.message="",this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset(),this.type=2,u(e.deviceId,e.userId).then((function(e){t.form=e.data,t.getPermissionList(),t.open=!0}))},shareDevice:function(){this.type=1,this.open=!0,this.form={}},handleDelete:function(e){var t=this,i={deviceId:e.deviceId,userId:e.userId};this.$modal.confirm("确认取消分享设备？").then((function(){return p(i)})).then((function(){t.getList(),t.$modal.msgSuccess("取消分享成功")})).catch((function(){}))},userQuery:function(){var e=this;this.$refs["queryForm"].validate((function(t){t&&(e.reset(),e.getShareUser())}))},getShareUser:function(){var e=this;this.permsLoading=!0,this.deviceInfo.deviceId?(this.permParams.deviceId=this.deviceInfo.deviceId,d(this.permParams).then((function(t){t.data?(e.form=t.data,e.getPermissionList()):(e.permsLoading=!1,e.message="查询不到用户信息，或者该用户已经是设备用户")}))):this.$modal.alert("查询不到设备信息，请刷新后重试")},getPermissionList:function(){var e=this;return Object(s["a"])(Object(a["a"])().mark((function t(){var i;return Object(a["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:i=[],e.form.perms&&(i=e.form.perms.split(",")),Object(o["g"])(e.deviceInfo.productId).then((function(t){if(e.sharePermissionList=[{identifier:"ota",modelName:"设备升级",remark:"设备OTA升级"},{identifier:"timer",modelName:"设备定时",remark:"定时执行任务"},{identifier:"log",modelName:"设备日志",remark:"包含事件日志和指令日志"},{identifier:"monitor",modelName:"实时监测",remark:"下发实时监测指令后，图表实时显示设备上报数据"},{identifier:"statistic",modelName:"监测统计",remark:"图表显示存储的历史监测数据"}],e.sharePermissionList=e.sharePermissionList.concat(t.data),i.length>0)for(var r=function(t){for(var r=0;r<i.length;r++)if(e.sharePermissionList[t].identifier==i[r]){e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(e.sharePermissionList[t],!0)}));break}},n=0;n<e.sharePermissionList.length;n++)r(n);e.permsLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},resetUserQuery:function(){this.resetForm("queryForm"),this.reset()},closeSelectUser:function(){this.open=!1,this.resetUserQuery()},handleSelectionChange:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},handleSelectionAll:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},submitForm:function(){var e=this;2==this.type?h(this.form).then((function(t){e.$modal.msgSuccess("更新成功"),e.resetUserQuery(),e.open=!1,e.getList()})):1==this.type&&(this.form.deviceId=this.deviceInfo.deviceId,this.form.deviceName=this.deviceInfo.deviceName,m(this.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.resetUserQuery(),e.open=!1,e.getList()})))}}},v=f,g=i("2877"),b=Object(g["a"])(v,r,n,!1,null,null,null);t["default"]=b.exports}}]);