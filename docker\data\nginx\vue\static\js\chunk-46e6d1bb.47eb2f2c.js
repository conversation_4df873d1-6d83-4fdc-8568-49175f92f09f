(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46e6d1bb"],{9467:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[r("el-form-item",{attrs:{label:e.$t("sip.sipidGen.998538-0")}},[r("el-cascader",{staticStyle:{width:"350px"},attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-9"),prop:"deviceType"}},[r("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("sip.index.998533-14")},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-15"),prop:"channelType"}},[r("el-select",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("sip.index.998533-16")},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-20"),prop:"createNum"}},[r("el-input-number",{staticStyle:{width:"350px"},attrs:{"controls-position":"right",placeholder:e.$t("sip.index.998533-19"),type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("sip.index.998533-21")))]),r("el-button",{on:{click:e.closeDialog}},[e._v(e._s(e.$t("cancel")))])],1)],1)},a=[],i=r("ef6c"),c=r("e2de"),o={name:"SipidDialog",dicts:["video_type","channel_type"],props:{product:{type:Object,default:null}},data:function(){return{loading:!0,title:this.$t("sip.sipidGen.998538-1"),total:0,open:!1,createForm:{city:"",deviceType:"",channelType:"",createNum:1},cityOptions:i["regionData"],city:"",cityCode:""}},created:function(){},methods:{changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=i["CodeToText"][e[0]]+"/"+i["CodeToText"][e[1]]+"/"+i["CodeToText"][e[2]];this.createForm.citycode=t}},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError(this.$t("sip.index.998533-42")):(this.createForm.productId=this.product.productId,this.createForm.productName=this.product.productName,this.createForm.tenantId=this.product.tenantId,this.createForm.tenantName=this.product.tenantName,this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?Object(c["a"])(this.createForm.createNum,this.createForm).then((function(t){if(200===t.code){var r=t.data;e.$emit("addGenEvent",r),e.$modal.msgSuccess(e.$t("sip.sipidGen.998538-2"))}e.open=!1})):this.$message({type:"error",message:this.$t("sip.sipidGen.998538-3")}))},closeDialog:function(){this.open=!1}}},l=o,s=r("2877"),u=Object(s["a"])(l,n,a,!1,null,"aa9c31e2",null);t["default"]=u.exports},e2de:function(e,t,r){"use strict";r.d(t,"g",(function(){return a})),r.d(t,"e",(function(){return i})),r.d(t,"a",(function(){return c})),r.d(t,"d",(function(){return o})),r.d(t,"k",(function(){return l})),r.d(t,"h",(function(){return s})),r.d(t,"c",(function(){return u})),r.d(t,"i",(function(){return p})),r.d(t,"b",(function(){return d})),r.d(t,"f",(function(){return m})),r.d(t,"j",(function(){return h})),r.d(t,"l",(function(){return f}));var n=r("b775");function a(e){return Object(n["a"])({url:"/sip/channel/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"get"})}function c(e,t){return Object(n["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(n["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function s(e,t,r){return Object(n["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:r})}function u(e,t,r){return Object(n["a"])({url:"/sip/player/closeStream/"+e+"/"+t+"/"+r,method:"get"})}function p(e,t,r,a){return Object(n["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+r,method:"get",params:a})}function d(e){return Object(n["a"])({url:"/iot/relation/addOrUp",method:"post",data:e})}function m(e,t){return Object(n["a"])({url:"/sip/talk/getPushUrl/"+e+"/"+t,method:"get"})}function h(e,t){return Object(n["a"])({url:"/sip/talk/broadcast/"+e+"/"+t,method:"get"})}function f(e,t){return Object(n["a"])({url:"/sip/talk/broadcast/stop/"+e+"/"+t,method:"get"})}}}]);