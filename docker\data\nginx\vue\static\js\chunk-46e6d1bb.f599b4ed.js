(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46e6d1bb"],{9467:function(e,t,r){"use strict";r.r(t);var c=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"行政区划"}},[r("el-cascader",{attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),r("el-form-item",{attrs:{label:"设备类型",prop:"deviceType"}},[r("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"通道类型",prop:"channelType"}},[r("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"通道数量",prop:"createNum"}},[r("el-input-number",{staticStyle:{width:"220px"},attrs:{"controls-position":"right",placeholder:"请输入生成通道数量",type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("生 成")]),r("el-button",{on:{click:e.closeDialog}},[e._v("取 消")])],1)],1)},a=[],o=r("ef6c"),n=r("e2de"),i={name:"SipidDialog",dicts:["video_type","channel_type"],props:{product:{type:Object,default:null}},data:function(){return{loading:!0,title:"生成设备编号和通道",total:0,open:!1,devsipid:"",createForm:{city:"",deviceType:"",channelType:"",createNum:1},cityOptions:o["regionData"],city:"",cityCode:""}},created:function(){},methods:{changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=o["CodeToText"][e[0]]+"/"+o["CodeToText"][e[1]]+"/"+o["CodeToText"][e[2]];this.createForm.citycode=t}},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError("通道数量至少一个"):(this.createForm.productId=this.product.productId,this.createForm.productName=this.product.productName,this.createForm.tenantId=this.product.tenantId,this.createForm.tenantName=this.product.tenantName,this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?Object(n["a"])(this.createForm.createNum,this.createForm).then((function(t){e.$modal.msgSuccess("已生成设备编号和通道"),e.devsipid=t.data,e.confirmSelectProduct()})):this.$message({type:"error",message:"请选择地区，设备类型，通道类型！！"}))},confirmSelectProduct:function(){this.open=!1,this.$emit("addGenEvent",this.devsipid)},closeDialog:function(){this.open=!1}}},l=i,s=r("2877"),u=Object(s["a"])(l,c,a,!1,null,"09358636",null);t["default"]=u.exports},e2de:function(e,t,r){"use strict";r.d(t,"e",(function(){return a})),r.d(t,"d",(function(){return o})),r.d(t,"a",(function(){return n})),r.d(t,"c",(function(){return i})),r.d(t,"h",(function(){return l})),r.d(t,"f",(function(){return s})),r.d(t,"b",(function(){return u})),r.d(t,"g",(function(){return p}));var c=r("b775");function a(e){return Object(c["a"])({url:"/sip/channel/list",method:"get",params:e})}function o(e){return Object(c["a"])({url:"/sip/channel/"+e,method:"get"})}function n(e,t){return Object(c["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function i(e){return Object(c["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(c["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function s(e,t,r){return Object(c["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:r})}function u(e,t){return Object(c["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function p(e,t,r,a){return Object(c["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+r,method:"get",params:a})}}}]);