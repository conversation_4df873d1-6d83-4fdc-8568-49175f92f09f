(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a3b7efe2"],{"45bc":function(e,t,o){},"5f33":function(e,t,o){"use strict";o("45bc")},dd7b:function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"login"},[o("el-row",[o("el-col",{attrs:{xs:24}},[o("div",{staticStyle:{color:"#fff","background-color":"#0f73ee",width:"100%",height:"200px","text-align":"center",padding:"15px","font-family":"'微软雅黑'"}},[o("div",{staticStyle:{"font-size":"42px","padding-top":"40px",width:"300px",margin:"0 auto"}},[o("img",{staticStyle:{width:"100px",height:"100px",float:"left"},attrs:{src:e.logo,alt:"logo"}}),o("div",{staticStyle:{float:"left","margin-top":"13px",width:"200px","text-align":"left"}},[o("div",[e._v("FastBee")]),o("div",{staticStyle:{"letter-spacing":"1.5px","font-size":"20px","font-weight":"600","margin-top":"-8px","margin-left":"3px"}},[e._v(" 开源物联网平台")])])])]),o("div",{staticClass:"tabs-login"},[o("el-tabs",{staticClass:"tabs",model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[o("el-tab-pane",{attrs:{label:"账号登录",name:"first"}},[o("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[null!=e.loginForm.bindId?o("el-form-item",[o("div",{staticClass:"alert-box-wrap"},[null!=e.loginForm.bindId?o("div",{staticClass:"alert-message-wrap"},[o("i",{staticClass:"el-icon-warning"}),e._v(" 如果你已经有账号,请直接输入账号进行绑定， ")]):e._e(),o("el-row",[o("el-col",{attrs:{span:10.5}},[null!=e.loginForm.bindId?o("div",{staticClass:"alert-message-wrap"},[e._v(" 如果还没有账号，请先去")]):e._e()]),null!=e.loginForm.bindId?o("router-link",{staticStyle:{"margin-left":"10px","font-size":"14px","font-family":"'微软雅黑'",color:"rgba(41, 96, 197, 0.856)"},attrs:{to:{path:"/register",query:this.$route.query}}},[e._v(" 注册 ")]):e._e()],1)],1)]):e._e(),o("div",{staticStyle:{"margin-bottom":"10px","font-size":"14px","font-family":"'微软雅黑'",color:"#f78e21"}},[e._v("演示账号：fastbee 123456 ")]),o("el-form-item",{attrs:{prop:"username"}},[o("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[o("svg-icon",{staticClass:"input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"password"}},[o("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[o("svg-icon",{staticClass:"input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),e.captchaOnOff?o("el-form-item",{attrs:{prop:"code"}},[o("el-input",{staticStyle:{width:"66%"},attrs:{"auto-complete":"off",placeholder:"验证码",type:"text"},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}},[o("svg-icon",{staticClass:"input-icon",attrs:{slot:"prefix","icon-class":"smscode"},slot:"prefix"})],1),o("div",{staticClass:"login-code"},[o("img",{staticStyle:{float:"right"},attrs:{src:e.codeUrl},on:{click:e.getCode}})])],1):e._e(),o("el-checkbox",{staticStyle:{margin:"0px 0px 25px 0px",color:"#000"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住密码")]),o("el-form-item",{staticStyle:{width:"100%"}},[o("div",{staticStyle:{"margin-bottom":"10px"}},[e.bindAccount?o("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleBind(t)}}},[e.loading?o("span",[e._v("绑 定 中...")]):o("span",[e._v("绑 定")])]):o("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?o("span",[e._v("登 录 中...")]):o("span",[e._v("登 录")])])],1)])],1)],1),o("el-tab-pane",{attrs:{label:"短信登录",name:"second"}},[o("el-form",{ref:"smsLoginForm",staticClass:"login-form",attrs:{model:e.smsLoginForm,rules:e.smsRules}},[o("el-form-item"),o("el-form-item",{attrs:{prop:"phonenumber"}},[o("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"请输入电话号码"},on:{input:e.validatePhoneNumber},model:{value:e.smsLoginForm.phonenumber,callback:function(t){e.$set(e.smsLoginForm,"phonenumber",t)},expression:"smsLoginForm.phonenumber"}},[o("svg-icon",{staticClass:"input-icon",attrs:{slot:"prefix","icon-class":"phone"},slot:"prefix"}),e.isPhoneValid?o("span",[e._v("请输入有效的手机号码！")]):e._e()],1)],1),o("el-form-item",{attrs:{prop:"smsCode"}},[o("el-input",{staticStyle:{width:"66%"},attrs:{"auto-complete":"off",placeholder:"请输入短信验证码",type:"text"},model:{value:e.smsLoginForm.smsCode,callback:function(t){e.$set(e.smsLoginForm,"smsCode",t)},expression:"smsLoginForm.smsCode"}},[o("svg-icon",{staticClass:"input-icon",attrs:{slot:"prefix","icon-class":"smscode"},slot:"prefix"})],1),o("div",{staticClass:"login-code"},[o("el-button",{staticStyle:{float:"right"},attrs:{slot:"append",disabled:""==this.smsLoginForm.phonenumber||e.isDisabled,type:"success"},on:{click:function(t){return t.preventDefault(),e.getSmsCode()}},slot:"append"},[e._v(e._s(e.buttonText))])],1)],1),o("el-form-item",{staticStyle:{width:"100%"}},[o("div",{staticStyle:{"margin-bottom":"10px"}},[o("el-button",{staticStyle:{width:"100%"},attrs:{type:"primary",smsLoading:e.loading},nativeOn:{click:function(t){return t.preventDefault(),e.handleSmsLogin(t)}}},[e.loading?o("span",[e._v("登 录 中...")]):o("span",[e._v("登 录")])])],1)])],1)],1),o("el-form",{staticClass:"login-form"},[o("el-form-item",{staticStyle:{margin:"-30px 0"}},[o("el-row",[null==e.loginForm.bindId?o("el-button",{attrs:{type:"text",wxloading:e.loading},nativeOn:{click:function(t){return t.preventDefault(),e.weChatLogin(t)}}},[o("svg-icon",{staticStyle:{color:"#13ce66"},attrs:{"icon-class":"wechat"}}),e._v(" 微信登录 ")],1):e._e(),e.bindAccount?o("router-link",{staticStyle:{float:"right"},attrs:{to:{path:"/register",query:this.$route.query}}},[e._v("注册绑定账号")]):o("router-link",{staticStyle:{float:"right","font-size":"14px"},attrs:{to:{path:"/register",query:this.$route.query}}},[e._v("注册账号")])],1)],1)],1)],1)],1)])],1),e._m(0)],1)},i=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"el-login-footer"},[o("span",[e._v(" Copyright © 2023 "),o("a",{attrs:{target:"_blank",href:"http://fastbee.cn"}},[e._v("FastBee")]),e._v(" All Rights Reserved. ")])])}],r=(o("14d9"),o("ac1f"),o("00b4"),o("e05f"),o("4309")),s=o.n(r),a=o("7ded"),l=o("852e"),c=o.n(l),m=o("24e5"),d=o.n(m),u="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH\nnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==",g="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY\n7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN\nPuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA\nkM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow\ncSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv\nDQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh\nYhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3\nUP8iWi1Qw0Y=";function p(e){var t=new d.a;return t.setPublicKey(u),t.encrypt(e)}function f(e){var t=new d.a;return t.setPrivateKey(g),t.decrypt(e)}var h=o("5f87"),b={name:"Login",data:function(){return{logo:s.a,codeUrl:"",loginForm:{username:"",password:"",rememberMe:!1,code:"",uuid:"",bindId:""},smsLoginForm:{phonenumber:"",smsCode:"",sourceType:1},smsLoading:!1,activeName:"first",isPhoneValid:!0,isDisabled:!1,countdownTimer:null,buttonText:"发送验证码",remainingSeconds:60,waitTime:61,loginRules:{username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},smsRules:{phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],smsCode:[{required:!0,trigger:"change",message:"验证码不能为空"}]},loading:!1,captchaOnOff:!0,bindAccount:!1,register:!0,redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){var e=this.$route.query.loginId;void 0===e||null===e?(this.checkBind(),this.getCode(),this.checkErrorMsg(),this.getCookie()):this.redirectSocialLogin(e)},methods:{redirectSocialLogin:function(){var e=this,t=this.$route.query,o=t.loginId;Object(a["o"])(o).then((function(t){e.loading=!0,Object(h["e"])(t.token),e.$router.push({path:e.redirect||"/"}).catch((function(){})),e.captchaOnOff&&(e.getCode(),e.loading=!1)}))},checkBind:function(){var e=this,t=this.$route.query,o=t.bindId;void 0===o||null===o?this.bindAccount=!1:(this.bindAccount=!0,Object(a["d"])(o).then((function(t){e.bindAccount=void 0===t.bindAccount||t.bindAccount,e.bindAccount?e.loginForm.bindId=o:(e.loginForm.bindId="",e.$router.push({query:{}}))})))},checkErrorMsg:function(){var e=this,t=this.$route.query.errorId;void 0!==t&&null!==t&&Object(a["g"])(t).then((function(e){})).catch((function(t){e.$router.push({query:{}}),console.log(t)}))},getCode:function(){var e=this;Object(a["f"])().then((function(t){e.captchaOnOff=void 0===t.captchaOnOff||t.captchaOnOff,e.captchaOnOff&&(e.codeUrl="data:image/gif;base64,"+t.img,e.loginForm.uuid=t.uuid)}))},getCookie:function(){var e=c.a.get("username"),t=c.a.get("password"),o=c.a.get("rememberMe"),n=c.a.get("loginId");this.loginForm={username:void 0===e?this.loginForm.username:e,password:void 0===t?this.loginForm.password:f(t),rememberMe:void 0!==o&&Boolean(o),loginId:void 0===n?this.loginForm.loginId:n}},qqLogin:function(){window.location.href="http://localhost:8080/auth/render/qq"},weChatLogin:function(){var e="/prod-api";window.location.href=e+"/auth/render/wechat_open_web"},validatePhoneNumber:function(e){var t=/^1[3456789]\d{9}$/;return t.test(e)},getSmsCode:function(){var e=this;this.validatePhoneNumber(this.smsLoginForm.phonenumber)?Object(a["i"])(this.smsLoginForm.phonenumber).then((function(t){200==t.code?(e.$message({type:"success",message:"获取成功"}),e.countdownTimer=setInterval((function(){e.remainingSeconds>0?(e.remainingSeconds--,e.buttonText="".concat(e.remainingSeconds,"秒后获取")):(clearInterval(e.countdownTimer),e.buttonText="发送验证码",e.isDisabled=!1)}),1e3),e.isDisabled=!0):e.$message({type:"warning",message:"获取失败"})})):this.$message({type:"warning",message:"手机号码格式错误，请输入正确手机号！"})},handleSmsLogin:function(){var e=this;this.$refs.smsLoginForm.validate((function(t){t&&(e.smsLoading=!0,Object(a["n"])(e.smsLoginForm).then((function(t){Object(h["e"])(t.data),e.$router.push({path:"/"}).catch((function(){}))})))}))},handleBind:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.loginForm.rememberMe?(c.a.set("username",e.loginForm.username,{expires:30}),c.a.set("password",p(e.loginForm.password),{expires:30}),c.a.set("rememberMe",e.loginForm.rememberMe,{expires:30})):(c.a.remove("username"),c.a.remove("password"),c.a.remove("rememberMe")),e.loginForm.bindId=e.$route.query.bindId,Object(a["b"])(e.loginForm).then((function(t){Object(h["e"])(t.token),e.$router.push({path:"/"}).catch((function(){}))})).catch((function(){e.loading=!1,e.captchaOnOff&&(e.loading=!1,e.getCode())})))}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.loginForm.rememberMe?(c.a.set("username",e.loginForm.username,{expires:30}),c.a.set("password",p(e.loginForm.password),{expires:30}),c.a.set("rememberMe",e.loginForm.rememberMe,{expires:30})):(c.a.remove("username"),c.a.remove("password"),c.a.remove("rememberMe")),e.$store.dispatch("Login",e.loginForm).then((function(){e.$router.push({path:e.redirect||"/"}).catch((function(){}))})).catch((function(){e.loading=!1,e.captchaOnOff&&e.getCode()})))}))}}},v=b,x=(o("5f33"),o("2877")),w=Object(x["a"])(v,n,i,!1,null,null,null);t["default"]=w.exports}}]);