(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4c609744"],{"0672":function(e,t,r){"use strict";r("0698")},"0698":function(e,t,r){},"5cfa":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"dept-wrap"},[r("el-card",{staticClass:"top-card-wrap"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0}},[r("el-form-item",{attrs:{label:"机构名称",prop:"deptName"}},[r("el-input",{attrs:{placeholder:"请输入机构名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deptName,callback:function(t){e.$set(e.queryParams,"deptName",t)},expression:"queryParams.deptName"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"机构状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{type:"info",plain:"",icon:"el-icon-sort",size:"mini"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1)],1),r("el-card",{staticClass:"card-wrap"},[e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deptList,"row-key":"deptId","default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"}}},[r("el-table-column",{attrs:{prop:"deptName",label:"机构名称",width:"260",align:"left"}}),r("el-table-column",{attrs:{prop:"deptType",label:"机构类型",width:"200",align:"left"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.department_type,value:t.row.deptType}})]}}],null,!1,170071197)}),r("el-table-column",{attrs:{prop:"status",label:"状态",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}],null,!1,2802338569)}),r("el-table-column",{attrs:{prop:"leader",label:"联系人",width:"200",align:"left"}}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}],null,!1,3078210614)}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:add"],expression:"['system:dept:add']"}],attrs:{size:"mini",type:"text",icon:"el-icon-plus"},on:{click:function(r){return e.handleAdd(t.row)}}},[e._v("新增机构")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:edit"],expression:"['system:dept:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("编辑机构")]),r("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini"},on:{command:function(r){return e.handleCommand(r,t.row)}}},[r("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-d-arrow-right"}},[e._v("更多管理")]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{command:"handleUserManage",icon:"el-icon-user"}},[e._v("用户管理")]),r("el-dropdown-item",{attrs:{command:"handleRoleManage",icon:"el-icon-circle-check"}},[e._v("角色管理")]),0!=t.row.parentId?r("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:remove"],expression:"['system:dept:remove']"}],attrs:{command:"handleDelete",size:"mini",type:"text",icon:"el-icon-delete"}},[e._v("删除")]):e._e()],1)],1)]}}],null,!1,3617785330)})],1):e._e(),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("div",{staticStyle:{"margin-top":"-55px"}},[r("el-divider",{staticStyle:{"margin-top":"-30px"}}),r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-row",[0!==e.form.parentId?r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"上级机构",prop:"parentId"}},[r("treeselect",{attrs:{options:e.deptOptions,normalizer:e.normalizer,disabled:"",placeholder:"选择上级机构"},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1)],1):e._e()],1),r("el-form-item",{attrs:{label:"机构名称",prop:"deptName"}},[r("el-input",{attrs:{placeholder:"请输入机构名称"},model:{value:e.form.deptName,callback:function(t){e.$set(e.form,"deptName",t)},expression:"form.deptName"}})],1),r("el-form-item",{attrs:{label:"机构类型",prop:"deptType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择机构类型",clearable:"",size:"small",disabled:null!=this.form.deptId},model:{value:e.form.deptType,callback:function(t){e.$set(e.form,"deptType",t)},expression:"form.deptType"}},e._l(e.deptTypeList,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"联系人",prop:"leader"}},[r("el-input",{attrs:{placeholder:"请输入联系人",maxlength:"20"},model:{value:e.form.leader,callback:function(t){e.$set(e.form,"leader",t)},expression:"form.leader"}})],1),r("el-form-item",{attrs:{label:"机构状态"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(" "+e._s(t.label))])})),1)],1),r("div",{staticClass:"title-wrap"},[e._v("系统账号 ")]),r("el-form-item",{attrs:{prop:"userName"}},[r("span",{attrs:{slot:"label"},slot:"label"},[r("span",{staticClass:"span-box"},[r("span",[e._v(" 登录账号 ")]),r("el-tooltip",{attrs:{content:"请为此机构创建一个账号名",placement:"top"}},[r("i",{staticClass:"el-icon-warning-outline"})])],1)]),r("el-input",{attrs:{placeholder:"请输入登录账号",disabled:null!=this.form.deptId},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1),null==this.form.deptId?r("el-form-item",{attrs:{label:"密码",prop:"password"}},[r("el-input",{attrs:{placeholder:"请输入密码",type:"password","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1):e._e(),null==this.form.deptId?r("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[r("el-input",{attrs:{placeholder:"请输入密码",type:"password","show-password":""},model:{value:e.form.confirmPassword,callback:function(t){e.$set(e.form,"confirmPassword",t)},expression:"form.confirmPassword"}})],1):e._e(),r("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[r("el-input",{attrs:{placeholder:"请输入联系电话",maxlength:"11",disabled:null!=this.form.deptId},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)])],1)],1)},s=[],l=(r("d9e2"),r("d81d"),r("14d9"),r("ac1f"),r("00b4"),r("b775"));function n(e){return Object(l["a"])({url:"/system/dept/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/system/dept/list/exclude/"+e,method:"get"})}function i(e,t){return Object(l["a"])({url:"/system/dept/getDeptType?deptType="+e+"&showOwner="+t,method:"get"})}function d(e){return Object(l["a"])({url:"/system/dept/"+e,method:"get"})}function p(e){return Object(l["a"])({url:"/system/dept",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/system/dept",method:"put",data:e})}function u(e){return Object(l["a"])({url:"/system/dept/"+e,method:"delete"})}var c=r("ca17"),h=r.n(c),f=(r("542c"),{name:"Dept",dicts:["sys_normal_disable","department_type"],components:{Treeselect:h.a},data:function(){var e=this,t=function(t,r,a){e.form.password!==r?a(new Error("两次输入的密码不一致")):a()};return{loading:!0,showSearch:!0,deptList:[],deptOptions:[],deptType:"",title:"",open:!1,isExpandAll:!0,refreshTable:!0,deptTypeList:[],queryParams:{deptName:void 0,status:void 0},form:{},rules:{parentId:[{required:!0,message:"上级机构不能为空",trigger:"blur"}],deptName:[{required:!0,message:"机构名称不能为空",trigger:"blur"}],deptType:[{required:!0,message:"机构类型不能为空",trigger:"blur"}],leader:[{required:!0,message:"联系人不能为空",trigger:"blur"}],userName:[{required:!0,message:"登陆账号不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{trigger:"blur",validator:function(e,t,r){var a=/(?![A-Z]*$)(?![a-z]*$)(?![0-9]*$)(?![^a-zA-Z0-9]*$)/;a.test(t)?r():r(new Error("密码须由大写,小写字母,数字,特殊符中的2种及以上类型组成"))}}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:t,trigger:"blur"}],phone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.deptList=e.handleTree(t.data,"deptId"),e.loading=!1}))},normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.deptId,label:e.deptName,children:e.children}},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deptId:null,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:"0",deptType:0,showOwner:!1},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(e){var t=this;this.reset(),this.form.deptType=null,void 0!=e&&(this.form.parentId=e.deptId),this.open=!0,this.title="添加机构",null==e.deptType?(e.deptType="",this.deptType=e.deptType,this.getDeptType()):(this.deptType=e.deptType,this.getDeptType()),n().then((function(e){t.deptOptions=t.handleTree(e.data,"deptId")}))},getDeptType:function(){var e=this;i(this.deptType,this.form.showOwner).then((function(t){e.deptTypeList=t.data.map((function(e){return{value:e.deptType,label:e.deptTypeName}}))}))},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this;this.reset(),d(e.deptId).then((function(r){t.form=r.data,t.open=!0,t.title="修改机构",t.form.showOwner=!0,null==e.deptType?(e.deptType="",t.deptType=e.deptType,t.getDeptType()):(t.deptType=e.deptType,t.getDeptType()),o(e.deptId).then((function(e){if(t.deptOptions=t.handleTree(e.data,"deptId"),0==t.deptOptions.length){var r={deptId:t.form.parentId,deptName:t.form.parentName,children:[]};t.deptOptions.push(r)}}))}))},handleCommand:function(e,t){switch(e){case"handleUserManage":this.handleUserManage(t);break;case"handleRoleManage":this.handleRoleManage(t);break;case"handleDelete":this.handleDelete(t);break;default:break}},handleUserManage:function(e){var t=e.deptId;this.$router.push("/system/user-manage/user/"+t)},handleRoleManage:function(e){var t=e.deptId;this.$router.push("/system/role-manage/role/"+t)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deptId?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):p(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$modal.confirm('是否确认删除名称为"'+e.deptName+'"的数据项？').then((function(){return u(e.deptId)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}}),y=f,b=(r("0672"),r("2877")),g=Object(b["a"])(y,a,s,!1,null,null,null);t["default"]=g.exports}}]);