(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3ef87020","chunk-688a2787"],{5169:function(e,t,n){"use strict";n("bdbe")},6827:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return s})),n.d(t,"c",(function(){return r})),n.d(t,"e",(function(){return o})),n.d(t,"d",(function(){return c})),n.d(t,"g",(function(){return l})),n.d(t,"f",(function(){return u})),n.d(t,"h",(function(){return d}));var i=n("b775");function a(e,t,n){return Object(i["a"])({url:"/sip/record/devquery/"+e+"/"+t,method:"get",params:n})}function s(e){return Object(i["a"])({url:"/sip/record/serverRecord/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/sip/record/serverRecord/date/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/sip/record/serverRecord/file/list",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/sip/record/serverRecord/device/list",method:"get",params:e})}function l(e,t){return Object(i["a"])({url:"/sip/record/play/"+e+"/"+t,method:"get"})}function u(e,t,n){return Object(i["a"])({url:"/sip/record/download/"+e+"/"+t,method:"get",params:n})}function d(e){return Object(i["a"])({url:"/sip/record/upload",method:"get",params:e})}},"97d6":function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"root"},[n("div",{staticClass:"container-shell"},[n("div",{ref:"container",attrs:{id:"container"}})])])},a=[],s=n("c7eb"),r=n("1da1"),o=(n("a9e3"),n("ac1f"),n("00b4"),n("f5a7")),c={},l={name:"player",props:{playerinfo:{type:Object,default:null}},mounted:function(){console.log(this._uid)},watch:{playerinfo:function(e,t){console.log("playerinfo 发生变化"),this.playinfo=e,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype)}},jessibuca:null,data:function(){return{isPlaybackPause:!1,useWebGPU:!1,isInit:!1,playinfo:{},playtype:"play",operateBtns:{fullscreen:!0,zoom:!0,play:!0,audio:!0}}},beforeDestroy:function(){},created:function(){this.playinfo=this.playerinfo,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype),this.init()},methods:{init:function(){var e=this,t="gpu"in navigator;t?(console.log("支持webGPU"),this.useWebGPU=!0):(console.log("暂不支持webGPU，降级到webgl渲染"),this.useWebGPU=!1);var n=this.isMobile()||this.isPad();n&&window.VConsole&&new window.VConsole,this.$nextTick((function(){e.initplayer()}))},initplayer:function(){this.isPlaybackPause=!1,this.initconf(),c[this._uid]=new window.JessibucaPro({container:this.$refs.container,decoder:"/js/jessibuca-pro/decoder-pro.js",videoBuffer:Number(.2),isResize:!1,useWCS:!1,useMSE:!1,useSIMD:!0,wcsUseVideoRender:!1,loadingText:"加载中",debug:!1,debugLevel:"debug",showBandwidth:!0,showPlaybackOperate:!0,operateBtns:this.operateBtns,forceNoOffscreen:!0,isNotMute:!0,showPerformance:!1,playbackForwardMaxRateDecodeIFrame:4,useWebGPU:this.useWebGPU});var e=c[this._uid];this.initcallback(e),this.isInit=!0},initconf:function(){"play"===this.playtype?this.operateBtns.ptz=!0:this.operateBtns.ptz=!1},initcallback:function(e){var t=this;e.on("error",(function(e){console.log("jessibuca error"),console.log(e)})),e.on("playFailedAndPaused",(function(e,t,n){console.log("playFailedAndPaused",e,n)})),e.on("visibilityChange",(function(e){!0===e?console.log("visibilityChange true"):console.log("visibilityChange false")})),e.on("pause",(function(e){console.log("pause success!"),console.log(e)})),e.on("play",(function(e){console.log("play!"),console.log(e)})),e.on("loading",(function(e){console.log("loading success!"),console.log(e)})),e.on("stats",(function(e){console.log("stats is",e)})),e.on("timeout",(function(e){console.log("timeout:",e)})),e.on("playbackPreRateChange",(function(t){e.forward(t)}));var n=0,i=0;e.on("timeUpdate",(function(e){i=parseInt(e/6e4),n!==i&&n++})),e.on(JessibucaPro.EVENTS.ptz,(function(e){console.log("ptz arrow",e),t.handlePtz(e)})),e.on("crashLog",(function(e){console.log("crashLog is",e)}))},registercallback:function(e,t){c[this._uid]&&c[this._uid].on(e,t)},isMobile:function(){return/iphone|ipad|android.*mobile|windows.*phone|blackberry.*mobile/i.test(window.navigator.userAgent.toLowerCase())},isPad:function(){return/ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(window.navigator.userAgent.toLowerCase())},play:function(e){c[this._uid]&&c[this._uid].play(e)},pause:function(){c[this._uid]&&c[this._uid].pause()},replay:function(e){var t=this;c[this._uid]?c[this._uid].destroy().then((function(){t.initplayer(),t.play(e)})):(this.initplayer(),this.play(e))},handlePtz:function(e){var t=0,n=0;"left"===e?t=2:"right"===e?t=1:"up"===e?n=1:"down"===e&&(n=2);var i={leftRight:t,upDown:n,moveSpeed:125};this.playinfo&&""!==this.playinfo.playtype&&Object(o["c"])(this.playinfo.deviceId,this.playinfo.channelId,i).then(function(){var e=Object(r["a"])(Object(s["a"])().mark((function e(t){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},playback:function(e,t){c[this._uid]&&(c[this._uid].playback(e,{playList:t,fps:25,showControl:!0,isUseFpsRender:!0,isCacheBeforeDecodeForFpsRender:!1,supportWheel:!0}),this.isPlaybackPause=!1)},playbackPause:function(){c[this._uid]&&(c[this._uid].playbackPause(),this.isPlaybackPause=!0)},replayback:function(e,t){var n=this;c[this._uid]?c[this._uid].destroy().then((function(){n.initplayer(),n.playback(e,t)})):(this.initplayer(),this.playback(e,t))},setPlaybackStartTime:function(e){c[this._uid]&&c[this._uid].setPlaybackStartTime(e)},destroy:function(){var e=this;c[this._uid]&&c[this._uid].destroy().then((function(){e.initplayer()}))},close:function(){c[this._uid]&&c[this._uid].close()}}},u=l,d=(n("5169"),n("2877")),h=Object(d["a"])(u,i,a,!1,null,"5072a715",null);t["default"]=h.exports},bdbe:function(e,t,n){},e2de:function(e,t,n){"use strict";n.d(t,"g",(function(){return a})),n.d(t,"e",(function(){return s})),n.d(t,"a",(function(){return r})),n.d(t,"d",(function(){return o})),n.d(t,"k",(function(){return c})),n.d(t,"h",(function(){return l})),n.d(t,"c",(function(){return u})),n.d(t,"i",(function(){return d})),n.d(t,"b",(function(){return h})),n.d(t,"f",(function(){return p})),n.d(t,"j",(function(){return f})),n.d(t,"l",(function(){return y}));var i=n("b775");function a(e){return Object(i["a"])({url:"/sip/channel/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"get"})}function r(e,t){return Object(i["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"delete"})}function c(e,t){return Object(i["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function l(e,t,n){return Object(i["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:n})}function u(e,t,n){return Object(i["a"])({url:"/sip/player/closeStream/"+e+"/"+t+"/"+n,method:"get"})}function d(e,t,n,a){return Object(i["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+n,method:"get",params:a})}function h(e){return Object(i["a"])({url:"/iot/relation/addOrUp",method:"post",data:e})}function p(e,t){return Object(i["a"])({url:"/sip/talk/getPushUrl/"+e+"/"+t,method:"get"})}function f(e,t){return Object(i["a"])({url:"/sip/talk/broadcast/"+e+"/"+t,method:"get"})}function y(e,t){return Object(i["a"])({url:"/sip/talk/broadcast/stop/"+e+"/"+t,method:"get"})}},ecd9:function(e,t,n){"use strict";n.r(t);var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:1.5}},[n("el-select",{attrs:{placeholder:e.$t("views.components.player.deviceLiveStream.48750-1")},on:{change:function(t){return e.changeChannel()}},model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},e._l(e.channelList,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),n("el-col",{staticStyle:{"line-height":"36px"},attrs:{span:1.5}},[n("span",{staticStyle:{"font-size":"14px"}},[e._v(e._s(e.$t("views.components.player.deviceLiveStream.48750-2"))+"：")]),n("el-switch",{attrs:{disabled:""===e.channelId},on:{change:e.startPushStream},model:{value:e.pushStream,callback:function(t){e.pushStream=t},expression:"pushStream"}})],1),n("el-col",{staticStyle:{"line-height":"36px"},attrs:{span:1.5}},[n("span",{staticStyle:{"font-size":"14px"}},[e._v(e._s(e.$t("views.components.player.deviceLiveStream.48750-3"))+"：")]),n("el-switch",{attrs:{disabled:""===e.channelId},on:{change:e.startPlayRecord},model:{value:e.playrecord,callback:function(t){e.playrecord=t},expression:"playrecord"}})],1)],1),n("player",{ref:"player",attrs:{playerinfo:e.playinfo}})],1)},a=[],s=(n("d81d"),n("97d6")),r=n("e2de"),o=n("6827"),c={name:"device-live-stream",components:{player:s["default"]},props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo.channelId&&(this.channelId=this.deviceInfo.channelId,this.changeChannel()),this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.deviceId=this.device.serialNumber)}},data:function(){return{deviceInfo:{},deviceId:"",channelId:"",streamId:"",ssrc:"",playurl:"",playinfo:{playtype:"play"},playrecord:!1,playrecording:!1,playing:!1,pushStream:!1,retrycount:0,channelList:[],queryParams:{pageNum:1,pageSize:10,deviceSipId:null,channelSipId:null}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.deviceId=this.device.serialNumber,this.getList(),this.playinfo={playtype:"play",deviceId:this.device.serialNumber}},beforeDestroy:function(){console.log("beforeDestroy"),this.closeDestroy(!1)},methods:{getList:function(){var e=this;this.loading=!0,Object(r["g"])(this.queryParams).then((function(t){e.channelList=t.rows.map((function(e){return{value:e.channelSipId,label:e.channelName}})),console.log(e.channelList)}))},changeChannel:function(){this.playinfo.channelId=this.channelId,this.startPlayer()},TimeoutCallback:function(){var e=this;this.closeDestroy(!1),this.retrycount=0,setTimeout((function(){e.startPlayer()}),1e3)},startPushStream:function(){this.channelId?(console.log("推流状态: ["+this.pushStream+"]"),this.pushStream?this.startPlayer():this.closeDestroy(!0)):console.log("开始通道: ["+this.channelId+"]")},startPlayRecord:function(){var e=this;console.log("录像状态: ["+this.playrecord+"]"),this.closeDestroy(!0),setTimeout((function(){e.startPlayer()}),500)},startPlayer:function(){var e=this;this.channelId?(this.deviceId=this.queryParams.deviceSipId,this.playing&&this.closeDestroy(!1),this.$refs.player.registercallback("loadingTimeout",this.TimeoutCallback),this.$refs.player.registercallback("delayTimeout",this.TimeoutCallback),this.playrecord?Object(o["g"])(this.deviceId,this.channelId).then((function(t){console.log("开始录像："+e.deviceId+" : "+e.channelId);var n=t.data;e.streamId=n.streamId,e.playurl=n.playurl,e.$refs.player.play(e.playurl),e.playing=!0,e.playrecording=!0,e.pushStream=!0})):Object(r["k"])(this.deviceId,this.channelId).then((function(t){console.log("开始推流: ["+e.channelId+"]");var n=t.data;e.streamId=n.streamId,e.playurl=n.playurl,e.$refs.player.play(e.playurl),e.playing=!0,e.playrecording=!1,e.pushStream=!0}))):console.log("直播录像通道: ["+this.channelId+"]")},closeStream:function(e){var t=this;if(e)this.playing&&this.streamId&&(console.log("强制关闭推流: ["+this.streamId+"]"),Object(r["c"])(this.deviceId,this.channelId,this.streamId).then((function(e){t.streamId="",t.ssrc="",t.playurl="",t.pushStream=!1})),this.playing=!1,this.playrecording=!1);else{if(!0===this.playrecording)return;this.playing&&this.streamId&&(console.log("关闭推流: ["+this.streamId+"]"),Object(r["c"])(this.deviceId,this.channelId,this.streamId).then((function(e){t.streamId="",t.ssrc="",t.playurl="",t.pushStream=!1})),this.playing=!1,this.playrecording=!1)}},closeDestroy:function(e){this.closeStream(e),this.$refs.player.destroy()},destroy:function(){this.$refs.player.destroy()}}},l=c,u=n("2877"),d=Object(u["a"])(l,i,a,!1,null,null,null);t["default"]=d.exports},f5a7:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return s})),n.d(t,"c",(function(){return r}));var i=n("b775");function a(e){return Object(i["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function r(e,t,n){return Object(i["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:n})}}}]);