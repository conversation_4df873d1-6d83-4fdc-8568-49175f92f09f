(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f88fd92"],{"0538":function(e,t,n){"use strict";var r=n("e330"),i=n("59ed"),o=n("861d"),a=n("1a2d"),s=n("f36a"),u=n("40d5"),c=Function,l=r([].concat),f=r([].join),h={},d=function(e,t,n){if(!a(h,t)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";h[t]=c("C,a","return new C("+f(r,",")+")")}return h[t](e,n)};e.exports=u?c.bind:function(e){var t=i(this),n=t.prototype,r=s(arguments,1),a=function(){var n=l(r,s(arguments));return this instanceof a?d(t,n.length,n):t.apply(e,n)};return o(n)&&(a.prototype=n),a}},"20bf":function(e,t,n){"use strict";var r=n("8aa7"),i=n("ebb5").exportTypedArrayStaticMethod,o=n("a078");i("from",o,r)},"2df2":function(e,t,n){var r,i,o=n("3c96").default,a=n("7ec2").default,s=n("c973").default,u=n("448a").default,c=n("a128").default,l=n("61e5").default,f=n("ed6d").default,h=n("7037").default,d=n("970b").default,p=n("5bc3").default;n("d9e2"),n("99af"),n("d81d"),n("14d9"),n("fb6a"),n("c19f"),n("ace4"),n("e9c4"),n("b680"),n("b64b"),n("d3b7"),n("ac1f"),n("25f0"),n("3ca3"),n("466d"),n("498a"),n("cfc3"),n("fd87"),n("8b09"),n("5cc6"),n("907a"),n("9a8c"),n("a975"),n("735e"),n("c1ac"),n("d139"),n("3a7b"),n("986a"),n("1d02"),n("d5d6"),n("20bf"),n("82f8"),n("e91f"),n("60bd"),n("5f96"),n("3280"),n("3fcc"),n("ec97"),n("ca91"),n("25a1"),n("cd26"),n("3c5d"),n("2954"),n("649e"),n("219c"),n("170b"),n("b39a"),n("72f7"),n("1b3b"),n("3d71"),n("c6e3"),n("159b"),n("ddb0"),n("2b3d"),n("bf19"),n("9861"),function(o){r=o,i="function"===typeof r?r.call(t,n,t,e):r,void 0===i||(e.exports=i)}((function(){"use strict";var e,t=function(){function e(){d(this,e)}return p(e,[{key:"on",value:function(e,t,n){var r=this.e||(this.e={});return(r[e]||(r[e]=[])).push({fn:t,ctx:n}),this}},{key:"once",value:function(e,t,n){var r=this;function i(){r.off(e,i);for(var o=arguments.length,a=new Array(o),s=0;s<o;s++)a[s]=arguments[s];t.apply(n,a)}return i._=t,this.on(e,i,n)}},{key:"emit",value:function(e){for(var t=((this.e||(this.e={}))[e]||[]).slice(),n=arguments.length,r=new Array(1<n?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(var o=0;o<t.length;o+=1)t[o].fn.apply(t[o].ctx,r);return this}},{key:"off",value:function(e,t){var n=this.e||(this.e={});if(e){var r=n[e],i=[];if(r&&t)for(var o=0,a=r.length;o<a;o+=1)r[o].fn!==t&&r[o].fn._!==t&&i.push(r[o]);return i.length?n[e]=i:delete n[e],this}Object.keys(n).forEach((function(e){delete n[e]})),delete this.e}}])}(),n="debug",r="warn",i="talkGetUserMediaSuccess",g="talkGetUserMediaFail",m="talkGetUserMediaTimeout",v="talkStreamClose",k="talkStreamError",y="talkStreamInactive",b={talkStreamClose:v,talkStreamError:k,talkStreamInactive:y,talkGetUserMediaTimeout:m},w="open",_="g711a",x="g711u",S="rtp",M="worklet",T={encType:_,packetType:S,rtpSsrc:"0000000000",numberChannels:1,sampleRate:8e3,sampleBitsWidth:16,debug:!1,debugLevel:r,testMicrophone:!1,audioBufferLength:160,engine:M,checkGetUserMediaTimeout:!1,getUserMediaTimeout:1e4};function U(){return(new Date).getTime()}function R(e){var n="";if("object"==h(e))try{n=JSON.stringify(e),n=JSON.parse(n)}catch(t){n=e}else n=e;return n}(function(e){var t,n,r,i,o;t="undefined"!=typeof window&&void 0!==window.document?window.document:{},n=e.exports,r=function(){for(var e,n=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,i=n.length,o={};r<i;r++)if((e=n[r])&&e[1]in t){for(r=0;r<e.length;r++)o[n[0][r]]=e[r];return o}return!1}(),i={change:r.fullscreenchange,error:r.fullscreenerror},o={request:function(e,n){return new Promise(function(i,o){var a=function(){this.off("change",a),i()}.bind(this),s=(this.on("change",a),(e=e||t.documentElement)[r.requestFullscreen](n));s instanceof Promise&&s.then(a).catch(o)}.bind(this))},exit:function(){return new Promise(function(e,n){var i,o;this.isFullscreen?(i=function(){this.off("change",i),e()}.bind(this),this.on("change",i),(o=t[r.exitFullscreen]())instanceof Promise&&o.then(i).catch(n)):e()}.bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,n){e=i[e],e&&t.addEventListener(e,n,!1)},off:function(e,n){e=i[e],e&&t.removeEventListener(e,n,!1)},raw:r},r?(Object.defineProperties(o,{isFullscreen:{get:function(){return Boolean(t[r.fullscreenElement])}},element:{enumerable:!0,get:function(){return t[r.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return Boolean(t[r.fullscreenEnabled])}}}),n?e.exports=o:window.screenfull=o):n?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1}})(e={exports:{}}),e.exports.isEnabled;try{if("object"==("undefined"===typeof WebAssembly?"undefined":h(WebAssembly))&&"function"==typeof WebAssembly.instantiate){var B=new WebAssembly.Module(Uint8Array.of(0,97,115,109,1,0,0,0));B instanceof WebAssembly.Module&&(new WebAssembly.Instance(B),WebAssembly.Instance)}}catch(t){}var E=function(){function e(t){d(this,e);var n=t,r=(t=n.fromSampleRate,n.toSampleRate),i=n.channels,o=n.inputBufferSize;if(!t||!r||!i)throw new Error("Invalid settings specified for the resampler.");this.resampler=null,this.fromSampleRate=t,this.toSampleRate=r,this.channels=i||0,this.inputBufferSize=o,this.initialize()}return p(e,[{key:"initialize",value:function(){this.fromSampleRate==this.toSampleRate?(this.resampler=function(e){return e},this.ratioWeight=1):(this.fromSampleRate<this.toSampleRate?(this.linearInterpolation(),this.lastWeight=1):(this.multiTap(),this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate)}},{key:"bufferSlice",value:function(e){try{return this.outputBuffer.subarray(0,e)}catch(t){try{return this.outputBuffer.length=e,this.outputBuffer}catch(t){return this.outputBuffer.slice(0,e)}}}},{key:"initializeBuffers",value:function(){this.outputBufferSize=Math.ceil(this.inputBufferSize*this.toSampleRate/this.fromSampleRate/this.channels*1.0000004768371582)+this.channels+this.channels;try{this.outputBuffer=new Float32Array(this.outputBufferSize),this.lastOutput=new Float32Array(this.channels)}catch(t){this.outputBuffer=[],this.lastOutput=[]}}},{key:"linearInterpolation",value:function(){var e=this;this.resampler=function(t){var n,r,i,o,a,s,u,c,l,f=t.length,h=e.channels;if(f%h!=0)throw new Error("Buffer was of incorrect sample length.");if(f<=0)return[];for(n=e.outputBufferSize,r=e.ratioWeight,i=e.lastWeight,o=0,a=0,s=0,u=0,c=e.outputBuffer;i<1;i+=r)for(a=i%1,o=1-a,e.lastWeight=i%1,l=0;l<e.channels;++l)c[u++]=e.lastOutput[l]*o+t[l]*a;for(--i,f-=h,s=Math.floor(i)*h;u<n&&s<f;){for(a=i%1,o=1-a,l=0;l<e.channels;++l)c[u++]=t[s+(0<l?l:0)]*o+t[s+(h+l)]*a;i+=r,s=Math.floor(i)*h}for(l=0;l<h;++l)e.lastOutput[l]=t[s++];return e.bufferSlice(u)}}},{key:"multiTap",value:function(){var e=this;this.resampler=function(t){var n,r,i,o,a,s,u,c,l,f,h,d=t.length,p=e.channels;if(d%p!=0)throw new Error("Buffer was of incorrect sample length.");if(d<=0)return[];for(n=e.outputBufferSize,r=[],i=e.ratioWeight,o=0,s=0,c=!e.tailExists,e.tailExists=!1,l=e.outputBuffer,f=0,h=0,a=0;a<p;++a)r[a]=0;do{if(c)for(o=i,a=0;a<p;++a)r[a]=0;else{for(o=e.lastWeight,a=0;a<p;++a)r[a]=e.lastOutput[a];c=!0}for(;0<o&&s<d;){if(u=1+s-h,!(o>=u)){for(a=0;a<p;++a)r[a]+=t[s+(0<a?a:0)]*o;h+=o,o=0;break}for(a=0;a<p;++a)r[a]+=t[s++]*u;h=s,o-=u}if(0!==o){for(e.lastWeight=o,a=0;a<p;++a)e.lastOutput[a]=r[a];e.tailExists=!0;break}for(a=0;a<p;++a)l[f++]=r[a]/i}while(s<d&&f<n);return e.bufferSlice(f)}}},{key:"resample",value:function(e){return this.fromSampleRate==this.toSampleRate?this.ratioWeight=1:(this.fromSampleRate<this.toSampleRate?this.lastWeight=1:(this.tailExists=!1,this.lastWeight=0),this.initializeBuffers(),this.ratioWeight=this.fromSampleRate/this.toSampleRate),this.resampler(e)}}])}(),F=[255,511,1023,2047,4095,8191,16383,32767];function A(e,t,n){for(var r=0;r<n;r++)if(e<=t[r])return r;return n}var C=p((function e(t){d(this,e),this.log=function(e){if(t._opt.debug&&t._opt.debugLevel==n){for(var r,i=t._opt.debugUuid?"[".concat(t._opt.debugUuid,"]"):"",o=arguments.length,a=new Array(1<o?o-1:0),s=1;s<o;s++)a[s-1]=arguments[s];(r=console).log.apply(r,["JessibucaPro".concat(i,":[✅✅✅][").concat(e,"]")].concat(a))}},this.warn=function(e){if(t._opt.debug&&(t._opt.debugLevel==n||t._opt.debugLevel==r)){for(var i,o=t._opt.debugUuid?"[".concat(t._opt.debugUuid,"]"):"",a=arguments.length,s=new Array(1<a?a-1:0),u=1;u<a;u++)s[u-1]=arguments[u];(i=console).log.apply(i,["JessibucaPro".concat(o,":[❗❗❗][").concat(e,"]")].concat(s))}},this.error=function(e){for(var n,r=t._opt.debugUuid?"[".concat(t._opt.debugUuid,"]"):"",i=arguments.length,o=new Array(1<i?i-1:0),a=1;a<i;a++)o[a-1]=arguments[a];(n=console).error.apply(n,["JessibucaPro".concat(r,":[❌❌❌][").concat(e,"]")].concat(o))}})),O=function(){function e(t){d(this,e),this.destroys=[],this.proxy=this.proxy.bind(this),this.master=t}return p(e,[{key:"proxy",value:function(e,t,n){var r=this,i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:{};if(e){if(Array.isArray(t))return t.map((function(t){return r.proxy(e,t,n,i)}));e.addEventListener(t,n,i);var o=function(){"function"==typeof e.removeEventListener&&e.removeEventListener(t,n,i)};return this.destroys.push(o),o}}},{key:"destroy",value:function(){this.master.debug&&this.master.debug.log("Events","destroy"),this.destroys.forEach((function(e){return e()}))}}])}(),L=function(e){function t(e){var n;d(this,t);var r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};n=l(this,t),e&&(n.player=e),n.tag="talk",e=R(T);return n._opt=Object.assign({},e,r),n._opt.sampleRate=parseInt(n._opt.sampleRate,10),n._opt.sampleBitsWidth=parseInt(n._opt.sampleBitsWidth,10),n.audioContext=null,n.gainNode=null,n.recorder=null,n.workletRecorder=null,n.biquadFilter=null,n.userMediaStream=null,n.bufferSize=512,n._opt.audioBufferLength=n.calcAudioBufferLength(),n.audioBufferList=[],n.socket=null,n.socketStatus="notConnect",n.mediaStreamSource=null,n.heartInterval=null,n.checkGetUserMediaTimeout=null,n.wsUrl=null,n.startTimestamp=0,n.sequenceId=0,n.tempTimestamp=null,n.tempRtpBufferList=[],n.events=new O(n),n._initTalk(),n.player||(n.debug=new C(n)),n.log(n.tag,"init",n._opt),n}return f(t,e),p(t,[{key:"destroy",value:function(){this.userMediaStream&&(this.userMediaStream.getTracks&&this.userMediaStream.getTracks().forEach((function(e){e.stop()})),this.userMediaStream=null),this.mediaStreamSource&&(this.mediaStreamSource.disconnect(),this.mediaStreamSource=null),this.recorder&&(this.recorder.disconnect(),this.recorder.onaudioprocess=null),this.biquadFilter&&(this.biquadFilter.disconnect(),this.biquadFilter=null),this.gainNode&&(this.gainNode.disconnect(),this.gainNode=null),this.workletRecorder&&(this.workletRecorder.disconnect(),this.workletRecorder=null),this.socket&&(this.socketStatus===w&&this._sendClose(),this.socket.close(),this.socket=null),this._stopHeartInterval(),this._stopCheckGetUserMediaTimeout(),this.audioContext=null,this.gainNode=null,this.recorder=null,this.audioBufferList=[],this.sequenceId=0,this.wsUrl=null,this.tempTimestamp=null,this.tempRtpBufferList=[],this.startTimestamp=0,this.log("talk","destroy")}},{key:"addRtpToBuffer",value:function(e){var t=e.length+this.tempRtpBufferList.length;t=new Uint8Array(t);t.set(this.tempRtpBufferList,0),t.set(e,this.tempRtpBufferList.length),this.tempRtpBufferList=t}},{key:"downloadRtpFile",value:function(){var e=new Blob([this.tempRtpBufferList]);try{var t=document.createElement("a");t.href=window.URL.createObjectURL(e),t.download=Date.now()+".rtp",t.click()}catch(e){console.error("downloadRtpFile",e)}}},{key:"calcAudioBufferLength",value:function(){var e=this._opt["sampleRate"];return 8*e*.02/8}},{key:"socketStatusOpen",get:function(){return this.socketStatus===w}},{key:"log",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._log.apply(this,["log"].concat(t))}},{key:"warn",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._log.apply(this,["warn"].concat(t))}},{key:"error",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._log.apply(this,["error"].concat(t))}},{key:"_log",value:function(e){for(var t,n=arguments.length,r=new Array(1<n?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];(t=this.player?this.player.debug:this.debug||console)[e].apply(t,r)}},{key:"_getSequenceId",value:function(){return++this.sequenceId}},{key:"_createWebSocket",value:function(){var e=this;return new Promise((function(t,n){var r=e.events.proxy;e.socket=new WebSocket(e.wsUrl),e.socket.binaryType="arraybuffer",e.emit("talkStreamStart"),r(e.socket,"open",(function(){e.socketStatus=w,e.log(e.tag,"websocket open -> do talk"),e.emit("talkStreamOpen"),t(),e._doTalk()})),r(e.socket,"message",(function(t){e.log(e.tag,"websocket message",t.data)})),r(e.socket,"close",(function(t){e.socketStatus="close",e.log(e.tag,"websocket close"),e.emit(v),n(t)})),r(e.socket,"error",(function(t){e.socketStatus="error",e.error(e.tag,"websocket error",t),e.emit(k,t),n(t)}))}))}},{key:"_sendClose",value:function(){}},{key:"_initTalk",value:function(){this._initMethods(),this._opt.engine===M?this._initWorklet():"script"===this._opt.engine&&this._initScriptProcessor(),this.log(this.tag,"audioContext samplerate",this.audioContext.sampleRate)}},{key:"_initMethods",value:function(){this.audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:48e3}),this.gainNode=this.audioContext.createGain(),this.gainNode.gain.value=1,this.biquadFilter=this.audioContext.createBiquadFilter(),this.biquadFilter.type="lowpass",this.biquadFilter.frequency.value=3e3,this.resampler=new E({fromSampleRate:this.audioContext.sampleRate,toSampleRate:this._opt.sampleRate,channels:this._opt.numberChannels,inputBufferSize:this.bufferSize})}},{key:"_initScriptProcessor",value:function(){var e=this,t=this.audioContext.createScriptProcessor||this.audioContext.createJavaScriptNode;this.recorder=t.apply(this.audioContext,[this.bufferSize,this._opt.numberChannels,this._opt.numberChannels]),this.recorder.onaudioprocess=function(t){return e._onaudioprocess(t)}}},{key:"_initWorklet",value:function(){var e,t=this;this.audioContext.audioWorklet.addModule((e=function(){var e=function(e){function t(e){var n;return d(this,t),n=l(this,t),n._cursor=0,n._bufferSize=e.processorOptions.bufferSize,n._buffer=new Float32Array(n._bufferSize),n}return f(t,e),p(t,[{key:"process",value:function(e,t,n){if(!e.length||!e[0].length)return!0;for(var r=0;r<e[0][0].length;r++)this._cursor+=1,this._cursor===this._bufferSize&&(this._cursor=0,this.port.postMessage({eventType:"data",buffer:this._buffer})),this._buffer[this._cursor]=e[0][0][r];return!0}}])}(c(AudioWorkletProcessor));registerProcessor("talk-processor",e)}.toString().trim().match(/^function\s*\w*\s*\([\w\s,]*\)\s*{([\w\W]*?)}$/)[1],e=new Blob([e],{type:"application/javascript"}),URL.createObjectURL(e))).then((function(){var e=new AudioWorkletNode(t.audioContext,"talk-processor",{processorOptions:{bufferSize:t.bufferSize}});e.connect(t.gainNode),e.port.onmessage=function(e){"data"===e.data.eventType&&t._encodeAudioData(e.data.buffer)},t.workletRecorder=e}))}},{key:"_onaudioprocess",value:function(e){e=e.inputBuffer.getChannelData(0),this._encodeAudioData(new Float32Array(e))}},{key:"_encodeAudioData",value:function(e){if(0===e[0]&&0===e[1])this.log(this.tag,"empty audio data");else{var t=this.resampler.resample(e),n=t;if(16===this._opt.sampleBitsWidth?n=function(e){for(var t=e.length,n=new Int16Array(t);t--;){var r=Math.max(-1,Math.min(1,e[t]));n[t]=r<0?32768*r:32767*r}return n}(t):8===this._opt.sampleBitsWidth&&(n=function(e){for(var t=e.length,n=new Int8Array(t);t--;){var r=Math.max(-1,Math.min(1,e[t]));n[t]=parseInt(255/(65535/(32768+(r<0?32768*r:32767*r))),10)}return n}(t)),null!==n.buffer){var r=null;this._opt.encType===_?r=function(e){var t=[];return Array.prototype.slice.call(e).forEach((function(e,n){t[n]=function(e){var t,n,r;return 0<=e?t=213:(t=85,(e=-e-1)<0&&(e=32767)),8<=(n=A(e,F,8))?127^t:(r=n<<4,(r|=n<2?e>>4&15:e>>n+3&15)^t)}(e)})),t}(n):this._opt.encType===x&&(r=function(e){var t=[];return Array.prototype.slice.call(e).forEach((function(e,n){t[n]=function(e){var t=0;t=e<0?(e=132-e,127):(e+=132,255);var n=A(e,F,8);return 8<=n?127^t:(n<<4|e>>n+3&15)^t}(e)})),t}(n));for(var i=Uint8Array.from(r),o=0;o<i.length;o++){var a=this.audioBufferList.length;this.audioBufferList[+a]=i[o],this.audioBufferList.length===this._opt.audioBufferLength&&(this._sendTalkMsg(new Uint8Array(this.audioBufferList)),this.audioBufferList=[])}}}}},{key:"_parseAudioMsg",value:function(e){var t=null;return this._opt.packetType!==S||this._opt.encType!==_&&this._opt.encType!==x?"opus"===this._opt.packetType?t=this.opusPacket(e):"empty"===this._opt.packetType&&(t=e):t=this.rtpPacket(e),t}},{key:"rtpPacket",value:function(e){for(var t,n,r=[],i=0,o=this._opt.rtpSsrc,a=e.length,s=(a=(this._opt.encType===_?i=8:this._opt.encType===x&&(i=0),this.startTimestamp||(this.startTimestamp=U()),n=U()-this.startTimestamp,t=this._getSequenceId(),a+12),r[0]=255&a>>8,r[1]=255&a>>0,r[2]=128,r[3]=128+i,r[4]=t/256,r[5]=t%256,r[6]=n/65536/256,r[7]=n/65536%256,r[8]=n%65536/256,r[9]=n%65536%256,r[10]=o/65536/256,r[11]=o/65536%256,r[12]=o%65536/256,r[13]=o%65536%256,r.concat(u(e))),c=new Uint8Array(s.length),l=0;l<s.length;l++)c[l]=s[l];return c}},{key:"opusPacket",value:function(e){return e}},{key:"_sendTalkMsg",value:function(e){null===this.tempTimestamp&&(this.tempTimestamp=U());var t=U(),n=t-this.tempTimestamp,r=this._parseAudioMsg(e);this.log(this.tag,"'send talk msg and diff is ".concat(n," and byteLength is ").concat(r.byteLength," and length is ").concat(r.length,", and g711 length is ")+e.length),this._opt.packetType===S&&this.addRtpToBuffer(r),r&&(this.socketStatusOpen?this.socket.send(r.buffer):this.emit("tallWebsocketClosedByError")),this.tempTimestamp=t}},{key:"_doTalk",value:function(){this._getUserMedia()}},{key:"_getUserMedia",value:function(){var e=this;this.log(this.tag,"getUserMedia"),void 0===window.navigator.mediaDevices&&(window.navigator.mediaDevices={}),void 0===window.navigator.mediaDevices.getUserMedia&&(this.log(this.tag,"window.navigator.mediaDevices.getUserMedia is undefined and init function"),window.navigator.mediaDevices.getUserMedia=function(e){var t=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return t?new Promise((function(n,r){t.call(navigator,e,n,r)})):Promise.reject(new Error("getUserMedia is not implemented in this browser"))}),this._opt.checkGetUserMediaTimeout&&this._startCheckGetUserMediaTimeout(),window.navigator.mediaDevices.getUserMedia({audio:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1},video:!1}).then((function(t){e.log(e.tag,"getUserMedia success"),e.userMediaStream=t,e.mediaStreamSource=e.audioContext.createMediaStreamSource(t),e.mediaStreamSource.connect(e.biquadFilter),e.recorder?(e.biquadFilter.connect(e.recorder),e.recorder.connect(e.gainNode)):e.workletRecorder&&(e.biquadFilter.connect(e.workletRecorder),e.workletRecorder.connect(e.gainNode)),e.gainNode.connect(e.audioContext.destination),e.emit(i),null===t.oninactive&&(t.oninactive=function(t){e._handleStreamInactive(t)})})).catch((function(t){e.error(e.tag,"getUserMedia error",t.toString()),e.emit(g,t.toString())})).finally((function(){e.log(e.tag,"getUserMedia finally"),e._stopCheckGetUserMediaTimeout()}))}},{key:"_getUserMedia2",value:function(){var e=this;this.log(this.tag,"getUserMedia"),navigator.mediaDevices?navigator.mediaDevices.getUserMedia({audio:!0}).then((function(t){e.log(e.tag,"getUserMedia2 success")})):navigator.getUserMedia({audio:!0},this.log(this.tag,"getUserMedia2 success"),this.log(this.tag,"getUserMedia2 fail"))}},{key:"_getUserMedia3",value:function(){var e=s(a().mark((function e(){var t;return a().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.log(this.tag,"getUserMedia3"),e.prev=1,e.next=4,navigator.mediaDevices.getUserMedia({audio:{latency:!0,noiseSuppression:!0,autoGainControl:!0,echoCancellation:!0,sampleRate:48e3,channelCount:1},video:!1});case 4:t=e.sent,console.log("getUserMedia() got stream:",t),this.log(this.tag,"getUserMedia3 success"),e.next=11;break;case 8:e.prev=8,e.t0=e["catch"](1),this.log(this.tag,"getUserMedia3 fail");case 11:case"end":return e.stop()}}),e,this,[[1,8]])})));function t(){return e.apply(this,arguments)}return t}()},{key:"_handleStreamInactive",value:function(e){this.userMediaStream&&(this.error(this.tag,"stream oninactive"),this.emit(y))}},{key:"_startCheckGetUserMediaTimeout",value:function(){var e=this;this._stopCheckGetUserMediaTimeout(),this.checkGetUserMediaTimeout=setTimeout((function(){e.log(e.tag,"check getUserMedia timeout"),e.emit(m)}),this._opt.getUserMediaTimeout)}},{key:"_stopCheckGetUserMediaTimeout",value:function(){this.checkGetUserMediaTimeout&&(this.log(this.tag,"stop checkGetUserMediaTimeout"),clearTimeout(this.checkGetUserMediaTimeout),this.checkGetUserMediaTimeout=null)}},{key:"_startHeartInterval",value:function(){var e=this;this.heartInterval=setInterval((function(){e.log(e.tag,"heart interval");var t=[35,36,0,0,0,0,0,0];t=new Uint8Array(t);e.socket.send(t.buffer)}),15e3)}},{key:"_stopHeartInterval",value:function(){this.heartInterval&&(this.log(this.tag,"stop heart interval"),clearInterval(this.heartInterval),this.heartInterval=null)}},{key:"startTalk",value:function(e){var t=this;return new Promise((function(n,r){return function(){var e=!1,t=window.navigator;return t?(e=!(!t.mediaDevices||!t.mediaDevices.getUserMedia))||!!(t.getUserMedia||t.webkitGetUserMedia||t.mozGetUserMedia||t.msGetUserMedia):e}()?(t.wsUrl=e,t._opt.testMicrophone?(t._doTalk(),n()):(t._createWebSocket().catch((function(e){r(e)})),t.once(g,(function(){r("getUserMedia fail")})),void t.once(i,(function(){n()})))):r("not support getUserMedia")}))}},{key:"setVolume",value:function(e){var t;e=parseFloat(e).toFixed(2),isNaN(e)||(t=e,e=Math.max(Math.min(t,Math.max(0,1)),Math.min(0,1)),this.gainNode.gain.value=e)}},{key:"getOption",value:function(){return this._opt}},{key:"volume",get:function(){return this.gainNode?parseFloat(100*this.gainNode.gain.value).toFixed(0):null}}])}(t),W=function(e){function t(){var e;d(this,t);var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return e=l(this,t),e.talk=null,e._opt=n,e.LOG_TAG="talk",e.debug=new C(o(e)),e}return f(t,e),p(t,[{key:"destroy",value:function(){this.off(),this.talk&&(this.talk.destroy(),this.talk=null)}},{key:"_initTalk",value:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.talk&&(this.talk.destroy(),this.talk=null),e=Object.assign({},R(this._opt),e);this.talk=new L(null,e),this.debug.log(this.LOG_TAG,"_initTalk",this.talk.getOption()),this._bindTalkEvents()}},{key:"_bindTalkEvents",value:function(){var e=this;Object.keys(b).forEach((function(t){e.talk.on(b[t],(function(n){e.emit(t,n)}))}))}},{key:"startTalk",value:function(e){var t=this,n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(r,i){t._initTalk(n),t.talk.startTalk(e).then((function(){r(),t.talk.once(v,(function(){t.stopTalk().catch((function(e){}))})),t.talk.once(k,(function(){t.stopTalk().catch((function(e){}))})),t.talk.once(y,(function(){t.stopTalk().catch((function(e){}))}))})).catch((function(e){i(e)}))}))}},{key:"stopTalk",value:function(){var e=this;return new Promise((function(t,n){e.talk||n("talk is not init"),e.talk.destroy(),t()}))}},{key:"getTalkVolume",value:function(){var e=this;return new Promise((function(t,n){e.talk||n("talk is not init"),t(e.talk.volume)}))}},{key:"setTalkVolume",value:function(e){var t=this;return new Promise((function(n,r){t.talk||r("talk is not init"),t.talk.setVolume(e/100),n()}))}},{key:"downloadTempRtpFile",value:function(){var e=this;return new Promise((function(t,n){e.talk?(e.talk.downloadRtpFile(),t()):n("talk is not init")}))}}])}(t);W.EVENTS=b,window.JessibucaProTalk=W}))},"36c6":function(e,t,n){function r(t){return e.exports=r=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,r(t)}n("3410"),n("1f68"),n("131a"),e.exports=r,e.exports.__esModule=!0,e.exports["default"]=e.exports},"3c96":function(e,t,n){function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n("d9e2"),e.exports=r,e.exports.__esModule=!0,e.exports["default"]=e.exports},"4a4b":function(e,t,n){function r(t,n){return e.exports=r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports["default"]=e.exports,r(t,n)}n("1f68"),n("131a"),e.exports=r,e.exports.__esModule=!0,e.exports["default"]=e.exports},"4ae1":function(e,t,n){var r=n("23e7"),i=n("d066"),o=n("2ba4"),a=n("0538"),s=n("5087"),u=n("825a"),c=n("861d"),l=n("7c73"),f=n("d039"),h=i("Reflect","construct"),d=Object.prototype,p=[].push,g=f((function(){function e(){}return!(h((function(){}),[],e)instanceof e)})),m=!f((function(){h((function(){}))})),v=g||m;r({target:"Reflect",stat:!0,forced:v,sham:v},{construct:function(e,t){s(e),u(t);var n=arguments.length<3?e:s(arguments[2]);if(m&&!g)return h(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return o(p,r,t),new(o(a,e,r))}var i=n.prototype,f=l(c(i)?i:d),v=o(e,f,t);return c(v)?v:f}})},"4ec9":function(e,t,n){n("6f48")},"5bc3":function(e,t,n){var r=n("a395");function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}function o(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},"61e5":function(e,t,n){n("4ae1");var r=n("36c6"),i=n("6f8f"),o=n("6b58");function a(e,t,n){return t=r(t),o(e,i()?Reflect.construct(t,n||[],r(e).constructor):t.apply(e,n))}e.exports=a,e.exports.__esModule=!0,e.exports["default"]=e.exports},"6b58":function(e,t,n){n("d9e2");var r=n("7037")["default"],i=n("3c96");function o(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return i(e)}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},"6f48":function(e,t,n){"use strict";var r=n("6d61"),i=n("6566");r("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},"6f8f":function(e,t,n){function r(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(e.exports=r=function(){return!!t},e.exports.__esModule=!0,e.exports["default"]=e.exports)()}n("4ae1"),e.exports=r,e.exports.__esModule=!0,e.exports["default"]=e.exports},"8b09":function(e,t,n){var r=n("74e8");r("Int16",(function(e){return function(t,n,r){return e(this,t,n,r)}}))},a128:function(e,t,n){n("d9e2"),n("4ec9"),n("d3b7"),n("3ca3"),n("ddb0");var r=n("36c6"),i=n("4a4b"),o=n("c5f7"),a=n("b17c");function s(t){var n="function"==typeof Map?new Map:void 0;return e.exports=s=function(e){if(null===e||!o(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==n){if(n.has(e))return n.get(e);n.set(e,t)}function t(){return a(e,arguments,r(this).constructor)}return t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),i(t,e)},e.exports.__esModule=!0,e.exports["default"]=e.exports,s(t)}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},b17c:function(e,t,n){n("14d9"),n("4ae1");var r=n("6f8f"),i=n("4a4b");function o(e,t,n){if(r())return Reflect.construct.apply(null,arguments);var o=[null];o.push.apply(o,t);var a=new(e.bind.apply(e,o));return n&&i(a,n.prototype),a}e.exports=o,e.exports.__esModule=!0,e.exports["default"]=e.exports},c5f7:function(e,t,n){function r(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}n("d3b7"),n("25f0"),e.exports=r,e.exports.__esModule=!0,e.exports["default"]=e.exports},cfc3:function(e,t,n){var r=n("74e8");r("Float32",(function(e){return function(t,n,r){return e(this,t,n,r)}}))},ec97:function(e,t,n){"use strict";var r=n("ebb5"),i=n("8aa7"),o=r.aTypedArrayConstructor,a=r.exportTypedArrayStaticMethod;a("of",(function(){var e=0,t=arguments.length,n=new(o(this))(t);while(t>e)n[e]=arguments[e++];return n}),i)},ed6d:function(e,t,n){n("d9e2");var r=n("4a4b");function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},fd87:function(e,t,n){var r=n("74e8");r("Int8",(function(e){return function(t,n,r){return e(this,t,n,r)}}))}}]);