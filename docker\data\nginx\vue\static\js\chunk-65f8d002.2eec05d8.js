(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-65f8d002"],{"09cb":function(e,t,o){"use strict";o.d(t,"a",(function(){return n}));o("d3b7");function n(){return new Promise((function(e,t){if("undefined"!==typeof BMap)return e(BMap),!0;window.onBMapCallback=function(){e(BMap)};var o=document.location.protocol;if("https:"==o){var n=document.createElement("meta");n.httpEquiv="Content-Security-Policy",n.content="upgrade-insecure-requests",n.onerror=t,document.head.appendChild(n)}var i=document.createElement("script");i.type="text/javascript",i.src="http://api.map.baidu.com/api?v=2.0&ak=nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD&s=1&__ec_v__=20190126&callback=onBMapCallback",i.onerror=t,document.head.appendChild(i)}))}},"584f":function(e,t,o){"use strict";o.d(t,"n",(function(){return i})),o.d(t,"t",(function(){return r})),o.d(t,"o",(function(){return a})),o.d(t,"p",(function(){return s})),o.d(t,"m",(function(){return c})),o.d(t,"f",(function(){return l})),o.d(t,"c",(function(){return u})),o.d(t,"g",(function(){return p})),o.d(t,"i",(function(){return d})),o.d(t,"d",(function(){return f})),o.d(t,"u",(function(){return m})),o.d(t,"q",(function(){return y})),o.d(t,"r",(function(){return h})),o.d(t,"h",(function(){return v})),o.d(t,"a",(function(){return b})),o.d(t,"v",(function(){return g})),o.d(t,"b",(function(){return O})),o.d(t,"e",(function(){return w})),o.d(t,"k",(function(){return T})),o.d(t,"l",(function(){return _})),o.d(t,"j",(function(){return S})),o.d(t,"s",(function(){return C}));var n=o("b775");function i(e){return Object(n["a"])({url:"/iot/device/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/iot/device/all",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/iot/device/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function p(e){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function d(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function f(e,t){return Object(n["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function m(e,t){return Object(n["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function y(e){return Object(n["a"])({url:"/iot/record/list",method:"get",params:e})}function h(e){return Object(n["a"])({url:"/iot/record/list",method:"get",params:e})}function v(e){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/iot/device",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/iot/device",method:"put",data:e})}function O(e){return Object(n["a"])({url:"/iot/device/"+e,method:"delete"})}function w(e){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:e})}function T(e){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function _(e){return Object(n["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function S(e){return Object(n["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function C(e){return Object(n["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},"731b":function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"centermap"},[e._m(0),o("div",{staticStyle:{height:"640px","background-color":"#0e2e87"}},[o("dv-border-box-8",[o("div",{ref:"map",staticStyle:{height:"600px",width:"760px",padding:"10px"}})])],1)])},i=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"maptitle"},[o("div",{staticClass:"zuo"}),o("span",{staticClass:"titletext"},[e._v("设备分布图")]),o("div",{staticClass:"you"})])}],r=o("ade3"),a=(o("d81d"),o("14d9"),o("b0c0"),o("09cb")),s=o("584f");o("a00a");var c={data:function(){return{deviceList:[]}},created:function(){this.getAllDevice()},beforeDestroy:function(){this.clearData()},methods:{clearData:function(){this.timer&&(clearInterval(this.timer),this.timer=null)},switper:function(){var e=this;if(!this.timer){var t=function(t){e.getAllDevice()};this.timer=setInterval(t,12e4)}},getAllDevice:function(){var e=this;Object(s["m"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.deviceCount=t.total,e.loadMap(),e.switper()}))},loadMap:function(){var e=this;Object(a["a"])().then((function(){e.getmap()}))},getmap:function(){var e,t=this,o=this.$echarts.init(this.$refs.map);o.on("click",(function(e){e.data.deviceId&&t.$router.push({path:"/iot/device-edit",query:{t:Date.now(),deviceId:e.data.deviceId}})}));var n=function(e,t){for(var o=[],n=0;n<e.length;n++){var i=[e[n].longitude,e[n].latitude];i&&e[n].status==t&&o.push(Object(r["a"])(Object(r["a"])({name:e[n].deviceName,value:i,serialNumber:e[n].serialNumber,status:e[n].status,isShadow:e[n].isShadow,firmwareVersion:e[n].firmwareVersion,networkAddress:e[n].networkAddress,productName:e[n].productName,activeTime:null==e[n].activeTime?"":e[n].activeTime,deviceId:e[n].deviceId},"serialNumber",e[n].serialNumber),"locationWay",e[n].locationWay))}return o};e={tooltip:{trigger:"item",backgroundColor:"rgba(58,73,116,0.7)",textStyle:{color:"rgba(65,235,246,1)"},formatter:function(e){var t='<div style="padding:5px;line-height:28px;">';return t+="设备名称： <span style='color:#FFF'>"+e.data.name+"</span><br />",t+="设备编号： "+e.data.serialNumber+"<br />",t+="设备状态： ",1==e.data.status?t+="<span style='color:#E6A23C'>未激活</span><br />":2==e.data.status?t+="<span style='color:#F56C6C'>禁用</span><br />":3==e.data.status?t+="<span style='color:#67C23A'>在线</span><br />":4==e.data.status&&(t+="<span style='color:#909399'>离线</span><br />"),1==e.data.isShadow?t+="设备影子： <span style='color:#67C23A'>启用</span><br />":t+="设备影子： <span style='color:#909399'>未启用</span><br />",t+="产品名称： "+e.data.productName+"<br />",t+="固件版本： Version "+e.data.firmwareVersion+"<br />",t+="激活时间： "+e.data.activeTime+"<br />",t+="定位方式： ",1==e.data.locationWay?t+="自动定位<br />":2==e.data.locationWay?t+="设备定位<br />":3==e.data.locationWay?t+="自定义位置<br />":t+="未知<br />",t+="所在地址： "+e.data.networkAddress+"<br />",t+="</div>",t}},bmap:{center:[106,37.5],zoom:5,roam:"move",mapStyle:{styleJson:[{featureType:"water",elementType:"all",stylers:{color:"#3863db"}},{featureType:"land",elementType:"all",stylers:{color:"#0e2e87"}},{featureType:"railway",elementType:"all",stylers:{visibility:"off"}},{featureType:"highway",elementType:"all",stylers:{visibility:"off",color:"#fdfdfd"}},{featureType:"highway",elementType:"labels",stylers:Object(r["a"])({visibility:"off"},"visibility","off")},{featureType:"arterial",elementType:"geometry",stylers:{visibility:"off",color:"#fefefe"}},{featureType:"arterial",elementType:"geometry.fill",stylers:{visibility:"off",color:"#fefefe"}},{featureType:"poi",elementType:"all",stylers:Object(r["a"])({visibility:"off"},"visibility","off")},{featureType:"green",elementType:"all",stylers:{visibility:"off"}},{featureType:"subway",elementType:"all",stylers:{visibility:"off"}},{featureType:"manmade",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"local",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"arterial",elementType:"labels",stylers:{visibility:"off"}},{featureType:"boundary",elementType:"all",stylers:{color:"#23cdd8"}},{featureType:"building",elementType:"all",stylers:{visibility:"off",color:"#d1d1d1"}},{featureType:"label",elementType:"labels.text.fill",stylers:{color:"#264194",visibility:"off"}}]}},series:[{type:"scatter",coordinateSystem:"bmap",data:n(this.deviceList,1),symbolSize:10,itemStyle:{color:"#e8fc05"}},{type:"scatter",coordinateSystem:"bmap",data:n(this.deviceList,2),symbolSize:10,itemStyle:{color:"#fc3464"}},{type:"scatter",coordinateSystem:"bmap",data:n(this.deviceList,4),symbolSize:10,itemStyle:{color:"#eee"}},{type:"effectScatter",coordinateSystem:"bmap",data:n(this.deviceList,3),symbolSize:12,showEffectOn:"render",rippleEffect:{brushType:"stroke",scale:5},label:{formatter:"{b}",position:"right",show:!1},itemStyle:{color:"#5de88e",shadowBlur:100,shadowColor:"#333"},zlevel:1}]},e&&o.setOption(e,!0)}}},l=c,u=(o("93c8"),o("2877")),p=Object(u["a"])(l,n,i,!1,null,null,null);t["default"]=p.exports},"93c8":function(e,t,o){"use strict";o("a757")},a00a:function(e,t,o){"use strict";o.r(t),o.d(t,"version",(function(){return p}));var n,i=o("313e");function r(e,t){this._bmap=e,this.dimensions=["lng","lat"],this._mapOffset=[0,0],this._api=t,this._projection=new BMap.MercatorProjection}function a(e,t){return t=t||[0,0],i["util"].map([0,1],(function(o){var n=t[o],i=e[o]/2,r=[],a=[];return r[o]=n-i,a[o]=n+i,r[1-o]=a[1-o]=t[1-o],Math.abs(this.dataToPoint(r)[o]-this.dataToPoint(a)[o])}),this)}function s(){function e(e){this._root=e}return e.prototype=new BMap.Overlay,e.prototype.initialize=function(e){return e.getPanes().labelPane.appendChild(this._root),this._root},e.prototype.draw=function(){},e}r.prototype.type="bmap",r.prototype.dimensions=["lng","lat"],r.prototype.setZoom=function(e){this._zoom=e},r.prototype.setCenter=function(e){this._center=this._projection.lngLatToPoint(new BMap.Point(e[0],e[1]))},r.prototype.setMapOffset=function(e){this._mapOffset=e},r.prototype.getBMap=function(){return this._bmap},r.prototype.dataToPoint=function(e){var t=new BMap.Point(e[0],e[1]),o=this._bmap.pointToOverlayPixel(t),n=this._mapOffset;return[o.x-n[0],o.y-n[1]]},r.prototype.pointToData=function(e){var t=this._mapOffset;return e=this._bmap.overlayPixelToPoint({x:e[0]+t[0],y:e[1]+t[1]}),[e.lng,e.lat]},r.prototype.getViewRect=function(){var e=this._api;return new i["graphic"].BoundingRect(0,0,e.getWidth(),e.getHeight())},r.prototype.getRoamTransform=function(){return i["matrix"].create()},r.prototype.prepareCustoms=function(){var e=this.getViewRect();return{coordSys:{type:"bmap",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:i["util"].bind(this.dataToPoint,this),size:i["util"].bind(a,this)}}},r.prototype.convertToPixel=function(e,t,o){return this.dataToPoint(o)},r.prototype.convertFromPixel=function(e,t,o){return this.pointToData(o)},r.dimensions=r.prototype.dimensions,r.create=function(e,t){var o,a=t.getDom();return e.eachComponent("bmap",(function(e){var c,l=t.getZr().painter,u=l.getViewportRoot();if("undefined"===typeof BMap)throw new Error("BMap api is not loaded");if(n=n||s(),o)throw new Error("Only one bmap component can exist");if(!e.__bmap){var p=a.querySelector(".ec-extension-bmap");p&&(u.style.left="0px",u.style.top="0px",a.removeChild(p)),p=document.createElement("div"),p.className="ec-extension-bmap",p.style.cssText="position:absolute;width:100%;height:100%",a.appendChild(p);var d=e.get("mapOptions");d&&(d=i["util"].clone(d),delete d.mapType),c=e.__bmap=new BMap.Map(p,d);var f=new n(u);c.addOverlay(f),l.getViewportRootOffset=function(){return{offsetLeft:0,offsetTop:0}}}c=e.__bmap;var m=e.get("center"),y=e.get("zoom");if(m&&y){var h=c.getCenter(),v=c.getZoom(),b=e.centerOrZoomChanged([h.lng,h.lat],v);if(b){var g=new BMap.Point(m[0],m[1]);c.centerAndZoom(g,y)}}o=new r(c,t),o.setMapOffset(e.__mapOffset||[0,0]),o.setZoom(y),o.setCenter(m),e.coordinateSystem=o})),e.eachSeries((function(e){"bmap"===e.get("coordinateSystem")&&(e.coordinateSystem=o)})),o&&[o]};var c=r;function l(e,t){return e&&t&&e[0]===t[0]&&e[1]===t[1]}i["extendComponentModel"]({type:"bmap",getBMap:function(){return this.__bmap},setCenterAndZoom:function(e,t){this.option.center=e,this.option.zoom=t},centerOrZoomChanged:function(e,t){var o=this.option;return!(l(e,o.center)&&t===o.zoom)},defaultOption:{center:[104.114129,37.550339],zoom:5,mapStyle:{},mapStyleV2:{},mapOptions:{},roam:!1}});function u(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}i["extendComponentView"]({type:"bmap",render:function(e,t,o){var n=!0,r=e.getBMap(),a=o.getZr().painter.getViewportRoot(),s=e.coordinateSystem,c=function(t,i){if(!n){var r=a.parentNode.parentNode.parentNode,c=[-parseInt(r.style.left,10)||0,-parseInt(r.style.top,10)||0],l=a.style,u=c[0]+"px",p=c[1]+"px";l.left!==u&&(l.left=u),l.top!==p&&(l.top=p),s.setMapOffset(c),e.__mapOffset=c,o.dispatchAction({type:"bmapRoam",animation:{duration:0}})}};function l(){n||o.dispatchAction({type:"bmapRoam",animation:{duration:0}})}r.removeEventListener("moving",this._oldMoveHandler),r.removeEventListener("moveend",this._oldMoveHandler),r.removeEventListener("zoomend",this._oldZoomEndHandler),r.addEventListener("moving",c),r.addEventListener("moveend",c),r.addEventListener("zoomend",l),this._oldMoveHandler=c,this._oldZoomEndHandler=l;var p=e.get("roam");p&&"scale"!==p?r.enableDragging():r.disableDragging(),p&&"move"!==p?(r.enableScrollWheelZoom(),r.enableDoubleClickZoom(),r.enablePinchToZoom()):(r.disableScrollWheelZoom(),r.disableDoubleClickZoom(),r.disablePinchToZoom());var d=e.__mapStyle,f=e.get("mapStyle")||{},m=JSON.stringify(f);JSON.stringify(d)!==m&&(u(f)||r.setMapStyle(i["util"].clone(f)),e.__mapStyle=JSON.parse(m));var y=e.__mapStyle2,h=e.get("mapStyleV2")||{},v=JSON.stringify(h);JSON.stringify(y)!==v&&(u(h)||r.setMapStyleV2(i["util"].clone(h)),e.__mapStyle2=JSON.parse(v)),n=!1}});i["registerCoordinateSystem"]("bmap",c),i["registerAction"]({type:"bmapRoam",event:"bmapRoam",update:"updateLayout"},(function(e,t){t.eachComponent("bmap",(function(e){var t=e.getBMap(),o=t.getCenter();e.setCenterAndZoom([o.lng,o.lat],t.getZoom())}))}));var p="1.0.0"},a757:function(e,t,o){}}]);