(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5f8473f2"],{"1cfd0":function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return r})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"f",(function(){return l})),a.d(t,"b",(function(){return c}));var s=a("b775");function n(e){return Object(s["a"])({url:"/iot/newsCategory/list",method:"get",params:e})}function r(){return Object(s["a"])({url:"/iot/newsCategory/newsCategoryShortList",method:"get"})}function i(e){return Object(s["a"])({url:"/iot/newsCategory/"+e,method:"get"})}function o(e){return Object(s["a"])({url:"/iot/newsCategory",method:"post",data:e})}function l(e){return Object(s["a"])({url:"/iot/newsCategory",method:"put",data:e})}function c(e){return Object(s["a"])({url:"/iot/newsCategory/"+e,method:"delete"})}},"803b":function(e,t,a){"use strict";a("dabb")},"8eba":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"iot-news"},[a("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticClass:"search-card"},[a("div",{staticClass:"form-wrap"},[e.isAdmin?a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"46px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{prop:"title"}},[a("el-input",{attrs:{placeholder:e.$t("system.news.893410-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{prop:"categoryName"}},[a("el-input",{attrs:{placeholder:e.$t("system.news.893410-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.categoryName,callback:function(t){e.$set(e.queryParams,"categoryName",t)},expression:"queryParams.categoryName"}})],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{attrs:{placeholder:e.$t("system.news.893410-9"),clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:Number(e.value)}})})),1)],1),e.searchShow?[a("el-form-item",{attrs:{prop:"isBanner"}},[a("el-select",{attrs:{placeholder:e.$t("system.news.893410-7"),clearable:""},model:{value:e.queryParams.isBanner,callback:function(t){e.$set(e.queryParams,"isBanner",t)},expression:"queryParams.isBanner"}},e._l(e.dict.type.iot_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]:e._e(),e.searchShow?[a("el-form-item",{attrs:{prop:"isTop"}},[a("el-select",{attrs:{placeholder:e.$t("system.news.893410-5"),clearable:""},model:{value:e.queryParams.isTop,callback:function(t){e.$set(e.queryParams,"isTop",t)},expression:"queryParams.isTop"}},e._l(e.dict.type.iot_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)]:e._e()],2):e._e(),a("div",{staticClass:"search-btn-group"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))]),a("el-button",{attrs:{type:"text"},on:{click:e.searchChange}},[a("span",{staticStyle:{color:"#486ff2","margin-left":"14px"}},[e._v(e._s(e.searchShow?e.$t("template.index.891112-113"):e.$t("template.index.891112-112")))]),a("i",{class:{"el-icon-arrow-down":!e.searchShow,"el-icon-arrow-up":e.searchShow},staticStyle:{color:"#486ff2","margin-left":"10px"}})])],1)],1)]),a("el-card",[a("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:add"],expression:"['iot:news:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:e.handleAdd}},[e._v(e._s(e.$t("add")))])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:edit"],expression:"['iot:news:edit']"}],attrs:{plain:"",icon:"el-icon-edit",size:"small",disabled:e.single},on:{click:e.handleUpdate}},[e._v(e._s(e.$t("update")))])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:remove"],expression:"['iot:news:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(e._s(e.$t("del")))])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:export"],expression:"['iot:news:export']"}],attrs:{plain:"",icon:"el-icon-download",size:"small"},on:{click:e.handleExport}},[e._v(e._s(e.$t("export")))])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.newsList,border:!1},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-10"),align:"center",prop:"imgUrl",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-image",{staticStyle:{"border-radius":"5px",height:"80px",width:"120px","margin-bottom":"-5px"},attrs:{lazy:"","preview-src-list":[e.baseUrl+t.row.imgUrl],src:e.baseUrl+t.row.imgUrl,fit:"cover"}})]}}])}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-0"),align:"left",prop:"title","min-width":"220"}}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-2"),align:"center",prop:"categoryName",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:"info"}},[e._v(e._s(t.row.categoryName))])]}}])}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-11"),align:"center",prop:"author",width:"120"}}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-4"),align:"center",prop:"isTop",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isTop}})]}}])}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-6"),align:"center",prop:"isBanner",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isBanner}})]}}])}),a("el-table-column",{attrs:{label:e.$t("system.news.893410-8"),align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:e.$t("creatTime"),align:"center",prop:"createTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:e.$t("remark"),align:"left",prop:"remark","min-width":"250"}}),a("el-table-column",{attrs:{fixed:"right",label:e.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"small",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.openDetailDialog(t.row.newsId)}}},[e._v(e._s(e.$t("look")))]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:edit"],expression:"['iot:news:edit']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("update")))]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:news:remove"],expression:"['iot:news:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("del")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"850px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"75px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.$t("system.news.893410-0"),prop:"title"}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:e.$t("system.news.893410-0")},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),a("el-form-item",{attrs:{label:e.$t("system.news.893410-11"),prop:"author"}},[a("el-input",{staticStyle:{width:"290px"},attrs:{placeholder:e.$t("system.news.893410-12")},model:{value:e.form.author,callback:function(t){e.$set(e.form,"author",t)},expression:"form.author"}})],1),a("el-form-item",{attrs:{label:e.$t("system.news.893410-13"),prop:"remark"}},[a("el-input",{staticStyle:{width:"290px"},attrs:{type:"textarea",placeholder:e.$t("plzInput"),autosize:{minRows:3,maxRows:5}},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:e.$t("system.news.893410-4"),prop:"isTop"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.isTop,callback:function(t){e.$set(e.form,"isTop",t)},expression:"form.isTop"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:e.$t("system.news.893410-6"),prop:"isBanner"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.isBanner,callback:function(t){e.$set(e.form,"isBanner",t)},expression:"form.isBanner"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:e.$t("system.news.893410-8"),prop:"status"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}})],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.$t("system.news.893410-2"),prop:"categoryId"}},[a("el-select",{staticStyle:{width:"282.5px"},attrs:{placeholder:"请选择分类"},on:{change:e.selectCategory},model:{value:e.form.categoryId,callback:function(t){e.$set(e.form,"categoryId",t)},expression:"form.categoryId"}},e._l(e.categoryList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),a("el-form-item",{attrs:{label:e.$t("system.news.893410-10")}},[a("imageUpload",{ref:"image-upload",attrs:{value:e.form.imgUrl,limit:1,fileSize:1},on:{input:function(t){return e.getImagePath(t)}}})],1)],1)],1),a("el-form-item",{attrs:{label:e.$t("system.news.893410-15")}},[a("editor",{staticStyle:{width:"697px"},attrs:{"min-height":192},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("confirm")))]),a("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1),a("el-dialog",{attrs:{title:e.form.title,visible:e.openDetail,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.openDetail=t}}},[a("div",{staticStyle:{"margin-bottom":"15px"}},[a("el-tag",{attrs:{size:"mini",effect:"dark",type:"success"}},[e._v(e._s(e.form.categoryName))]),a("span",{staticStyle:{"margin-left":"15px"}},[e._v(e._s(e.form.createTime))])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loadingDetail,expression:"loadingDetail"}],staticClass:"content"},[a("div",{domProps:{innerHTML:e._s(e.form.content)}})]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.closeDetail}},[e._v(e._s(e.$t("close")))])],1)])],1)},n=[],r=a("5530"),i=(a("d81d"),a("b0c0"),a("b775"));function o(e){return Object(i["a"])({url:"/iot/news/list",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/news/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/news",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/iot/news",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/iot/news/"+e,method:"delete"})}var d=a("1cfd0"),p=a("0835"),h={name:"News",dicts:["iot_yes_no"],components:{imageUpload:p["a"]},data:function(){return{isAdmin:!1,loadingDetail:!1,openDetail:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,searchShow:!1,total:0,newsList:[],title:"",open:!1,categoryList:[],queryParams:{pageNum:1,pageSize:10,title:null,isTop:null,isBanner:null,categoryName:null,status:1},form:{},baseUrl:"/prod-api",rules:{title:[{required:!0,message:this.$t("system.news.893410-16"),trigger:"blur"}],content:[{required:!0,message:this.$t("system.news.893410-17"),trigger:"blur"}],categoryId:[{required:!0,message:this.$t("system.news.893410-18"),trigger:"blur"}],author:[{required:!0,message:this.$t("system.news.893410-19"),trigger:"blur"}]}}},created:function(){this.getList(),this.init()},methods:{init:function(){var e=this;-1===this.$store.state.user.roles.indexOf("tenant")&&-1===this.$store.state.user.roles.indexOf("general")&&(this.isAdmin=!0,Object(d["e"])().then((function(t){e.categoryList=t.data})))},getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.newsList=t.rows,e.total=t.total,e.loading=!1}))},getImagePath:function(e){this.form.imgUrl=e},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={newsId:null,title:null,content:this.$t("system.news.893410-20"),imgUrl:"",isTop:null,isBanner:null,categoryId:null,categoryName:null,status:0,author:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.newsId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=this.$t("system.news.893410-21")},handleUpdate:function(e){var t=this;this.reset();var a=e.newsId||this.ids;l(a).then((function(e){t.form=e.data,t.open=!0,t.title=t.$t("system.news.893410-22")}))},submitForm:function(){var e=this;console.log(this.form),null!=this.form.imgUrl&&""!=this.form.imgUrl?this.$refs["form"].validate((function(t){t&&(null!=e.form.newsId?u(e.form).then((function(t){e.$modal.msgSuccess(e.$t("updateSuccess")),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess(e.$t("addSuccess")),e.open=!1,e.getList()})))})):this.$modal.msgError(this.$t("system.news.893410-23"))},handleDelete:function(e){var t=this,a=e.newsId||this.ids;this.$modal.confirm(this.$t("system.news.893410-24",[a])).then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("delSuccess"))})).catch((function(){}))},handleExport:function(){this.download("iot/news/export",Object(r["a"])({},this.queryParams),"news_".concat((new Date).getTime(),".xlsx"))},selectCategory:function(e){for(var t=0;t<this.categoryList.length;t++)if(this.categoryList[t].id==e)return void(this.form.categoryName=this.categoryList[t].name)},openDetailDialog:function(e){var t=this;this.openDetail=!0,this.loadingDetail=!0,l(e).then((function(e){t.form=e.data,t.openDetail=!0,t.loadingDetail=!1}))},closeDetail:function(){this.titleDetail=this.$t("system.news.893410-25"),this.openDetail=!1,this.reset()},searchChange:function(){this.searchShow=!this.searchShow}}},f=h,y=(a("803b"),a("2877")),w=Object(y["a"])(f,s,n,!1,null,"d74e8502",null);t["default"]=w.exports},dabb:function(e,t,a){}}]);