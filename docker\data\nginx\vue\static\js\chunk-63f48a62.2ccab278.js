(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63f48a62"],{"30fc":function(t,e,r){"use strict";r.r(e);var o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("el-dialog",{attrs:{title:t.$t("sip.product-list.998536-0"),visible:t.open,width:"800px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[r("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:t.$t("product.index.091251-1"),clearable:"",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":t.rowClick}},[r("el-table-column",{attrs:{label:t.$t("select"),width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:t.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:t.$t("product.index.091251-0"),align:"left",prop:"productName","min-width":"160"}}),r("el-table-column",{attrs:{label:t.$t("product.index.091251-2"),align:"left",prop:"categoryName","min-width":"140"}}),r("el-table-column",{attrs:{label:t.$t("sip.product-list.998536-1"),align:"left",prop:"tenantName","min-width":"120"}}),r("el-table-column",{attrs:{label:t.$t("sip.product-list.998536-2"),align:"center",prop:"networkMethod","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("dict-tag",{attrs:{options:t.dict.type.iot_network_method,value:e.row.networkMethod}})]}}])}),r("el-table-column",{attrs:{label:t.$t("sip.mediaServer.998535-4"),align:"center",prop:"createTime",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("span",[t._v(t._s(t.parseTime(e.row.createTime,"{y}-{m}-{d}")))])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectProduct}},[t._v(t._s(t.$t("confirm")))]),r("el-button",{attrs:{type:"info"},on:{click:t.closeDialog}},[t._v(t._s(t.$t("close")))])],1)],1)},n=[],i=(r("a9e3"),r("9b9c")),a={name:"SipProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:3,networkMethod:null}}},created:function(){},methods:{getList:function(){var t=this;this.loading=!0,Object(i["g"])(this.queryParams).then((function(e){for(var r=0;r<e.rows.length;r++)e.rows[r].isSelect=!1;t.productList=e.rows,t.total=e.total,0!=t.productId&&t.setRadioSelected(t.productId),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(t){null!=t&&(this.setRadioSelected(t.productId),this.product=t)},setRadioSelected:function(t){for(var e=0;e<this.productList.length;e++)this.productList[e].productId==t?this.productList[e].isSelect=!0:this.productList[e].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},u=a,l=r("2877"),c=Object(l["a"])(u,o,n,!1,null,null,null);e["default"]=c.exports},"9b9c":function(t,e,r){"use strict";r.d(e,"g",(function(){return n})),r.d(e,"h",(function(){return i})),r.d(e,"f",(function(){return a})),r.d(e,"a",(function(){return u})),r.d(e,"i",(function(){return l})),r.d(e,"e",(function(){return c})),r.d(e,"b",(function(){return s})),r.d(e,"d",(function(){return d})),r.d(e,"c",(function(){return p}));var o=r("b775");function n(t){return Object(o["a"])({url:"/iot/product/list",method:"get",params:t})}function i(t){return Object(o["a"])({url:"/iot/product/shortList",method:"get",params:t})}function a(t){return Object(o["a"])({url:"/iot/product/"+t,method:"get"})}function u(t){return Object(o["a"])({url:"/iot/product",method:"post",data:t})}function l(t){return Object(o["a"])({url:"/iot/product",method:"put",data:t})}function c(t){return Object(o["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function s(t){return Object(o["a"])({url:"/iot/product/status",method:"put",data:t})}function d(t){return Object(o["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(o["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}}}]);