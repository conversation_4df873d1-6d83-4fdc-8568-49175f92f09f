(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3339dee9","chunk-101c9b3f","chunk-5344ade2","chunk-d5c9c454","chunk-50d5d644","chunk-00213f14","chunk-0966317c","chunk-6b984810","chunk-03fb653e","chunk-4145a4bd","chunk-2ee8ac7c","chunk-722c5e57","chunk-e0347614","chunk-46e6d1bb","chunk-2d0d6012","chunk-2d229411"],{"01ca":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"g",(function(){return s})),a.d(t,"a",(function(){return o})),a.d(t,"e",(function(){return l})),a.d(t,"i",(function(){return d})),a.d(t,"c",(function(){return c})),a.d(t,"b",(function(){return u})),a.d(t,"h",(function(){return p}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/model/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/model/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/model/permList/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/model",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/model/import",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/iot/model",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/model/"+e,method:"delete"})}function u(e){return Object(i["a"])({url:"/iot/model/cache/"+e,method:"get"})}function p(e){return Object(i["a"])({url:"/iot/model/synchron",method:"post",data:e})}},"09cb":function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));a("d3b7");function i(){return new Promise((function(e,t){if("undefined"!==typeof BMap)return e(BMap),!0;window.onBMapCallback=function(){e(BMap)};var a=document.location.protocol;if("https:"==a){var i=document.createElement("meta");i.httpEquiv="Content-Security-Policy",i.content="upgrade-insecure-requests",i.onerror=t,document.head.appendChild(i)}var n=document.createElement("script");n.type="text/javascript",n.src="http://api.map.baidu.com/api?v=2.0&ak=nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD&s=1&__ec_v__=20190126&callback=onBMapCallback",n.onerror=t,document.head.appendChild(n)}))}},"0bc2":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/runtime/service/invoke",method:"post",data:e})}function r(e){return Object(i["a"])({url:"/iot/runtime/funcLog",method:"get",params:e})}},"0ddb":function(e,t,a){"use strict";a("41f80")},"1b7a":function(e,t,a){"use strict";a("abf5")},"1c4f":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[e.isSubDev?a("el-form-item",{attrs:{label:"请选择设备从机:","label-width":"120px"}},[a("el-select",{attrs:{placeholder:"请选择设备从机"},on:{change:e.selectSlave},model:{value:e.queryParams.slaveId,callback:function(t){e.$set(e.queryParams,"slaveId",t)},expression:"queryParams.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+"   (从机地址:"+e.slaveId+")",value:e.slaveId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"日志类型",prop:"funType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.funType,callback:function(t){e.$set(e.queryParams,"funType",t)},expression:"queryParams.funType"}},e._l(e.dict.type.iot_function_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identify"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identify,callback:function(t){e.$set(e.queryParams,"identify",t)},expression:"queryParams.identify"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:e.showName,align:"center",prop:"identify"}}),a("el-table-column",{attrs:{label:"指令类型",align:"center",prop:"funType",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_function_type,value:t.row.funType}})]}}])}),a("el-table-column",{attrs:{label:"设置值",align:"center",prop:"funValue"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),a("el-table-column",{attrs:{label:"下发时间",align:"center",prop:"createTime"}}),a("el-table-column",{attrs:{label:"下发结果描述",align:"center",prop:"resultMsg"}}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:remove"],expression:"['iot:log:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},n=[],r=a("5530"),s=(a("d81d"),a("dc9c")),o=(a("01ca"),{name:"device-func",dicts:["iot_function_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.isSubDev=this.deviceInfo.subDeviceList&&this.deviceInfo.subDeviceList.length>0,this.showName=this.isSubDev?"寄存器地址":"标识符",this.queryParams.deviceId=this.deviceInfo.deviceId,this.queryParams.slaveId=this.deviceInfo.slaveId,this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.slaveList=e.subDeviceList,this.getList())}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,logList:[],title:"",open:!1,deviceInfo:{},daterangeTime:[],queryParams:{pageNum:1,pageSize:10,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,slaveId:null},form:{},isSubDev:!1,showName:null,slaveList:[],rules:{identify:[{required:!0,message:"标识符不能为空",trigger:"blur"}],funType:[{required:!0,message:"1==服务下发，2=属性获取，3.OTA升级不能为空",trigger:"change"}],funValue:[{required:!0,message:"日志值不能为空",trigger:"blur"}],serialNumber:[{required:!0,message:"设备编号不能为空",trigger:"blur"}]}}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]),this.queryParams.slaveId&&(this.queryParams.serialNumber=this.queryParams.serialNumber+"_"+this.queryParams.slaveId),Object(s["b"])(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,identify:null,funType:null,funValue:null,messageId:null,deviceName:null,serialNumber:null,mode:null,userId:null,resultMsg:null,resultCode:null,createBy:null,createTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除设备服务下发日志编号为"'+a+'"的数据项？').then((function(){return Object(s["a"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/log/export",Object(r["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))},selectSlave:function(){}}}),l=o,d=a("2877"),c=Object(d["a"])(l,i,n,!1,null,null,null);t["default"]=c.exports},2544:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return s}));var i=a("b775");function n(e,t){return Object(i["a"])({url:"/iot/share/detail?deviceId="+e+"&userId="+t,method:"get"})}function r(e){return Object(i["a"])({url:"/iot/deviceUser",method:"post",data:e})}function s(e){return Object(i["a"])({url:"/iot/deviceUser/addDeviceUsers",method:"post",data:e})}},"377d":function(e,t,a){"use strict";a("a4a0")},"38da":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"g",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"c",(function(){return d})),a.d(t,"e",(function(){return c}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/temp/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/temp/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/temp",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/temp",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/iot/temp/"+e,method:"delete"})}function d(e){return Object(i["a"])({url:"/iot/temp/getTemp",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/iot/temp/getTempByPid",method:"get",params:e})}},"41f80":function(e,t,a){},"466d":function(e,t,a){"use strict";var i=a("c65b"),n=a("d784"),r=a("825a"),s=a("7234"),o=a("50c4"),l=a("577e"),d=a("1d80"),c=a("dc4a"),u=a("8aa5"),p=a("14c3");n("match",(function(e,t,a){return[function(t){var a=d(this),n=s(t)?void 0:c(t,e);return n?i(n,t,a):new RegExp(t)[e](l(a))},function(e){var i=r(this),n=l(e),s=a(t,i,n);if(s.done)return s.value;if(!i.global)return p(i,n);var d=i.unicode;i.lastIndex=0;var c,m=[],h=0;while(null!==(c=p(i,n))){var f=l(c[0]);m[h]=f,""===f&&(i.lastIndex=u(n,o(i.lastIndex),d)),h++}return 0===h?null:m}]}))},"584f":function(e,t,a){"use strict";a.d(t,"l",(function(){return n})),a.d(t,"q",(function(){return r})),a.d(t,"m",(function(){return s})),a.d(t,"n",(function(){return o})),a.d(t,"k",(function(){return l})),a.d(t,"f",(function(){return d})),a.d(t,"c",(function(){return c})),a.d(t,"g",(function(){return u})),a.d(t,"i",(function(){return p})),a.d(t,"d",(function(){return m})),a.d(t,"r",(function(){return h})),a.d(t,"o",(function(){return f})),a.d(t,"p",(function(){return v})),a.d(t,"h",(function(){return y})),a.d(t,"a",(function(){return b})),a.d(t,"s",(function(){return g})),a.d(t,"b",(function(){return w})),a.d(t,"e",(function(){return x})),a.d(t,"j",(function(){return I}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(){return Object(i["a"])({url:"/iot/device/all",method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function p(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function m(e,t){return Object(i["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function h(e){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+e,method:"post"})}function f(){return Object(i["a"])({url:"",method:"get"})}function v(){return Object(i["a"])({url:"",method:"get"})}function y(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function b(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function g(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function w(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function x(e){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:e})}function I(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}},"5daf":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"running-status H100"},[a("div",[a("el-tabs",{staticStyle:{flex:"1",height:"800px","margin-bottom":"5px"},attrs:{type:"border-card"},on:{"tab-click":e.runtimeClick},model:{value:e.runtimeName,callback:function(t){e.runtimeName=t},expression:"runtimeName"}},[a("el-tab-pane",{attrs:{label:"从机实时状态",name:"slave"}},[a("el-tabs",{staticStyle:{"margin-top":"-1px",height:"800px","margin-bottom":"5px"},attrs:{type:"card"},on:{"tab-click":e.handleClick},model:{value:e.thingsType,callback:function(t){e.thingsType=t},expression:"thingsType"}},[a("el-tab-pane",{attrs:{label:"属性上报",name:"prop"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},e._l(e.runningData,(function(t,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{xs:24,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticStyle:{padding:"0px",height:"90px"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[e._v(e._s(t.name)+"("+e._s(t.id)+")")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[e._v(e._s(t.value))]),t.datatype.unit?a("span",[e._v(e._s(t.datatype.unit||t.datatype.unitName))]):e._e()])]),a("div",[e._v("时间："+e._s(t.ts))])])],1)})),1)],1)],1),a("el-tab-pane",{attrs:{label:"服务下发",name:"function"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},[e._l(e.functionData,(function(t,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{":xs":17,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticClass:"elcard",staticStyle:{height:"90px"},attrs:{shadow:"hover"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[e._v(" "+e._s(t.name)+" ")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[e._v(e._s(t.value))]),t.datatype.unit?a("span",[e._v(e._s(t.datatype.unit))]):e._e(),a("el-button",{staticStyle:{float:"right","margin-right":"-5px",padding:"3px 5px"},attrs:{type:"primary",plain:"",icon:"el-icon-s-promotion",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.editFunc(t)}}},[e._v("发送")])],1)]),a("div",[a("span",[e._v("时间："+e._s(t.ts))])])])],1)})),a("el-col",{staticClass:"phone-main",attrs:{":xs":7,sm:12,md:12,lg:8,xl:6}},[a("div",{staticClass:"phone"},[a("div",{staticClass:"phone-container"},[a("div",{staticClass:"phone-title"},[e._v("设 备 指 令")]),a("div",{ref:"logContent",staticClass:"log-content"},[a("el-scrollbar",{ref:"scrollContent",staticStyle:{height:"100%"}},e._l(e.logList,(function(t,i){return a("ul",{key:i},[a("li",[a("a",{staticStyle:{float:"left","text-align":"left"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.createTime))]),a("div",{staticClass:"spa"},[a("span",{staticClass:"lable-s1"},[e._v("服务下发:")]),e._v(" "+e._s(t.modelName)+": "+e._s(t.showValue)+" ")])]),a("a",{staticStyle:{float:"right","text-align":"right"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.replyTime))]),a("div",{class:{fail:201==t.resultCode,wait:203==t.resultCode}},[a("span",{staticClass:"lable-s1"},[e._v("设备应答:")]),e._v(" "+e._s(t.resultMsg)+" ")])])])])})),0)],1)])])])],2),a("el-empty",{directives:[{name:"show",rawName:"v-show",value:0==e.runningData.length,expression:"runningData.length == 0"}],attrs:{description:"暂无数据"}})],1)],1),a("el-tab-pane",{attrs:{label:"遥控指令",name:"remote"}},[a("el-main",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"H100",staticStyle:{position:"relative"}},[a("el-row",{staticClass:"row-list",attrs:{gutter:20}},[e._l(e.controlData,(function(t,i){return a("el-col",{key:i,staticStyle:{"margin-bottom":"10px"},attrs:{":xs":17,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticClass:"elcard",staticStyle:{height:"90px"},attrs:{shadow:"hover"}},[a("div",{staticClass:"head"},[a("div",{staticClass:"title"},[e._v(" "+e._s(t.name)+" ")]),a("div",{staticClass:"name"},[a("span",{staticStyle:{color:"#0f73ee"}},[e._v(e._s(t.value))]),t.datatype.unit?a("span",[e._v(e._s(t.datatype.unit))]):e._e(),a("el-button",{staticStyle:{float:"right","margin-right":"-5px",padding:"3px 5px"},attrs:{type:"primary",plain:"",icon:"el-icon-s-promotion",size:"mini"},on:{click:function(a){return a.stopPropagation(),e.editFunc(t)}}},[e._v("发送")])],1)]),a("div",[a("span",[e._v("时间："+e._s(t.ts))])])])],1)})),a("el-col",{staticClass:"phone-main",attrs:{":xs":7,sm:12,md:12,lg:8,xl:6}},[a("div",{staticClass:"phone"},[a("div",{staticClass:"phone-container"},[a("div",{staticClass:"phone-title"},[e._v("设 备 指 令")]),a("div",{ref:"logContent",staticClass:"log-content"},[a("el-scrollbar",{ref:"scrollContent",staticStyle:{height:"100%"}},e._l(e.logList,(function(t,i){return a("ul",{key:i},[a("li",[a("a",{staticStyle:{float:"left","text-align":"left"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.createTime))]),a("div",{staticClass:"spa"},[a("span",{staticClass:"lable-s1"},[e._v("服务下发:")]),e._v(" "+e._s(t.modelName)+" :"+e._s(t.showValue)+" ")])]),a("a",{staticStyle:{float:"right","text-align":"right"},attrs:{href:"#"}},[a("div",{staticClass:"time"},[e._v(e._s(t.replyTime))]),a("div",{class:{fail:201==t.resultCode,wait:203==t.resultCode}},[a("span",{staticClass:"lable-s1"},[e._v("设备应答:")]),e._v(" "+e._s(t.resultMsg)+" ")])])])])})),0)],1)])])])],2),a("el-empty",{directives:[{name:"show",rawName:"v-show",value:0==e.runningData.length,expression:"runningData.length == 0"}],attrs:{description:"暂无数据"}})],1)],1),a("el-tab-pane",{attrs:{disabled:"",name:"slave"}},[a("span",{staticStyle:{"margin-left":"50px"},attrs:{slot:"label"},slot:"label"},[a("span",{ref:"statusTitle",staticStyle:{color:"#409eff","margin-right":"30px"}},[e._v(e._s(e.title))]),a("el-select",{attrs:{placeholder:"请选择设备从机",size:"mini"},on:{change:e.selectSlave},model:{value:e.params.slaveId,callback:function(t){e.$set(e.params,"slaveId",t)},expression:"params.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+"   ("+e.slaveId+")",value:e.slaveId}})})),1)],1)])],1)],1),a("el-tab-pane",{attrs:{label:"网关实时状态",name:"gateway"}},[a("el-row",{attrs:{gutter:120}},[a("el-col",{staticStyle:{"margin-bottom":"50px"},attrs:{xs:24,sm:24,md:24,lg:14,xl:10}},[a("el-descriptions",{staticStyle:{"margin-bottom":"50px"},attrs:{column:1,border:""}},[a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-menu"}),e._v(" 设备模式 ")]),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v(e._s(e.title))])],2),e._l(this.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串 "+(t.datatype.unit?"，单位："+t.datatype.unit:""),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"2px 0 0 5px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"integer"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"4px 0 0 10px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e()],2)}))],2),1==e.deviceInfo.isShadow&&3!=e.deviceInfo.status?a("el-descriptions",{attrs:{column:1,border:"",size:"mini"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("设备离线时状态")])]),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(t){return a("el-button",{key:t.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:""}},[e._v(e._s(t.text))])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{size:"mini","active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2):e._e()],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:10,xl:14}},[e.deviceInfo.chartList.length>0?a("el-row",{staticStyle:{"background-color":"#f5f7fa",padding:"20px 10px 20px 10px","border-radius":"15px","margin-right":"5px"},attrs:{gutter:20}},e._l(e.deviceInfo.chartList,(function(e,t){return a("el-col",{key:t,attrs:{xs:24,sm:12,md:12,lg:24,xl:8}},[a("el-card",{staticStyle:{"border-radius":"30px","margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("div",{ref:"map",refInFor:!0,staticStyle:{height:"230px",width:"185px",margin:"0 auto"}})])],1)})),1):e._e()],1)],1)],1)],1)],1),a("el-dialog",{attrs:{title:"服务调用",visible:e.dialogValue,"label-width":"200px"},on:{"update:visible":function(t){e.dialogValue=t}}},[a("el-form",{staticStyle:{height:"100%",padding:"0 20px"},attrs:{size:"mini"},model:{value:e.from,callback:function(t){e.from=t},expression:"from"}},[a("el-form-item",{attrs:{label:e.from.name,"label-width":"180px"}},["integer"==e.from.datatype.type||"decimal"==e.from.datatype.type||"string"==e.from.datatype.type?a("el-input",{staticStyle:{width:"50%"},attrs:{type:"number"},on:{input:function(t){return e.justicNumber()}},model:{value:e.from.shadow,callback:function(t){e.$set(e.from,"shadow",t)},expression:"from.shadow"}}):e._e(),"enum"==e.from.datatype.type?a("el-select",{on:{change:function(t){return e.changeSelect()}},model:{value:e.from.shadow,callback:function(t){e.$set(e.from,"shadow",t)},expression:"from.shadow"}},e._l(e.from.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1):e._e(),"bool"===e.from.datatype.type?a("el-switch",{attrs:{"active-value":"1","inactive-value":"0","inline-prompt":""},model:{value:e.from.shadow,callback:function(t){e.$set(e.from,"shadow",t)},expression:"from.shadow"}}):e._e(),"integer"!=e.from.datatype.type&&"decimal"!=e.from.datatype.type||!e.from.datatype.type.unit||"un"==e.from.datatype.type.unit||"/"==e.from.datatype.type.unit?e._e():a("span",[e._v("（"+e._s(e.from.unit)+"）")]),"integer"==e.from.datatype.type||"decimal"==e.from.datatype.type?a("div",{staticClass:"range"},[e._v(" (数据范围:"+e._s("null"==e.from.datatype.max?"bool"==e.from.datatype.type?0:"":e.from.datatype.min)+" ~ "+e._s("null"==e.from.datatype.max?"bool"==e.from.datatype.type?1:"":e.from.datatype.max)+") ")]):e._e()],1)],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.dialogValue=!1}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.btnLoading,disabled:!e.canSend},on:{click:e.sendService}},[e._v("确认")])],1)],1)],1)},n=[],r=(a("4de4"),a("b0c0"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("8a79"),a("159b"),a("0bc2")),s=(a("ed08"),a("a824"),a("584f")),o=(a("ba95"),"integer"),l="decimal",d="bool",c="enum",u={name:"realTime-status",props:{device:{type:Object,default:null}},data:function(){return{shadowUnEnable:!1,statusColor:{background:"#67C23A",color:"#fff",minWidth:"100px"},messageList:[],simulateForm:{},deviceInfo:{},dialogValue:!1,gridData:[],groupId:1,treeData:[],runningData:[],gatewayData:[],functionData:[],controlData:[],loading:!1,debounceGetRuntime:"",serialNumber:"",isControlled:2,slaveId:1,params:{serialNumber:void 0,type:1},slaveList:[],queryParams:{},thingsType:"prop",runtimeName:"slave",opationList:[],funVal:{},canSend:!1,functionName:{},btnLoading:!1,logList:[],showValue:"",from:{datatype:{type:""}},title:"在线模式"}},created:function(){},watch:{device:function(e){var t=this;e&&e.serialNumber&&(this.params.serialNumber=e.serialNumber,this.serialNumber=e.serialNumber,this.params.productId=e.productId,this.params.slaveId=e.slaveId,this.params.deviceId=e.deviceId,this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.slaveList=e.subDeviceList,this.getSlaveList(this.deviceInfo),this.$busEvent.$on("updateData",(function(e){e.data&&e.data[0].remark&&(t.getDeviceFuncLog(),e.data[0].ts=e.data[0].remark),t.updateData(e)})),this.$busEvent.$on("updateLog",(function(e){t.getDeviceFuncLog()})),this.mqttCallback())}},methods:{qosChange:function(e){},payloadTypeChange:function(e){},getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate(),n=e.getHours(),r=e.getMinutes(),s=e.getSeconds();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,t+"-"+a+"-"+i+" "+n+":"+r+":"+s},getRuntimeStatus:function(){var e=this;Object(s["h"])(this.params).then((function(t){e.runningData=t.data.thingsModels,e.runningData.forEach((function(e){"enum"==e.datatype.type?e.datatype.enumList.forEach((function(t){t.value==e.value&&(e.value=t.text)})):"bool"==e.datatype.type&&(e.value=0==e.value?e.falseText:e.trueText)})),e.functionData=e.runningData.filter((function(e){return 0==e.isReadonly}))}))},getGateway:function(){var e=this;Object(s["h"])(this.params).then((function(t){e.deviceInfo.thingsModels=t.data.thingsModels}))},getSlaveList:function(){this.getRuntimeStatus(),this.getDeviceFuncLog()},selectSlave:function(){this.params.serialNumber=this.serialNumber+"_"+this.params.slaveId,this.getRuntimeStatus()},handleClick:function(){"prop"===this.thingsType?(this.params.type=1,this.runningData=this.data.filter((function(e){return 1==e.isReadonly}))):"function"===this.thingsType?(this.isControlled=2,this.params.type=2,this.functionData=this.runningData.filter((function(e){return 0==e.isReadonly}))):(this.params.type=3,this.isControlled=1,this.controlData=this.data.filter((function(e){return 1==e.isMonitor})))},runtimeClick:function(){"gateway"===this.runtimeName?(this.params.serialNumber=this.serialNumber,this.slaveId=this.params.slaveId,this.params.slaveId=void 0,this.getGateway()):(this.params.serialNumber=this.serialNumber+"_"+this.slaveId,this.params.slaveId=this.slaveId,this.getRuntimeStatus())},updateParam:function(e){},editFunc:function(e){this.dialogValue=!0,this.canSend=!0,this.funVal={},this.getValueName(e),this.from=e,console.log(this.runningData)},updateDeviceStatus:function(e){3==e.status?(this.statusColor.background="#12d09f",this.title="在线模式"):1==e.isShadow?(this.statusColor.background="#409EFF",this.title="影子模式"):(this.statusColor.background="#909399",this.title="离线模式",this.shadowUnEnable=!0),this.$emit("statusEvent",this.deviceInfo.status)},getValueName:function(e){this.funVal[e.id]=e.value},sendService:function(){var e=this;console.log("下发指令",this.from.shadow);try{this.funVal[this.from.id]=this.from.shadow;var t={serialNumber:this.serialNumber,productId:this.params.productId,remoteCommand:this.funVal,identifier:this.from.id,slaveId:this.params.slaveId,modelName:this.from.name,isShadow:3!=this.device.status,type:this.from.type,isControlled:this.isControlled};Object(r["b"])(t).then((function(t){200==t.code&&(e.$message({type:"success",message:"服务调用成功!"}),e.getDeviceFuncLog())}))}finally{this.dialogValue=!1}},mqttPublish:function(e,t){var a=this,i={};i[t.id]=t.shadow;var n={serialNumber:e.serialNumber,productId:e.productId,remoteCommand:i,identifier:t.id,modelName:t.name,isShadow:3!=e.status,type:t.type};Object(r["b"])(n).then((function(e){200===e.code&&a.$message({type:"success",message:"服务调用成功!"})}))},getShowValue:function(e){var t=this;switch(this.from.datatype.type){case c:var a=this.from.datatype.enumList;a.forEach((function(a){a.value===e&&(t.showValue=a.text)}));break;case o:case l:this.showValue=e;case d:this.showValue=1==e?this.from.datatype.trueText:this.from.datatype.falseText;break}},changeSelect:function(){this.$forceUpdate()},justicNumber:function(){if(this.canSend=!0,this.from.datatype.max<this.funVal[this.from.identity]||this.from.datatype.min>this.funVal[this.from.identity])return this.canSend=!1,!0;this.$forceUpdate()},getDeviceFuncLog:function(){var e=this,t={serialNumber:this.serialNumber};console.log("params --",t),Object(r["a"])(t).then((function(t){e.logList=t.rows}))},updateData:function(e){var t=this;e.data&&e.data.forEach((function(e){t.runningData.some((function(a,i){if(e.slaveId===a.slaveId&&e.id==a.id){var n=t.runningData[i];return n.ts=e.ts,n.value=e.value,"enum"==a.datatype.type?a.datatype.enumList.forEach((function(e){e.value==n.value&&(n.value=e.text)})):"bool"==a.datatype.type&&(n.value=0==n.value?a.datatype.falseText:a.datatype.trueText),t.$set(t.runningData,i,n),!0}}))}))},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态-运行】主题：",t),console.log("接收到【设备状态-运行】内容：",a),e.deviceInfo.serialNumber==r&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi,e.updateDeviceStatus(e.deviceInfo))),"reply"==n[4]&&e.$modal.notifySuccess(a),t.endsWith("ws/service")&&(console.log("接收到【物模型】主题1：",t),console.log("接收到【物模型】内容：",a),e.deviceInfo.serialNumber==r)))for(var s=0;s<a.length;s++){for(var o=!1,l=0;l<e.deviceInfo.thingsModels.length&&!o;l++){if(e.deviceInfo.thingsModels[l].id==a[s].id){"decimal"==e.deviceInfo.thingsModels[l].datatype.type||"integer"==e.deviceInfo.thingsModels[l].datatype.type?e.deviceInfo.thingsModels[l].shadow=Number(a[s].value):e.deviceInfo.thingsModels[l].shadow=a[s].value,o=!0;break}if("object"==e.deviceInfo.thingsModels[l].datatype.type){for(var d=0;d<e.deviceInfo.thingsModels[l].datatype.params.length;d++)if(e.deviceInfo.thingsModels[l].datatype.params[d].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.params[d].shadow=a[s].value,o=!0;break}}else if("array"==e.deviceInfo.thingsModels[l].datatype.type)if("object"==e.deviceInfo.thingsModels[l].datatype.arrayType)if(0==String(a[s].id).indexOf("array_"))for(var c=0;c<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;c++){for(var u=0;u<e.deviceInfo.thingsModels[l].datatype.arrayParams[c].length;u++)if(e.deviceInfo.thingsModels[l].datatype.arrayParams[c][u].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayParams[c][u].shadow=a[s].value,o=!0;break}if(o)break}else for(var p=0;p<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;p++){for(var m=0;m<e.deviceInfo.thingsModels[l].datatype.arrayParams[p].length;m++){var h=p>9?String(p):"0"+l,f="array_"+h+"_";e.deviceInfo.thingsModels[l].datatype.arrayParams[p][m].id==f+a[s].id&&(e.deviceInfo.thingsModels[l].datatype.arrayParams[p][m].shadow=a[s].value,o=!0)}if(o)break}else for(var v=0;v<e.deviceInfo.thingsModels[l].datatype.arrayModel.length;v++)if(e.deviceInfo.thingsModels[l].datatype.arrayModel[v].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayModel[v].shadow=a[s].value,o=!0;break}}for(var y=0;y<e.deviceInfo.chartList.length;y++){if(0==e.deviceInfo.chartList[y].id.indexOf("array_")){if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var b=0;b<e.monitorChart.length;b++)if(a[s].id==e.monitorChart[b].data.id){var g=[{value:a[s].value,name:e.monitorChart[b].data.name}];e.monitorChart[b].chart.setOption({series:[{data:g}]});break}}}else if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var w=0;w<e.monitorChart.length;w++)if(a[s].id==e.monitorChart[w].data.id){o=!0;var x=[{value:a[s].value,name:e.monitorChart[w].data.name}];e.monitorChart[w].chart.setOption({series:[{data:x}]});break}}if(o)break}}}))}}},p=u,m=(a("0ddb"),a("2877")),h=Object(m["a"])(p,i,n,!1,null,"134301aa",null);t["default"]=h.exports},"5f43":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{attrs:{gutter:120}},[a("el-col",{staticStyle:{"margin-bottom":"50px"},attrs:{xs:24,sm:24,md:24,lg:14,xl:10}},[a("el-descriptions",{staticStyle:{"margin-bottom":"50px"},attrs:{column:1,border:""}},[a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-menu"}),e._v(" 设备模式 ")]),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v(e._s(e.title))])],2),e.hasShrarePerm("ota")?a("el-descriptions-item",{attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("svg-icon",{attrs:{"icon-class":"ota"}}),e._v(" OTA升级 ")],1),a("el-link",{staticStyle:{"line-height":"28px","font-size":"16px","padding-right":"10px"},attrs:{underline:!1}},[e._v("Version "+e._s(e.deviceInfo.firmwareVersion))]),a("el-button",{staticStyle:{float:"right"},attrs:{type:"success",size:"mini",disabled:3!=e.deviceInfo.status},on:{click:function(t){return e.getLatestFirmware(e.deviceInfo.deviceId)}}},[e._v("检查更新")])],2):e._e(),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{labelStyle:e.statusColor}},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串 "+(t.datatype.unit?"，单位："+t.datatype.unit:""),disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"2px 0 0 5px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"integer"==t.datatype.type?a("div",[a("div",{staticStyle:{width:"80%",float:"left"}},[a("el-slider",{attrs:{min:t.datatype.min,max:t.datatype.max,step:t.datatype.step,"format-tooltip":function(e){return e+" "+t.datatype.unit},disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"item.shadow"}})],1),a("div",{staticStyle:{width:"20%",float:"left"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"16px",padding:"1px 8px",margin:"4px 0 0 10px","border-radius":"3px"},attrs:{icon:"el-icon-s-promotion",type:"info",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}}})],1)]):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{size:"small",placeholder:"请选择",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{input:function(a){return e.arrayItemChange(a,t)}},model:{value:i.shadow,callback:function(t){e.$set(i,"shadow",t)},expression:"model.shadow"}},[e.shadowUnEnable&&0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(t){return e.mqttPublish(e.deviceInfo,i)}},slot:"append"})],1)],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(i){return a("el-button",{key:i.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:e.shadowUnEnable||1==t.isReadonly},on:{click:function(a){return e.enumButtonClick(e.deviceInfo,t,i.value)}}},[e._v(" "+e._s(i.text)+" ")])})),1):a("el-select",{attrs:{placeholder:"请选择",size:"small",disabled:e.shadowUnEnable||1==t.isReadonly},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:e.shadowUnEnable||1==t.isReadonly},model:{value:t.shadow,callback:function(a){e.$set(t,"shadow",a)},expression:"param.shadow"}},[e.shadowUnEnable||0!=t.isReadonly?e._e():a("el-button",{staticStyle:{"font-size":"20px"},attrs:{slot:"append",icon:"el-icon-s-promotion",title:"指令发送"},on:{click:function(a){return e.mqttPublish(e.deviceInfo,t)}},slot:"append"})],1)],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2),1==e.deviceInfo.isShadow&&3!=e.deviceInfo.status?a("el-descriptions",{attrs:{column:1,border:"",size:"mini"}},[a("template",{slot:"title"},[a("span",{staticStyle:{"font-size":"14px",color:"#606266"}},[e._v("设备离线时状态")])]),e._l(e.deviceInfo.thingsModels,(function(t,i){return a("el-descriptions-item",{key:i},[a("template",{slot:"label"},[a("i",{staticClass:"el-icon-open"}),e._v(" "+e._s(t.name)+" ")]),"bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[t.datatype.showWay&&"button"==t.datatype.showWay?a("div",e._l(t.datatype.enumList,(function(t){return a("el-button",{key:t.value,staticStyle:{margin:"5px"},attrs:{size:"mini",disabled:""}},[e._v(e._s(t.text))])})),1):a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1):e._e(),"object"==t.datatype.type?a("div",[a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.params,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{size:"mini","active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],1):e._e(),"array"==t.datatype.type?a("div",["object"!=t.datatype.arrayType?a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(t.datatype.arrayModel,(function(i,n){return a("el-descriptions-item",{key:n,attrs:{label:t.name+(n+1)}},["string"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"decimal"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e(),"integer"==t.datatype.arrayType?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",size:"mini",disabled:""},model:{value:i.value,callback:function(t){e.$set(i,"value",t)},expression:"model.value"}})],1):e._e()])})),1):e._e(),"object"==t.datatype.arrayType?a("el-collapse",e._l(t.datatype.arrayParams,(function(i,n){return a("el-collapse-item",{key:n},[a("template",{slot:"title"},[a("span",{staticStyle:{color:"#666"}},[a("i",{staticClass:"el-icon-tickets"}),e._v(" "+e._s(t.name+(n+1))+" ")])]),a("el-descriptions",{attrs:{column:1,size:"mini",border:""}},e._l(i,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name}},["bool"==t.datatype.type?a("div",[a("el-switch",{staticStyle:{"min-width":"100px"},attrs:{"active-text":"","inactive-text":"","active-value":"1","inactive-value":"0",disabled:""},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"enum"==t.datatype.type?a("div",[a("el-select",{attrs:{placeholder:"请选择",disabled:"",size:"mini"},on:{change:function(a){return e.mqttPublish(e.deviceInfo,t)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}},e._l(t.datatype.enumList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.text,value:e.value}})})),1)],1):e._e(),"string"==t.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"decimal"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"number",placeholder:"请输入小数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e(),"integer"==t.datatype.type?a("div",[a("el-input",{attrs:{type:"integer",placeholder:"请输入整数 ",disabled:"",size:"mini"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"param.value"}})],1):e._e()])})),1)],2)})),1):e._e()],1):e._e()],2)}))],2):e._e()],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:10,xl:14}},[e.deviceInfo.chartList.length>0?a("el-row",{staticStyle:{"background-color":"#f5f7fa",padding:"20px 10px 20px 10px","border-radius":"15px","margin-right":"5px"},attrs:{gutter:20}},e._l(e.deviceInfo.chartList,(function(e,t){return a("el-col",{key:t,attrs:{xs:24,sm:12,md:12,lg:24,xl:8}},[a("el-card",{staticStyle:{"border-radius":"30px","margin-bottom":"20px"},attrs:{shadow:"hover"}},[a("div",{ref:"map",refInFor:!0,staticStyle:{height:"230px",width:"185px",margin:"0 auto"}})])],1)})),1):e._e()],1)],1),a("el-dialog",{attrs:{title:"设备固件升级",visible:e.openFirmware,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openFirmware=t}}},[null==e.firmware?a("div",{staticStyle:{"text-align":"center","font-size":"16px"}},[a("i",{staticClass:"el-icon-success",staticStyle:{color:"#67c23a"}}),e._v(" 已经是最新版本，不需要升级 ")]):e._e(),null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-descriptions",{attrs:{column:1,border:"",size:"large",labelStyle:{width:"150px","font-weight":"bold"}}},[a("template",{slot:"title"},[a("el-link",{attrs:{icon:"el-icon-success",type:"success",underline:!1}},[e._v("可以升级到以下版本")])],1),a("el-descriptions-item",{attrs:{label:"固件名称"}},[e._v(e._s(e.firmware.firmwareName))]),a("el-descriptions-item",{attrs:{label:"所属产品"}},[e._v(e._s(e.firmware.productName))]),a("el-descriptions-item",{attrs:{label:"固件版本"}},[e._v("Version "+e._s(e.firmware.version))]),a("el-descriptions-item",{attrs:{label:"下载地址"}},[a("el-link",{attrs:{href:e.getDownloadUrl(e.firmware.filePath),underline:!1,type:"primary"}},[e._v(e._s(e.getDownloadUrl(e.firmware.filePath)))])],1),a("el-descriptions-item",{attrs:{label:"固件描述"}},[e._v(e._s(e.firmware.remark))])],2):e._e(),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[null!=e.firmware&&e.deviceInfo.firmwareVersion<e.firmware.version?a("el-button",{attrs:{type:"success"},on:{click:e.otaUpgrade}},[e._v("升 级")]):e._e(),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=(a("d81d"),a("b0c0"),a("a9e3"),a("b64b"),a("d3b7"),a("25f0"),a("8a79"),a("814a")),s=a("0bc2"),o={name:"running-status",props:{device:{type:Object,default:null}},watch:{device:function(e,t){e&&0!=e.deviceId&&(this.deviceInfo=e,this.updateDeviceStatus(this.deviceInfo),this.$nextTick((function(){this.MonitorChart()})),this.mqttCallback())}},data:function(){return{title:"设备控制 ",shadowUnEnable:!1,statusColor:{background:"#67C23A",color:"#fff",minWidth:"100px"},firmware:{},openFirmware:!1,loading:!0,deviceInfo:{boolList:[],enumList:[],stringList:[],integerList:[],decimalList:[],arrayList:[],thingsModels:[],chartList:[]},monitorChart:[{chart:{},data:{id:"",name:"",value:""}}],remoteCommand:{}}},created:function(){},methods:{mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态-运行】主题：",t),console.log("接收到【设备状态-运行】内容：",a),e.deviceInfo.serialNumber==r&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi,e.updateDeviceStatus(e.deviceInfo))),"reply"==n[4]&&e.$modal.notifySuccess(a),t.endsWith("ws/service")&&(console.log("接收到【物模型】主题1：",t),console.log("接收到【物模型】内容：",a),e.deviceInfo.serialNumber==r)))for(var s=0;s<a.length;s++){for(var o=!1,l=0;l<e.deviceInfo.thingsModels.length&&!o;l++){if(e.deviceInfo.thingsModels[l].id==a[s].id){"decimal"==e.deviceInfo.thingsModels[l].datatype.type||"integer"==e.deviceInfo.thingsModels[l].datatype.type?e.deviceInfo.thingsModels[l].shadow=Number(a[s].value):e.deviceInfo.thingsModels[l].shadow=a[s].value,o=!0;break}if("object"==e.deviceInfo.thingsModels[l].datatype.type){for(var d=0;d<e.deviceInfo.thingsModels[l].datatype.params.length;d++)if(e.deviceInfo.thingsModels[l].datatype.params[d].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.params[d].shadow=a[s].value,o=!0;break}}else if("array"==e.deviceInfo.thingsModels[l].datatype.type)if("object"==e.deviceInfo.thingsModels[l].datatype.arrayType)if(0==String(a[s].id).indexOf("array_"))for(var c=0;c<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;c++){for(var u=0;u<e.deviceInfo.thingsModels[l].datatype.arrayParams[c].length;u++)if(e.deviceInfo.thingsModels[l].datatype.arrayParams[c][u].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayParams[c][u].shadow=a[s].value,o=!0;break}if(o)break}else for(var p=0;p<e.deviceInfo.thingsModels[l].datatype.arrayParams.length;p++){for(var m=0;m<e.deviceInfo.thingsModels[l].datatype.arrayParams[p].length;m++){var h=p>9?String(p):"0"+l,f="array_"+h+"_";e.deviceInfo.thingsModels[l].datatype.arrayParams[p][m].id==f+a[s].id&&(e.deviceInfo.thingsModels[l].datatype.arrayParams[p][m].shadow=a[s].value,o=!0)}if(o)break}else for(var v=0;v<e.deviceInfo.thingsModels[l].datatype.arrayModel.length;v++)if(e.deviceInfo.thingsModels[l].datatype.arrayModel[v].id==a[s].id){e.deviceInfo.thingsModels[l].datatype.arrayModel[v].shadow=a[s].value,o=!0;break}}for(var y=0;y<e.deviceInfo.chartList.length;y++){if(0==e.deviceInfo.chartList[y].id.indexOf("array_")){if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var b=0;b<e.monitorChart.length;b++)if(a[s].id==e.monitorChart[b].data.id){var g=[{value:a[s].value,name:e.monitorChart[b].data.name}];e.monitorChart[b].chart.setOption({series:[{data:g}]});break}}}else if(e.deviceInfo.chartList[y].id==a[s].id){e.deviceInfo.chartList[y].shadow=a[s].value;for(var w=0;w<e.monitorChart.length;w++)if(a[s].id==e.monitorChart[w].data.id){o=!0;var x=[{value:a[s].value,name:e.monitorChart[w].data.name}];e.monitorChart[w].chart.setOption({series:[{data:x}]});break}}if(o)break}}}))},mqttPublish:function(e,t){var a=this,i={};i[t.id]=t.shadow;var n={serialNumber:e.serialNumber,productId:e.productId,remoteCommand:i,identifier:t.id,modelName:t.name,isShadow:3!=e.status,type:t.type};Object(s["b"])(n).then((function(e){200===e.code&&a.$message({type:"success",message:"服务调用成功!"})}))},enumButtonClick:function(e,t,a){t.shadow=a,this.mqttPublish(e,t)},updateDeviceStatus:function(e){3==e.status?(this.statusColor.background="#12d09f",this.title="在线模式"):1==e.isShadow?(this.statusColor.background="#409EFF",this.title="影子模式"):(this.statusColor.background="#909399",this.title="离线模式",this.shadowUnEnable=!0),this.$emit("statusEvent",this.deviceInfo.status)},arrayItemChange:function(e,t){for(var a="",i=0;i<t.datatype.arrayCount;i++)a+=t.datatype.arrayModel[i].shadow+",";a=a.substring(0,a.length-1),t.shadow=a},arrayInputChange:function(e,t){var a=e.split(",");if(a.length!=t.datatype.arrayCount)this.$modal.alertWarning("元素个数不匹配，数组元素个数为"+t.datatype.arrayCount+"个，以英文逗号分隔。");else for(var i=0;i<t.datatype.arrayCount;i++)t.datatype.arrayModel[i].shadow=a[i]},hasShrarePerm:function(e){return 0!=this.deviceInfo.isOwner||-1!=this.deviceInfo.userPerms.indexOf(e)},otaUpgrade:function(){var e=this,t="/"+this.deviceInfo.productId+"/"+this.deviceInfo.serialNumber+"/ota/get",a='{"version":'+this.firmware.version+',"downloadUrl":"'+this.getDownloadUrl(this.firmware.filePath)+'"}';this.$mqttTool.publish(t,a,"设备升级").then((function(t){e.$modal.notifySuccess(t)})).catch((function(t){e.$modal.notifyError(t)})),this.openFirmware=!1},getLatestFirmware:function(e){var t=this;Object(r["d"])(e).then((function(e){t.firmware=e.data,t.openFirmware=!0}))},cancel:function(){this.openFirmware=!1},getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},MonitorChart:function(){for(var e=0;e<this.deviceInfo.chartList.length;e++){var t;this.monitorChart[e]={chart:this.$echarts.init(this.$refs.map[e]),data:{id:this.deviceInfo.chartList[e].id,name:this.deviceInfo.chartList[e].name,value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min}},t={tooltip:{formatter:" {b} <br/> {c}"+this.deviceInfo.chartList[e].datatype.unit},series:[{name:this.deviceInfo.chartList[e].datatype.type,type:"gauge",min:this.deviceInfo.chartList[e].datatype.min,max:this.deviceInfo.chartList[e].datatype.max,colorBy:"data",splitNumber:10,radius:"100%",splitLine:{distance:4},axisLabel:{fontSize:10,distance:10},axisTick:{distance:4},axisLine:{lineStyle:{width:8,color:[[.2,"#409EFF"],[.8,"#12d09f"],[1,"#F56C6C"]],opacity:.3}},pointer:{icon:"triangle",length:"60%",width:7},progress:{show:!0,width:8},detail:{valueAnimation:!0,formatter:"{value} "+this.deviceInfo.chartList[e].datatype.unit,offsetCenter:[0,"80%"],fontSize:20},data:[{value:this.deviceInfo.chartList[e].shadow?this.deviceInfo.chartList[e].shadow:this.deviceInfo.chartList[e].datatype.min,name:this.deviceInfo.chartList[e].name}],title:{offsetCenter:[0,"115%"],fontSize:16}}]},t&&this.monitorChart[e].chart.setOption(t)}}}},l=o,d=(a("6db0"),a("2877")),c=Object(d["a"])(l,i,n,!1,null,null,null);t["default"]=c.exports},"67dd":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.channelList,size:"mini"}},[a("el-table-column",{attrs:{label:"设备ID",align:"center",prop:"deviceSipId"}}),a("el-table-column",{attrs:{label:"通道ID",align:"center",prop:"channelSipId"}}),a("el-table-column",{attrs:{label:"快照","min-width":"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.isVideoChannel(t.row)?a("el-image",{staticStyle:{width:"60px"},attrs:{src:e.getSnap(t.row),"preview-src-list":e.getBigSnap(t.row),fit:"contain"}},[a("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[a("i",{staticClass:"el-icon-picture-outline"})])]):e._e()]}}])}),a("el-table-column",{attrs:{label:"通道名称",align:"center",prop:"channelName"}}),a("el-table-column",{attrs:{label:"产品型号",align:"center",prop:"model"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sip_gen_status,value:t.row.status,size:"mini"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center",width:"120","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",type:"success",icon:"el-icon-video-play",disabled:2!==t.row.status},on:{click:function(a){return e.sendDevicePush(t.row)}}},[e._v("查看直播")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},n=[],r=a("e2de"),s={name:"Channel",dicts:["sip_gen_status","video_type","channel_type"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber)}},data:function(){return{loadSnap:{},deviceInfo:{},loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,channelList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,deviceSipId:null},form:{}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.getList()},methods:{sendDevicePush:function(e){var t={tabName:"sipPlayer",channelId:e.channelSipId};this.$emit("playerEvent",t),console.log("通知设备推流："+e.deviceSipId+" : "+e.channelSipId)},getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){console.log(t),e.channelList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={channelId:null,channelSipId:null,deviceSipId:null,channelName:null,manufacture:null,model:null,owner:null,civilcode:null,block:null,address:null,parentid:null,ipaddress:null,port:null,password:null,ptztype:null,ptztypetext:null,status:0,longitude:null,latitude:null,streamid:null,subcount:null,parental:1,hasaudio:1},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset();var a=e.channelId||this.ids;Object(r["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改监控设备通道信息"}))},handleDelete:function(e){var t=this,a=e.channelId||this.ids;this.$modal.confirm('是否确认删除监控设备通道信息编号为"'+a+'"的数据项？').then((function(){return Object(r["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getSnap:function(e){return console.log("getSnap:/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"),"/prod-api/profile/snap/"+e.deviceSipId+"_"+e.channelSipId+".jpg"},getBigSnap:function(e){return[this.getSnap(e)]},isVideoChannel:function(e){var t=e.channelSipId.substring(10,13);return!("111"!==t&&"112"!==t&&"118"!==t&&"131"!==t&&"132"!==t)}}},o=s,l=a("2877"),d=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=d.exports},6827:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return s})),a.d(t,"d",(function(){return o})),a.d(t,"f",(function(){return l})),a.d(t,"e",(function(){return d}));var i=a("b775");function n(e,t,a){return Object(i["a"])({url:"/sip/record/devquery/"+e+"/"+t,method:"get",params:a})}function r(e){return Object(i["a"])({url:"/sip/record/serverRecord/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/sip/record/serverRecord/date/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/sip/record/serverRecord/file/list",method:"get",params:e})}function l(e,t){return Object(i["a"])({url:"/sip/record/play/"+e+"/"+t,method:"get"})}function d(e,t,a){return Object(i["a"])({url:"/sip/record/download/"+e+"/"+t,method:"get",params:a})}},"6ca8":function(e,t,a){},"6db0":function(e,t,a){"use strict";a("6ca8")},7168:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"日志类型",prop:"logType"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.dict.type.iot_event_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标识符",prop:"identity"}},[a("el-input",{attrs:{placeholder:"请输入标识符",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.identity,callback:function(t){e.$set(e.queryParams,"identity",t)},expression:"queryParams.identity"}})],1),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceLogList,size:"mini"}},[a("el-table-column",{attrs:{label:"类型",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_event_type,value:t.row.logType}})]}}])}),a("el-table-column",{attrs:{label:"模式",align:"center",prop:"logType",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.mode?a("el-tag",{attrs:{type:"primary"}},[e._v("影子模式")]):2==t.row.mode?a("el-tag",{attrs:{type:"success"}},[e._v("在线模式")]):a("el-tag",{attrs:{type:"info"}},[e._v("其他信息")])]}}])}),a("el-table-column",{attrs:{label:"时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.createTime))])]}}])}),a("el-table-column",{attrs:{label:"标识符",align:"center",prop:"identity"}}),a("el-table-column",{attrs:{label:"动作",align:"left","header-align":"center",prop:"logValue"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatValueDisplay(t.row))}})]}}])}),a("el-table-column",{attrs:{label:"备注","header-align":"center",align:"left",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null==t.row.remark?"无":t.row.remark)+" ")]}}])})],1),a("div",{staticStyle:{height:"40px"}},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1)},n=[],r=a("5530"),s=(a("b0c0"),a("a9e3"),a("b775"));function o(e){return Object(s["a"])({url:"/iot/event/list",method:"get",params:e})}var l={name:"DeviceLog",dicts:["iot_event_type","iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList(),this.thingsModel=this.deviceInfo.cacheThingsModel)}},data:function(){return{thingsModel:{},loading:!0,showSearch:!0,total:0,deviceLogList:[],queryParams:{pageNum:1,pageSize:10,logType:null,logValue:null,deviceId:null,serialNumber:null,deviceName:null,identity:null,isMonitor:null},daterangeTime:[]}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.addDateRange(this.queryParams,this.daterangeTime)).then((function(t){e.deviceLogList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.daterangeTime=[],this.handleQuery()},handleExport:function(){this.download("iot/event/export",Object(r["a"])({},this.queryParams),"eventLog_".concat((new Date).getTime(),".xlsx"))},formatValueDisplay:function(e){if(1==e.logType){var t=this.getThingsModelItem(1,e.identity);if(""!=t)return(t.parentName?"["+t.parentName+(t.arrayIndex?t.arrayIndex:"")+"] ":"")+t.name+'： <span style="color:#409EFF;">'+this.getThingsModelItemValue(t,e.logValue)+" "+(void 0!=t.datatype.unit?t.datatype.unit:"")+"</span>"}else if(2==e.logType){var a=this.getThingsModelItem(2,e.identity);if(""!=a)return(a.parentName?"["+a.parentName+(a.arrayIndex?a.arrayIndex:"")+"] ":"")+a.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(a,e.logValue)+" "+(void 0!=a.datatype.unit?a.datatype.unit:"")+"</span>"}else{if(3==e.logType){var i=this.getThingsModelItem(3,e.identity);return""!=i?(i.parentName?"["+i.parentName+(i.arrayIndex?i.arrayIndex:"")+"] ":"")+i.name+'： <span style="color:#409EFF">'+this.getThingsModelItemValue(i,e.logValue)+" "+(void 0!=i.datatype.unit?i.datatype.unit:"")+"</span>":e.logValue}if(4==e.logType)return'<span style="font-weight:bold">设备升级</span>';if(5==e.logType)return'<span style="font-weight:bold">设备上线</span>';if(6==e.logType)return'<span style="font-weight:bold">设备离线</span>'}return""},getThingsModelItemValue:function(e,t){if("bool"==e.datatype.type){if("0"==t)return e.datatype.falseText;if("1"==t)return e.datatype.trueText}else if("enum"==e.datatype.type)for(var a=0;a<e.datatype.enumList.length;a++)if(t==e.datatype.enumList[a].value)return e.datatype.enumList[a].text;return t},getThingsModelItem:function(e,t){if(1==e&&this.thingsModel.properties)for(var a=0;a<this.thingsModel.properties.length;a++){if(this.thingsModel.properties[a].id==t)return this.thingsModel.properties[a];if("object"==this.thingsModel.properties[a].datatype.type)for(var i=0;i<this.thingsModel.properties[a].datatype.params.length;i++)if(this.thingsModel.properties[a].datatype.params[i].id==t)return this.thingsModel.properties[a].datatype.params[i].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[i];if("array"==this.thingsModel.properties[a].datatype.type&&this.thingsModel.properties[a].datatype.arrayType)if("object"==this.thingsModel.properties[a].datatype.arrayType){var n=t,r=0;t.indexOf("array_")>-1&&(r=t.substring(6,8),n=t.substring(9));for(var s=0;s<this.thingsModel.properties[a].datatype.params.length;s++)if(this.thingsModel.properties[a].datatype.params[s].id==n)return this.thingsModel.properties[a].datatype.params[s].arrayIndex=Number(r)+1,this.thingsModel.properties[a].datatype.params[s].parentName=this.thingsModel.properties[a].name,this.thingsModel.properties[a].datatype.params[s]}else for(var o=0;o<this.thingsModel.properties[a].datatype.arrayCount.length;o++)if(this.thingsModel.properties[a].id==realIdentity)return this.thingsModel.properties[a].arrayIndex=Number(arrayIndex)+1,this.thingsModel.properties[a].parentName="元素",this.thingsModel.properties[a]}else if(2==e&&this.thingsModel.functions)for(var l=0;l<this.thingsModel.functions.length;l++){if(this.thingsModel.functions[l].id==t)return this.thingsModel.functions[l];if("object"==this.thingsModel.functions[l].datatype.type)for(var d=0;d<this.thingsModel.functions[l].datatype.params.length;d++)if(this.thingsModel.functions[l].datatype.params[d].id==t)return this.thingsModel.functions[l].datatype.params[d].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[d];if("array"==this.thingsModel.functions[l].datatype.type&&this.thingsModel.functions[l].datatype.arrayType){var c=t,u=0;if(t.indexOf("array_")>-1&&(u=t.substring(6,8),c=t.substring(9)),"object"==this.thingsModel.functions[l].datatype.arrayType){for(var p=0;p<this.thingsModel.functions[l].datatype.params.length;p++)if(this.thingsModel.functions[l].datatype.params[p].id==c)return this.thingsModel.functions[l].datatype.params[p].arrayIndex=Number(u)+1,this.thingsModel.functions[l].datatype.params[p].parentName=this.thingsModel.functions[l].name,this.thingsModel.functions[l].datatype.params[p]}else for(var m=0;m<this.thingsModel.functions[l].datatype.arrayCount.length;m++)if(this.thingsModel.functions[l].id==c)return this.thingsModel.functions[l].arrayIndex=Number(u)+1,this.thingsModel.functions[l].parentName="元素",this.thingsModel.functions[l]}}else if(3==e&&this.thingsModel.events)for(var h=0;h<this.thingsModel.events.length;h++)if(this.thingsModel.events[h].id==t)return this.thingsModel.events[h];return""}}},d=l,c=a("2877"),u=Object(c["a"])(d,i,n,!1,null,null,null);t["default"]=u.exports},"7a72":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"device-timer-wrap"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"70px"}},[a("el-form-item",{attrs:{label:"定时名称",prop:"jobName"}},[a("el-input",{attrs:{placeholder:"请输入定时名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{label:"定时状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择定时状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_job_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:add"],expression:"['iot:device:timer:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobList,size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"描述",align:"center",prop:"cronText"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatCronDisplay(t.row))}})]}}])}),a("el-table-column",{attrs:{label:"CRON表达式",align:"center",prop:"cronExpression","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"动作",align:"left",prop:"actions","show-overflow-tooltip":!0,width:"260"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{overflow:"hidden","white-space":"nowrap"},domProps:{innerHTML:e._s(e.formatActionsDisplay(t.row.actions))}})]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1","active-text":"启用"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:query"],expression:"['iot:device:timer:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("查看")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:query"],expression:"['iot:device:timer:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-caret-right"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("定时详细")]),a("br"),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:remove"],expression:"['iot:device:timer:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:execute"],expression:"['iot:device:timer:execute']"}],attrs:{size:"mini",type:"text",icon:"el-icon-caret-right"},on:{click:function(a){return e.handleRun(t.row)}}},[e._v("执行一次")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{staticClass:"device-timer-config-dialog",attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":"","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticClass:"el-divider el-divider--horizontal",staticStyle:{"margin-top":"-25px"}}),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"定时名称",prop:"jobName"}},[a("el-input",{staticStyle:{width:"280px"},attrs:{placeholder:"请输入定时名称"},model:{value:e.form.jobName,callback:function(t){e.$set(e.form,"jobName",t)},expression:"form.jobName"}})],1),a("el-form-item",{attrs:{label:"执行时间",prop:"timerTimeValue"}},[a("el-time-picker",{staticStyle:{width:"280px"},attrs:{"value-format":"HH:mm",format:"HH:mm",placeholder:"选择时间",disabled:1==e.form.isAdvance},on:{change:e.timeChange},model:{value:e.timerTimeValue,callback:function(t){e.timerTimeValue=t},expression:"timerTimeValue"}})],1),a("el-form-item",{attrs:{label:"选择星期",prop:"timerWeek"}},[a("el-row",[a("el-col",{attrs:{span:18}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",multiple:"",disabled:1==e.form.isAdvance},on:{change:e.weekChange},model:{value:e.timerWeekValue,callback:function(t){e.timerWeekValue=t},expression:"timerWeekValue"}},e._l(e.timerWeeks,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"cron表达式",prop:"cron"}},[a("el-row",[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"cron执行表达式",disabled:0==e.form.isAdvance},model:{value:e.form.cronExpression,callback:function(t){e.$set(e.form,"cronExpression",t)},expression:"form.cronExpression"}},[a("template",{slot:"append"},[a("el-button",{attrs:{type:"primary",disabled:0==e.form.isAdvance},on:{click:e.handleShowCron}},[e._v(" 生成表达式 "),a("i",{staticClass:"el-icon-time el-icon--right"})])],1)],2)],1),a("el-col",{attrs:{span:4,offset:1}},[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:e.customerCronChange},model:{value:e.form.isAdvance,callback:function(t){e.$set(e.form,"isAdvance",t)},expression:"form.isAdvance"}},[e._v("自定义表达式")])],1)],1)],1),a("el-form-item",{attrs:{label:"定时状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_job_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label))])})),1)],1),a("div",{staticStyle:{height:"1px","background-color":"#ddd",margin:"0 0 20px 0"}}),a("el-form-item",{staticClass:"action-wrap",attrs:{label:"执行动作",prop:"actions"}},[e._l(e.actionList,(function(t,i){return a("div",{key:i+"action",staticClass:"item-wrap"},[a("el-row",{attrs:{gutter:16}},[a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{placeholder:"请选择类型",size:"small"},on:{change:function(t){return e.handleActionTypeChange(t,i)}},model:{value:t.type,callback:function(a){e.$set(t,"type",a)},expression:"actionItem.type"}},e._l(e.modelTypes,(function(e,t){return a("el-option",{key:t+"type",attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:10}},[1==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择父级物模型",size:"small"},on:{change:function(t){return e.handleActionParentModelChange(t,i)}},model:{value:t.parentId,callback:function(a){e.$set(t,"parentId",a)},expression:"actionItem.parentId"}},e._l(e.thingsModel.properties,(function(e,t){return a("el-option",{key:t+"property",attrs:{label:e.name,value:e.id}})})),1):2==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择父级物模型",size:"small"},on:{change:function(t){return e.handleActionParentModelChange(t,i)}},model:{value:t.parentId,callback:function(a){e.$set(t,"parentId",a)},expression:"actionItem.parentId"}},e._l(e.thingsModel.functions,(function(e,t){return a("el-option",{key:t+"func",attrs:{label:e.name,value:e.id}})})),1):e._e()],1),a("div",{staticClass:"delete-wrap"},[0!==i?a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",plain:"",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.handleRemoveActionItem(i)}}},[e._v("删除")]):e._e()],1)],1),a("el-row",{attrs:{gutter:16}},[t.parentModel&&"array"===t.parentModel.datatype.type?a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{placeholder:"请选择",size:"small"},on:{change:function(t){return e.handleActionIndexChange(t,i)}},model:{value:t.arrayIndex,callback:function(a){e.$set(t,"arrayIndex",a)},expression:"actionItem.arrayIndex"}},e._l(t.parentModel.datatype.arrayModel,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t.parentModel&&"array"===t.parentModel.datatype.type&&"object"===t.parentModel.datatype.arrayType?a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{placeholder:"请选择",size:"small"},on:{change:function(t){return e.handleActionModelChange(t,i)}},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"actionItem.id"}},e._l(t.parentModel.datatype.params,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t.parentModel&&"object"===t.parentModel.datatype.type?a("el-col",{attrs:{span:5}},[a("el-select",{attrs:{placeholder:"请选择",size:"small"},on:{change:function(t){return e.handleActionModelChange(t,i)}},model:{value:t.id,callback:function(a){e.$set(t,"id",a)},expression:"actionItem.id"}},e._l(t.parentModel.datatype.params,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:e.id}})})),1)],1):e._e(),t.model?a("el-col",{attrs:{span:10}},["integer"==t.model.datatype.type||"decimal"==t.model.datatype.type?a("div",[a("el-input",{staticStyle:{"vertical-align":"baseline"},attrs:{placeholder:"值",max:t.model.datatype.max,min:t.model.datatype.min,type:"number",size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}},[a("template",{slot:"append"},[e._v(e._s(t.model.datatype.unit))])],2)],1):"bool"==t.model.datatype.type?a("div",[a("el-switch",{staticStyle:{"vertical-align":"baseline"},attrs:{"active-text":t.model.datatype.trueText,"inactive-text":t.model.datatype.falseText,"active-value":1,"inactive-value":0},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}})],1):"enum"==t.model.datatype.type?a("div",[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}},e._l(t.model.datatype.enumList,(function(e,t){return a("el-option",{key:t+"things",attrs:{label:e.text,value:e.value}})})),1)],1):"string"==t.model.datatype.type?a("div",[a("el-input",{attrs:{placeholder:"请输入字符串",max:t.model.datatype.maxLength,size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"actionItem.value"}})],1):e._e()]):e._e()],1)],1)})),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.handleAddActionItem()}}},[e._v("添加执行动作")])])],2)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:add"],expression:"['iot:device:timer:add']"},{name:"show",rawName:"v-show",value:!e.form.jobId,expression:"!form.jobId\n                    "}],attrs:{type:"primary",loading:e.submitButtonLoading},on:{click:e.handleSubmitForm}},[e._v("新 增")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:timer:edit"],expression:"['iot:device:timer:edit']"},{name:"show",rawName:"v-show",value:e.form.jobId,expression:"form.jobId"}],attrs:{type:"primary",loading:e.submitButtonLoading},on:{click:e.handleSubmitForm}},[e._v("修 改")]),a("el-button",{on:{click:e.handleCancel}},[e._v("取 消")])],1)],1),a("el-dialog",{staticClass:"scrollbar",attrs:{title:"Cron表达式生成器",visible:e.openCron,"append-to-body":"","destroy-on-close":""},on:{"update:visible":function(t){e.openCron=t}}},[a("crontab",{staticStyle:{"padding-bottom":"80px"},attrs:{expression:e.expression},on:{hide:function(t){e.openCron=!1},fill:e.crontabFill}})],1),a("el-dialog",{attrs:{title:"定时详细",visible:e.openView,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openView=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"120px",size:"mini"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时编号："}},[e._v(e._s(e.form.jobId))]),a("el-form-item",{attrs:{label:"定时名称："}},[e._v(e._s(e.form.jobName))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时分组："}},[e._v(e._s(e.jobGroupFormat(e.form)))]),a("el-form-item",{attrs:{label:"创建时间："}},[e._v(e._s(e.form.createTime))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否并发："}},[0==e.form.concurrent?a("div",[e._v("允许")]):1==e.form.concurrent?a("div",[e._v("禁止")]):e._e()])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"cron表达式："}},[e._v(e._s(e.form.cronExpression))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"执行策略："}},[0==e.form.misfirePolicy?a("div",[e._v("默认策略")]):1==e.form.misfirePolicy?a("div",[e._v("立即执行")]):2==e.form.misfirePolicy?a("div",[e._v("执行一次")]):3==e.form.misfirePolicy?a("div",[e._v("放弃执行")]):e._e()])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"下次执行时间："}},[e._v(e._s(e.parseTime(e.form.nextValidTime)))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"定时状态："}},[0==e.form.status?a("div",[e._v("正常")]):1==e.form.status?a("div",[e._v("暂停")]):e._e()])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行动作："}},[a("div",{staticStyle:{border:"1px solid #ddd",padding:"10px","border-radius":"5px",width:"465px"},domProps:{innerHTML:e._s(e.formatActionsDisplay(e.form.actions))}})])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.openView=!1}}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("5530"),s=(a("99af"),a("4de4"),a("7db0"),a("d81d"),a("14d9"),a("4e82"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("b775"));function o(e){return Object(s["a"])({url:"/iot/job/list",method:"get",params:e})}function l(e){return Object(s["a"])({url:"/iot/job/"+e,method:"get"})}function d(e){return Object(s["a"])({url:"/iot/job",method:"post",data:e})}function c(e){return Object(s["a"])({url:"/iot/job",method:"put",data:e})}function u(e){return Object(s["a"])({url:"/iot/job/"+e,method:"delete"})}function p(e,t){var a={jobId:e,status:t};return Object(s["a"])({url:"/iot/job/changeStatus",method:"put",data:a})}function m(e,t){var a={jobId:e,jobGroup:t};return Object(s["a"])({url:"/iot/job/run",method:"put",data:a})}var h=a("bdd0"),f={components:{Crontab:h["a"]},name:"device-timer",dicts:["sys_job_group","sys_job_status"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.thingsModel=this.formatArrayIndex(e.cacheThingsModel),this.thingsModel.properties&&0!==this.thingsModel.properties.length&&(this.thingsModel.properties=this.thingsModel.properties.filter((function(e){return e.datatype.params&&0!==e.datatype.params.length&&(e.datatype.params=e.datatype.params.filter((function(e){return 0==e.isMonitor&&0==e.isReadonly}))),0==e.isMonitor&&0==e.isReadonly}))),this.thingsModel.functions&&0!==this.thingsModel.functions.length&&(this.thingsModel.functions=this.thingsModel.functions.filter((function(e){return e.datatype.params&&0!==e.datatype.params.length&&(e.datatype.params=e.datatype.params.filter((function(e){return 0==e.isMonitor&&0==e.isReadonly}))),0==e.isMonitor&&0==e.isReadonly}))),this.queryParams.deviceId=this.deviceInfo.deviceId)}},data:function(){return{deviceInfo:{},actionList:[],thingsModel:{},loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,jobList:[],title:"",open:!1,openView:!1,openCron:!1,expression:"",submitButtonLoading:!1,queryParams:{pageNum:1,pageSize:10,deviceId:0,jobName:void 0,jobGroup:void 0,status:void 0},timerWeeks:[{value:1,label:"周一"},{value:2,label:"周二"},{value:3,label:"周三"},{value:4,label:"周四"},{value:5,label:"周五"},{value:6,label:"周六"},{value:7,label:"周日"}],timerWeekValue:[1,2,3,4,5,6,7],timerTimeValue:"",modelTypes:[{value:1,label:"属性"},{value:2,label:"功能"}],form:{},rules:{jobName:[{required:!0,message:"定时名称不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.jobList=t.rows,e.total=t.total,e.loading=!1}))},jobGroupFormat:function(e,t){return this.selectDictLabel(this.dict.type.sys_job_group,e.jobGroup)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobId})),this.single=1!=e.length,this.multiple=!e.length},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+'""'+e.jobName+'"定时吗？').then((function(){return p(e.jobId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleRun:function(e){var t=this;this.$modal.confirm('确认要立即执行一次"'+e.jobName+'"定时吗？').then((function(){return m(e.jobId,e.jobGroup)})).then((function(){t.$modal.msgSuccess("执行成功")})).catch((function(){}))},handleView:function(e){var t=this;l(e.jobId).then((function(e){t.form=e.data,t.openView=!0}))},handleShowCron:function(){this.expression=this.form.cronExpression,this.openCron=!0},crontabFill:function(e){this.form.cronExpression=e},handleAdd:function(){this.reset(),this.open=!0,this.title="添加定时"},handleCancel:function(){this.open=!1,this.reset()},reset:function(){this.form={jobId:void 0,jobName:void 0,cronExpression:void 0,status:"0",jobGroup:"DEFAULT",misfirePolicy:2,concurrent:1,isAdvance:0,jobType:1,productId:0,productName:"",sceneId:0,alertId:0,actions:""},this.submitButtonLoading=!1,this.timerWeekValue=[1,2,3,4,5,6,7],this.timerTimeValue="",this.actionList=[{id:"",name:"",value:"",valueName:"",type:1,parentId:"",parentName:"",arrayIndex:"",arrayIndexName:"",model:null}],this.resetForm("form")},handleUpdate:function(e){var t=this;this.reset();var a=e.jobId||this.ids;l(a).then((function(e){t.form=e.data,t.actionList=JSON.parse(t.form.actions);for(var a=0;a<t.actionList.length;a++)1==t.actionList[a].type?t.setParentAndModelData(t.actionList[a],t.thingsModel.properties):2==t.actionList[a].type&&t.setParentAndModelData(t.actionList[a],t.thingsModel.functions);if(0==t.form.isAdvance){var i=t.form.cronExpression.substring(12).split(",").map(Number);t.timerWeekValue=i,t.timerTimeValue=t.form.cronExpression.substring(5,7)+":"+t.form.cronExpression.substring(2,4)}t.open=!0,t.title="修改定时"}))},setParentAndModelData:function(e,t){for(var a=0;a<t.length;a++)if(e.parentId==t[a].id){if(e.parentModel=t[a],"object"===e.parentModel.datatype.type)for(var i=0;i<e.parentModel.datatype.params.length;i++)e.id==e.parentModel.datatype.params[i].id&&(e.model=e.parentModel.datatype.params[i]);else if("object"===e.parentModel.datatype.arrayType&&"array"===e.parentModel.datatype.type){-1!=e.id.indexOf("array_")&&(e.id=e.id.substring(9));for(var n=0;n<e.parentModel.datatype.params.length;n++)e.id==e.parentModel.datatype.params[n].id&&(e.model=e.parentModel.datatype.params[n])}else"object"!==e.parentModel.datatype.arrayType&&"array"===e.parentModel.datatype.type?(-1!=e.id.indexOf("array_")&&(e.id=e.id.substring(9)),e.model={datatype:{type:e.parentModel.datatype.arrayType,maxLength:-1,min:-1,max:-1,unit:"无单位"}}):e.model=e.parentModel;break}},handleDelete:function(e){var t=this,a=e.jobId||this.ids;this.$modal.confirm('是否确认删除定时定时编号为"'+a+'"的数据项？').then((function(){return u(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/job/export",Object(r["a"])({},this.queryParams),"job_".concat((new Date).getTime(),".xlsx"))},weekChange:function(e){this.gentCronExpression()},timeChange:function(e){this.gentCronExpression()},customerCronChange:function(e){0==e&&this.gentCronExpression()},gentCronExpression:function(){var e="00",t="00";null!=this.timerTimeValue&&""!=this.timerTimeValue&&(e=this.timerTimeValue.substring(0,2),t=this.timerTimeValue.substring(3));var a="*";this.timerWeekValue.length>0&&(a=this.timerWeekValue.sort()),this.form.cronExpression="0 "+t+" "+e+" ? * "+a},formatCronDisplay:function(e){var t="";if(0==e.isAdvance){var a='<br /><span style="color:#F56C6C">时间 '+e.cronExpression.substring(5,7)+":"+e.cronExpression.substring(2,4)+"</span>",i=e.cronExpression.substring(12);if("1,2,3,4,5,6,7"==i)t="每天 "+a;else{for(var n=i.split(","),r=0;r<n.length;r++)"1"==n[r]?t+="周一、":"2"==n[r]?t+="周二、":"3"==n[r]?t+="周三、":"4"==n[r]?t+="周四、":"5"==n[r]?t+="周五、":"6"==n[r]?t+="周六、":"7"==n[r]&&(t+="周日、");t=t.substring(0,t.length-1)+" "+a}}else t="自定义Cron表达式";return t},formatActionsDisplay:function(e){if(null!=e&&""!=e){for(var t=JSON.parse(e),a="",i=0;i<t.length;i++)t[i].arrayIndexName?a+="".concat(t[i].parentName," >> ").concat(t[i].arrayIndexName," >> ").concat(t[i].name,' <span style="color:#F56C6C"> ').concat(t[i].valueName?t[i].valueName:t[i].value,"</span><br />"):t[i].parentName!==t[i].name?a+="".concat(t[i].parentName," >> ").concat(t[i].name,' <span style="color:#F56C6C">').concat(t[i].valueName?t[i].valueName:t[i].value,"</span><br />"):a+="".concat(t[i].name,' <span style="color:#F56C6C">').concat(t[i].valueName?t[i].valueName:t[i].value,"</span><br />");return""==a?"无":a}},formatArrayIndex:function(e){var t=Object(r["a"])({},e);for(var a in t)t[a]=t[a].map((function(e){if("array"===e.datatype.type){for(var t=[],a=0;a<e.datatype.arrayCount;a++){var i=a>9?String(a):"0"+a;e.datatype.arrayType,t.push({id:i,name:e.name+" "+(a+1)})}e.datatype.arrayModel=t}return e}));return t},handleAddActionItem:function(){this.actionList.push({id:"",name:"",value:"",valueName:"",type:1,parentId:"",parentName:"",arrayIndex:"",arrayIndexName:"",model:null})},handleRemoveActionItem:function(e){this.actionList.splice(e,1)},handleActionTypeChange:function(e,t){this.actionList[t].id="",this.actionList[t].name="",this.actionList[t].value="",this.actionList[t].valueName="",this.actionList[t].parentId="",this.actionList[t].parentName="",this.actionList[t].arrayIndex="",this.actionList[t].arrayIndexName="",this.actionList[t].parentModel=null,this.actionList[t].model=null},handleActionIndexChange:function(e,t){this.actionList[t].arrayIndexName=this.actionList[t].parentModel.datatype.arrayModel.find((function(t){return t.id==e})).name,this.actionList[t].value="","object"===this.actionList[t].parentModel.datatype.arrayType&&(this.actionList[t].id="",this.actionList[t].name="")},handleActionParentModelChange:function(e,t){this.actionList[t].model=null,this.actionList[t].value="",this.actionList[t].arrayIndex="",this.actionList[t].arrayIndexName="";var a=[];1==this.actionList[t].type?a=this.thingsModel.properties:2==this.actionList[t].type&&(a=this.thingsModel.functions);for(var i=0;i<a.length;i++)if(a[i].id==e){this.actionList[t].parentName=a[i].name,this.actionList[t].parentModel=a[i],"object"===a[i].datatype.type||"array"===a[i].datatype.type&&"object"===a[i].datatype.arrayType?(this.actionList[t].id="",this.actionList[t].name=""):"array"===a[i].datatype.type&&"object"!==a[i].datatype.arrayType?(this.actionList[t].id=a[i].id,this.actionList[t].name=a[i].name,this.actionList[t].model={datatype:{type:a[i].datatype.arrayType,maxLength:-1,min:-1,max:-1,unit:"无单位"}}):(this.actionList[t].id=a[i].id,this.actionList[t].name=a[i].name,this.actionList[t].model=a[i]);break}},handleActionModelChange:function(e,t){this.actionList[t].value="";var a=null;"array"!==this.actionList[t].parentModel.datatype.type&&"object"!==this.actionList[t].parentModel.datatype.type||(a=this.actionList[t].parentModel.datatype.params.find((function(t){return t.id==e})),this.actionList[t].name=a.name,this.actionList[t].model=a)},handleSubmitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){var a=[];if(0==e.form.isAdvance){if(""==e.timerTimeValue||null==e.timerTimeValue)return void e.$modal.alertError("执行时间不能空");if(null==e.timerWeekValue||""==e.timerWeekValue)return void e.$modal.alertError("请选择要执行的星期")}else if(1==e.form.isAdvance&&""==e.form.cronExpression)return void e.$modal.alertError("cron表达式不能为空");for(var i,n=function(){if(""===e.actionList[r].value)return e.$modal.alertError("执行动作中的选项和值不能为空"),{v:void 0};var t=e.actionList[r];""!=t.arrayIndex?(t.arrayIndex,t.id):t.id;var i="";i="bool"===t.model.datatype.type?1===t.value?t.model.datatype.trueText:t.model.datatype.falseText:"enum"===t.model.datatype.type?t.model.datatype.enumList.find((function(e){return e.value===t.value})).text:"",a[r]={type:t.type,id:t.id,name:t.name,value:t.value,valueName:i,parentId:t.parentId,parentName:t.parentName,arrayIndex:t.arrayIndex,arrayIndexName:t.arrayIndexName,deviceId:e.deviceInfo.deviceId,deviceName:e.deviceInfo.deviceName}},r=0;r<e.actionList.length;r++)if(i=n(),i)return i.v;e.form.actions=JSON.stringify(a),e.form.deviceId=e.deviceInfo.deviceId,e.form.deviceName=e.deviceInfo.deviceName,e.form.serialNumber=e.deviceInfo.serialNumber,e.form.productId=e.deviceInfo.productId,e.form.productName=e.deviceInfo.productName,e.submitButtonLoading=!0,void 0!=e.form.jobId?c(e.form).then((function(){e.$modal.msgSuccess("修改成功"),e.submitButtonLoading=!1,e.open=!1,e.getList()})):d(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.submitButtonLoading=!1,e.open=!1,e.getList()}))}}))}}},v=f,y=(a("377d"),a("2877")),b=Object(y["a"])(v,i,n,!1,null,"2aeb3c34",null);t["default"]=b.exports},"814a":function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"f",(function(){return r})),a.d(t,"d",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return d})),a.d(t,"b",(function(){return c}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/getLatest/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function d(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},"8a79":function(e,t,a){"use strict";var i=a("23e7"),n=a("e330"),r=a("06cf").f,s=a("50c4"),o=a("577e"),l=a("5a34"),d=a("1d80"),c=a("ab13"),u=a("c430"),p=n("".endsWith),m=n("".slice),h=Math.min,f=c("endsWith"),v=!u&&!f&&!!function(){var e=r(String.prototype,"endsWith");return e&&!e.writable}();i({target:"String",proto:!0,forced:!v&&!f},{endsWith:function(e){var t=o(d(this));l(e);var a=arguments.length>1?arguments[1]:void 0,i=t.length,n=void 0===a?i:h(s(a),i),r=o(e);return p?p(t,r,n):m(t,n-r.length,n)===r}})},9467:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"行政区划"}},[a("el-cascader",{attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),a("el-form-item",{attrs:{label:"设备类型",prop:"deviceType"}},[a("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"通道类型",prop:"channelType"}},[a("el-select",{attrs:{placeholder:"请选择设备类型"},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"通道数量",prop:"createNum"}},[a("el-input-number",{staticStyle:{width:"220px"},attrs:{"controls-position":"right",placeholder:"请输入生成通道数量",type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("生 成")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取 消")])],1)],1)},n=[],r=a("ef6c"),s=a("e2de"),o={name:"SipidDialog",dicts:["video_type","channel_type"],props:{product:{type:Object,default:null}},data:function(){return{loading:!0,title:"生成设备编号和通道",total:0,open:!1,devsipid:"",createForm:{city:"",deviceType:"",channelType:"",createNum:1},cityOptions:r["regionData"],city:"",cityCode:""}},created:function(){},methods:{changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=r["CodeToText"][e[0]]+"/"+r["CodeToText"][e[1]]+"/"+r["CodeToText"][e[2]];this.createForm.citycode=t}},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError("通道数量至少一个"):(this.createForm.productId=this.product.productId,this.createForm.productName=this.product.productName,this.createForm.tenantId=this.product.tenantId,this.createForm.tenantName=this.product.tenantName,this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?Object(s["a"])(this.createForm.createNum,this.createForm).then((function(t){e.$modal.msgSuccess("已生成设备编号和通道"),e.devsipid=t.data,e.confirmSelectProduct()})):this.$message({type:"error",message:"请选择地区，设备类型，通道类型！！"}))},confirmSelectProduct:function(){this.open=!1,this.$emit("addGenEvent",this.devsipid)},closeDialog:function(){this.open=!1}}},l=o,d=a("2877"),c=Object(d["a"])(l,i,n,!1,null,"09358636",null);t["default"]=c.exports},9626:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"设备编号",prop:"serialNumber"}},[a("el-input",{attrs:{placeholder:"请输入设备编号",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),a("el-form-item",{attrs:{label:"设备状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择设备状态",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),a("el-table-column",{attrs:{label:"设备编号",align:"center",prop:"serialNumber"}}),a("el-table-column",{attrs:{label:"网关编码",align:"center",prop:"gwDevCode"}}),a("el-table-column",{attrs:{label:"从机地址",align:"center",prop:"addr"}}),a("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"firmwareVersion"}}),a("el-table-column",{attrs:{label:"设备状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"激活时间",align:"center",prop:"activeTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.activeTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:edit"],expression:"['iot:device:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"设备名",prop:"deviceName"}},[a("el-input",{attrs:{placeholder:"请输入设备名"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1),a("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[a("el-input",{attrs:{placeholder:"请输入固件版本"},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=(a("d81d"),a("584f")),s={name:"device-sub",props:{device:{type:Object,default:null}},dicts:["iot_device_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,deviceList:[],title:"",open:!1,daterangeActiveTime:[],queryParams:{pageNum:1,pageSize:10,gwDevCode:""},form:{},rules:{deviceName:[{required:!0,message:"设备名不能为空",trigger:"blur"}],firmwareVersion:[{required:!0,message:"固件版本不能为空",trigger:"blur"}]}}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.gwDevCode=this.deviceInfo.serialNumber,this.getList())}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,null!=this.daterangeActiveTime&&""!=this.daterangeActiveTime&&(this.queryParams.params["beginActiveTime"]=this.daterangeActiveTime[0],this.queryParams.params["endActiveTime"]=this.daterangeActiveTime[1]),Object(r["l"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.map((function(e){var t=e.serialNumber.split("_");e.addr=t[1]}))}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deviceId:null,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,firmwareVersion:null,status:0,isShadow:null,rssi:null,isCustomerLocation:null,networkAddress:null,networkIp:null,thingsModelValue:null,longitude:null,latitude:null,activeTime:null,delFlag:null,createBy:null,imgUrl:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeActiveTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.deviceId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加设备"},handleUpdate:function(e){var t=this;this.reset();var a=e.deviceId||this.ids;Object(r["f"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改设备"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deviceId?Object(r["s"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.deviceId||this.ids;this.$modal.confirm('是否确认删除设备编号为"'+a+'"的数据项？').then((function(){return Object(r["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},o=s,l=a("2877"),d=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=d.exports},"97d6":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"root"},[a("div",{staticClass:"container-shell"},[a("div",{ref:"container",attrs:{id:"container"}})])])},n=[],r=a("c7eb"),s=a("1da1"),o=(a("a9e3"),a("ac1f"),a("00b4"),a("f5a7")),l={},d={name:"player",props:{playerinfo:{type:Object,default:null}},mounted:function(){console.log(this._uid)},watch:{playerinfo:function(e,t){console.log("playerinfo 发生变化"),this.playinfo=e,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype)}},jessibuca:null,data:function(){return{isPlaybackPause:!1,useWebGPU:!1,isInit:!1,playinfo:{},playtype:"play",operateBtns:{}}},beforeDestroy:function(){},created:function(){this.playinfo=this.playerinfo,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype),this.init()},methods:{init:function(){var e=this,t="gpu"in navigator;t?(console.log("支持webGPU"),this.useWebGPU=!0):(console.log("暂不支持webGPU，降级到webgl渲染"),this.useWebGPU=!1);var a=this.isMobile()||this.isPad();a&&window.VConsole&&new window.VConsole,this.$nextTick((function(){e.initplayer()}))},initplayer:function(){this.isPlaybackPause=!1,this.initconf(),l[this._uid]=new window.JessibucaPro({container:this.$refs.container,decoder:"/js/jessibuca-pro/decoder-pro.js",videoBuffer:Number(.2),isResize:!1,useWCS:!1,useMSE:!1,useSIMD:!0,wcsUseVideoRender:!1,loadingText:"加载中",debug:!1,showBandwidth:!0,showPlaybackOperate:!0,operateBtns:this.operateBtns,forceNoOffscreen:!0,isNotMute:!1,showPerformance:!1,playbackForwardMaxRateDecodeIFrame:4,useWebGPU:this.useWebGPU});var e=l[this._uid];this.initcallback(e),this.isInit=!0},initconf:function(){"play"===this.playtype?this.operateBtns={fullscreen:!0,zoom:!0,ptz:!0,play:!0}:this.operateBtns={fullscreen:!0,zoom:!0,play:!0,ptz:!1}},initcallback:function(e){var t=this;e.on("error",(function(e){console.log("error"),console.log(e),t.destroy()})),e.on("pause",(function(e){console.log("pause success!"),console.log(e)})),e.on("stats",(function(e){console.log("stats is",e)})),e.on("timeout",(function(){console.log("timeout")})),e.on("playbackPreRateChange",(function(t){e.forward(t)}));var a=0,i=0;e.on("timeUpdate",(function(e){i=parseInt(e/6e4),a!==i&&a++})),e.on(JessibucaPro.EVENTS.ptz,(function(e){console.log("ptz arrow",e),t.handlePtz(e)}))},registercallback:function(e,t){l[this._uid]&&l[this._uid].on(e,t)},isMobile:function(){return/iphone|ipad|android.*mobile|windows.*phone|blackberry.*mobile/i.test(window.navigator.userAgent.toLowerCase())},isPad:function(){return/ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(window.navigator.userAgent.toLowerCase())},play:function(e){l[this._uid]&&l[this._uid].play(e)},pause:function(){l[this._uid]&&l[this._uid].pause()},replay:function(e){var t=this;l[this._uid]?l[this._uid].destroy().then((function(){t.initplayer(),t.play(e)})):(this.initplayer(),this.play(e))},handlePtz:function(e){var t=0,a=0;"left"===e?t=2:"right"===e?t=1:"up"===e?a=1:"down"===e&&(a=2);var i={leftRight:t,upDown:a,moveSpeed:125};this.playinfo&&""!==this.playinfo.playtype&&Object(o["c"])(this.playinfo.deviceId,this.playinfo.channelId,i).then(function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(t){return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},playback:function(e,t){l[this._uid]&&(l[this._uid].playback(e,{playList:t,fps:25,showControl:!0,showRateBtn:!0,isUseFpsRender:!0,isCacheBeforeDecodeForFpsRender:!1,supportWheel:!0,rateConfig:[{label:"正常",value:1},{label:"2倍",value:2},{label:"4倍",value:4},{label:"8倍",value:8}]}),this.isPlaybackPause=!1)},playbackPause:function(){l[this._uid]&&(l[this._uid].playbackPause(),this.isPlaybackPause=!0)},replayback:function(e,t){var a=this;l[this._uid]?l[this._uid].destroy().then((function(){a.initplayer(),a.playback(e,t)})):(this.initplayer(),this.playback(e,t))},destroy:function(){var e=this;l[this._uid]&&l[this._uid].destroy().then((function(){e.initplayer()}))},close:function(){l[this._uid]&&l[this._uid].close()}}},c=d,u=(a("1b7a"),a("2877")),p=Object(u["a"])(c,i,n,!1,null,"263ca075",null);t["default"]=p.exports},"9b9c":function(e,t,a){"use strict";a.d(t,"f",(function(){return n})),a.d(t,"g",(function(){return r})),a.d(t,"e",(function(){return s})),a.d(t,"a",(function(){return o})),a.d(t,"i",(function(){return l})),a.d(t,"d",(function(){return d})),a.d(t,"b",(function(){return c})),a.d(t,"c",(function(){return u})),a.d(t,"h",(function(){return p}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/product/list",method:"get",params:e})}function r(){return Object(i["a"])({url:"/iot/product/shortList",method:"get"})}function s(e){return Object(i["a"])({url:"/iot/product/"+e,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/product",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/iot/product",method:"put",data:e})}function d(e){return Object(i["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/iot/product/status/",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/product/"+e,method:"delete"})}function p(e){return Object(i["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:e})}},a035:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/deviceLog/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/deviceLog/history",method:"get",params:e})}},a4a0:function(e,t,a){},a824:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return l})),a.d(t,"d",(function(){return d}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/salve/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/salve/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/salve",method:"post",data:e})}function o(e){return Object(i["a"])({url:"/iot/salve",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/iot/salve/"+e,method:"delete"})}function d(e){return Object(i["a"])({url:"/iot/salve/listByPId",method:"get",params:e})}},abf5:function(e,t,a){},b52e:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:share"],expression:"['iot:device:user:share']"}],attrs:{type:"primary",plain:"",icon:"el-icon-share",size:"mini",disabled:0==e.deviceInfo.isOwner||null==e.deviceInfo.isOwner},on:{click:e.shareDevice}},[e._v("分享设备")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceUserList,size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"用户编号",align:"center",prop:"userId",width:"100"}}),a("el-table-column",{attrs:{label:"用户名称",align:"center",prop:"userName"}}),a("el-table-column",{attrs:{label:"手机号码",align:"center",prop:"phonenumber",width:"150"}}),a("el-table-column",{attrs:{label:"用户类型",align:"center",prop:"isOwner",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.isOwner?a("el-tag",{attrs:{type:"primary"}},[e._v("拥有者")]):a("el-tag",{attrs:{type:"success"}},[e._v("分享")])]}}])}),a("el-table-column",{attrs:{label:"分享时间",align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"left",prop:"remark","header-align":"center","min-width":"150"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner&&1==e.deviceInfo.isOwner?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:query"],expression:"['iot:device:user:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("查看")]):e._e(),0==t.row.isOwner&&1==e.deviceInfo.isOwner?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:remove"],expression:"['iot:device:user:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("取消分享")]):e._e()]}}])})],1),a("el-dialog",{attrs:{title:"设备分享",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-50px"}},[a("el-divider")],1),1==e.type?a("el-form",{ref:"queryForm",attrs:{model:e.permParams,rules:e.rules,inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"手机号码",prop:"phonenumber"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{type:"text",placeholder:"请输入用户手机号码",minlength:"10",clearable:"",size:"small","show-word-limit":""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.permParams.phonenumber,callback:function(t){e.$set(e.permParams,"phonenumber",t)},expression:"permParams.phonenumber"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.userQuery}},[e._v("查询用户")])],1)],1):e._e(),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.permsLoading,expression:"permsLoading"}],staticStyle:{"background-color":"#f8f8f9","line-height":"28px"}},[e.message?a("div",{staticStyle:{padding:"20px"}},[e._v(e._s(e.message))]):e._e(),e.form.userId?a("div",{staticStyle:{padding:"15px"}},[a("div",{staticStyle:{"font-weight":"bold","line-height":"28px"}},[e._v("用户信息")]),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户ID：")]),a("span",[e._v(e._s(e.form.userId))]),a("br"),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("手机号码：")]),a("span",[e._v(e._s(e.form.phonenumber))]),a("br"),a("span",{staticStyle:{width:"80px",display:"inline-block"}},[e._v("用户名称：")]),a("span",[e._v(e._s(e.form.userName))]),a("br"),a("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("设置用户权限")]),a("el-table",{ref:"multipleTable",attrs:{data:e.sharePermissionList,"highlight-current-row":"",size:"mini"},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{key:"modelName",attrs:{label:"权限名称",align:"center",prop:"modelName"}}),a("el-table-column",{key:"identifier",attrs:{label:"权限标识",align:"center",prop:"identifier"}}),a("el-table-column",{key:"remark",attrs:{label:"备注信息",align:"left","min-width":"100","header-align":"center",prop:"remark"}})],1),a("div",{staticStyle:{"font-weight":"bold",margin:"15px 0 10px"}},[e._v("备注信息")]),a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",rows:"2"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1):e._e()]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:user:edit"],expression:"['iot:device:user:edit']"}],attrs:{type:"primary",disabled:!e.form.userId||!e.deviceInfo.deviceId},on:{click:e.submitForm}},[e._v("修改")]),a("el-button",{on:{click:e.closeSelectUser}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("c7eb"),s=a("1da1"),o=(a("99af"),a("a15b"),a("d81d"),a("01ca")),l=a("b775");function d(e){return Object(l["a"])({url:"/iot/share/list",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/iot/share/shareUser",method:"get",params:e})}function u(e,t){return Object(l["a"])({url:"/iot/share/detail?deviceId="+e+"&userId="+t,method:"get"})}function p(e){return Object(l["a"])({url:"/iot/share",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/iot/share",method:"put",data:e})}function h(e){return Object(l["a"])({url:"/iot/share",method:"delete",data:e})}var f={name:"device-user",dicts:["iot_yes_no"],props:{device:{type:Object,default:null}},watch:{device:{deep:!0,immediate:!0,handler:function(e){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.deviceId=this.deviceInfo.deviceId,this.getList())}}},data:function(){return{type:1,message:"",permsLoading:!1,sharePermissionList:[],open:!1,permParams:{userName:void 0,phonenumber:void 0,deviceId:null},rules:{phonenumber:[{required:!0,message:"手机号码不能为空",trigger:"blur"},{min:11,max:11,message:"手机号码长度为11位",trigger:"blur"}]},loading:!0,total:0,deviceUserList:[],deviceInfo:{},queryParams:{pageNum:1,pageSize:10,deviceName:null,userName:null,userId:null,tenantName:null,isOwner:null},form:{}}},created:function(){this.queryParams.deviceId=this.device.deviceId,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,d(this.queryParams).then((function(t){e.deviceUserList=t.rows,e.total=t.total,e.loading=!1}))},reset:function(){this.form={deviceId:null,userId:null,deviceName:null,userName:null,perms:null,phonenumber:null,remark:null},this.sharePermissionList=[],this.message="",this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleUpdate:function(e){var t=this;this.reset(),this.type=2,u(e.deviceId,e.userId).then((function(e){t.form=e.data,t.getPermissionList(),t.open=!0}))},shareDevice:function(){this.type=1,this.open=!0,this.form={}},handleDelete:function(e){var t=this,a={deviceId:e.deviceId,userId:e.userId};this.$modal.confirm("确认取消分享设备？").then((function(){return h(a)})).then((function(){t.getList(),t.$modal.msgSuccess("取消分享成功")})).catch((function(){}))},userQuery:function(){var e=this;this.$refs["queryForm"].validate((function(t){t&&(e.reset(),e.getShareUser())}))},getShareUser:function(){var e=this;this.permsLoading=!0,this.deviceInfo.deviceId?(this.permParams.deviceId=this.deviceInfo.deviceId,c(this.permParams).then((function(t){t.data?(e.form=t.data,e.getPermissionList()):(e.permsLoading=!1,e.message="查询不到用户信息，或者该用户已经是设备用户")}))):this.$modal.alert("查询不到设备信息，请刷新后重试")},getPermissionList:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=[],e.form.perms&&(a=e.form.perms.split(",")),Object(o["g"])(e.deviceInfo.productId).then((function(t){if(e.sharePermissionList=[{identifier:"ota",modelName:"设备升级",remark:"设备OTA升级"},{identifier:"timer",modelName:"设备定时",remark:"定时执行任务"},{identifier:"log",modelName:"设备日志",remark:"包含事件日志和指令日志"},{identifier:"monitor",modelName:"实时监测",remark:"下发实时监测指令后，图表实时显示设备上报数据"},{identifier:"statistic",modelName:"监测统计",remark:"图表显示存储的历史监测数据"}],e.sharePermissionList=e.sharePermissionList.concat(t.data),a.length>0)for(var i=function(t){for(var i=0;i<a.length;i++)if(e.sharePermissionList[t].identifier==a[i]){e.$nextTick((function(){e.$refs.multipleTable.toggleRowSelection(e.sharePermissionList[t],!0)}));break}},n=0;n<e.sharePermissionList.length;n++)i(n);e.permsLoading=!1}));case 3:case"end":return t.stop()}}),t)})))()},resetUserQuery:function(){this.resetForm("queryForm"),this.reset()},closeSelectUser:function(){this.open=!1,this.resetUserQuery()},handleSelectionChange:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},handleSelectionAll:function(e){this.form.perms=e.map((function(e){return e.identifier})).join(",")},submitForm:function(){var e=this;2==this.type?m(this.form).then((function(t){e.$modal.msgSuccess("更新成功"),e.resetUserQuery(),e.open=!1,e.getList()})):1==this.type&&(this.form.deviceId=this.deviceInfo.deviceId,this.form.deviceName=this.deviceInfo.deviceName,p(this.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.resetUserQuery(),e.open=!1,e.getList()})))}}},v=f,y=a("2877"),b=Object(y["a"])(v,i,n,!1,null,null,null);t["default"]=b.exports},ba95:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/simulate/list",method:"get",params:e})}},dc9c:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return r}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/log/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/log/"+e,method:"delete"})}},dd50:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-form",{attrs:{inline:!0,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"监测间隔(ms)"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值范围500-10000毫秒",placement:"top"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测间隔",type:"number",clearable:"",size:"small"},model:{value:e.monitorInterval,callback:function(t){e.monitorInterval=t},expression:"monitorInterval"}})],1)],1),a("el-form-item",{attrs:{label:"监测次数"}},[a("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"取值方位1-300",placement:"top"}},[a("el-input",{staticStyle:{width:"180px"},attrs:{placeholder:"请输入监测次数",type:"number",clearable:"",size:"small"},model:{value:e.monitorNumber,callback:function(t){e.monitorNumber=t},expression:"monitorNumber"}})],1)],1),a("el-form-item",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:service:invoke "],expression:"['iot:service:invoke ']"}],staticStyle:{"margin-left":"30px"},attrs:{type:"success",icon:"el-icon-video-play",size:"mini"},on:{click:function(t){return e.beginMonitor()}}},[e._v("开始监测")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:service:invoke "],expression:"['iot:service:invoke ']"}],attrs:{type:"danger",icon:"el-icon-video-pause",size:"mini"},on:{click:function(t){return e.stopMonitor()}}},[e._v("停止监测")])],1)],1),a("el-row",{directives:[{name:"loading",rawName:"v-loading",value:e.chartLoading,expression:"chartLoading"}],attrs:{gutter:20,"element-loading-text":"正在接收设备数据，请耐心等待......","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"}},e._l(e.monitorThings,(function(e,t){return a("el-col",{key:t,staticStyle:{"margin-bottom":"20px"},attrs:{span:12}},[a("el-card",{attrs:{shadow:"hover","body-style":{paddingTop:"10px",marginBottom:"-20px"}}},[a("div",{ref:"monitor",refInFor:!0,staticStyle:{height:"210px",padding:"0"}})])],1)})),1)],1)},n=[],r=(a("14d9"),a("b0c0"),a("b64b"),a("d3b7"),a("25f0"),{name:"DeviceMonitor",props:{device:{type:Object,default:null}},watch:{device:function(e,t){if(this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId){this.monitorThings=this.deviceInfo.monitorList,this.dataList=[];for(var a=0;a<this.monitorThings.length;a++)this.dataList.push({id:this.monitorThings[a].id,name:this.monitorThings[a].name,data:[]});this.$nextTick((function(){this.getMonitorChart()})),this.mqttCallback()}}},data:function(){return{monitorInterval:1e3,monitorNumber:60,chart:[],dataList:[],monitorThings:[],chartLoading:!1,deviceInfo:{}}},created:function(){},methods:{mqttPublish:function(e,t){var a=this,i="",n="";4==t.type&&(i="/"+e.productId+"/"+e.serialNumber+"/monitor/get",n='{"count":'+t.value+',"interval":'+this.monitorInterval+"}",""!=i&&this.$mqttTool.publish(i,n,t.name).then((function(e){a.$modal.notifySuccess(e)})).catch((function(e){a.$modal.notifyError(e)})))},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);if(a=JSON.parse(a.toString()),a&&("status"==n[3]&&(console.log("接收到【设备状态】主题：",t),console.log("接收到【设备状态】内容：",a),e.deviceInfo.serialNumber==r&&(e.deviceInfo.status=a.status,e.deviceInfo.isShadow=a.isShadow,e.deviceInfo.rssi=a.rssi)),"monitor"==n[3])){console.log("接收到【实时监测】主题：",t),console.log("接收到【实时监测】内容：",a),e.chartLoading=!1;for(var s=0;s<a.length;s++)for(var o=a[s].value,l=a[s].id,d=(a[s].remark,0);d<e.dataList.length;d++){if(l==e.dataList[d].id){e.dataList[d].length>50&&e.dataList[d].shift(),e.dataList[d].data.push([e.getTime(),o]),e.chart[d].setOption({series:[{data:e.dataList[d].data}]});break}if(0==e.dataList[d].id.indexOf("array_")){var c=e.dataList[d].id.substring(6,8),u=e.dataList[d].id.substring(9);if(u==l){var p=o.split(",");e.dataList[d].length>50&&e.dataList[d].shift(),e.dataList[d].data.push([e.getTime(),p[c]]),e.chart[d].setOption({series:[{data:e.dataList[d].data}]});break}}}}}))},beginMonitor:function(){if(3==this.deviceInfo.status){for(var e=0;e<this.dataList.length;e++)this.dataList[e].data=[];(this.monitorInterval<500||this.monitorInterval>1e4)&&this.$modal.alertError("实时监测的间隔范围500-10000毫秒"),(0==this.monitorNumber||this.monitorNumber>300)&&this.$modal.alertError("实时监测数量范围1-300");var t={name:"更新实时监测"};t.value=this.monitorNumber,t.type=4,this.mqttPublish(this.deviceInfo,t),this.chartLoading=!0}else this.$modal.alertError("设备不在线，下发指令失败")},stopMonitor:function(){if(3==this.deviceInfo.status){this.chartLoading=!1;var e={name:"关闭实时监测",value:0,type:4};this.mqttPublish(this.deviceInfo,e)}else this.$modal.alertError("设备不在线，下发指令失败")},getMonitorChart:function(){for(var e=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],t=0;t<this.monitorThings.length;t++){var a;this.$refs.monitor[t].style.width=document.documentElement.clientWidth/2-255+"px",this.chart[t]=this.$echarts.init(this.$refs.monitor[t]),a={title:{left:"center",text:this.monitorThings[t].name+" （单位 "+(void 0!=this.monitorThings[t].datatype.unit?this.monitorThings[t].datatype.unit:"无")+"）",textStyle:{fontSize:14}},grid:{top:"50px",left:"20px",right:"20px",bottom:"10px",containLabel:!0},tooltip:{trigger:"axis",axisPointer:{animation:!0}},xAxis:{type:"time",show:!1,splitLine:{show:!1}},yAxis:{type:"value",boundaryGap:[0,"100%"],splitLine:{show:!0}},series:[{name:this.monitorThings[t].name,type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:t>9?e[0]:e[t]},areaStyle:{},data:[]}]},a&&this.chart[t].setOption(a)}},getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate(),n=e.getHours(),r=e.getMinutes(),s=e.getSeconds();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,n=n<10?"0"+n:n,t+"-"+a+"-"+i+" "+n+":"+r+":"+s}}}),s=r,o=a("2877"),l=Object(o["a"])(s,i,n,!1,null,null,null);t["default"]=l.exports},e2de:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"f",(function(){return d})),a.d(t,"b",(function(){return c})),a.d(t,"g",(function(){return u}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/channel/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"get"})}function s(e,t){return Object(i["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(i["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function d(e,t,a){return Object(i["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:a})}function c(e,t){return Object(i["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function u(e,t,a,n){return Object(i["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+a,method:"get",params:n})}},e51f:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"选择产品",visible:e.open,width:"800px"},on:{"update:visible":function(t){e.open=t}}},[a("div",{staticStyle:{"margin-top":"-55px"}},[a("el-divider",{staticStyle:{"margin-top":"-30px"}}),a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"产品名称",prop:"productName"}},[a("el-input",{attrs:{placeholder:"请输入产品名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.productList,"highlight-current-row":"",size:"mini"},on:{"row-click":e.rowClick}},[a("el-table-column",{attrs:{label:"选择",width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:"产品名称",align:"center",prop:"productName"}}),a("el-table-column",{attrs:{label:"分类名称",align:"center",prop:"categoryName"}}),a("el-table-column",{attrs:{label:"租户名称",align:"center",prop:"tenantName"}}),a("el-table-column",{attrs:{label:"授权码",align:"center",prop:"status",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isAuthorize?a("el-tag",{attrs:{type:"success"}},[e._v("启用")]):e._e(),0==t.row.isAuthorize?a("el-tag",{attrs:{type:"info"}},[e._v("未启用")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"认证方式",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_vertificate_method,value:t.row.vertificateMethod}})]}}])}),a("el-table-column",{attrs:{label:"联网方式",align:"center",prop:"networkMethod"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_network_method,value:t.row.networkMethod}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectProduct}},[e._v("确定")]),a("el-button",{attrs:{type:"info"},on:{click:e.closeDialog}},[e._v("关 闭")])],1)])},n=[],r=(a("a9e3"),a("9b9c")),s={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,Object(r["f"])(this.queryParams).then((function(t){for(var a=0;a<t.rows.length;a++)t.rows[a].isSelect=!1;e.productList=t.rows,e.total=t.total,0!=e.productId&&e.setRadioSelected(e.productId),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.productId),this.product=e)},setRadioSelected:function(e){for(var t=0;t<this.productList.length;t++)this.productList[t].productId==e?this.productList[t].isSelect=!0:this.productList[t].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},o=s,l=a("2877"),d=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=d.exports},e626:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticStyle:{margin:"6px","padding-bottom":"100px"}},[a("el-tabs",{staticStyle:{padding:"10px","min-height":"400px"},attrs:{"tab-position":"left"},on:{"tab-click":e.tabChange},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{name:"basic"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("* 基本信息")]),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",{attrs:{gutter:100}},[a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[a("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}},[0!=e.form.deviceId?a("el-button",{attrs:{slot:"append"},on:{click:e.openSummaryDialog},slot:"append"},[e._v("摘要")]):e._e()],1)],1),a("el-form-item",{attrs:{label:"",prop:"productName"}},[a("template",{slot:"label"},[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" 所属产品 ")]),a("el-input",{attrs:{readonly:"",placeholder:"请选择产品",disabled:1!=e.form.status},model:{value:e.form.productName,callback:function(t){e.$set(e.form,"productName",t)},expression:"form.productName"}},[a("el-button",{attrs:{slot:"append",disabled:1!=e.form.status},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v("选择")])],1)],2),a("el-form-item",{attrs:{label:"",prop:"serialNumber"}},[a("template",{slot:"label"},[a("span",{staticStyle:{color:"red"}},[e._v("*")]),e._v(" 设备编号 ")]),a("el-input",{attrs:{placeholder:"请输入设备编号",disabled:1!=e.form.status,maxlength:"32"},model:{value:e.form.serialNumber,callback:function(t){e.$set(e.form,"serialNumber",t)},expression:"form.serialNumber"}},[3!==e.form.deviceType?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{slot:"append",loading:e.genDisabled,disabled:1!=e.form.status},on:{click:e.generateNum},slot:"append"},[e._v("生成")]):e._e(),3===e.form.deviceType?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{slot:"append",disabled:1!=e.form.status},on:{click:function(t){return e.genSipID()}},slot:"append"},[e._v("生成")]):e._e()],1)],2),e.openServerTip?a("el-form-item",[[a("el-alert",{attrs:{type:"info","show-icon":"",description:"当前选择TCP协议,设备编号生成为HEX格式"}})]],2):e._e(),e.openTip?a("el-form-item",[[a("el-alert",{attrs:{type:"success","show-icon":"",description:"当前选择的产品属于modbus协议,将在网关设备创建后根据采集点模板生成子设备"}})]],2):e._e(),a("el-form-item",{attrs:{label:"固件版本",prop:"firmwareVersion"}},[a("el-input",{attrs:{placeholder:"请输入固件版本",type:"number",step:"0.1",disabled:1!=e.form.status||3===e.form.deviceType},model:{value:e.form.firmwareVersion,callback:function(t){e.$set(e.form,"firmwareVersion",t)},expression:"form.firmwareVersion"}},[a("template",{slot:"prepend"},[e._v("Version")])],2)],1),a("el-form-item",{attrs:{label:"模拟设备",prop:"isSimulate"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0,disabled:3===e.form.deviceType},model:{value:e.form.isSimulate,callback:function(t){e.$set(e.form,"isSimulate",t)},expression:"form.isSimulate"}})],1),a("el-form-item",{attrs:{label:"设备影子",prop:"isShadow"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0,disabled:3===e.form.deviceType},model:{value:e.form.isShadow,callback:function(t){e.$set(e.form,"isShadow",t)},expression:"form.isShadow"}})],1),a("el-form-item",{attrs:{label:"禁用设备",prop:"deviceStatus"}},[a("el-switch",{attrs:{"active-text":"","inactive-text":"",disabled:1==e.form.status||3===e.form.deviceType,"active-value":1,"inactive-value":0,"active-color":"#F56C6C"},model:{value:e.deviceStatus,callback:function(t){e.deviceStatus=t},expression:"deviceStatus"}})],1),a("el-form-item",{attrs:{label:"备注信息",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容",rows:"1"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("el-form-item",{attrs:{label:"定位方式",prop:"locationWay"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择设备状态",clearable:"",size:"small",disabled:3===e.form.deviceType},model:{value:e.form.locationWay,callback:function(t){e.$set(e.form,"locationWay",t)},expression:"form.locationWay"}},e._l(e.dict.type.iot_location_way,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:Number(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"设备经度",prop:"longitude"}},[a("el-input",{attrs:{placeholder:"请输入设备经度",type:"number",disabled:3!=e.form.locationWay},model:{value:e.form.longitude,callback:function(t){e.$set(e.form,"longitude",t)},expression:"form.longitude"}},[a("el-link",{attrs:{slot:"append",underline:!1,href:"https://api.map.baidu.com/lbsapi/getpoint/index.html",target:"_blank",disabled:3!=e.form.locationWay},slot:"append"},[e._v("坐标拾取")])],1)],1),a("el-form-item",{attrs:{label:"设备纬度",prop:"latitude"}},[a("el-input",{attrs:{placeholder:"请输入设备纬度",type:"number",disabled:3!=e.form.locationWay},model:{value:e.form.latitude,callback:function(t){e.$set(e.form,"latitude",t)},expression:"form.latitude"}},[a("el-link",{attrs:{slot:"append",underline:!1,href:"https://api.map.baidu.com/lbsapi/getpoint/index.html",target:"_blank",disabled:3!=e.form.locationWay},slot:"append"},[e._v("坐标拾取")])],1)],1),a("el-form-item",{attrs:{label:"所在地址",prop:"networkAddress"}},[a("el-input",{attrs:{placeholder:"请输入设备所在地址",disabled:3!=e.form.locationWay},model:{value:e.form.networkAddress,callback:function(t){e.$set(e.form,"networkAddress",t)},expression:"form.networkAddress"}})],1),a("el-form-item",{attrs:{label:"入网地址",prop:"networkIp"}},[a("el-input",{attrs:{placeholder:"设备入网IP",disabled:""},model:{value:e.form.networkIp,callback:function(t){e.$set(e.form,"networkIp",t)},expression:"form.networkIp"}})],1),a("el-form-item",{attrs:{label:"激活时间",prop:"activeTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"设备激活时间",disabled:""},model:{value:e.form.activeTime,callback:function(t){e.$set(e.form,"activeTime",t)},expression:"form.activeTime"}})],1),a("el-form-item",{attrs:{label:"设备信号",prop:"rssi"}},[a("el-input",{attrs:{placeholder:"设备信号强度",disabled:""},model:{value:e.form.rssi,callback:function(t){e.$set(e.form,"rssi",t)},expression:"form.rssi"}})],1),0!=e.form.deviceId?a("el-form-item",{attrs:{label:"其他信息",prop:"remark"}},[a("dict-tag",{staticStyle:{display:"inline-block","margin-right":"8px"},attrs:{options:e.dict.type.iot_device_status,value:e.form.status}}),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.handleViewMqtt()}}},[e._v("认证信息")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){return e.openCodeDialog()}}},[e._v("二维码")])],1):e._e()],1),0!=e.form.deviceId?a("el-col",{attrs:{xs:24,sm:24,md:24,lg:12,xl:8}},[a("div",{staticStyle:{border:"1px solid #dfe4ed","border-radius":"5px",padding:"5px","text-align":"center","line-height":"400px"}},[a("div",{staticStyle:{height:"435px",width:"100%"},attrs:{id:"map"}},[e._v("地图展示区域，新增后显示")])])]):e._e()],1)],1),a("el-form",{staticStyle:{"margin-top":"50px"},attrs:{"label-width":"100px"}},[a("el-form-item",{staticStyle:{"text-align":"center","margin-left":"-100px","margin-top":"10px"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:edit"],expression:"['iot:device:edit']"},{name:"show",rawName:"v-show",value:0!=e.form.deviceId,expression:"form.deviceId != 0"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("修 改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"},{name:"show",rawName:"v-show",value:0==e.form.deviceId,expression:"form.deviceId == 0"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("新 增")])],1)],1),a("product-list",{ref:"productList",attrs:{productId:e.form.productId},on:{productEvent:function(t){return e.getProductData(t)}}}),a("sipid",{ref:"sipidGen",attrs:{product:e.form},on:{addGenEvent:function(t){return e.getSipIDData(t)}}})],1),3!==e.form.deviceType?a("el-tab-pane",{attrs:{name:"runningStatus"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("运行状态")]),e.isSubDev?a("real-time-status",{ref:"realTimeStatus",attrs:{device:e.form},on:{statusEvent:function(t){return e.getDeviceStatusData(t)}}}):a("running-status",{ref:"runningStatus",attrs:{device:e.form},on:{statusEvent:function(t){return e.getDeviceStatusData(t)}}})],1):e._e(),e.isSubDev&&3!==e.form.deviceType?a("el-tab-pane",{attrs:{name:"deviceSub",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("子设备")]),a("device-sub",{ref:"deviceSub",attrs:{device:e.form}})],1):e._e(),3===e.form.deviceType?a("el-tab-pane",{attrs:{name:"sipChannel",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备通道")]),a("channel",{ref:"deviceChannel",attrs:{device:e.form},on:{playerEvent:function(t){return e.getPlayerData(t)}}})],1):e._e(),3===e.form.deviceType?a("el-tab-pane",{attrs:{disabled:0==e.form.deviceId,name:"sipPlayer"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备直播")]),a("device-live-stream",{ref:"deviceLiveStream",attrs:{device:e.form}})],1):e._e(),3===e.form.deviceType?a("el-tab-pane",{attrs:{disabled:0==e.form.deviceId,name:"sipVideo"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("直播录像")]),a("deviceVideo",{ref:"deviceVideo",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("timer")?a("el-tab-pane",{attrs:{name:"deviceTimer",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备定时")]),a("device-timer",{ref:"deviceTimer",attrs:{device:e.form}})],1):e._e(),a("el-tab-pane",{attrs:{name:"deviceUser",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备分享")]),a("device-user",{ref:"deviceUser",attrs:{device:e.form},on:{userEvent:function(t){return e.getUserData(t)}}})],1),3!==e.form.deviceType&&e.hasShrarePerm("log")?a("el-tab-pane",{attrs:{name:"deviceLog",disabled:0==e.form.deviceId,lazy:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("事件日志")]),a("device-log",{ref:"deviceLog",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("log")?a("el-tab-pane",{attrs:{name:"deviceFuncLog",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("指令日志")]),a("device-func",{ref:"deviceFuncLog",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("monitor")?a("el-tab-pane",{attrs:{name:"deviceMonitor",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("实时监测")]),a("device-monitor",{ref:"deviceMonitor",attrs:{device:e.form}})],1):e._e(),3!==e.form.deviceType&&e.hasShrarePerm("statistic")?a("el-tab-pane",{attrs:{name:"deviceStastic",disabled:0==e.form.deviceId}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("监测统计")]),a("device-statistic",{ref:"deviceStatistic",attrs:{device:e.form}})],1):e._e(),a("el-tab-pane",{attrs:{disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-top":"350px"}})])]),a("el-tab-pane",{attrs:{name:"deviceReturn",disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(t){return e.goBack()}}},[e._v("返回列表")])],1)])],1),a("el-dialog",{attrs:{title:"摘要（设备上传的只读数据）",visible:e.openSummary,width:"700px","append-to-body":""},on:{"update:visible":function(t){e.openSummary=t}}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:14}},[a("div",{staticStyle:{border:"1px solid #ccc","margin-top":"-15px",height:"350px",width:"360px",overflow:"scroll"}},[a("json-viewer",{attrs:{value:e.summary,"expand-depth":10,copyable:""},scopedSlots:e._u([{key:"copy",fn:function(){return[e._v("复制")]},proxy:!0}])})],1)]),a("el-col",{attrs:{span:10}},[a("div",{staticStyle:{border:"1px solid #ccc",width:"200px","text-align":"center","margin-left":"20px","margin-top":"-10px"}},[a("vue-qr",{attrs:{text:e.qrText,size:200}}),a("div",{staticStyle:{"padding-bottom":"10px"}},[e._v("设备二维码")])],1)])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"info"},on:{click:e.closeSummaryDialog}},[e._v("关 闭")])],1)],1),a("el-dialog",{attrs:{visible:e.openCode,width:"300px","append-to-body":""},on:{"update:visible":function(t){e.openCode=t}}},[a("div",{staticStyle:{border:"1px solid #ccc",width:"220px","text-align":"center",margin:"0 auto","margin-top":"-15px"}},[a("vue-qr",{attrs:{text:e.qrText,size:200}}),a("div",{staticStyle:{"padding-bottom":"10px"}},[e._v("设备二维码")])],1)]),a("el-dialog",{attrs:{title:"Mqtt连接参数",visible:e.openViewMqtt,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.openViewMqtt=t}}},[a("el-form",{ref:"listQuery",attrs:{model:e.listQuery,rules:e.rules,"label-width":"150px"}},[a("el-form-item",{attrs:{label:"clientId",prop:"clientId"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:""},model:{value:e.listQuery.clientId,callback:function(t){e.$set(e.listQuery,"clientId",t)},expression:"listQuery.clientId"}})],1),a("el-form-item",{attrs:{label:"username",prop:"username"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{disabled:""},model:{value:e.listQuery.username,callback:function(t){e.$set(e.listQuery,"username",t)},expression:"listQuery.username"}})],1),a("el-form-item",{attrs:{label:"passwd",prop:"passwd"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{clearable:"",disabled:""},model:{value:e.listQuery.passwd,callback:function(t){e.$set(e.listQuery,"passwd",t)},expression:"listQuery.passwd"}})],1),a("el-form-item",{attrs:{label:"port",prop:"port"}},[a("el-input",{staticStyle:{width:"80%"},attrs:{clearable:"",disabled:""},model:{value:e.listQuery.port,callback:function(t){e.$set(e.listQuery,"port",t)},expression:"listQuery.port"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticClass:"btns",attrs:{type:"primary"},on:{click:function(t){return e.doCopy(2)}}},[e._v("一键复制")]),a("el-button",{on:{click:e.closeSummaryDialog}},[e._v("关 闭")])],1)],1)],1)},n=[],r=a("c7eb"),s=a("1da1"),o=(a("d81d"),a("14d9"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("8a79"),a("349e")),l=a.n(o),d=(a("0b22"),a("e51f")),c=a("7168"),u=a("b52e"),p=a("5f43"),m=a("dd50"),h=a("f14e"),f=a("7a72"),v=a("67dd"),y=a("97d6"),b=a("f5fc"),g=a("ecd9"),w=a("9467"),x=a("1c4f"),I=a("9626"),_=a("5daf"),k=a("658f5"),S=a.n(k),N=a("09cb"),L=a("584f"),P=a("2544"),M=a("5f87"),T=a("01ca"),C=(a("f5a7"),a("38da")),j=a("f5de"),$={name:"DeviceEdit",dicts:["iot_device_status","iot_location_way"],components:{RealTimeStatus:_["default"],DeviceFunc:x["default"],deviceLog:c["default"],deviceUser:u["default"],deviceMonitor:m["default"],deviceStatistic:h["default"],runningStatus:p["default"],productList:d["default"],deviceTimer:f["default"],deviceFuncLog:x["default"],deviceVideo:b["default"],player:y["default"],deviceLiveStream:g["default"],deviceSub:I["default"],JsonViewer:l.a,vueQr:S.a,channel:v["default"],sipid:w["default"]},watch:{activeName:function(e){"deviceStastic"==e&&this.$nextTick((function(){}))}},computed:{deviceStatus:{set:function(e){this.form.status=1==e?2:0==e?4:this.oldDeviceStatus},get:function(){return 2==this.form.status?1:0}}},data:function(){return{qrText:"fastbee",openSummary:!1,openCode:!1,openViewMqtt:!1,genDisabled:!1,activeName:"basic",mqttList:[],loading:!0,oldDeviceStatus:null,deviceId:"",channelId:"",form:{productId:0,status:1,locationWay:1,firmwareVersion:1,serialNumber:"",deviceType:1,isSimulate:0},listQuery:{clientId:0,username:"",passwd:"",port:""},openTip:!1,openServerTip:!1,serverType:1,isSubDev:!1,summary:[],baseUrl:"/prod-api",map:null,mk:null,latitude:"",longitude:"",rules:{deviceName:[{required:!0,message:"设备名称不能为空",trigger:"blur"},{min:2,max:32,message:"设备名称长度在 2 到 32 个字符",trigger:"blur"}],firmwareVersion:[{required:!0,message:"固件版本不能为空",trigger:"blur"}]},isMediaDevice:!1}},created:function(){var e=this.$route.query.activeName;null!=e&&""!=e&&(this.activeName=e),this.form.deviceId=this.$route.query&&this.$route.query.deviceId,0!=this.form.deviceId&&(this.connectMqtt(),this.getDevice(this.form.deviceId)),this.isSubDev=1==this.$route.query.isSubDev},activated:function(){var e=this.$route.query.activeName;null!=e&&""!=e&&(this.activeName=e)},destroyed:function(){this.mqttUnSubscribe(this.form)},methods:{connectMqtt:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.$mqttTool.client){t.next=3;break}return t.next=3,e.$mqttTool.connect(e.vuex_token);case 3:e.mqttCallback();case 4:case"end":return t.stop()}}),t)})))()},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){var n=t.split("/"),r=(n[1],n[2]);a=JSON.parse(a.toString()),a&&("status"!=n[3]&&"status"!=n[2]||(console.log("接收到【设备状态-详情】主题：",t),console.log("接收到【设备状态-详情】内容：",a),e.form.serialNumber==r&&(e.oldDeviceStatus=a.status,e.form.status=a.status,e.form.isShadow=a.isShadow,e.form.rssid=a.rssid)),e.isSubDev&&(t.endsWith("ws/service")&&e.$busEvent.$emit("updateData",{serialNumber:n[2],productId:e.form.productId,data:a}),t.endsWith("service/reply")&&e.$busEvent.$emit("updateLog",{serialNumber:n[2],productId:e.form.productId,data:a})),t.endsWith("ws/post/simulate")&&e.$busEvent.$emit("logData",{serialNumber:n[1],productId:e.form.productId,data:a}))}))},mqttSubscribe:function(e){var t="/"+e.productId+"/"+e.serialNumber+"/status/post",a=(e.productId,e.serialNumber,"/"+e.productId+"/"+e.serialNumber+"/function/post"),i="/"+e.productId+"/"+e.serialNumber+"/monitor/post",n="/"+e.productId+"/"+e.serialNumber+"/service/reply",r=[],s="/"+e.productId+"/"+e.serialNumber+"/ws/service";r.push(s),r.push(t),r.push(a),r.push(i),r.push(n),this.isSubDev,this.$mqttTool.subscribe(r)},mqttUnSubscribe:function(e){var t="/"+e.productId+"/"+e.serialNumber+"/status/post",a=(e.productId,e.serialNumber,"/"+e.productId+"/"+e.serialNumber+"/function/post"),i="/"+e.productId+"/"+e.serialNumber+"/monitor/post",n="/"+e.productId+"/"+e.serialNumber+"/service/reply",r=[],s="/"+e.productId+"/"+e.serialNumber+"/ws/service";r.push(s),r.push(t),r.push(a),r.push(i),r.push(n),this.isSubDev,this.$mqttTool.unsubscribe(r)},getDeviceStatusData:function(e){this.form.status=e},getPlayerData:function(e){this.activeName=e.tabName,this.channelId=e.channelId,this.channelId&&(this.$refs.deviceLiveStream.channelId=this.channelId,this.$refs.deviceLiveStream.changeChannel())},tabChange:function(e){var t=this;3==this.form.deviceType&&"deviceReturn"!=e.name&&("sipPlayer"===e.name?(this.$refs.deviceVideo.destroy(),this.channelId&&(this.$refs.deviceLiveStream.channelId=this.channelId,this.$refs.deviceLiveStream.changeChannel()),this.$refs.deviceLiveStream.channelId&&this.$refs.deviceLiveStream.changeChannel()):"sipVideo"===e.name?(this.$refs.deviceLiveStream.destroy(),this.$refs.deviceVideo.channelId&&this.$refs.deviceVideo.queryDate&&this.$refs.deviceVideo.loadDevRecord()):(this.$refs.deviceVideo.destroy(),this.$refs.deviceLiveStream.destroy())),this.$nextTick((function(){"deviceStastic"===e.name?t.$refs.deviceStatistic.getListHistory():"deviceTimer"===e.name?t.$refs.deviceTimer.getList():"deviceSub"===e.name&&t.form.serialNumber&&(t.$refs.deviceSub.queryParams.gwDevCode=t.form.serialNumber,t.$refs.deviceSub.getList())}))},deviceSynchronization:function(){var e=this;Object(L["c"])(this.form.serialNumber).then(function(){var t=Object(s["a"])(Object(r["a"])().mark((function t(a){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getCacheThingsModdel(a.data.productId);case 2:return a.data.cacheThingsModel=t.sent,t.next=5,e.getDeviceStatus(e.form);case 5:a.data.thingsModels=t.sent,e.formatThingsModel(a.data),e.form=a.data,e.activeName="runningStatus",e.oldDeviceStatus=e.form.status,e.loadMap();case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getDevice:function(e){var t=this;Object(L["f"])(e).then(function(){var a=Object(s["a"])(Object(r["a"])().mark((function a(i){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:i.data.userPerms=[],0==i.data.isOwner?Object(P["c"])(e,Object(M["b"])()).then((function(e){i.data.userPerms=e.data.perms.split(","),t.getDeviceStatusWitchThingsModel(i)})):t.getDeviceStatusWitchThingsModel(i);case 2:case"end":return a.stop()}}),a)})));return function(e){return a.apply(this,arguments)}}())},hasShrarePerm:function(e){return 0!=this.form.isOwner||-1!=this.form.userPerms.indexOf(e)},getCacheThingsModdel:function(e){return new Promise((function(t,a){Object(T["b"])(e).then((function(e){t(JSON.parse(e.data))})).catch((function(e){a(e)}))}))},getDeviceStatus:function(e){var t={deviceId:e.deviceId,slaveId:e.slaveId};return new Promise((function(e,a){Object(L["h"])(t).then((function(t){e(t.data.thingsModels)})).catch((function(e){a(e)}))}))},formatThingsModel:function(e){e.chartList=[],e.monitorList=[],e.staticList=[];for(var t=0;t<e.thingsModels.length;t++)if("integer"!=e.thingsModels[t].datatype.type&&"decimal"!=e.thingsModels[t].datatype.type||(""==e.thingsModels[t].shadow?e.thingsModels[t].shadow=Number(e.thingsModels[t].datatype.min):e.thingsModels[t].shadow=Number(e.thingsModels[t].shadow)),"array"==e.thingsModels[t].datatype.type)if("object"==e.thingsModels[t].datatype.arrayType)for(var a=0;a<e.thingsModels[t].datatype.arrayParams.length;a++)for(var i=0;i<e.thingsModels[t].datatype.arrayParams[a].length;i++){var n=a>9?String(a):"0"+a,r="array_"+n+"_";e.thingsModels[t].datatype.arrayParams[a][i].id=r+e.thingsModels[t].datatype.arrayParams[a][i].id,1==e.thingsModels[t].datatype.arrayParams[a][i].isChart&&(e.thingsModels[t].datatype.arrayParams[a][i].name="["+e.thingsModels[t].name+(a+1)+"] "+e.thingsModels[t].datatype.arrayParams[a][i].name,e.thingsModels[t].datatype.arrayParams[a][i].datatype.arrayType="object",e.chartList.push(e.thingsModels[t].datatype.arrayParams[a][i]),1==e.thingsModels[t].datatype.arrayParams[a][i].isHistory&&e.staticList.push(e.thingsModels[t].datatype.arrayParams[a][i]),1==e.thingsModels[t].datatype.arrayParams[a][i].isMonitor&&e.monitorList.push(e.thingsModels[t].datatype.arrayParams[a][i]),e.thingsModels[t].datatype.arrayParams[a].splice(i--,1))}else for(var s=""!=e.thingsModels[t].value?e.thingsModels[t].value.split(","):[],o=""!=e.thingsModels[t].shadow?e.thingsModels[t].shadow.split(","):[],l=0;l<e.thingsModels[t].datatype.arrayCount;l++){e.thingsModels[t].datatype.arrayModel||(e.thingsModels[t].datatype.arrayModel=[]);var d=l>9?String(l):"0"+l,c="array_"+d+"_";e.thingsModels[t].datatype.arrayModel[l]={id:c+e.thingsModels[t].id,name:e.thingsModels[t].name,type:e.thingsModels[t].type,isReadonly:e.thingsModels[t].isReadonly,value:s[l]?s[l]:"",shadow:o[l]?o[l]:""}}else if("object"==e.thingsModels[t].datatype.type)for(var u=0;u<e.thingsModels[t].datatype.params.length;u++)1==e.thingsModels[t].datatype.params[u].isChart&&(e.thingsModels[t].datatype.params[u].name="["+e.thingsModels[t].name+"] "+e.thingsModels[t].datatype.params[u].name,e.chartList.push(e.thingsModels[t].datatype.params[u]),1==e.thingsModels[t].datatype.params[u].isHistory&&e.staticList.push(e.thingsModels[t].datatype.params[u]),1==e.thingsModels[t].datatype.params[u].isMonitor&&e.monitorList.push(e.thingsModels[t].datatype.params[u]),e.thingsModels[t].datatype.params.splice(u--,1));else 1==e.thingsModels[t].isChart&&(e.chartList.push(e.thingsModels[t]),1==e.thingsModels[t].isHistory&&e.staticList.push(e.thingsModels[t]),1==e.thingsModels[t].isMonitor&&e.monitorList.push(e.thingsModels[t]),e.thingsModels.splice(t--,1))},loadMap:function(){var e=this;this.$nextTick((function(){Object(N["a"])().then((function(){e.getmap()}))}))},goBack:function(){var e={path:"/iot/device",query:{t:Date.now(),pageNum:this.$route.query.pageNum}};this.$tab.closeOpenPage(e),this.reset()},reset:function(){this.form={deviceId:0,deviceName:null,productId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:"",firmwareVersion:1,status:1,rssi:null,networkAddress:null,networkIp:null,longitude:null,latitude:null,activeTime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,locationWay:1,clientId:0},this.deviceStatus=0,this.resetForm("form")},submitForm:function(){var e=this;return Object(s["a"])(Object(r["a"])().mark((function t(){var a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.form.serialNumber&&0!=e.form.serialNumber){t.next=3;break}return e.$modal.alertError("设备编号不能为空"),t.abrupt("return");case 3:if(a=/^[0-9a-zA-Z]+$/,a.test(e.form.serialNumber)){t.next=7;break}return e.$modal.alertError("设备编号只能是字母和数字"),t.abrupt("return");case 7:if(null!=e.form.productId&&0!=e.form.productId){t.next=10;break}return e.$modal.alertError("所属产品不能为空"),t.abrupt("return");case 10:e.$refs["form"].validate((function(t){t&&(0!=e.form.deviceId?Object(L["s"])(e.form).then((function(t){if(0==t.data)e.$modal.alertError(t.msg);else if(e.$modal.alertSuccess("修改成功"),e.form=JSON.parse(JSON.stringify(e.form)),console.log("form",e.form),e.loadMap(),2===e.form.status){var a={clientId:e.form.serialNumber};Object(j["a"])(a).then((function(e){}))}})):Object(L["a"])(e.form).then(function(){var t=Object(s["a"])(Object(r["a"])().mark((function t(a){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.getDeviceStatusWitchThingsModel(a);case 2:null==e.form.deviceId||0==e.form.deviceId?e.$modal.alertError("设备编号已经存在，添加设备失败"):(2==e.form.status&&(e.deviceStatus=1),e.$modal.alertSuccess("添加设备成功"),e.loadMap());case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()))}));case 11:case"end":return t.stop()}}),t)})))()},getDeviceStatusWitchThingsModel:function(e){var t=this;return Object(s["a"])(Object(r["a"])().mark((function a(){var i;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,t.getCacheThingsModdel(e.data.productId);case 2:return e.data.cacheThingsModel=a.sent,a.next=5,t.getDeviceStatus(e.data);case 5:if(e.data.thingsModels=a.sent,0==e.data.isOwner)for(i=0;i<e.data.thingsModels.length;i++)-1==e.data.userPerms.indexOf(e.data.thingsModels[i].id)&&e.data.thingsModels.splice(i--,1);t.formatThingsModel(e.data),t.form=e.data,null!=t.form.summary&&""!=t.form.summary&&(t.summary=JSON.parse(t.form.summary)),t.isSubDev=t.form.subDeviceList&&t.form.subDeviceList.length>0,t.oldDeviceStatus=t.form.status,t.loadMap(),t.connectMqtt(),t.mqttSubscribe(t.form);case 15:case"end":return a.stop()}}),a)})))()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},genSipID:function(){this.$refs.sipidGen.open=!0},getProductData:function(e){this.form.productId=e.productId,this.form.productName=e.productName,this.form.deviceType=e.deviceType,this.getDeviceTemp(),this.form.tenantId=e.tenantId,this.form.tenantName=e.tenantName,"TCP"===e.transport?(this.openServerTip=!0,this.serverType=3):(this.openServerTip=!1,this.serverType=1)},getSipIDData:function(e){this.form.serialNumber=e},getDeviceTemp:function(e){var t=this;Object(C["c"])(this.form).then((function(e){e.data&&2==t.form.deviceType?t.openTip=!0:t.openTip=!1}))},getUserData:function(e){},openSummaryDialog:function(){var e={type:1,deviceNumber:this.form.serialNumber,productId:this.form.productId};this.qrText=JSON.stringify(e),this.openSummary=!0},closeSummaryDialog:function(){this.openSummary=!1,this.openViewMqtt=!1},doCopy:function(e){if(2==e){var t=document.createElement("input");t.value="{clientId:"+this.listQuery.clientId+",username:"+this.listQuery.username+",passwd:"+this.listQuery.passwd+",port:"+this.listQuery.port+"}",document.body.appendChild(t),t.select(),document.execCommand("Copy"),document.body.removeChild(t),this.$message.success("复制成功")}},openCodeDialog:function(){var e={type:1,deviceNumber:this.form.serialNumber,productId:this.form.productId,productName:this.form.productName};this.qrText=JSON.stringify(e),this.openCode=!0},getmap:function(){this.map=new BMap.Map("map");var e=null;e=null!=this.form.longitude&&""!=this.form.longitude&&null!=this.form.latitude&&""!=this.form.latitude?new BMap.Point(this.form.longitude,this.form.latitude):new BMap.Point(116.404,39.915),this.map.centerAndZoom(e,19),this.map.enableScrollWheelZoom(!0),this.map.addControl(new BMap.NavigationControl),this.mk=new BMap.Marker(e),this.map.addOverlay(this.mk),this.map.panTo(e)},generateNum:function(){var e=this;if(this.form.productId&&0!=this.form.productId){this.genDisabled=!0;var t={type:this.serverType};Object(L["e"])(t).then((function(t){e.form.serialNumber=t.data,e.genDisabled=!1}))}else this.$modal.alertError("请先选择产品")},handleViewMqtt:function(){var e=this;this.openViewMqtt=!0,this.loading=!0;var t={deviceId:this.form.deviceId};Object(L["j"])(t).then((function(t){200==t.code&&(e.listQuery=t.data,e.loading=!1)}))}}},O=$,q=a("2877"),D=Object(q["a"])(O,i,n,!1,null,null,null);t["default"]=D.exports},ecd9:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("span",{staticStyle:{"margin-left":"10px"},attrs:{prop:"channelName"}},[e._v("通道名称：")]),a("el-select",{attrs:{placeholder:"请选择",size:"small"},on:{change:function(t){return e.changeChannel()}},model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},e._l(e.channelList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("span",{staticStyle:{margin:"10px 10px 10px 30px"}},[e._v("开启直播录像:")]),a("el-switch",{staticStyle:{"border-radius":"10px"},attrs:{"active-color":"#13ce66","inactive-color":"#c4c6c9",disabled:""===e.channelId},on:{change:e.startPlayRecord},model:{value:e.playrecord,callback:function(t){e.playrecord=t},expression:"playrecord"}})],1),a("player",{ref:"player",staticClass:"components-container",attrs:{playerinfo:e.playinfo}})],1)},n=[],r=(a("d81d"),a("97d6")),s=a("e2de"),o=a("6827"),l={name:"device-live-stream",components:{player:r["default"]},props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo.channelId&&(this.channelId=this.deviceInfo.channelId,this.changeChannel()),this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.deviceId=this.device.serialNumber)}},data:function(){return{deviceInfo:{},deviceId:"",channelId:"",streamId:"",ssrc:"",playurl:"",playinfo:{},playrecord:!1,playrecording:!1,playing:!1,channelList:[],queryParams:{pageNum:1,pageSize:10,deviceSipId:null,channelSipId:null}}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.deviceId=this.device.serialNumber,this.getList(),this.playinfo={playtype:"play",deviceId:this.device.serialNumber}},beforeDestroy:function(){},destroyed:function(){this.closeStream()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["e"])(this.queryParams).then((function(t){e.channelList=t.rows.map((function(e){return{value:e.channelSipId,label:e.channelName}})),console.log(e.channelList)}))},changeChannel:function(){this.playinfo.channelId=this.channelId,this.sendDevicePush()},sendDevicePush:function(){var e=this;this.channelId?Object(s["h"])(this.deviceId,this.channelId).then((function(t){var a=t.data;e.streamId=a.streamId,e.playurl=a.playurl,e.$refs.player.isInit||e.$refs.player.init(),e.$refs.player.play(a.playurl),e.playing=!0})):console.log("直播通道: ["+this.channelId+"]")},startPlayRecord:function(){var e=this;this.deviceId=this.queryParams.deviceSipId,this.channelId?this.playrecord?(this.closeStream(),Object(o["f"])(this.deviceId,this.channelId).then((function(t){console.log("开始录像："+e.deviceId+" : "+e.channelId),e.playrecording=!0;var a=t.data;e.streamId=a.streamId,e.playurl=a.playurl,e.$refs.player.isInit||e.$refs.player.init(),e.$refs.player.play(a.playurl),e.playing=!0}))):(this.playrecording=!1,this.closeStream(),this.sendDevicePush()):console.log("直播录像通道: ["+this.channelId+"]")},closeStream:function(){var e=this;!0!==this.playrecording&&this.playing&&this.streamId&&(Object(s["b"])(this.deviceId,this.streamId).then((function(t){e.streamId="",e.ssrc="",e.playurl=""})),this.playing=!1)},destroy:function(){this.playing&&this.streamId&&this.$refs.player.destroy()}}},d=l,c=a("2877"),u=Object(c["a"])(d,i,n,!1,null,null,null);t["default"]=u.exports},ed08:function(e,t,a){"use strict";a.d(t,"b",(function(){return i})),a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return o})),a.d(t,"d",(function(){return l}));a("53ca"),a("d9e2"),a("a630"),a("a15b"),a("d81d"),a("14d9"),a("fb6a"),a("b64b"),a("d3b7"),a("4d63"),a("c607"),a("ac1f"),a("2c3e"),a("00b4"),a("25f0"),a("6062"),a("3ca3"),a("466d"),a("5319"),a("159b"),a("ddb0"),a("c38a");function i(e,t,a){var i,n,r,s,o,l=function l(){var d=+new Date-s;d<t&&d>0?i=setTimeout(l,t-d):(i=null,a||(o=e.apply(r,n),i||(r=n=null)))};return function(){for(var n=arguments.length,d=new Array(n),c=0;c<n;c++)d[c]=arguments[c];r=this,s=+new Date;var u=a&&!i;return i||(i=setTimeout(l,t)),u&&(o=e.apply(r,d),r=d=null),o}}function n(e,t){for(var a=Object.create(null),i=e.split(","),n=0;n<i.length;n++)a[i[n]]=!0;return t?function(e){return a[e.toLowerCase()]}:function(e){return a[e]}}var r="export default ",s={html:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"separate",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!1,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0},js:{indent_size:"2",indent_char:" ",max_preserve_newlines:"-1",preserve_newlines:!1,keep_array_indentation:!1,break_chained_methods:!1,indent_scripts:"normal",brace_style:"end-expand",space_before_conditional:!0,unescape_strings:!1,jslint_happy:!0,end_with_newline:!0,wrap_line_length:"110",indent_inner_html:!0,comma_first:!1,e4x:!0,indent_empty_lines:!0}};function o(e){return e.replace(/( |^)[a-z]/g,(function(e){return e.toUpperCase()}))}function l(e){return/^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(e)}},f14e:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-left":"20px"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"75px"}},[e.isSubDev?a("el-form-item",{attrs:{label:"请选择设备从机:","label-width":"120px"}},[a("el-select",{attrs:{placeholder:"请选择设备从机"},on:{change:e.selectSlave},model:{value:e.queryParams.slaveId,callback:function(t){e.$set(e.queryParams,"slaveId",t)},expression:"queryParams.slaveId"}},e._l(e.slaveList,(function(e){return a("el-option",{key:e.slaveId,attrs:{label:e.deviceName+" ("+e.slaveId+")",value:e.slaveId}})})),1)],1):e._e(),a("el-form-item",{attrs:{label:"时间范围"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{size:"small","value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.getListHistory}},[e._v("查询")])],1)],1)],1),a("el-col",{attrs:{span:23}},e._l(e.staticList,(function(t,i){return a("div",{key:i,staticStyle:{"margin-bottom":"30px"}},[a("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{shadow:"hover","body-style":{padding:"10px 0px",overflow:"auto"}}},[a("div",{ref:"statisticMap",refInFor:!0,staticStyle:{height:"300px",width:"1080px"}})])],1)})),0)],1)],1)},n=[],r=(a("4de4"),a("14d9"),a("b0c0"),a("b680"),a("d3b7"),a("a035")),s={name:"device-statistic",props:{device:{type:Object,default:null}},watch:{device:function(e,t){var a=this;this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.isSubDev=this.deviceInfo.subDeviceList&&this.deviceInfo.subDeviceList.length>0,this.queryParams.slaveId=this.deviceInfo.slaveId,this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.slaveList=e.subDeviceList,this.isSubDev?this.staticList=this.deviceInfo.cacheThingsModel["properties"].filter((function(e){return e.tempSlaveId==a.queryParams.slaveId})):this.staticList=this.deviceInfo.staticList,this.$nextTick((function(){this.getStatistic()})))}},data:function(){return{loading:!0,deviceInfo:{},staticList:[],chart:[],daterangeTime:[this.getTime(),this.getTime()],queryParams:{serialNumber:null,identity:"",slaveId:void 0},arrayData:[],slaveList:[],isSubDev:!1}},mounted:function(){},methods:{getTime:function(){var e=new Date,t=e.getFullYear(),a=e.getMonth()+1,i=e.getDate();return a=a<10?"0"+a:a,i=i<10?"0"+i:i,t+"-"+a+"-"+i},getListHistory:function(){var e=this;this.loading=!0,this.queryParams.serialNumber=this.queryParams.slaveId?this.deviceInfo.serialNumber+"_"+this.queryParams.slaveId:this.deviceInfo.serialNumber,null!=this.daterangeTime&&""!=this.daterangeTime&&(this.queryParams.beginTime=this.daterangeTime[0],this.queryParams.endTime=this.daterangeTime[1]+" 23:59"),Object(r["b"])(this.queryParams).then((function(t){for(var a in t.data)for(var i=0;i<e.staticList.length;i++)if(a==e.staticList[i].id){for(var n=[],r=0;r<t.data[a].length;r++){var s=[];s[0]=t.data[a][r].time,s[1]=t.data[a][r].value,n.push(s)}e.chart[i].setOption({series:[{data:n}]})}e.loading=!1}))},getStatistic:function(){for(var e=["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],t=0;t<this.staticList.length;t++){var a;this.$refs.statisticMap[t].style.width=document.documentElement.clientWidth-510+"px",this.chart[t]=this.$echarts.init(this.$refs.statisticMap[t]),a={animationDurationUpdate:3e3,tooltip:{trigger:"axis"},title:{left:"center",text:this.staticList[t].name+"统计 （单位 "+(this.staticList[t].datatype&&void 0!=this.staticList[t].datatype.unit?this.staticList[t].datatype.unit:"无")+"）"},grid:{top:"80px",left:"40px",right:"20px",bottom:"60px",containLabel:!0},toolbox:{feature:{dataZoom:{yAxisIndex:"none"},restore:{},saveAsImage:{}}},xAxis:{type:"time"},yAxis:{type:"value",scale:!0,axisLabel:{formatter:function(e,t){return e.toFixed(2)}}},dataZoom:[{type:"inside",start:0,end:100},{start:0,end:100}],series:[{name:this.staticList[t].name,type:"line",symbol:"none",sampling:"lttb",itemStyle:{color:t>9?e[0]:e[t]},areaStyle:{},data:[]}]},a&&this.chart[t].setOption(a)}},selectSlave:function(){var e=this;this.staticList=this.deviceInfo.cacheThingsModel["properties"].filter((function(t){return t.tempSlaveId==e.queryParams.slaveId})),this.$nextTick((function(){this.getStatistic(),this.getListHistory()}))}}},o=s,l=a("2877"),d=Object(l["a"])(o,i,n,!1,null,null,null);t["default"]=d.exports},f5a7:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return s}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function r(e){return Object(i["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function s(e,t,a){return Object(i["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:a})}},f5de:function(e,t,a){"use strict";a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return s})),a.d(t,"d",(function(){return o}));var i=a("b775");function n(e){return Object(i["a"])({url:"/iot/mqtt/clients",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/mqtt/client/out",method:"get",params:e})}function s(){return Object(i["a"])({url:"/bashBoard/stats",method:"get"})}function o(e){return Object(i["a"])({url:"/bashBoard/metrics",method:"get",params:e})}},f5fc:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{display:"block",width:"1000px"}},[a("div",{staticStyle:{display:"flex"}},[a("el-row",[a("span",{staticStyle:{"margin-left":"10px"},attrs:{prop:"channelName"}},[e._v("通道：")]),a("el-select",{staticStyle:{width:"200px","margin-right":"10px"},attrs:{placeholder:"请选择",size:"small"},on:{change:e.changeChannel},model:{value:e.channelId,callback:function(t){e.channelId=t},expression:"channelId"}},e._l(e.channelList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),a("span",{staticStyle:{overflow:"auto","margin-left":"10px"}},[e._v("日期：")]),a("el-date-picker",{staticStyle:{width:"180px","margin-right":"10px"},attrs:{type:"date",size:"small","value-format":"yyyy-MM-dd",clearable:"",placeholder:"选择日期"},model:{value:e.queryDate,callback:function(t){e.queryDate=t},expression:"queryDate"}}),a("el-button-group",{staticStyle:{margin:"0"}},[a("el-button",{attrs:{size:"mini",type:"success",title:"查看录像",disabled:""===e.channelId||!e.queryDate},on:{click:function(t){return e.loadDevRecord()}}},[a("i",{staticClass:"el-icon-video-camera"}),e._v(" 查看 ")])],1),a("span",{staticStyle:{"margin-left":"82px",overflow:"auto"}},[e._v("时间：")]),a("el-button-group",[a("el-time-picker",{staticStyle:{width:"200px"},attrs:{size:"small","is-range":"",align:"left","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围",disabled:""===e.channelId||!e.queryDate},on:{change:e.timePickerChange},model:{value:e.timeRange,callback:function(t){e.timeRange=t},expression:"timeRange"}})],1),a("el-button-group",{staticStyle:{margin:"0 0 0 10px"}},[a("el-button",{attrs:{size:"mini",type:"primary",title:"下载选定录像",disabled:""===e.channelId||!e.timeRange},on:{click:function(t){return e.downloadRecord()}}},[a("i",{staticClass:"el-icon-download"}),e._v(" 转存 ")])],1)],1)],1),a("player",{ref:"playbacker",staticClass:"components-container",attrs:{playerinfo:e.playinfo}})],1)},n=[],r=(a("d81d"),a("d3b7"),a("97d6")),s=a("e2de"),o=a("6827"),l={name:"DeviceVideo",components:{player:r["default"]},data:function(){return{deviceId:"",channelId:"",streamId:"",ssrc:"",playurl:"",queryDate:"",playing:!1,vodData:{},hisData:[],playinfo:{},channelList:[],playbackinfo:{},timeRange:null,startTime:null,endTime:null,queryParams:{pageNum:1,pageSize:10,deviceSipId:null,channelSipId:null}}},props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!==this.deviceInfo.deviceId&&(this.queryParams.deviceSipId=this.deviceInfo.serialNumber,this.deviceId=this.device.serialNumber)}},created:function(){this.queryParams.deviceSipId=this.device.serialNumber,this.deviceId=this.device.serialNumber,this.getList(),this.playinfo={playtype:"playback",deviceId:this.device.serialNumber}},beforeDestroy:function(){},destroyed:function(){this.closeStream()},methods:{getList:function(){var e=this;this.loading=!0,Object(s["e"])(this.queryParams).then((function(t){e.channelList=t.rows.map((function(e){return{value:e.channelSipId,label:e.channelName}}))}))},changeChannel:function(){this.playinfo.channelId=this.channelId},initUrl:function(e){e?(this.streamId=e.ssrc,this.ssrc=e.ssrc,this.playurl=e.playurl):(this.streamId="",this.ssrc="",this.playurl="")},loadDevRecord:function(){var e=this;if(this.$refs.playbacker.registercallback("playbackSeek",this.seekPlay),this.deviceId&&this.channelId){var t=this.queryDate?new Date(this.queryDate).getTime():new Date((new Date).toLocaleDateString()).getTime(),a=t/1e3,i=Math.floor((t+864e5-1)/1e3),n={start:a,end:i};this.vodData={start:a,end:i,base:a},this.setTime(this.queryDate+" 00:00:00",this.queryDate+" 23:59:59"),Object(o["a"])(this.deviceId,this.channelId,n).then((function(t){if(e.hisData=t.data.recordItems,t.data.recordItems){var n=e.hisData.length;n>0?(e.hisData[0].start<a?(e.hisData[0].start=a,e.vodData.start=a):e.vodData.start=e.hisData[0].start,0!==e.hisData[0].end&&e.hisData[0].end<i&&(e.vodData.end=e.hisData[0].end),e.playback()):e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}else e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}))}},playback:function(){var e=this,t={start:this.vodData.start,end:this.vodData.end};this.ssrc?Object(s["b"])(this.deviceId,this.channelId,this.ssrc).then((function(a){Object(s["f"])(e.deviceId,e.channelId,t).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))})):Object(s["f"])(this.deviceId,this.channelId,t).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))},triggerPlay:function(e){this.$refs.playbacker.playback(this.playurl,e),this.playing=!0},seekPlay:function(e){var t=this.vodData.base+3600*e.hour+60*e.min+e.second,a=t-this.vodData.start;if(this.ssrc){var i={seek:a},n=this;Object(s["g"])(this.deviceId,this.channelId,this.streamId,i).then((function(e){n.$refs.playbacker.setPlaybackStartTime(t)}))}},closeStream:function(){var e=this;this.playing&&this.streamId&&Object(s["b"])(this.deviceId,this.streamId).then((function(t){e.streamId="",e.ssrc="",e.playurl="",e.playing=!1}))},destroy:function(){this.playing&&this.streamId&&this.$refs.playbacker.destroy()},timePickerChange:function(e){this.setTime(e[0],e[1])},setTime:function(e,t){this.startTime=e,this.endTime=t,this.timeRange=[e,t]},downloadRecord:function(){var e=this,t=new Date(this.startTime).getTime()/1e3,a=new Date(this.endTime).getTime()/1e3,i={startTime:t,endTime:a,speed:"4"};Object(o["e"])(this.deviceId,this.channelId,i).then((function(t){console.log("开始转存到流服务器："+e.deviceId+" : "+e.channelId),200===t.code&&e.$message({type:"success",message:"转存到流服务器,请前往视频中心->录像管理查看！"})}))}}},d=l,c=a("2877"),u=Object(c["a"])(d,i,n,!1,null,null,null);t["default"]=u.exports}}]);