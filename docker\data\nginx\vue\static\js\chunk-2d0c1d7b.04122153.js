(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c1d7b"],{4889:function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticStyle:{padding:"6px"}},[l("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"6px"}},[l("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-20px"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[l("el-form-item",{attrs:{label:"日志名称",prop:"logName"}},[l("el-input",{attrs:{placeholder:"请输入日志名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logName,callback:function(t){e.$set(e.queryParams,"logName",t)},expression:"queryParams.logName"}})],1),l("el-form-item",{attrs:{label:"类型",prop:"logType"}},[l("el-select",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:e.queryParams.logType,callback:function(t){e.$set(e.queryParams,"logType",t)},expression:"queryParams.logType"}},e._l(e.dict.type.iot_things_type,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l("el-form-item",{attrs:{label:"日志级别",prop:"logLevel"}},[l("el-input",{attrs:{placeholder:"请输入日志级别",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logLevel,callback:function(t){e.$set(e.queryParams,"logLevel",t)},expression:"queryParams.logLevel"}})],1),l("el-form-item",{attrs:{label:"设备ID",prop:"deviceId"}},[l("el-input",{attrs:{placeholder:"请输入设备ID",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceId,callback:function(t){e.$set(e.queryParams,"deviceId",t)},expression:"queryParams.deviceId"}})],1),l("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[l("el-input",{attrs:{placeholder:"请输入设备名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),l("el-form-item",{attrs:{label:"用户昵称",prop:"userName"}},[l("el-input",{attrs:{placeholder:"请输入用户昵称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),l("el-form-item",{attrs:{label:"租户名称",prop:"tenantName"}},[l("el-input",{attrs:{placeholder:"请输入租户名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.tenantName,callback:function(t){e.$set(e.queryParams,"tenantName",t)},expression:"queryParams.tenantName"}})],1),l("el-form-item",{attrs:{label:"生成告警",prop:"isAlert"}},[l("el-input",{attrs:{placeholder:"请输入是否生成告警",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isAlert,callback:function(t){e.$set(e.queryParams,"isAlert",t)},expression:"queryParams.isAlert"}})],1),l("el-form-item",{attrs:{label:"告警处理",prop:"status"}},[l("el-select",{attrs:{placeholder:"请选择告警处理",clearable:"",size:"small"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_yes_no,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),l("el-form-item",[l("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),l("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),l("el-card",{staticStyle:{"padding-bottom":"100px"}},[l("el-row",{staticClass:"mb8",attrs:{gutter:10}},[l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:add"],expression:"['iot:log:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:edit"],expression:"['iot:log:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:remove"],expression:"['iot:log:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:export"],expression:"['iot:log:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),l("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.logList,border:""},on:{"selection-change":e.handleSelectionChange}},[l("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),l("el-table-column",{attrs:{label:"日志名称",align:"center",prop:"logName"}}),l("el-table-column",{attrs:{label:"值",align:"center",prop:"logValue"}}),l("el-table-column",{attrs:{label:"类型",align:"center",prop:"logType"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("dict-tag",{attrs:{options:e.dict.type.iot_things_type,value:t.row.logType}})]}}])}),l("el-table-column",{attrs:{label:"日志级别",align:"center",prop:"logLevel"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.logLevel}})]}}])}),l("el-table-column",{attrs:{label:"设备ID",align:"center",prop:"deviceId"}}),l("el-table-column",{attrs:{label:"设备名称",align:"center",prop:"deviceName"}}),l("el-table-column",{attrs:{label:"用户ID",align:"center",prop:"userId"}}),l("el-table-column",{attrs:{label:"用户昵称",align:"center",prop:"userName"}}),l("el-table-column",{attrs:{label:"租户ID",align:"center",prop:"tenantId"}}),l("el-table-column",{attrs:{label:"租户名称",align:"center",prop:"tenantName"}}),l("el-table-column",{attrs:{label:"触发源",align:"center",prop:"triggerSource"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.triggerSource}})]}}])}),l("el-table-column",{attrs:{label:"生成告警",align:"center",prop:"isAlert"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isAlert}})]}}])}),l("el-table-column",{attrs:{label:"告警处理",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.status}})]}}])}),l("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),l("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),l("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:log:edit"],expression:"['iot:log:edit']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-edit"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v("处理")])]}}])})],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),l("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[l("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[l("el-form-item",{attrs:{label:"日志名称",prop:"logName"}},[l("el-input",{attrs:{placeholder:"请输入日志名称"},model:{value:e.form.logName,callback:function(t){e.$set(e.form,"logName",t)},expression:"form.logName"}})],1),l("el-form-item",{attrs:{label:"类型",prop:"logType"}},[l("el-select",{attrs:{placeholder:"请选择类型"},model:{value:e.form.logType,callback:function(t){e.$set(e.form,"logType",t)},expression:"form.logType"}},e._l(e.dict.type.iot_things_type,(function(e){return l("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),l("el-form-item",{attrs:{label:"日志级别",prop:"logLevel"}},[l("el-input",{attrs:{placeholder:"请输入日志级别"},model:{value:e.form.logLevel,callback:function(t){e.$set(e.form,"logLevel",t)},expression:"form.logLevel"}})],1),l("el-form-item",{attrs:{label:"设备ID",prop:"deviceId"}},[l("el-input",{attrs:{placeholder:"请输入设备ID"},model:{value:e.form.deviceId,callback:function(t){e.$set(e.form,"deviceId",t)},expression:"form.deviceId"}})],1),l("el-form-item",{attrs:{label:"设备名称",prop:"deviceName"}},[l("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:e.form.deviceName,callback:function(t){e.$set(e.form,"deviceName",t)},expression:"form.deviceName"}})],1),l("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[l("el-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.form.userId,callback:function(t){e.$set(e.form,"userId",t)},expression:"form.userId"}})],1),l("el-form-item",{attrs:{label:"用户昵称",prop:"userName"}},[l("el-input",{attrs:{placeholder:"请输入用户昵称"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1),l("el-form-item",{attrs:{label:"租户ID",prop:"tenantId"}},[l("el-input",{attrs:{placeholder:"请输入租户ID"},model:{value:e.form.tenantId,callback:function(t){e.$set(e.form,"tenantId",t)},expression:"form.tenantId"}})],1),l("el-form-item",{attrs:{label:"租户名称",prop:"tenantName"}},[l("el-input",{attrs:{placeholder:"请输入租户名称"},model:{value:e.form.tenantName,callback:function(t){e.$set(e.form,"tenantName",t)},expression:"form.tenantName"}})],1),l("el-form-item",{attrs:{label:"触发源",prop:"triggerSource"}},[l("el-input",{attrs:{placeholder:"请输入触发源"},model:{value:e.form.triggerSource,callback:function(t){e.$set(e.form,"triggerSource",t)},expression:"form.triggerSource"}})],1),l("el-form-item",{attrs:{label:"是否生成告警",prop:"isAlert"}},[l("el-input",{attrs:{placeholder:"请输入是否生成告警"},model:{value:e.form.isAlert,callback:function(t){e.$set(e.form,"isAlert",t)},expression:"form.isAlert"}})],1),l("el-form-item",{attrs:{label:"告警处理"}},[l("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.iot_yes_no,(function(t){return l("el-radio",{key:t.value,attrs:{label:parseInt(t.value)}},[e._v(e._s(t.label))])})),1)],1),l("el-form-item",{attrs:{label:"备注",prop:"remark"}},[l("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),l("el-form-item",{attrs:{label:"日志收到的值",prop:"logValue"}},[l("el-input",{attrs:{placeholder:"请输入日志收到的值"},model:{value:e.form.logValue,callback:function(t){e.$set(e.form,"logValue",t)},expression:"form.logValue"}})],1),l("el-form-item",{attrs:{label:"是否置顶",prop:"istop"}},[l("el-input",{attrs:{placeholder:"请输入是否置顶"},model:{value:e.form.istop,callback:function(t){e.$set(e.form,"istop",t)},expression:"form.istop"}})],1),l("el-form-item",{attrs:{label:"是否监测",prop:"ismonitor"}},[l("el-input",{attrs:{placeholder:"请输入是否监测"},model:{value:e.form.ismonitor,callback:function(t){e.$set(e.form,"ismonitor",t)},expression:"form.ismonitor"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),l("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)],1)},r=[],o=l("5530"),n=(l("d81d"),l("b775"));function i(e){return Object(n["a"])({url:"/iot/log/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/iot/log/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/iot/log",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/iot/log",method:"put",data:e})}function m(e){return Object(n["a"])({url:"/iot/log/"+e,method:"delete"})}var p={name:"Log",dicts:["iot_things_type","iot_yes_no"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,logList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,logName:null,logType:null,logLevel:null,deviceId:null,deviceName:null,userName:null,tenantName:null,triggerSource:null,isAlert:null,status:null,istop:null,ismonitor:null},form:{},rules:{logName:[{required:!0,message:"日志名称不能为空",trigger:"blur"}],logType:[{required:!0,message:"类型不能为空",trigger:"change"}],logLevel:[{required:!0,message:"日志级别不能为空",trigger:"blur"}],deviceId:[{required:!0,message:"设备ID不能为空",trigger:"blur"}],deviceName:[{required:!0,message:"设备名称不能为空",trigger:"blur"}],userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}],userName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],tenantId:[{required:!0,message:"租户ID不能为空",trigger:"blur"}],tenantName:[{required:!0,message:"租户名称不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,i(this.queryParams).then((function(t){e.logList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deviceLogId:null,logName:null,logType:null,logLevel:null,deviceId:null,deviceName:null,userId:null,userName:null,tenantId:null,tenantName:null,triggerSource:null,isAlert:null,status:0,createBy:null,createTime:null,remark:null,logValue:null,istop:null,ismonitor:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.deviceLogId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加设备日志"},handleUpdate:function(e){var t=this;this.reset();var l=e.deviceLogId||this.ids;s(l).then((function(e){t.form=e.data,t.open=!0,t.title="修改设备日志"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.deviceLogId?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,l=e.deviceLogId||this.ids;this.$modal.confirm('是否确认删除设备日志编号为"'+l+'"的数据项？').then((function(){return m(l)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/log/export",Object(o["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},d=p,f=l("2877"),g=Object(f["a"])(d,a,r,!1,null,null,null);t["default"]=g.exports}}]);