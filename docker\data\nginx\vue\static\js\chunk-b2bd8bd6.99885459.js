(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b2bd8bd6"],{"814a":function(e,t,r){"use strict";r.d(t,"e",(function(){return a})),r.d(t,"f",(function(){return n})),r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return l})),r.d(t,"a",(function(){return s})),r.d(t,"g",(function(){return u})),r.d(t,"b",(function(){return m}));var i=r("b775");function a(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/firmware/getLatest/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function u(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},9355:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-left":"20px"}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[0!=e.productInfo.isOwner?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:add"],expression:"['iot:firmware:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")]):e._e()],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:list"],expression:"['iot:firmware:list']"}],attrs:{type:"warning",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.getList}},[e._v("刷新")])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareList,size:"small"},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{label:"固件名称",align:"center",prop:"firmwareName"}}),r("el-table-column",{attrs:{label:"固件版本",align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v("Version ")]),e._v(" "+e._s(t.row.version)+" ")]}}])}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"isLatest",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isLatest?r("el-tag",{attrs:{type:"success"}},[e._v("最新")]):r("el-tag",{attrs:{type:"info"}},[e._v("默认")])]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"下载地址",align:"center",prop:"filePath","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{href:e.getDownloadUrl(t.row.filePath),underline:!1,type:"success"}},[e._v(e._s(e.getDownloadUrl(t.row.filePath)))])]}}])}),r("el-table-column",{attrs:{label:"固件描述",align:"center",prop:"remark","min-width":"200"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[0!=e.productInfo.isOwner?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:query"],expression:"['iot:firmware:query']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-view"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("查看")]):e._e(),0!=e.productInfo.isOwner?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:remove"],expression:"['iot:firmware:remove']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")]):e._e()]}}])})],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"固件名称",prop:"firmwareName"}},[r("el-input",{attrs:{placeholder:"请输入固件名称"},model:{value:e.form.firmwareName,callback:function(t){e.$set(e.form,"firmwareName",t)},expression:"form.firmwareName"}})],1),r("el-form-item",{attrs:{label:"固件版本",prop:"version"}},[r("el-input",{attrs:{placeholder:"请输入固件版本",type:"number",step:"0.1"},model:{value:e.form.version,callback:function(t){e.$set(e.form,"version",t)},expression:"form.version"}})],1),r("el-form-item",{attrs:{label:"最新固件",prop:"isLatest"}},[r("el-switch",{attrs:{"active-text":"","inactive-text":"","active-value":1,"inactive-value":0},model:{value:e.form.isLatest,callback:function(t){e.$set(e.form,"isLatest",t)},expression:"form.isLatest"}}),r("el-link",{staticStyle:{"font-size":"12px","margin-left":"15px"},attrs:{type:"info",underline:!1}},[e._v("提示：产品中只能有一个最新固件")])],1),r("el-form-item",{attrs:{label:"固件上传",prop:"filePath"}},[r("fileUpload",{ref:"file-upload",attrs:{value:e.form.filePath,limit:1,fileSize:10,fileType:["bin","zip","pdf"]},on:{input:function(t){return e.getFilePath(t)}}})],1),r("el-form-item",{attrs:{label:"固件描述",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",rows:"4",placeholder:"请输入固件信息"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"},{name:"show",rawName:"v-show",value:!e.form.firmwareId,expression:"!form.firmwareId"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("新 增")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:firmware:edit"],expression:"['iot:firmware:edit']"},{name:"show",rawName:"v-show",value:e.form.firmwareId,expression:"form.firmwareId"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("修 改")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],n=r("5530"),o=(r("d81d"),r("2a75")),l=r("814a"),s=r("5f87"),u={name:"product-firmware",dicts:["iot_yes_no"],components:{fileUpload:o["a"]},props:{product:{type:Object,default:null}},watch:{product:function(e,t){this.productInfo=e,this.productInfo&&0!=this.productInfo.productId&&(this.queryParams.productId=this.productInfo.productId,this.form.productId=this.productInfo.productId,this.form.productName=this.productInfo.productName,this.getList())}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,firmwareList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:100,firmwareName:null,productName:null,productId:0,isSys:null},productInfo:{},form:{version:1},rules:{firmwareName:[{required:!0,message:"固件名称不能为空",trigger:"blur"}],version:[{required:!0,message:"固件版本不能为空",trigger:"blur"}],filePath:[{required:!0,message:"文件路径不能为空",trigger:"blur"}]},upload:{isUploading:!1,headers:{Authorization:"Bearer "+Object(s["a"])()},url:"/prod-api/iot/tool/upload",fileList:[]}}},created:function(){},methods:{getDownloadUrl:function(e){return window.location.origin+"/prod-api"+e},getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.firmwareList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={firmwareId:null,firmwareName:null,tenantId:null,tenantName:null,productId:this.form.productId,productName:this.form.productName,isSys:null,isLatest:0,version:1,filePath:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.firmwareId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加产品固件",this.upload.fileList=[]},handleUpdate:function(e){var t=this;this.reset();var r=e.firmwareId||this.ids;Object(l["c"])(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改产品固件",t.upload.fileList=[{name:t.form.firmwareName,url:t.form.filePath}]}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.firmwareId?Object(l["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.firmwareId||this.ids;this.$modal.confirm('是否确认删除产品固件编号为"'+r+'"的数据项？').then((function(){return Object(l["b"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/firmware/export",Object(n["a"])({},this.queryParams),"firmware_".concat((new Date).getTime(),".xlsx"))},getFilePath:function(e){this.form.filePath=e},submitUpload:function(){this.$refs.upload.submit()},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.isUploading=!1,this.form.filePath=e.url,this.$modal.msgSuccess(e.msg)},handleDownload:function(e){window.open("/prod-api"+e.filePath)}}},m=u,c=r("2877"),d=Object(c["a"])(m,i,a,!1,null,null,null);t["default"]=d.exports}}]);