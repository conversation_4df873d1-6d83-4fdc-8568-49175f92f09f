(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-40553a81"],{"9b9c":function(t,e,o){"use strict";o.d(e,"f",(function(){return n})),o.d(e,"g",(function(){return l})),o.d(e,"e",(function(){return a})),o.d(e,"a",(function(){return i})),o.d(e,"i",(function(){return u})),o.d(e,"d",(function(){return s})),o.d(e,"b",(function(){return d})),o.d(e,"c",(function(){return c})),o.d(e,"h",(function(){return p}));var r=o("b775");function n(t){return Object(r["a"])({url:"/iot/product/list",method:"get",params:t})}function l(){return Object(r["a"])({url:"/iot/product/shortList",method:"get"})}function a(t){return Object(r["a"])({url:"/iot/product/"+t,method:"get"})}function i(t){return Object(r["a"])({url:"/iot/product",method:"post",data:t})}function u(t){return Object(r["a"])({url:"/iot/product",method:"put",data:t})}function s(t){return Object(r["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function d(t){return Object(r["a"])({url:"/iot/product/status/",method:"put",data:t})}function c(t){return Object(r["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(r["a"])({url:"/iot/product/queryByTemplateId",method:"get",params:t})}},c0c7:function(t,e,o){"use strict";o.d(e,"k",(function(){return l})),o.d(e,"i",(function(){return a})),o.d(e,"h",(function(){return i})),o.d(e,"a",(function(){return u})),o.d(e,"o",(function(){return s})),o.d(e,"c",(function(){return d})),o.d(e,"l",(function(){return c})),o.d(e,"b",(function(){return p})),o.d(e,"g",(function(){return m})),o.d(e,"m",(function(){return f})),o.d(e,"j",(function(){return h})),o.d(e,"p",(function(){return g})),o.d(e,"q",(function(){return b})),o.d(e,"r",(function(){return v})),o.d(e,"f",(function(){return y})),o.d(e,"n",(function(){return O})),o.d(e,"d",(function(){return j})),o.d(e,"e",(function(){return F}));var r=o("b775"),n=o("c38a");function l(t){return Object(r["a"])({url:"/system/user/list",method:"get",params:t})}function a(t){return Object(r["a"])({url:"/system/user/"+Object(n["e"])(t),method:"get"})}function i(t){return Object(r["a"])({url:"/system/dept/getRole?deptId="+t,method:"get"})}function u(t){return Object(r["a"])({url:"/system/user",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/system/user",method:"put",data:t})}function d(t){return Object(r["a"])({url:"/system/user/"+t,method:"delete"})}function c(t,e){var o={userId:t,password:e};return Object(r["a"])({url:"/system/user/resetPwd",method:"put",data:o})}function p(t,e){var o={userId:t,status:e};return Object(r["a"])({url:"/system/user/changeStatus",method:"put",data:o})}function m(){return Object(r["a"])({url:"/wechat/getWxBindQr",method:"get"})}function f(t){return Object(r["a"])({url:"/wechat/cancelBind",method:"post",data:t})}function h(){return Object(r["a"])({url:"/system/user/profile",method:"get"})}function g(t){return Object(r["a"])({url:"/system/user/profile",method:"put",data:t})}function b(t,e){var o={oldPassword:t,newPassword:e};return Object(r["a"])({url:"/system/user/profile/updatePwd",method:"put",params:o})}function v(t){return Object(r["a"])({url:"/system/user/profile/avatar",method:"post",data:t})}function y(t){return Object(r["a"])({url:"/system/user/authRole/"+t,method:"get"})}function O(t){return Object(r["a"])({url:"/system/user/authRole",method:"put",params:t})}function j(){return Object(r["a"])({url:"/system/user/deptTree",method:"get"})}function F(t){return Object(r["a"])({url:"/system/user/deptTree?showOwner="+t,method:"get"})}},f4c2:function(t,e,o){"use strict";o.r(e);var r=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-dialog",{attrs:{title:t.upload.title,visible:t.upload.importAllotDialog,width:"550px","append-to-body":""},on:{"update:visible":function(e){return t.$set(t.upload,"importAllotDialog",e)}}},[o("div",{staticStyle:{"margin-top":"-55px"}},[o("el-divider",{staticStyle:{"margin-top":"-30px"}}),o("el-form",{ref:"allotForm",attrs:{"label-position":"top",model:t.allotForm,rules:t.allotRules}},[o("el-form-item",{attrs:{label:"产品",prop:"productId"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择产品",filterable:""},model:{value:t.allotForm.productId,callback:function(e){t.$set(t.allotForm,"productId",e)},expression:"allotForm.productId"}},t._l(t.productList,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),o("el-form-item",{attrs:{label:"目标机构",prop:"deptId"}},[o("treeselect",{attrs:{options:t.deptOptions,"show-count":!0,placeholder:"请选择目标机构"},model:{value:t.allotForm.deptId,callback:function(e){t.$set(t.allotForm,"deptId",e)},expression:"allotForm.deptId"}})],1),o("el-form-item",{attrs:{label:"上传文件",prop:"fileList"}},[o("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:t.upload.headers,action:t.upload.url+"?productId="+t.allotForm.productId+"&deptId="+t.allotForm.deptId,disabled:t.upload.isUploading,"on-progress":t.handleFileUploadProgress,"on-success":t.handleFileSuccess,"auto-upload":!1,"on-change":t.handleChange,"on-remove":t.handleRemove,drag:""},model:{value:t.allotForm.fileList,callback:function(e){t.$set(t.allotForm,"fileList",e)},expression:"allotForm.fileList"}},[o("i",{staticClass:"el-icon-upload"}),o("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),o("em",[t._v("点击上传")])]),o("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[o("div",{staticStyle:{"margin-top":"-5px"}},[o("span",[t._v("1.仅允许导入xls、xlsx格式文件。")]),o("div",{staticStyle:{"margin-top":"-10px"}},[o("span",[t._v("2.单次最多分配1000个设备,单次设备较多时需要较长的校验、导入时间。")])]),o("div",{staticStyle:{"margin-top":"-10px"}},[o("span",[t._v("3.上传文件并分配后，可到设备列表-更多操作-设备导入记录中查看上传失败的设备详情信息。")])])])])]),t._v(" "),o("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:t.importAllotTemplate}},[o("i",{staticClass:"el-icon-download"}),t._v("设备分配模板")])],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.upload.importAllotDialog=!1}}},[t._v("取消")]),o("el-button",{attrs:{type:"primary"},on:{click:t.submitImportDevice}},[t._v("确认分配")])],1)])},n=[],l=(o("d81d"),o("9b9c")),a=o("5f87"),i=o("c0c7"),u=o("ca17"),s=o.n(u),d=(o("542c"),{name:"allotImport",components:{Treeselect:s.a},data:function(){return{type:1,allotForm:{productId:0,deptId:0,fileList:[]},productList:[],deptOptions:[],upload:{title:"导入分配",importAllotDialog:!1,isUploading:!1,headers:{Authorization:"Bearer "+Object(a["a"])()},url:"/prod-api/iot/device/importAssignmentData"},isSubDev:!1,allotRules:{productId:[{required:!0,message:"产品不能为空",trigger:"change"}],deptId:[{required:!0,message:"目标机构不能为空",trigger:"change"}],fileList:[{required:!0,message:"请上传文件",trigger:"change"}]}}},created:function(){this.getDeptTree(),this.getProductList()},methods:{getDeptTree:function(){var t=this;Object(i["d"])().then((function(e){t.deptOptions=e.data}))},importAllotTemplate:function(){this.type=2,this.download("/iot/device/uploadTemplate?type="+this.type,{},"allot_device_".concat((new Date).getTime(),".xlsx"))},handleChange:function(t,e){this.allotForm.fileList=e,this.allotForm.fileList&&this.$refs.allotForm.clearValidate("fileList")},handleRemove:function(t,e){this.allotForm.fileList=e,this.$refs.allotForm.validateField("fileList")},handleFileUploadProgress:function(t,e,o){this.upload.isUploading=!0},handleFileSuccess:function(t,e,o){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0})},getProductList:function(){var t=this;this.loading=!0;var e={pageSize:999};Object(l["f"])(e).then((function(e){t.productList=e.rows.map((function(t){return{value:t.productId,label:t.productName}})),t.total=e.total,t.loading=!1}))},submitImportDevice:function(){var t=this;this.$refs["allotForm"].validate((function(e){e&&(t.$refs.upload.submit(),t.upload.importAllotDialog=!1)}))}}}),c=d,p=o("2877"),m=Object(p["a"])(c,r,n,!1,null,null,null);e["default"]=m.exports}}]);