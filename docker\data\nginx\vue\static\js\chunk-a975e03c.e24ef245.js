(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a975e03c"],{3235:function(t,e,a){},"5e6c":function(t,e,a){"use strict";a.d(e,"g",(function(){return s})),a.d(e,"e",(function(){return o})),a.d(e,"a",(function(){return i})),a.d(e,"i",(function(){return c})),a.d(e,"c",(function(){return n})),a.d(e,"h",(function(){return u})),a.d(e,"b",(function(){return l})),a.d(e,"j",(function(){return d})),a.d(e,"d",(function(){return m})),a.d(e,"f",(function(){return p}));var r=a("b775");function s(t){return Object(r["a"])({url:"/modbus/job/list",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/modbus/job/"+t,method:"get"})}function i(t){return Object(r["a"])({url:"/modbus/job",method:"post",data:t})}function c(t,e){var a={taskId:t,status:e};return Object(r["a"])({url:"/modbus/job",method:"put",data:a})}function n(t){return Object(r["a"])({url:"/modbus/job/del",method:"post",data:t})}function u(t){return Object(r["a"])({url:"/productModbus/job/list",method:"get",params:t})}function l(t){return Object(r["a"])({url:"/productModbus/job",method:"post",data:t})}function d(t,e){var a={taskId:t,status:e};return Object(r["a"])({url:"/productModbus/job",method:"put",data:a})}function m(t){return Object(r["a"])({url:"/productModbus/job/"+t,method:"delete"})}function p(t,e){return Object(r["a"])({url:"/productModbus/job/getSlaveId?productId="+t+"&deviceId="+e,method:"get"})}},bc13:function(t,e,a){"use strict";a.d(e,"d",(function(){return s})),a.d(e,"e",(function(){return o})),a.d(e,"f",(function(){return i})),a.d(e,"a",(function(){return c})),a.d(e,"c",(function(){return n})),a.d(e,"b",(function(){return u}));var r=a("b775");function s(t){return Object(r["a"])({url:"/iot/message/encode",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/iot/message/post",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/iot/preferences/list",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/iot/preferences",method:"post",data:t})}function n(t){return Object(r["a"])({url:"/iot/preferences",method:"put",data:t})}function u(t){return Object(r["a"])({url:"/iot/preferences/".concat(t.id),method:"DELETE"})}},c7b4:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"modbus-task"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"70px"}},[a("el-form-item",{attrs:{prop:"jobName"}},[a("el-input",{attrs:{placeholder:t.$t("product.product-modbus-task.894593-1"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.jobName,callback:function(e){t.$set(t.queryParams,"jobName",e)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{attrs:{placeholder:t.$t("product.product-modbus-task.894593-3"),clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.dict.type.sys_job_status,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("product.product-modbus-task.894593-4")))]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("product.product-modbus-task.894593-5")))])],1)],1),a("el-row",{staticStyle:{"margin-bottom":"8px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:t.openEdit}},[t._v(t._s(t.$t("device.device-modbus.433390-1")))])],1),a("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.jobList,border:!1}},[a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-56"),align:"center",prop:"taskId","min-width":"100"}}),a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-7"),align:"left",prop:"jobName","min-width":"180"}}),a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-57"),align:"left",prop:"command","min-width":"160"}}),a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-58"),align:"center",prop:"status","min-width":"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":0,"inactive-value":1,disabled:!t.isEnableSwitch},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-59"),align:"center",prop:"remarkStr","min-width":"110"}}),a("el-table-column",{attrs:{label:t.$t("product.product-modbus-task.894593-60"),align:"center",fixed:"right",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:job:remove"],expression:"['productModbus:job:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(t._s(t.$t("product.product-modbus-task.894593-61")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),a("el-dialog",{attrs:{title:t.editName?t.$t("product.product-modbus-task.894593-12"):t.$t("product.product-modbus-task.894593-13"),visible:t.editDialog,width:t.editName?"800":"900"},on:{"update:visible":function(e){t.editDialog=e}}},[a("div",{staticClass:"dialog-content"},[a("el-form",{attrs:{model:t.createForm,"label-position":"top"}},[a("el-form-item",{attrs:{label:t.$t("product.product-modbus-task.894593-0"),prop:"jobName"}},[a("el-input",{staticClass:"input-item",attrs:{placeholder:t.$t("product.product-modbus-task.894593-1")},model:{value:t.createForm.jobName,callback:function(e){t.$set(t.createForm,"jobName",e)},expression:"createForm.jobName"}})],1),a("el-row",{attrs:{gutter:40}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:t.$t("product.product-modbus-task.894593-14"),prop:"path"}},[a("el-input",{staticClass:"input-item",attrs:{disabled:""},model:{value:t.createForm.path,callback:function(e){t.$set(t.createForm,"path",e)},expression:"createForm.path"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:t.$t("product.product-modbus-task.894593-15"),prop:"functionCode"}},[a("el-select",{staticClass:"input-item",on:{change:t.changeNum},model:{value:t.createForm.functionCode,callback:function(e){t.$set(t.createForm,"functionCode",e)},expression:"createForm.functionCode"}},t._l(t.functionCodeList,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{prop:"startPath"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[t._v(t._s(t.$t("product.product-modbus-task.894593-16")))]),a("el-tooltip",{attrs:{content:t.createForm.startPathSwitch,placement:"top"}},[a("el-switch",{staticStyle:{"margin-left":"10px"},attrs:{size:"mini","active-value":"Dec","inactive-value":"Hex"},model:{value:t.createForm.startPathSwitch,callback:function(e){t.$set(t.createForm,"startPathSwitch",e)},expression:"createForm.startPathSwitch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==t.createForm.startPathSwitch,expression:"createForm.startPathSwitch == 'Dec'"}],staticClass:"input-item",attrs:{type:"number",min:0},on:{change:function(){t.createForm.startPath16=t.int2hex(t.createForm.startPath)},input:function(){t.createForm.startPath16=t.int2hex(t.createForm.startPath)}},model:{value:t.createForm.startPath,callback:function(e){t.$set(t.createForm,"startPath",e)},expression:"createForm.startPath"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v("0x"+t._s(t.createForm.startPath16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=t.createForm.startPathSwitch,expression:"createForm.startPathSwitch != 'Dec'"}],staticClass:"input-item",on:{input:function(){t.createForm.startPath=t.hex2int(t.createForm.startPath16)}},model:{value:t.createForm.startPath16,callback:function(e){t.$set(t.createForm,"startPath16",e)},expression:"createForm.startPath16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(t.createForm.startPath))])])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!["05","06"].includes(t.createForm.functionCode),expression:"!['05', '06'].includes(createForm.functionCode)"}],attrs:{label:t.registerNumTitle,prop:"registerNum"}},[a("el-input-number",{staticClass:"input-item",attrs:{"controls-position":"right",min:0},on:{change:t.changeNum},model:{value:t.createForm.registerNum,callback:function(e){t.$set(t.createForm,"registerNum",e)},expression:"createForm.registerNum"}})],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:["05","06"].includes(t.createForm.functionCode),expression:"['05', '06'].includes(createForm.functionCode)"}],attrs:{prop:"setValue"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[t._v(t._s(t.registerNumTitle))]),a("el-tooltip",{attrs:{content:t.createForm.setValueSwitch,placement:"top"}},[a("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},model:{value:t.createForm.setValueSwitch,callback:function(e){t.$set(t.createForm,"setValueSwitch",e)},expression:"createForm.setValueSwitch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==t.createForm.setValueSwitch,expression:"createForm.setValueSwitch == 'Dec'"}],attrs:{type:"number"},on:{change:function(){t.createForm.setValue16=t.int2hex(t.createForm.setValue)},input:function(){t.createForm.setValue16=t.int2hex(t.createForm.setValue)}},model:{value:t.createForm.setValue,callback:function(e){t.$set(t.createForm,"setValue",e)},expression:"createForm.setValue"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v("0x"+t._s(t.createForm.setValue16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=t.createForm.setValueSwitch,expression:"createForm.setValueSwitch != 'Dec'"}],on:{input:function(){t.createForm.setValue=t.hex2int(t.createForm.setValue16)}},model:{value:t.createForm.setValue16,callback:function(e){t.$set(t.createForm,"setValue16",e)},expression:"createForm.setValue16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(t.createForm.setValue))])])],1)],1),t._l(t.registerValList,(function(e,r){return a("el-col",{directives:[{name:"show",rawName:"v-show",value:"16"==t.createForm.functionCode,expression:"createForm.functionCode == '16'"}],key:"register"+r,attrs:{span:12}},[a("el-form-item",{attrs:{prop:"registerValList"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[t._v("#"+t._s(r)+" "+t._s(t.$t("product.product-modbus-task.894593-17")))]),a("el-tooltip",{attrs:{content:e.switch,placement:"top"}},[a("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},on:{change:function(){t.refreshRegisterInpust(e,r)}},model:{value:e.switch,callback:function(a){t.$set(e,"switch",a)},expression:"item.switch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==e.switch,expression:"item.switch == 'Dec'"}],attrs:{type:"number",min:0},on:{change:function(){e.value16=t.int2hex(e.value),t.refreshRegisterInpust(e,r)},input:function(){e.value16=t.int2hex(e.value),t.refreshRegisterInpust(e,r)}},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v("0x"+t._s(e.value16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=e.switch,expression:"item.switch != 'Dec'"}],on:{input:function(){e.value=t.hex2int(e.value16),t.refreshRegisterInpust(e,r)}},model:{value:e.value16,callback:function(a){t.$set(e,"value16",a)},expression:"item.value16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[t._v(t._s(e.value))])])],1)],1)})),t._l(t.IOValList,(function(e,r){return a("el-col",{directives:[{name:"show",rawName:"v-show",value:"15"==t.createForm.functionCode,expression:"createForm.functionCode == '15'"}],key:"IO"+r,attrs:{span:6}},[a("el-form-item",{attrs:{prop:"registerValList"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[t._v("#"+t._s(r)+" "+t._s(t.$t("product.product-modbus-task.894593-18")))])]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:function(){t.refreshIOInpust(e,r)}},model:{value:e.value,callback:function(a){t.$set(e,"value",a)},expression:"item.value"}})],1)],1)}))],2),a("el-form-item",{attrs:{label:t.$t("device.device-timer.433369-2"),prop:"status"}},[a("el-radio-group",{model:{value:t.createForm.status,callback:function(e){t.$set(t.createForm,"status",e)},expression:"createForm.status"}},t._l(t.dict.type.sys_job_status,(function(e){return a("el-radio",{key:e.value,attrs:{label:Number(e.value)}},[t._v(t._s(e.label))])})),1)],1),a("el-form-item",{attrs:{label:t.$t("product.product-modbus-task.894593-19"),prop:"cycleType"}},[a("div",{staticClass:"timer-wrap"},[a("el-radio-group",{on:{input:t.handleCycleTypeInput},model:{value:t.createForm.cycleType,callback:function(e){t.$set(t.createForm,"cycleType",e)},expression:"createForm.cycleType"}},[a("el-radio",{staticStyle:{display:"block"},attrs:{label:1}},[t._v(" "+t._s(t.$t("product.product-modbus-task.894593-20"))+" "),a("el-tooltip",{attrs:{placement:"right"}},[a("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("product.product-modbus-task.894593-21"))+" "),a("br"),t._v(" "+t._s(t.$t("product.product-modbus-task.894593-22"))+" ")]),a("i",{staticClass:"el-icon-question",staticStyle:{color:"#909399"}})]),a("div",{staticClass:"timer-period"},[a("span",[t._v(t._s(t.$t("product.product-modbus-task.894593-23")))]),a("el-select",{staticStyle:{width:"100px","margin-left":"10px"},attrs:{size:"mini",disabled:2===t.createForm.cycleType},on:{change:t.handleCycleInterval},model:{value:t.cycles1[0].interval,callback:function(e){t.$set(t.cycles1[0],"interval",e)},expression:"cycles1[0].interval"}},t._l(t.dict.type.variable_operation_interval,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1),"week"===t.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===t.createForm.cycleType},model:{value:t.cycles1[0].week,callback:function(e){t.$set(t.cycles1[0],"week",e)},expression:"cycles1[0].week"}},t._l(t.dict.type.variable_operation_week,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1):t._e(),"month"===t.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===t.createForm.cycleType},model:{value:t.cycles1[0].day,callback:function(e){t.$set(t.cycles1[0],"day",e)},expression:"cycles1[0].day"}},t._l(t.dict.type.variable_operation_day,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1):t._e(),"day"===t.cycles1[0].interval||"week"===t.cycles1[0].interval||"month"===t.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===t.createForm.cycleType},on:{change:t.handleCycleTime},model:{value:t.cycles1[0].time,callback:function(e){t.$set(t.cycles1[0],"time",e)},expression:"cycles1[0].time"}},t._l(t.dict.type.variable_operation_time,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1):t._e(),a("span",{staticStyle:{"margin-left":"10px"}},[t._v(t._s(t.$t("product.product-modbus-task.894593-24")))])],1)],1)],1)],1)])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.createLoading,expression:"createLoading"}]},[a("div",{staticClass:"create-title"},[a("el-button",{attrs:{type:"text"},on:{click:function(e){return e.stopPropagation(),t.encode(e)}}},[t._v(t._s(t.$t("product.product-modbus-task.894593-25")))]),a("div",{staticClass:"title-right"},[a("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(e){return t.copyText(t.createCode)}}},[t._v(t._s(t.$t("product.product-modbus-task.894593-26")))])],1)],1),a("div",{staticClass:"create-code"},[t._v(t._s(t.createCode))])])],1),a("div",{staticClass:"dialog-btn",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:""},on:{click:function(e){t.editDialog=!1}}},[t._v(t._s(t.$t("product.product-modbus-task.894593-27")))]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["productModbus:job:add"],expression:"['productModbus:job:add']"}],attrs:{type:"primary"},on:{click:t.handleAdd}},[t._v(t._s(t.$t("confirm")))])],1)])],1)},s=[],o=a("c7eb"),i=a("1da1"),c=a("ade3"),n=(a("a15b"),a("d81d"),a("14d9"),a("a434"),a("e9c4"),a("cf45")),u=a("bc13"),l=a("5e6c"),d=a("e350"),m={name:"product-modbus-task",dicts:["sys_job_group","sys_job_status","variable_operation_interval","variable_operation_time","variable_operation_week","variable_operation_day","variable_operation_type"],props:{product:{type:Object,default:null}},watch:{product:{handler:function(t,e){t.productId&&t.productId!==e.productId&&(this.queryParams.productId=t.productId,this.queryParams.slaveId=t.slaveId,this.productInfo=t,this.getList())}}},computed:{registerNumTitle:function(){switch(this.createForm.functionCode){case"01":case"02":case"15":return this.$t("product.product-modbus-task.894593-29");case"03":case"04":case"16":return this.$t("product.product-modbus-task.894593-30");case"05":return this.$t("product.product-modbus-task.894593-31");case"06":return this.$t("product.product-modbus-task.894593-32")}}},data:function(){return Object(c["a"])(Object(c["a"])(Object(c["a"])(Object(c["a"])(Object(c["a"])({format:"Hex",loading:!1,editDialog:!1,createForm:{cycleType:1,status:0},ids:[],single:!0,multiple:!0,total:0,productInfo:{},functionCodeList:[{label:this.$t("product.product-modbus-task.894593-33"),value:"01"},{label:this.$t("product.product-modbus-task.894593-34"),value:"02"},{label:this.$t("product.product-modbus-task.894593-35"),value:"03"},{label:this.$t("product.product-modbus-task.894593-36"),value:"04"},{label:this.$t("product.product-modbus-task.894593-37"),value:"05"},{label:this.$t("product.product-modbus-task.894593-38"),value:"06"},{label:this.$t("product.product-modbus-task.894593-39"),value:"15"},{label:this.$t("product.product-modbus-task.894593-40"),value:"16"}],jobList:[],showSearch:!0,createCode:"",registerValList:[],IOValList:[],editName:!1,editNameForm:{},createLoading:!1,delDialog:!1,delItem:{}},"productInfo",{}),"isEnableSwitch",!1),"queryParams",{pageNum:1,pageSize:10,productId:null,subSerialNumber:null,command:null,taskId:null,status:null}),"cycles1",[{interval:"300",time:"",week:"",day:""}]),"cycles2",[{type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}])},created:function(){var t=Object(d["a"])(["productModbus:job:edit"]);t&&(this.isEnableSwitch=!0)},methods:{getList:function(){var t=this;this.loading=!0,Object(l["h"])(this.queryParams).then((function(e){t.jobList=e.rows,t.total=e.total,t.loading=!1}))},getSlaveId:function(){var t=this,e="";Object(l["f"])(this.product.productId,e).then((function(e){e.data?t.editDialog=!0:t.$confirm(t.$t("product.product-modbus-task.894593-66"),t.$t("product.product-modbus-task.894593-67"),{confirmButtonText:t.$t("product.product-modbus-task.894593-68"),cancelButtonText:t.$t("product.product-modbus-task.894593-69"),type:"warning"}).then((function(){t.gotoModbusConfig(),t.editDialog=!1})).catch((function(){t.editDialog=!1,t.$message({type:"info",message:t.$t("product.product-modbus-task.894593-70")})})),t.createForm.path=e.data}))},gotoModbusConfig:function(){var t="productModbus";this.$emit("getSendData",t)},submitForm:function(){var t=this;null!=this.createForm.taskId?Object(l["j"])(this.createForm).then((function(e){t.$modal.msgSuccess(t.$t("product.product-modbus-task.894593-62")),t.open=!1,t.getList()})):Object(l["b"])(this.createForm).then((function(e){t.$modal.msgSuccess(t.$t("product.product-modbus-task.894593-63")),t.open=!1,t.getList()}))},handleDelete:function(t){var e=this,a=t.taskId||this.ids;this.$modal.confirm(this.$t("product.product-modbus-task.894593-64",[a])).then((function(){return Object(l["d"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess(e.$t("product.product-modbus-task.894593-65"))})).catch((function(){}))},handleAdd:function(){var t=this,e={slaveId:parseInt(this.createForm.path),address:this.createForm.startPath,code:parseInt(this.createForm.functionCode),protocolCode:this.product.protocolCode,serialNumber:this.product.serialNumber};switch(this.createForm.functionCode){case"01":case"02":case"03":case"04":e.count=this.createForm.registerNum;break;case"05":case"06":e.writeData=this.createForm.setValue;break;case"15":e.count=this.createForm.registerNum;var a=this.IOValList.map((function(t){return t.value}));e.bitString=a.join("");break;case"16":e.count=this.createForm.registerNum;var r=this.registerValList.map((function(t){return t.value}));e.tenWriteData=r;break}Object(u["d"])(e).then((function(e){t.createCode=e.msg,t.handlePush()}))},handlePush:function(){var t="",e=this.cycles1.map((function(t){return"hour"===t.interval?{type:"hour"}:"day"===t.interval?{type:"day",time:t.time}:"week"===t.interval?{type:"week",week:t.week,time:t.time}:"month"===t.interval?{type:"month",day:t.day,time:t.time}:{interval:t.interval}}));t=JSON.stringify(e),this.createForm.productId=this.product.productId,this.createForm.command=this.createCode,this.createForm.remark=t,this.submitForm(),this.editDialog=!1},openEdit:function(){this.getSlaveId(),this.resetCreateForm(),this.editName=!1},reset:function(){this.form=Object(c["a"])(Object(c["a"])(Object(c["a"])(Object(c["a"])(Object(c["a"])({taskId:null,subDeviceId:null,subSerialNumber:null,command:null},"taskId",null),"status",0),"createBy",null),"createTime",null),"remark",null),this.resetForm("form")},resetCreateForm:function(){this.createForm={path:"01",functionCode:"01",startPath:0,startPath16:"0000",registerNum:1,startPathSwitch:"Dec",setValue:0,setValue16:"0000",setValueSwitch:"Dec",status:0,cycleType:1},this.createCode=""},int2hex:function(t){return Object(n["f"])(t)},hex2int:function(t){return Object(n["e"])(t)},changeNum:function(){if("16"==this.createForm.functionCode){for(var t=0;t<this.createForm.registerNum;t++){var e=this.registerValList[t];e||(this.registerValList[t]={value:0,value16:"0000",switch:"Dec"})}if(this.registerValList.length>this.createForm.registerNum){var a=this.registerValList.length-this.createForm.registerNum;this.registerValList.splice(this.createForm.registerNum,a)}}if("15"==this.createForm.functionCode){for(var r=0;r<this.createForm.registerNum;r++){var s=this.IOValList[r];s||(this.IOValList[r]={value:"0"})}if(this.IOValList.length>this.createForm.registerNum){var o=this.IOValList.length-this.createForm.registerNum;this.IOValList.splice(this.createForm.registerNum,o)}}},refreshRegisterInpust:function(t,e){this.$set(this.registerValList,e,t)},refreshIOInpust:function(t,e){this.$set(this.IOValList,e,t)},copyText:function(t){var e=Object(n["a"])(t);this.$message({type:e.type,message:e.message})},encode:function(){var t=this;return Object(i["a"])(Object(o["a"])().mark((function e(){var a,r,s,i;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:e.prev=0,t.createLoading=!0,a={slaveId:parseInt(t.createForm.path),address:t.createForm.startPath,code:parseInt(t.createForm.functionCode),protocolCode:t.product.protocolCode,serialNumber:t.product.serialNumber},e.t0=t.createForm.functionCode,e.next="01"===e.t0||"02"===e.t0||"03"===e.t0||"04"===e.t0?6:"05"===e.t0||"06"===e.t0?8:"15"===e.t0?10:"16"===e.t0?14:18;break;case 6:return a.count=t.createForm.registerNum,e.abrupt("break",18);case 8:return a.writeData=t.createForm.setValue,e.abrupt("break",18);case 10:return a.count=t.createForm.registerNum,r=t.IOValList.map((function(t){return t.value})),a.bitString=r.join(""),e.abrupt("break",18);case 14:return a.count=t.createForm.registerNum,s=t.registerValList.map((function(t){return t.value})),a.tenWriteData=s,e.abrupt("break",18);case 18:return e.next=20,Object(u["d"])(a);case 20:i=e.sent,t.createCode=i.msg,e.next=27;break;case 24:e.prev=24,e.t1=e["catch"](0),t.$message({type:"error",message:e.t1.message||t.$t("product.product-modbus-task.894593-41")});case 27:return e.prev=27,t.createLoading=!1,e.finish(27);case 30:case"end":return e.stop()}}),e,null,[[0,24,27,30]])})))()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(t){var e=this,a=0===t.status?this.$t("product.product-modbus-task.894593-42"):this.$t("product.product-modbus-task.894593-43");this.$modal.confirm(this.$t("product.product-modbus-task.894593-44",[a+'""'+t.jobName])).then((function(){return Object(l["j"])(t.taskId,t.status)})).then((function(){e.$modal.msgSuccess(a+e.$t("product.product-modbus-task.894593-45"))})).catch((function(){t.status=0===t.status?1:0}))},formatCronDisplay:function(t){var e="";if(0==t.isAdvance){var a='<br /><span style="color:#F56C6C">时间 '+t.cronExpression.substring(5,7)+":"+t.cronExpression.substring(2,4)+"</span>",r=t.cronExpression.substring(12);if("1,2,3,4,5,6,7"==r)e=this.$t("product.product-modbus-task.894593-47");else{for(var s=r.split(","),o=0;o<s.length;o++)"1"==s[o]?e+=this.$t("product.product-modbus-task.894593-48"):"2"==s[o]?e+=this.$t("product.product-modbus-task.894593-49"):"3"==s[o]?e+=this.$t("product.product-modbus-task.894593-50"):"4"==s[o]?e+=this.$t("product.product-modbus-task.894593-51"):"5"==s[o]?e+=this.$t("product.product-modbus-task.894593-52"):"6"==s[o]?e+=this.$t("product.product-modbus-task.894593-53"):"7"==s[o]&&(e+=this.$t("product.product-modbus-task.894593-54"));e=e.substring(0,e.length-1)+" "+a}}else e=this.$t("product.product-modbus-task.894593-55");return e},handleCustomIntervalAdd:function(){this.cycles2.push({type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""})},handleCycleTypeInput:function(t){1===t?this.cycles2=[{type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}]:this.cycles1=[{interval:"hour",time:"",week:"",day:""}]},handleCustomIntervalDelete:function(t){this.cycles2.splice(t,1)},handleCycleInterval:function(t){"hour"===t?this.$set(this.cycles1,0,{interval:t,time:"",week:"",day:""}):"day"===t?this.$set(this.cycles1,0,{interval:t,time:"01",week:"",day:""}):"week"===t?this.$set(this.cycles1,0,{interval:t,time:"01",week:"1",day:""}):"month"===t?this.$set(this.cycles1,0,{interval:t,time:"01",week:"",day:"1"}):this.$set(this.cycles1,0,{interval:t,time:"",week:"",day:""})},handleCustomInterval:function(t,e){"day"===e?this.$set(this.cycles2,t,{type:e,time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}):"week"===e?this.$set(this.cycles2,t,{type:e,time:"00",week:"1",day:"",toType:"3",toTime:"02",toWeek:"2",toDay:""}):"month"===e&&this.$set(this.cycles2,t,{type:e,time:"00",week:"",day:"1",toType:"4",toTime:"02",toWeek:"",toDay:"2"})}},mounted:function(){var t=this.product.productId;t&&(this.queryParams.productId=t,this.getList()),this.resetCreateForm()}},p=m,h=(a("e0e2"),a("2877")),b=Object(h["a"])(p,r,s,!1,null,"00e239e8",null);e["default"]=b.exports},e0e2:function(t,e,a){"use strict";a("3235")},e350:function(t,e,a){"use strict";a.d(e,"a",(function(){return s}));a("caad"),a("d3b7"),a("2532");var r=a("4360");function s(t){if(t&&t instanceof Array&&t.length>0){var e=r["a"].getters&&r["a"].getters.permissions,a=t,s="*:*:*",o=e.some((function(t){return s===t||a.includes(t)}));return!!o}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}}}]);