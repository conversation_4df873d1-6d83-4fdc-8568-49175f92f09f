(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bd31c26"],{"86d9":function(e,t,i){"use strict";i("f4ed")},bc13:function(e,t,i){"use strict";i.d(t,"d",(function(){return s})),i.d(t,"e",(function(){return r})),i.d(t,"f",(function(){return n})),i.d(t,"a",(function(){return c})),i.d(t,"c",(function(){return o})),i.d(t,"b",(function(){return l}));var a=i("b775");function s(e){return Object(a["a"])({url:"/iot/message/encode",method:"get",params:e})}function r(e){return Object(a["a"])({url:"/iot/message/post",method:"post",data:e})}function n(e){return Object(a["a"])({url:"/iot/preferences/list",method:"get",params:e})}function c(e){return Object(a["a"])({url:"/iot/preferences",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/iot/preferences",method:"put",data:e})}function l(e){return Object(a["a"])({url:"/iot/preferences/".concat(e.id),method:"DELETE"})}},e1f3:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticClass:"instruction-parsing"},[i("el-row",[i("el-col",{staticClass:"left",attrs:{span:16}},[i("div",{staticClass:"head"},[i("span",{staticStyle:{color:"#909399"}},[e._v(e._s(e.$t("device.instruction-parsing.830424-0")))]),i("span",[e._v(e._s(e.device.serialNumber))]),i("el-dropdown",{staticStyle:{"margin-left":"auto"}},[i("span",{staticClass:"el-dropdown-link"},[e._v(" "+e._s(e.format)+" "),i("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.formatList,(function(t,a){return i("el-dropdown-item",{key:a,attrs:{disabled:0!==a}},[e._v(e._s(t))])})),1)],1)],1),i("div",{staticClass:"content"},e._l(e.logList,(function(t,a){return i("div",{key:a,staticClass:"item-class",class:{"send-class":0==t.type,"receive-class":1==t.type,"report-class":2==t.type}},[i("div",{staticClass:"item-head"},[i("div",[e._v(" "+e._s(0==t.type?e.$t("device.instruction-parsing.830424-1"):1==t.type?e.$t("device.instruction-parsing.830424-2"):e.$t("device.instruction-parsing.830424-3"))+" ")]),i("div",{staticClass:"head-time"},[e._v(e._s(t.time))]),0==t.type||t.analysis?e._e():i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"item.loading"}],staticClass:"analysis-btn right-btn",on:{click:function(i){return i.stopPropagation(),e.analysisData(t,a)}}},[e._v(" "+e._s(e.$t("device.instruction-parsing.830424-4"))+" ")]),0!=t.type&&t.analysis?i("div",{staticClass:"analysised right-btn"},[e._v(" "+e._s(e.$t("device.instruction-parsing.830424-5"))+" ")]):e._e()]),i("div",{staticClass:"item-content"},[i("div",{staticClass:"content-value"},[e._v(e._s(t.value))])]),0!=t.type&&t.analysis?i("div",{staticClass:"analysis-data"},[i("div",{staticClass:"data-col left-col"},[i("div",{staticClass:"label"},[e._v(e._s(e.$t("device.instruction-parsing.830424-6")))]),i("div",{staticClass:"value"},[e._v(e._s(t.analysisVal.name||"--"))])]),i("div",{staticClass:"data-col right-col"},[i("div",{staticClass:"label"},[e._v(e._s(e.$t("device.instruction-parsing.830424-7")))]),i("div",{staticClass:"value"},[e._v(e._s(t.analysisVal.id||"--"))])]),i("div",{staticClass:"data-col left-col"},[i("div",{staticClass:"label"},[e._v(e._s(e.$t("device.instruction-parsing.830424-8")))]),i("div",{staticClass:"value"},[e._v(e._s(t.analysisVal.value||"--"))])]),t.analysisList.length>1?i("div",{staticClass:"data-col right-col"},[i("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(i){return i.stopPropagation(),e.openMore(t)}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-9")))])],1):e._e()]):e._e()])})),0),i("div",{staticClass:"bottom"},[i("el-input",{staticClass:"input-with-select",attrs:{placeholder:e.$t("device.instruction-parsing.830424-10")},model:{value:e.sendVal,callback:function(t){e.sendVal=t},expression:"sendVal"}},[i("el-select",{attrs:{slot:"prepend",placeholder:e.$t("device.instruction-parsing.830424-11")},slot:"prepend",model:{value:e.dataType,callback:function(t){e.dataType=t},expression:"dataType"}},[i("el-option",{attrs:{label:"hex",value:"hex"}}),i("el-option",{attrs:{label:"json",value:"json"}})],1)],1),i("el-button",{staticClass:"send-btn",attrs:{round:"",type:"primary",size:"mini"},on:{click:function(t){return t.stopPropagation(),e.sendMessage(t)}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-14")))])],1)]),i("el-col",{staticClass:"right",attrs:{span:8}},[i("div",{staticClass:"head right-head"},[e._v(e._s(e.$t("device.instruction-parsing.830424-15")))]),i("div",{staticClass:"content"},e._l(e.quickParsing,(function(t,a){return i("div",{key:a,staticClass:"quick-item",on:{click:function(i){return i.stopPropagation(),e.quickClick(t)},contextmenu:function(i){return i.preventDefault(),e.onContextmenu(i,t)}}},[e._v(" "+e._s(t.name)+" ")])})),0),i("div",{staticClass:"right-bottom",on:{click:function(t){return t.stopPropagation(),e.openEdit(t)}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-16")))])])],1),i("el-dialog",{attrs:{title:e.editName?e.$t("device.instruction-parsing.830424-17"):e.$t("device.instruction-parsing.830424-18"),visible:e.editDialog,width:e.editName?"550px":"600px"},on:{"update:visible":function(t){e.editDialog=t}}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!e.editName,expression:"!editName"}],staticClass:"dialog-content"},[i("el-form",{attrs:{model:e.createForm,"label-position":"top"}},[i("el-row",{attrs:{gutter:40}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:e.$t("device.instruction-parsing.830424-19"),prop:"path"}},[i("el-input",{attrs:{type:"number"},model:{value:e.createForm.path,callback:function(t){e.$set(e.createForm,"path",t)},expression:"createForm.path"}})],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:e.$t("device.instruction-parsing.830424-20"),prop:"start"}},[i("el-select",{staticStyle:{width:"100%"},on:{change:e.changeNum},model:{value:e.createForm.start,callback:function(t){e.$set(e.createForm,"start",t)},expression:"createForm.start"}},e._l(e.startList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:e.$t("device.instruction-parsing.830424-21"),prop:"functionCode"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{disabled:"0xFFAA"==e.createForm.start},on:{change:e.changeNum},model:{value:e.createForm.functionCode,callback:function(t){e.$set(e.createForm,"functionCode",t)},expression:"createForm.functionCode"}},e._l(e.functionCodeList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{prop:"startPath"}},[i("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[i("div",{staticStyle:{"margin-right":"6px"}},[e._v(e._s(e.$t("device.instruction-parsing.830424-22")))]),i("el-tooltip",{attrs:{content:e.createForm.startPathSwitch,placement:"top"}},[i("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},model:{value:e.createForm.startPathSwitch,callback:function(t){e.$set(e.createForm,"startPathSwitch",t)},expression:"createForm.startPathSwitch"}})],1)],1),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==e.createForm.startPathSwitch,expression:"createForm.startPathSwitch == 'Dec'"}],staticStyle:{width:"100%"},attrs:{type:"number",min:0},on:{change:function(){e.createForm.startPath16=e.int2hex(e.createForm.startPath)},input:function(){e.createForm.startPath16=e.int2hex(e.createForm.startPath)}},model:{value:e.createForm.startPath,callback:function(t){e.$set(e.createForm,"startPath",t)},expression:"createForm.startPath"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(e.createForm.startPath16))])]),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=e.createForm.startPathSwitch,expression:"createForm.startPathSwitch != 'Dec'"}],staticStyle:{width:"100%"},on:{input:function(){e.createForm.startPath=e.hex2int(e.createForm.startPath16)}},model:{value:e.createForm.startPath16,callback:function(t){e.$set(e.createForm,"startPath16",t)},expression:"createForm.startPath16"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(e.createForm.startPath))])])],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!["05","06"].includes(e.createForm.functionCode),expression:"!['05', '06'].includes(createForm.functionCode)"}],attrs:{label:e.registerNumTitle,prop:"registerNum"}},[i("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0},on:{change:e.changeNum},model:{value:e.createForm.registerNum,callback:function(t){e.$set(e.createForm,"registerNum",t)},expression:"createForm.registerNum"}})],1),i("el-form-item",{directives:[{name:"show",rawName:"v-show",value:["05","06"].includes(e.createForm.functionCode),expression:"['05', '06'].includes(createForm.functionCode)"}],attrs:{prop:"setValue"}},[i("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[i("div",{staticStyle:{"margin-right":"auto"}},[e._v(e._s(e.registerNumTitle))]),i("el-tooltip",{attrs:{content:e.createForm.setValueSwitch,placement:"top"}},[i("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},model:{value:e.createForm.setValueSwitch,callback:function(t){e.$set(e.createForm,"setValueSwitch",t)},expression:"createForm.setValueSwitch"}})],1)],1),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==e.createForm.setValueSwitch,expression:"createForm.setValueSwitch == 'Dec'"}],staticStyle:{width:"100%"},attrs:{type:"number"},on:{change:function(){e.createForm.setValue16=e.int2hex(e.createForm.setValue)},input:function(){e.createForm.setValue16=e.int2hex(e.createForm.setValue)}},model:{value:e.createForm.setValue,callback:function(t){e.$set(e.createForm,"setValue",t)},expression:"createForm.setValue"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(e.createForm.setValue16))])]),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=e.createForm.setValueSwitch,expression:"createForm.setValueSwitch != 'Dec'"}],staticStyle:{width:"100%"},on:{input:function(){e.createForm.setValue=e.hex2int(e.createForm.setValue16)}},model:{value:e.createForm.setValue16,callback:function(t){e.$set(e.createForm,"setValue16",t)},expression:"createForm.setValue16"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(e.createForm.setValue))])])],1)],1),e._l(e.registerValList,(function(t,a){return i("el-col",{directives:[{name:"show",rawName:"v-show",value:"16"==e.createForm.functionCode,expression:"createForm.functionCode == '16'"}],key:"register"+a,attrs:{span:12}},[i("el-form-item",{attrs:{prop:"registerValList"}},[i("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[i("div",{staticStyle:{"margin-right":"6px"}},[e._v("#"+e._s(a)+" "+e._s(e.$t("device.instruction-parsing.830424-23")))]),i("el-tooltip",{attrs:{content:t.switch,placement:"top"}},[i("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},on:{change:function(){e.refreshRegisterInpust(t,a)}},model:{value:t.switch,callback:function(i){e.$set(t,"switch",i)},expression:"item.switch"}})],1)],1),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==t.switch,expression:"item.switch == 'Dec'"}],staticStyle:{width:"100%"},attrs:{type:"number",min:0},on:{change:function(){t.value16=e.int2hex(t.value),e.refreshRegisterInpust(t,a)},input:function(){t.value16=e.int2hex(t.value),e.refreshRegisterInpust(t,a)}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(t.value16))])]),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=t.switch,expression:"item.switch != 'Dec'"}],staticStyle:{width:"100%"},on:{input:function(){t.value=e.hex2int(t.value16),e.refreshRegisterInpust(t,a)}},model:{value:t.value16,callback:function(i){e.$set(t,"value16",i)},expression:"item.value16"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(t.value))])])],1)],1)})),e._l(e.reportValList,(function(t,a){return i("el-col",{directives:[{name:"show",rawName:"v-show",value:"0xFFAA"==e.createForm.start,expression:"createForm.start == '0xFFAA'"}],key:"report"+a,attrs:{span:12}},[i("el-form-item",{attrs:{prop:"reportValList"}},[i("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[i("div",{staticStyle:{"margin-right":"6px"}},[e._v("#"+e._s(a)+" "+e._s(e.$t("device.instruction-parsing.830424-24")))]),i("el-tooltip",{attrs:{content:t.switch,placement:"top"}},[i("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},on:{change:function(){e.refreshReportValList(t,a)}},model:{value:t.switch,callback:function(i){e.$set(t,"switch",i)},expression:"item.switch"}})],1)],1),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==t.switch,expression:"item.switch == 'Dec'"}],staticStyle:{width:"100%"},attrs:{type:"number",min:0},on:{change:function(){t.value16=e.int2hex(t.value),e.refreshReportValList(t,a)},input:function(){t.value16=e.int2hex(t.value),e.refreshReportValList(t,a)}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(t.value16))])]),i("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=t.switch,expression:"item.switch != 'Dec'"}],staticStyle:{width:"100%"},on:{input:function(){t.value=e.hex2int(t.value16),e.refreshReportValList(t,a)}},model:{value:t.value16,callback:function(i){e.$set(t,"value16",i)},expression:"item.value16"}},[i("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(t.value))])])],1)],1)})),e._l(e.IOValList,(function(t,a){return i("el-col",{directives:[{name:"show",rawName:"v-show",value:"15"==e.createForm.functionCode,expression:"createForm.functionCode == '15'"}],key:"IO"+a,attrs:{span:6}},[i("el-form-item",{attrs:{prop:"registerValList"}},[i("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[i("div",{staticStyle:{"margin-right":"6px"}},[e._v("#"+e._s(a)+" "+e._s(e.$t("device.instruction-parsing.830424-25")))])]),i("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:function(){e.refreshIOInpust(t,a)}},model:{value:t.value,callback:function(i){e.$set(t,"value",i)},expression:"item.value"}})],1)],1)}))],2)],1),i("div",{directives:[{name:"loading",rawName:"v-loading",value:e.createLoading,expression:"createLoading"}]},[i("div",{staticClass:"create-title"},[i("el-button",{attrs:{type:"text",icon:"el-icon-check"},on:{click:function(t){return t.stopPropagation(),e.encode(t)}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-26")))]),i("div",{staticClass:"title-right"},[i("el-button",{attrs:{type:"",size:"mini",disabled:!e.canSend},on:{click:function(t){return e.openEditName({command:e.createCode})}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-27")))]),i("el-button",{attrs:{type:"primary",disabled:!e.canSend,size:"mini"},on:{click:function(t){return e.copyText(e.createCode)}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-28")))])],1)],1),i("div",{staticClass:"create-code"},[e._v(e._s(e.createCode))])])],1),i("div",{directives:[{name:"show",rawName:"v-show",value:e.editName,expression:"editName"}],staticClass:"dialog-content"},[i("el-form",{attrs:{model:e.editNameForm,"label-width":"100px"}},[i("el-form-item",{attrs:{label:e.$t("device.instruction-parsing.830424-29")}},[i("el-input",{staticStyle:{width:"350px"},attrs:{placeholder:e.$t("device.instruction-parsing.830424-30")},model:{value:e.editNameForm.name,callback:function(t){e.$set(e.editNameForm,"name",t)},expression:"editNameForm.name"}})],1),i("el-form-item",{attrs:{label:e.$t("device.instruction-parsing.830424-31")}},[i("el-input",{staticStyle:{width:"350px"},attrs:{disabled:!0},model:{value:e.editNameForm.command,callback:function(t){e.$set(e.editNameForm,"command",t)},expression:"editNameForm.command"}})],1)],1)],1),i("div",{staticClass:"dialog-btn",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{directives:[{name:"loading",rawName:"v-loading",value:e.saveLoading,expression:"saveLoading"}],attrs:{type:"primary",disabled:!e.canSend},on:{click:e.confrimBtn}},[e._v(e._s(e.$t("device.instruction-parsing.830424-33")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.editDialog=!1}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-32")))])],1)]),i("el-dialog",{attrs:{visible:e.delDialog,title:e.$t("device.instruction-parsing.830424-34"),width:"420px"},on:{"update:visible":function(t){e.delDialog=t}}},[i("div",{staticStyle:{padding:"20px"}},[e._v(e._s(e.$t("device.instruction-parsing.830424-35"))+e._s(e.delItem.name)+e._s(e.$t("device.instruction-parsing.830424-36")))]),i("div",{staticClass:"dialog-btn",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.delQuick}},[e._v(e._s(e.$t("device.instruction-parsing.830424-38")))]),i("el-button",{attrs:{type:""},on:{click:function(t){e.delDialog=!1}}},[e._v(e._s(e.$t("device.instruction-parsing.830424-37")))])],1)]),i("el-dialog",{attrs:{title:e.$t("device.instruction-parsing.830424-39"),width:"700px",visible:e.moreDialog},on:{"update:visible":function(t){e.moreDialog=t}}},[i("div",{staticClass:"dialog-content"},[i("el-table",{attrs:{data:e.analysisList,height:"400",border:!1}},[i("el-table-column",{attrs:{type:"index",label:e.$t("device.instruction-parsing.830424-40"),align:"center","min-width":"100"}}),i("el-table-column",{attrs:{prop:"name",label:e.$t("device.instruction-parsing.830424-41"),align:"left","min-width":"160"}}),i("el-table-column",{attrs:{prop:"id",label:e.$t("device.instruction-parsing.830424-42"),align:"left","min-width":"120"}}),i("el-table-column",{attrs:{prop:"value",label:e.$t("device.instruction-parsing.830424-43"),align:"left","min-width":"120"}})],1)],1)])],1)},s=[],r=i("c7eb"),n=i("1da1"),c=(i("a15b"),i("d81d"),i("14d9"),i("a434"),i("b0c0"),i("cf45")),o=i("bc13"),l={props:{device:{type:Object,default:null}},created:function(){this.getPreferencesList()},watch:{device:function(){this.getPreferencesList()}},computed:{registerNumTitle:function(){switch(this.createForm.functionCode){case"01":case"02":case"15":return this.$t("device.instruction-parsing.830424-44");case"03":case"04":case"16":return this.$t("device.instruction-parsing.830424-45");case"05":return this.$t("device.instruction-parsing.830424-46");case"06":return this.$t("device.instruction-parsing.830424-47")}}},data:function(){return{format:"Hex",formatList:["Hex","JSON","Plaintext"],logList:[],sendVal:"",topic:"/function/get",dataType:"hex",quickParsing:[],editDialog:!1,createForm:{},functionCodeList:[{label:this.$t("device.instruction-parsing.830424-48"),value:"01"},{label:this.$t("device.instruction-parsing.830424-49"),value:"02"},{label:this.$t("device.instruction-parsing.830424-50"),value:"03"},{label:this.$t("device.instruction-parsing.830424-51"),value:"04"},{label:this.$t("device.instruction-parsing.830424-52"),value:"05"},{label:this.$t("device.instruction-parsing.830424-53"),value:"06"},{label:this.$t("device.instruction-parsing.830424-54"),value:"15"},{label:this.$t("device.instruction-parsing.830424-55"),value:"16"}],startList:[{label:this.$t("device.instruction-parsing.830424-56"),value:"0xFFDD"},{label:this.$t("device.instruction-parsing.830424-57"),value:"0xFFAA"}],createCode:"",registerValList:[],reportValList:[],IOValList:[],editName:!1,editNameForm:{},createLoading:!1,delDialog:!1,delItem:{},analysisList:[],moreDialog:!1,saveLoading:!1,canSend:!1}},methods:{openEdit:function(){this.resetCreateForm(),this.editName=!1,this.editDialog=!0,this.canSend=!1},openMore:function(e){this.analysisList=e.analysisList||[],this.moreDialog=!0},openEditName:function(e){this.editNameForm={name:e.name||"",command:e.command},this.editName=!0,this.editDialog=!0},resetCreateForm:function(){this.createForm={path:"01",functionCode:"01",startPath:0,startPath16:"0000",registerNum:1,startPathSwitch:"Dec",setValue:0,setValue16:"0000",setValueSwitch:"Dec",start:"0xFFDD"},this.createCode=""},int2hex:function(e){return Object(c["f"])(e)},hex2int:function(e){return Object(c["e"])(e)},changeNum:function(){if("0xFFAA"==this.createForm.start){for(var e=0;e<this.createForm.registerNum;e++){var t=this.reportValList[e];t||(this.reportValList[e]={value:0,value16:"0000",switch:"Dec"})}if(this.registerValList.length>this.createForm.reportValList){var i=this.reportValList.length-this.createForm.reportValList;this.registerValList.splice(this.createForm.reportValList,i)}this.createForm.functionCode="03"}else{if("16"==this.createForm.functionCode){for(var a=0;a<this.createForm.registerNum;a++){var s=this.registerValList[a];s||(this.registerValList[a]={value:0,value16:"0000",switch:"Dec"})}if(this.registerValList.length>this.createForm.registerNum){var r=this.registerValList.length-this.createForm.registerNum;this.registerValList.splice(this.createForm.registerNum,r)}}if("15"==this.createForm.functionCode){for(var n=0;n<this.createForm.registerNum;n++){var c=this.IOValList[n];c||(this.IOValList[n]={value:"0"})}if(this.IOValList.length>this.createForm.registerNum){var o=this.IOValList.length-this.createForm.registerNum;this.IOValList.splice(this.createForm.registerNum,o)}}}},refreshRegisterInpust:function(e,t){this.$set(this.registerValList,t,e)},refreshReportValList:function(e,t){this.$set(this.reportValList,t,e)},refreshIOInpust:function(e,t){this.$set(this.IOValList,t,e)},confrimBtn:function(){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.editName){t.next=19;break}if(t.prev=1,e.saveLoading=!0,!e.editNameForm.id){t.next=8;break}return t.next=6,Object(o["c"])({command:e.editNameForm.command,name:e.editNameForm.name,serialNumber:e.device.serialNumber,id:e.editNameForm.id});case 6:t.next=10;break;case 8:return t.next=10,Object(o["a"])({command:e.editNameForm.command,name:e.editNameForm.name,serialNumber:e.device.serialNumber});case 10:e.$message({type:"success",message:"".concat(e.editNameForm.id?e.$t("device.instruction-parsing.830424-58"):e.$t("device.instruction-parsing.830424-59")+e.$t("device.instruction-parsing.830424-60"))}),e.getPreferencesList(),t.next=17;break;case 14:t.prev=14,t.t0=t["catch"](1),e.$message({type:"error",message:t.t0.message||"".concat(e.editNameForm.id?e.$t("device.instruction-parsing.830424-58"):e.$t("device.instruction-parsing.830424-59")+e.$t("device.instruction-parsing.830424-61"))});case 17:t.next=20;break;case 19:e.sendVal=e.createCode;case 20:e.saveLoading=!1,e.editDialog=!1;case 22:case"end":return t.stop()}}),t,null,[[1,14]])})))()},getPreferencesList:function(){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){var i,a;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(o["f"])({serialNumber:e.device.serialNumber});case 3:i=t.sent,a=i.rows,e.quickParsing=a,t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),console.log("🚀 ~ getPreferencesList ~ err:",t.t0);case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()},quickClick:function(e){this.sendVal=e.command},copyText:function(e){var t=Object(c["a"])(e);this.$message({type:t.type,message:t.message})},encode:function(){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){var i,a,s,n,c;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.createLoading=!0,i={slaveId:parseInt(e.createForm.path),address:e.createForm.startPath,code:parseInt(e.createForm.functionCode),start:e.createForm.start,protocolCode:e.device.protocolCode,serialNumber:e.device.serialNumber},"0xFFAA"!=e.createForm.start){t.next=10;break}i.address=e.createForm.startPath,i.bitCount=2*e.createForm.registerNum,a=e.reportValList.map((function(e){return e.value})),i.data=a,t.next=25;break;case 10:t.t0=e.createForm.functionCode,t.next="01"===t.t0||"02"===t.t0||"03"===t.t0||"04"===t.t0?13:"05"===t.t0||"06"===t.t0?15:"15"===t.t0?17:"16"===t.t0?21:25;break;case 13:return i.count=e.createForm.registerNum,t.abrupt("break",25);case 15:return i.writeData=e.createForm.setValue,t.abrupt("break",25);case 17:return i.count=e.createForm.registerNum,s=e.IOValList.map((function(e){return e.value})),i.bitString=s.join(""),t.abrupt("break",25);case 21:return i.count=e.createForm.registerNum,n=e.registerValList.map((function(e){return e.value})),i.tenWriteData=n,t.abrupt("break",25);case 25:return t.next=27,Object(o["d"])(i);case 27:c=t.sent,e.createCode=c.msg,t.next=34;break;case 31:t.prev=31,t.t1=t["catch"](0),e.$message({type:"error",message:t.t1.message||e.$t("device.instruction-parsing.830424-62")});case 34:return t.prev=34,e.createLoading=!1,e.canSend=!0,t.finish(34);case 38:case"end":return t.stop()}}),t,null,[[0,31,34,38]])})))()},sendMessage:function(){var e=this;try{if(!this.sendVal)throw{message:this.$t("device.instruction-parsing.830424-63")};Object(o["e"])({message:this.sendVal,serialNumber:this.device.serialNumber,topicName:this.topic,dataType:this.dataType}).then((function(t){e.logList.push({type:0,time:Object(c["d"])(new Date),value:e.sendVal,loading:!1,analysis:!1,analysisList:[]})}))}catch(t){this.$message({type:"error",message:t.message||this.$t("device.instruction-parsing.830424-64")})}},analysisData:function(e,t){try{if(e.loading=!0,e.analysis=!0,!e.analysisList[0])throw{message:this.$t("device.instruction-parsing.830424-65")};e.analysisVal={name:e.analysisList[0].name,value:e.analysisList[0].value,id:e.analysisList[0].id}}catch(i){this.$message({type:"error",message:i.message||this.$t("device.instruction-parsing.830424-66")})}finally{e.loading=!1}},onContextmenu:function(e,t){var i=this,a=[{label:this.$t("device.instruction-parsing.830424-67"),icon:"iconfont el-icon-edit-outline",onClick:function(){i.editNameForm=Object(c["b"])(t),i.editName=!0,i.editDialog=!0}},{label:this.$t("device.instruction-parsing.830424-68"),icon:"iconfont el-icon-delete",onClick:function(){i.delItem=t,i.delDialog=!0}}];return this.$contextmenu({items:a,event:e,zIndex:3,minWidth:230}),!1},delQuick:function(){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(o["b"])(e.delItem);case 3:e.$message({type:"success",message:e.$t("device.instruction-parsing.830424-69")}),e.delDialog=!1,e.getPreferencesList(),t.next=11;break;case 8:t.prev=8,t.t0=t["catch"](0),e.$message({type:"error",message:t.t0.message||e.$t("device.instruction-parsing.830424-70")});case 11:case"end":return t.stop()}}),t,null,[[0,8]])})))()}},mounted:function(){var e=this;this.resetCreateForm(),this.$busEvent.$on("updateData",(function(t){var i=t.serialNumber,a=(t.productId,t.data);i==e.device.serialNumber&&e.logList.push({type:2,time:Object(c["d"])(new Date),value:a.sources,loading:!1,analysis:!1,analysisList:a.message})}))},beforeDestroy:function(){this.$busEvent.$off("updateMqttMessage")}},u=l,d=(i("86d9"),i("2877")),m=Object(d["a"])(u,a,s,!1,null,"55634bb7",null);t["default"]=m.exports},f4ed:function(e,t,i){}}]);