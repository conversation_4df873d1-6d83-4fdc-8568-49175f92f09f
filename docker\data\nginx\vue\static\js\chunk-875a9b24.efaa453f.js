(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-875a9b24"],{"2f32":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"instruction-parsing"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"70px"}},[a("el-form-item",{attrs:{prop:"jobName"}},[a("el-input",{attrs:{placeholder:e.$t("device.device-modbus-task.384302-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{attrs:{placeholder:e.$t("device.device-modbus-task.384302-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_job_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.device-modbus-task.384302-4")))]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.device-modbus-task.384302-5")))])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:e.openEdit}},[e._v(e._s(e.$t("device.device-modbus.433390-1")))])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobList,border:!1}},[a("el-table-column",{attrs:{label:e.$t("device.device-modbus-task.384302-56"),align:"center",prop:"taskId","min-width":"100"}}),a("el-table-column",{attrs:{label:e.$t("device.device-timer.433369-7"),align:"center",prop:"jobName","min-width":"180"}}),a("el-table-column",{attrs:{label:e.$t("device.device-modbus-task.384302-57"),align:"center",prop:"command","min-width":"160"}}),a("el-table-column",{attrs:{label:e.$t("device.device-modbus-task.384302-58"),align:"center",prop:"status","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":0,"inactive-value":1},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:e.$t("device.device-modbus-task.384302-59"),align:"center",prop:"remarkStr","min-width":"110"}}),a("el-table-column",{attrs:{label:e.$t("device.device-modbus-task.384302-60"),align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["modbus:job:remove"],expression:"['modbus:job:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("device.device-modbus-task.384302-61")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.editName?e.$t("device.device-modbus-task.384302-12"):e.$t("device.device-modbus-task.384302-13"),visible:e.editDialog,width:e.editName?"800":"900"},on:{"update:visible":function(t){e.editDialog=t}}},[a("div",{staticClass:"dialog-content"},[a("el-form",{attrs:{model:e.createForm,"label-position":"top"}},[a("el-form-item",{attrs:{label:e.$t("device.device-modbus-task.384302-0"),prop:"jobName"}},[a("el-input",{staticClass:"input-item",attrs:{placeholder:e.$t("device.device-modbus-task.384302-1")},model:{value:e.createForm.jobName,callback:function(t){e.$set(e.createForm,"jobName",t)},expression:"createForm.jobName"}})],1),a("el-row",{attrs:{gutter:40}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.$t("device.device-modbus-task.384302-14"),prop:"path"}},[a("el-input",{staticClass:"input-item",attrs:{disabled:""},model:{value:e.createForm.path,callback:function(t){e.$set(e.createForm,"path",t)},expression:"createForm.path"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:e.$t("device.device-modbus-task.384302-15"),prop:"functionCode"}},[a("el-select",{staticClass:"input-item",on:{change:e.changeNum},model:{value:e.createForm.functionCode,callback:function(t){e.$set(e.createForm,"functionCode",t)},expression:"createForm.functionCode"}},e._l(e.functionCodeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{prop:"startPath"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[e._v(e._s(e.$t("device.device-modbus-task.384302-16")))]),a("el-tooltip",{attrs:{content:e.createForm.startPathSwitch,placement:"top"}},[a("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},model:{value:e.createForm.startPathSwitch,callback:function(t){e.$set(e.createForm,"startPathSwitch",t)},expression:"createForm.startPathSwitch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==e.createForm.startPathSwitch,expression:"createForm.startPathSwitch == 'Dec'"}],staticClass:"input-item",attrs:{type:"number",min:0},on:{change:function(){e.createForm.startPath16=e.int2hex(e.createForm.startPath)},input:function(){e.createForm.startPath16=e.int2hex(e.createForm.startPath)}},model:{value:e.createForm.startPath,callback:function(t){e.$set(e.createForm,"startPath",t)},expression:"createForm.startPath"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(e.createForm.startPath16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=e.createForm.startPathSwitch,expression:"createForm.startPathSwitch != 'Dec'"}],on:{input:function(){e.createForm.startPath=e.hex2int(e.createForm.startPath16)}},model:{value:e.createForm.startPath16,callback:function(t){e.$set(e.createForm,"startPath16",t)},expression:"createForm.startPath16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(e.createForm.startPath))])])],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!["05","06"].includes(e.createForm.functionCode),expression:"!['05', '06'].includes(createForm.functionCode)"}],attrs:{label:e.registerNumTitle,prop:"registerNum"}},[a("el-input-number",{staticClass:"input-item",attrs:{"controls-position":"right",min:0},on:{change:e.changeNum},model:{value:e.createForm.registerNum,callback:function(t){e.$set(e.createForm,"registerNum",t)},expression:"createForm.registerNum"}})],1),a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:["05","06"].includes(e.createForm.functionCode),expression:"['05', '06'].includes(createForm.functionCode)"}],attrs:{prop:"setValue"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[e._v(e._s(e.registerNumTitle))]),a("el-tooltip",{attrs:{content:e.createForm.setValueSwitch,placement:"top"}},[a("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},model:{value:e.createForm.setValueSwitch,callback:function(t){e.$set(e.createForm,"setValueSwitch",t)},expression:"createForm.setValueSwitch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==e.createForm.setValueSwitch,expression:"createForm.setValueSwitch == 'Dec'"}],attrs:{type:"number"},on:{change:function(){e.createForm.setValue16=e.int2hex(e.createForm.setValue)},input:function(){e.createForm.setValue16=e.int2hex(e.createForm.setValue)}},model:{value:e.createForm.setValue,callback:function(t){e.$set(e.createForm,"setValue",t)},expression:"createForm.setValue"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(e.createForm.setValue16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=e.createForm.setValueSwitch,expression:"createForm.setValueSwitch != 'Dec'"}],on:{input:function(){e.createForm.setValue=e.hex2int(e.createForm.setValue16)}},model:{value:e.createForm.setValue16,callback:function(t){e.$set(e.createForm,"setValue16",t)},expression:"createForm.setValue16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(e.createForm.setValue))])])],1)],1),e._l(e.registerValList,(function(t,i){return a("el-col",{directives:[{name:"show",rawName:"v-show",value:"16"==e.createForm.functionCode,expression:"createForm.functionCode == '16'"}],key:"register"+i,attrs:{span:12}},[a("el-form-item",{attrs:{prop:"registerValList"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[e._v("#"+e._s(i)+" "+e._s(e.$t("device.device-modbus-task.384302-17")))]),a("el-tooltip",{attrs:{content:t.switch,placement:"top"}},[a("el-switch",{attrs:{size:"mini","active-color":"#13ce66","inactive-color":"#ff4949","active-value":"Dec","inactive-value":"Hex"},on:{change:function(){e.refreshRegisterInpust(t,i)}},model:{value:t.switch,callback:function(a){e.$set(t,"switch",a)},expression:"item.switch"}})],1)],1),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"==t.switch,expression:"item.switch == 'Dec'"}],attrs:{type:"number",min:0},on:{change:function(){t.value16=e.int2hex(t.value),e.refreshRegisterInpust(t,i)},input:function(){t.value16=e.int2hex(t.value),e.refreshRegisterInpust(t,i)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v("0x"+e._s(t.value16))])]),a("el-input",{directives:[{name:"show",rawName:"v-show",value:"Dec"!=t.switch,expression:"item.switch != 'Dec'"}],on:{input:function(){t.value=e.hex2int(t.value16),e.refreshRegisterInpust(t,i)}},model:{value:t.value16,callback:function(a){e.$set(t,"value16",a)},expression:"item.value16"}},[a("div",{attrs:{slot:"append"},slot:"append"},[e._v(e._s(t.value))])])],1)],1)})),e._l(e.IOValList,(function(t,i){return a("el-col",{directives:[{name:"show",rawName:"v-show",value:"15"==e.createForm.functionCode,expression:"createForm.functionCode == '15'"}],key:"IO"+i,attrs:{span:6}},[a("el-form-item",{attrs:{prop:"registerValList"}},[a("div",{staticClass:"form-item-label",attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-right":"auto"}},[e._v("#"+e._s(i)+" "+e._s(e.$t("device.device-modbus-task.384302-18")))])]),a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:function(){e.refreshIOInpust(t,i)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1)],1)}))],2),a("el-form-item",{attrs:{label:e.$t("device.device-timer.433369-2"),prop:"status"}},[a("el-radio-group",{model:{value:e.createForm.status,callback:function(t){e.$set(e.createForm,"status",t)},expression:"createForm.status"}},e._l(e.dict.type.sys_job_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:Number(t.value)}},[e._v(e._s(t.label))])})),1)],1),a("el-form-item",{attrs:{label:e.$t("device.device-modbus-task.384302-19"),prop:"cycleType"}},[a("div",{staticClass:"timer-wrap"},[a("el-radio-group",{on:{input:e.handleCycleTypeInput},model:{value:e.createForm.cycleType,callback:function(t){e.$set(e.createForm,"cycleType",t)},expression:"createForm.cycleType"}},[a("el-radio",{staticStyle:{display:"block"},attrs:{label:1}},[e._v(" "+e._s(e.$t("device.device-modbus-task.384302-20"))+" "),a("el-tooltip",{attrs:{placement:"right"}},[a("div",{attrs:{slot:"content"},slot:"content"},[e._v(" "+e._s(e.$t("device.device-modbus-task.384302-21"))+" "),a("br"),e._v(" "+e._s(e.$t("device.device-modbus-task.384302-22"))+" ")]),a("i",{staticClass:"el-icon-question",staticStyle:{color:"#909399"}})]),a("div",{staticClass:"timer-period"},[a("span",[e._v(e._s(e.$t("device.device-modbus-task.384302-23")))]),a("el-select",{staticStyle:{width:"100px","margin-left":"10px"},attrs:{size:"mini",disabled:2===e.createForm.cycleType},on:{change:e.handleCycleInterval},model:{value:e.cycles1[0].interval,callback:function(t){e.$set(e.cycles1[0],"interval",t)},expression:"cycles1[0].interval"}},e._l(e.dict.type.variable_operation_interval,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1),"week"===e.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===e.createForm.cycleType},model:{value:e.cycles1[0].week,callback:function(t){e.$set(e.cycles1[0],"week",t)},expression:"cycles1[0].week"}},e._l(e.dict.type.variable_operation_week,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):e._e(),"month"===e.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===e.createForm.cycleType},model:{value:e.cycles1[0].day,callback:function(t){e.$set(e.cycles1[0],"day",t)},expression:"cycles1[0].day"}},e._l(e.dict.type.variable_operation_day,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):e._e(),"day"===e.cycles1[0].interval||"week"===e.cycles1[0].interval||"month"===e.cycles1[0].interval?a("el-select",{staticStyle:{width:"100px","margin-left":"5px"},attrs:{size:"mini",disabled:2===e.createForm.cycleType},on:{change:e.handleCycleTime},model:{value:e.cycles1[0].time,callback:function(t){e.$set(e.cycles1[0],"time",t)},expression:"cycles1[0].time"}},e._l(e.dict.type.variable_operation_time,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):e._e(),a("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(e.$t("device.device-modbus-task.384302-24")))])],1)],1)],1)],1)])],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.createLoading,expression:"createLoading"}]},[a("div",{staticClass:"create-title"},[a("el-button",{attrs:{type:"text"},on:{click:function(t){return t.stopPropagation(),e.encode(t)}}},[e._v(e._s(e.$t("device.device-modbus-task.384302-25")))]),a("div",{staticClass:"title-right"},[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.copyText(e.createCode)}}},[e._v(e._s(e.$t("device.device-modbus-task.384302-26")))])],1)],1),a("div",{staticClass:"create-code"},[e._v(e._s(e.createCode))])])],1),a("div",{staticClass:"dialog-btn",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:""},on:{click:function(t){e.editDialog=!1}}},[e._v(e._s(e.$t("device.device-modbus-task.384302-27")))]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v(e._s(e.$t("confirm")))])],1)])],1)},s=[],r=a("c7eb"),c=a("1da1"),o=(a("a15b"),a("d81d"),a("14d9"),a("a434"),a("e9c4"),a("a9e3"),a("b64b"),a("cf45")),n=a("bc13"),l=a("5e6c"),u={name:"device-modbus-task",dicts:["sys_job_group","sys_job_status","variable_operation_interval","variable_operation_time","variable_operation_week","variable_operation_day","variable_operation_type"],props:{device:{type:Object,default:null}},watch:{device:{deep:!0,handler:function(e,t){e.deviceId&&e.deviceId!==t.deviceId&&(this.queryParams.subDeviceId=e.deviceId,this.deviceInfo=e,this.getList())}}},computed:{registerNumTitle:function(){switch(this.createForm.functionCode){case"01":case"02":case"15":return this.$t("device.device-modbus-task.384302-29");case"03":case"04":case"16":return this.$t("device.device-modbus-task.384302-30");case"05":return this.$t("device.device-modbus-task.384302-31");case"06":return this.$t("device.device-modbus-task.384302-32")}}},data:function(){return{format:"Hex",loading:!1,editDialog:!1,createForm:{cycleType:1,status:0},ids:[],single:!0,multiple:!0,total:0,functionCodeList:[{label:this.$t("device.device-modbus-task.384302-33"),value:"01"},{label:this.$t("device.device-modbus-task.384302-34"),value:"02"},{label:this.$t("device.device-modbus-task.384302-35"),value:"03"},{label:this.$t("device.device-modbus-task.384302-36"),value:"04"},{label:this.$t("device.device-modbus-task.384302-37"),value:"05"},{label:this.$t("device.device-modbus-task.384302-38"),value:"06"},{label:this.$t("device.device-modbus-task.384302-39"),value:"15"},{label:this.$t("device.device-modbus-task.384302-40"),value:"16"}],jobList:[],showSearch:!0,createCode:"",registerValList:[],IOValList:[],editName:!1,editNameForm:{},createLoading:!1,delDialog:!1,delItem:{},deviceInfo:{},subdeviceId:"",queryParams:{pageNum:1,pageSize:10,subDeviceId:null,subSerialNumber:null,command:null,jobId:null,status:null},cycles1:[{interval:"300",time:"",week:"",day:""}],cycles2:[{type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}]}},methods:{getList:function(){var e=this;this.loading=!0,Object(l["g"])(this.queryParams).then((function(t){e.jobList=t.rows,e.total=t.total,e.loading=!1}))},submitForm:function(){var e=this;null!=this.createForm.taskId?Object(l["i"])(this.createForm).then((function(t){e.$modal.msgSuccess(e.$t("device.device-modbus-task.384302-62")),e.open=!1,e.getList()})):Object(l["a"])(this.createForm).then((function(t){e.$modal.msgSuccess(e.$t("device.device-modbus-task.384302-63")),e.open=!1,e.getList()}))},handleDelete:function(e){var t=this,a=e.taskId||this.ids,i={taskId:a,jobId:e.jobId};this.$modal.confirm(this.$t("device.device-modbus-task.384302-64",[a])).then((function(){return Object(l["c"])(i)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("device.device-modbus-task.384302-65"))})).catch((function(){}))},handleAdd:function(){var e=this,t={slaveId:parseInt(this.createForm.path),address:this.createForm.startPath,code:parseInt(this.createForm.functionCode),protocolCode:this.device.protocolCode,serialNumber:this.device.serialNumber};switch(this.createForm.functionCode){case"01":case"02":case"03":case"04":t.count=this.createForm.registerNum;break;case"05":case"06":t.writeData=this.createForm.setValue;break;case"15":t.count=this.createForm.registerNum;var a=this.IOValList.map((function(e){return e.value}));t.bitString=a.join("");break;case"16":t.count=this.createForm.registerNum;var i=this.registerValList.map((function(e){return e.value}));t.tenWriteData=i;break}Object(n["d"])(t).then((function(t){e.createCode=t.msg,e.handlePush()}))},handlePush:function(){var e="",t=this.cycles1.map((function(e){return"hour"===e.interval?{type:"hour"}:"day"===e.interval?{type:"day",time:e.time}:"week"===e.interval?{type:"week",week:e.week,time:e.time}:"month"===e.interval?{type:"month",day:e.day,time:e.time}:{interval:e.interval}}));e=JSON.stringify(t),this.createForm.subDeviceId=this.device.deviceId,this.createForm.subSerialNumber=this.device.serialNumber,this.createForm.command=this.createCode,this.createForm.remark=e,this.submitForm(),this.editDialog=!1},openEdit:function(){this.resetCreateForm(),this.getSlaveId(),this.editName=!1},reset:function(){this.form={taskId:null,subDeviceId:null,subSerialNumber:null,command:null,jobId:null,status:0,createBy:null,createTime:null,remark:null},this.resetForm("form")},resetCreateForm:function(){this.createForm={path:"01",functionCode:"01",startPath:0,startPath16:"0000",registerNum:1,startPathSwitch:"Dec",setValue:0,setValue16:"0000",setValueSwitch:"Dec",status:0,cycleType:1},this.createCode=""},int2hex:function(e){return Object(o["f"])(e)},hex2int:function(e){return Object(o["e"])(e)},changeNum:function(){if("16"==this.createForm.functionCode){for(var e=0;e<this.createForm.registerNum;e++){var t=this.registerValList[e];t||(this.registerValList[e]={value:0,value16:"0000",switch:"Dec"})}if(this.registerValList.length>this.createForm.registerNum){var a=this.registerValList.length-this.createForm.registerNum;this.registerValList.splice(this.createForm.registerNum,a)}}if("15"==this.createForm.functionCode){for(var i=0;i<this.createForm.registerNum;i++){var s=this.IOValList[i];s||(this.IOValList[i]={value:"0"})}if(this.IOValList.length>this.createForm.registerNum){var r=this.IOValList.length-this.createForm.registerNum;this.IOValList.splice(this.createForm.registerNum,r)}}},refreshRegisterInpust:function(e,t){this.$set(this.registerValList,t,e)},refreshIOInpust:function(e,t){this.$set(this.IOValList,t,e)},copyText:function(e){var t=Object(o["a"])(e);this.$message({type:t.type,message:t.message})},encode:function(){var e=this;return Object(c["a"])(Object(r["a"])().mark((function t(){var a,i,s,c;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:t.prev=0,e.createLoading=!0,a={slaveId:parseInt(e.createForm.path),address:e.createForm.startPath,code:parseInt(e.createForm.functionCode),protocolCode:e.device.protocolCode,serialNumber:e.device.serialNumber},t.t0=e.createForm.functionCode,t.next="01"===t.t0||"02"===t.t0||"03"===t.t0||"04"===t.t0?6:"05"===t.t0||"06"===t.t0?8:"15"===t.t0?10:"16"===t.t0?14:18;break;case 6:return a.count=e.createForm.registerNum,t.abrupt("break",18);case 8:return a.writeData=e.createForm.setValue,t.abrupt("break",18);case 10:return a.count=e.createForm.registerNum,i=e.IOValList.map((function(e){return e.value})),a.bitString=i.join(""),t.abrupt("break",18);case 14:return a.count=e.createForm.registerNum,s=e.registerValList.map((function(e){return e.value})),a.tenWriteData=s,t.abrupt("break",18);case 18:return t.next=20,Object(n["d"])(a);case 20:c=t.sent,e.createCode=c.msg,t.next=27;break;case 24:t.prev=24,t.t1=t["catch"](0),e.$message({type:"error",message:t.t1.message||e.$t("device.device-modbus-task.384302-41")});case 27:return t.prev=27,e.createLoading=!1,t.finish(27);case 30:case"end":return t.stop()}}),t,null,[[0,24,27,30]])})))()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},getSlaveId:function(){var e=this;4===this.device.deviceType&&(this.subdeviceId=this.device.deviceId),Object(l["f"])(this.device.productId,this.subdeviceId).then((function(t){t.data?e.editDialog=!0:e.$confirm(e.$t("device.device-modbus-task.384302-66"),e.$t("device.device-modbus-task.384302-67"),{confirmButtonText:e.$t("device.device-modbus-task.384302-68"),cancelButtonText:e.$t("device.device-modbus-task.384302-69"),type:"warning"}).then((function(){})).catch((function(){e.editDialog=!1,e.$message({type:"info",message:e.$t("device.device-modbus-task.384302-70")})})),e.createForm.path=t.data}))},handleStatusChange:function(e){var t=this,a=0===e.status?this.$t("device.device-modbus-task.384302-42"):this.$t("device.device-modbus-task.384302-43");this.$modal.confirm(this.$t("device.device-modbus-task.384302-44",[a+""+e.jobId])).then((function(){return Object(l["i"])(e.taskId,e.status)})).then((function(){t.$modal.msgSuccess(a+t.$t("device.device-modbus-task.384302-45"))})).catch((function(){e.status=0===e.status?0:1}))},formatCronDisplay:function(e){var t="";if(0==e.isAdvance){var a='<br /><span style="color:#F56C6C">时间 '+e.cronExpression.substring(5,7)+":"+e.cronExpression.substring(2,4)+"</span>",i=e.cronExpression.substring(12);if("1,2,3,4,5,6,7"==i)t=this.$t("device.device-modbus-task.384302-47");else{for(var s=i.split(","),r=0;r<s.length;r++)"1"==s[r]?t+=this.$t("device.device-modbus-task.384302-48"):"2"==s[r]?t+=this.$t("device.device-modbus-task.384302-49"):"3"==s[r]?t+=this.$t("device.device-modbus-task.384302-50"):"4"==s[r]?t+=this.$t("device.device-modbus-task.384302-51"):"5"==s[r]?t+=this.$t("device.device-modbus-task.384302-52"):"6"==s[r]?t+=this.$t("device.device-modbus-task.384302-53"):"7"==s[r]&&(t+=this.$t("device.device-modbus-task.384302-54"));t=t.substring(0,t.length-1)+" "+a}}else t=this.$t("device.device-modbus-task.384302-55");return t},handleUpdate:function(e){var t=this;this.reset();var a=e.jobId||this.ids;Object(l["e"])(a).then((function(e){t.form=e.data,t.actionList=JSON.parse(t.form.actions);for(var a=0;a<t.actionList.length;a++)1==t.actionList[a].type?t.setParentAndModelData(t.actionList[a],t.thingsModel.properties):2==t.actionList[a].type&&t.setParentAndModelData(t.actionList[a],t.thingsModel.functions);if(0==t.form.isAdvance){var i=t.form.cronExpression.substring(12).split(",").map(Number);t.timerWeekValue=i,t.timerTimeValue=t.form.cronExpression.substring(5,7)+":"+t.form.cronExpression.substring(2,4)}t.open=!0,t.title=t.$t("device.device-timer.433369-71")}))},handleCustomIntervalAdd:function(){this.cycles2.push({type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""})},handleCycleTypeInput:function(e){1===e?this.cycles2=[{type:"day",time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}]:this.cycles1=[{interval:"hour",time:"",week:"",day:""}]},handleCustomIntervalDelete:function(e){this.cycles2.splice(e,1)},handleCycleInterval:function(e){"hour"===e?this.$set(this.cycles1,0,{interval:e,time:"",week:"",day:""}):"day"===e?this.$set(this.cycles1,0,{interval:e,time:"01",week:"",day:""}):"week"===e?this.$set(this.cycles1,0,{interval:e,time:"01",week:"1",day:""}):"month"===e?this.$set(this.cycles1,0,{interval:e,time:"01",week:"",day:"1"}):this.$set(this.cycles1,0,{interval:e,time:"",week:"",day:""})},handleCustomInterval:function(e,t){"day"===t?this.$set(this.cycles2,e,{type:t,time:"00",week:"",day:"",toType:"1",toTime:"02",toWeek:"",toDay:""}):"week"===t?this.$set(this.cycles2,e,{type:t,time:"00",week:"1",day:"",toType:"3",toTime:"02",toWeek:"2",toDay:""}):"month"===t&&this.$set(this.cycles2,e,{type:t,time:"00",week:"",day:"1",toType:"4",toTime:"02",toWeek:"",toDay:"2"})}},mounted:function(){var e=this.device.deviceId;e&&(this.queryParams.subDeviceId=e,this.getList()),this.resetCreateForm()}},d=u,m=(a("a04d"),a("2877")),v=Object(m["a"])(d,i,s,!1,null,"dbfb9730",null);t["default"]=v.exports},4683:function(e,t,a){},"5e6c":function(e,t,a){"use strict";a.d(t,"g",(function(){return s})),a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return c})),a.d(t,"i",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"h",(function(){return l})),a.d(t,"b",(function(){return u})),a.d(t,"j",(function(){return d})),a.d(t,"d",(function(){return m})),a.d(t,"f",(function(){return v}));var i=a("b775");function s(e){return Object(i["a"])({url:"/modbus/job/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/modbus/job/"+e,method:"get"})}function c(e){return Object(i["a"])({url:"/modbus/job",method:"post",data:e})}function o(e,t){var a={taskId:e,status:t};return Object(i["a"])({url:"/modbus/job",method:"put",data:a})}function n(e){return Object(i["a"])({url:"/modbus/job/del",method:"post",data:e})}function l(e){return Object(i["a"])({url:"/productModbus/job/list",method:"get",params:e})}function u(e){return Object(i["a"])({url:"/productModbus/job",method:"post",data:e})}function d(e,t){var a={taskId:e,status:t};return Object(i["a"])({url:"/productModbus/job",method:"put",data:a})}function m(e){return Object(i["a"])({url:"/productModbus/job/"+e,method:"delete"})}function v(e,t){return Object(i["a"])({url:"/productModbus/job/getSlaveId?productId="+e+"&deviceId="+t,method:"get"})}},a04d:function(e,t,a){"use strict";a("4683")},bc13:function(e,t,a){"use strict";a.d(t,"d",(function(){return s})),a.d(t,"e",(function(){return r})),a.d(t,"f",(function(){return c})),a.d(t,"a",(function(){return o})),a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return l}));var i=a("b775");function s(e){return Object(i["a"])({url:"/iot/message/encode",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/iot/message/post",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/iot/preferences/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/preferences",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/iot/preferences",method:"put",data:e})}function l(e){return Object(i["a"])({url:"/iot/preferences/".concat(e.id),method:"DELETE"})}}}]);