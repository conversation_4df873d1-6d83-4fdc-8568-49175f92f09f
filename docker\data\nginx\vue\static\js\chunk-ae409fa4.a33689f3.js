(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ae409fa4"],{"06d9":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.$t("device.import-record.086254-0"),visible:e.open,width:"890px"},on:{"update:visible":function(t){e.open=t}}},[i("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",[i("el-date-picker",{staticStyle:{width:"340px"},attrs:{size:"small","value-format":"yyyy-MM-dd HH:mm:ss",type:"datetimerange","range-separator":"-","start-placeholder":e.$t("device.import-record.086254-3"),"end-placeholder":e.$t("device.import-record.086254-4")},model:{value:e.daterangeTime,callback:function(t){e.daterangeTime=t},expression:"daterangeTime"}})],1),i("el-form-item",{attrs:{prop:"status"}},[i("el-select",{attrs:{placeholder:e.$t("device.import-record.086254-2"),size:"small",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.common_status_type,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.import-record.086254-5")))]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.import-record.086254-6")))])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"small",border:!1}},[i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-7"),align:"center",prop:"id",width:"80"}}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-8"),align:"center",prop:"total","min-width":"100"}}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-13"),align:"left",prop:"productName","min-width":"170"}}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-9"),align:"center",prop:"successQuantity","min-width":"100"}}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-10"),align:"center",prop:"failQuantity","min-width":"100"}}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-11"),align:"center",prop:"status","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.common_status_type,value:t.row.status,size:"small","min-width":"100"}})]}}])}),i("el-table-column",{attrs:{label:e.$t("device.import-record.086254-12"),align:"center",prop:"createTime",width:"200"}})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)},r=[],o=(i("d81d"),i("9b9c")),l=i("584f"),s={name:"importRecord",dicts:["common_status_type"],data:function(){return{loading:!0,total:0,open:!1,productList:[],statusList:[],dataList:[],daterangeTime:[],queryParams:{pageNum:1,pageSize:10,productName:null,type:1}}},created:function(){this.getProductList()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999,showSenior:!0};Object(o["g"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(l["q"])(this.addDateRange(this.queryParams,this.daterangeTime)).then((function(t){e.dataList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}},n=s,c=i("2877"),d=Object(c["a"])(n,a,r,!1,null,null,null);t["default"]=d.exports},"10f3":function(e,t,i){"use strict";i.d(t,"e",(function(){return r})),i.d(t,"d",(function(){return o})),i.d(t,"c",(function(){return l})),i.d(t,"a",(function(){return s})),i.d(t,"g",(function(){return n})),i.d(t,"f",(function(){return c})),i.d(t,"b",(function(){return d}));var a=i("b775");function r(e){return Object(a["a"])({url:"/iot/group/list",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/iot/group/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/iot/group/getDeviceIds/"+e,method:"get"})}function s(e){return Object(a["a"])({url:"/iot/group",method:"post",data:e})}function n(e){return Object(a["a"])({url:"/iot/group",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/iot/group/updateDeviceGroups",method:"put",data:e})}function d(e){return Object(a["a"])({url:"/iot/group/"+e,method:"delete"})}},3106:function(e,t,i){},"3aec":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.importDeviceDialog,width:"550px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"importDeviceDialog",t)}}},[i("el-form",{ref:"importForm",attrs:{"label-width":"100px",model:e.importForm,rules:e.importRules}},[i("el-form-item",{attrs:{label:"",prop:"productName"}},[i("template",{slot:"label"},[e._v(" "+e._s(e.$t("device.device-edit.148398-4"))+" ")]),i("el-input",{staticStyle:{width:"360px"},attrs:{readonly:"",placeholder:e.$t("device.device-edit.148398-5")},model:{value:e.importForm.productName,callback:function(t){e.$set(e.importForm,"productName",t)},expression:"importForm.productName"}},[i("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v(e._s(e.$t("device.device-edit.148398-6")))])],1)],2),i("el-form-item",{attrs:{label:e.$t("uploadFile"),prop:"fileList"}},[i("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?productId="+e.importForm.productId,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,"on-change":e.handleChange,"on-remove":e.handleRemove,drag:""},model:{value:e.importForm.fileList,callback:function(t){e.$set(e.importForm,"fileList",t)},expression:"importForm.fileList"}},[i("i",{staticClass:"el-icon-upload"}),i("div",{staticClass:"el-upload__text"},[e._v(" "+e._s(e.$t("dragFileTips"))+" "),i("em",[e._v(e._s(e.$t("clickFileTips")))])]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[i("div",{staticStyle:{"margin-top":"10px"}},[i("span",[e._v(e._s(e.$t("device.batch-import-dialog.850870-5")))])])])]),i("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importTemplate}},[i("i",{staticClass:"el-icon-download"}),e._v(" "+e._s(e.$t("device.batch-import-dialog.850870-6"))+" ")])],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v(e._s(e.$t("confirm")))]),i("el-button",{on:{click:function(t){e.upload.importDeviceDialog=!1}}},[e._v(e._s(e.$t("cancel")))])],1)],1),i("product-list",{ref:"productList",attrs:{productId:e.importForm.productId},on:{productEvent:function(t){return e.getProductData(t)}}})],1)},r=[],o=i("5f87"),l=i("e51f"),s={name:"batchImport",components:{productList:l["default"]},props:{device:{type:Object,default:null}},data:function(){return{type:1,importForm:{productId:null,fileList:[],productName:""},productList:[],file:null,upload:{importDeviceDialog:!1,title:this.$t("batchImport"),isUploading:!1,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/iot/device/importData"},importRules:{productName:[{required:!0,message:this.$t("device.allot-import-dialog.060657-14"),trigger:"blur"}],fileList:[{required:!0,message:this.$t("plzUploadFile"),trigger:"change"}]}}},created:function(){},methods:{importTemplate:function(){this.download("/iot/device/uploadTemplate?type="+this.type,{},"device_template_".concat((new Date).getTime(),".xlsx"))},handleChange:function(e,t){this.importForm.fileList=t,this.importForm.fileList&&this.$refs.importForm.clearValidate("fileList")},handleRemove:function(e,t){this.importForm.fileList=t,this.$refs.importForm.validateField("fileList")},handleFileUploadProgress:function(e,t,i){this.upload.isUploading=!0},handleFileSuccess:function(e,t,i){this.upload.importDeviceDialog=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0}),this.$parent.getList()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(e){this.importForm.productId=e.productId,this.importForm.productName=e.productName},submitFileForm:function(){var e=this;this.$refs["importForm"].validate((function(t){t&&(e.$refs.upload.submit(),e.upload.importDeviceDialog=!1)}))}}},n=s,c=i("2877"),d=Object(c["a"])(n,a,r,!1,null,null,null);t["default"]=d.exports},"4efc":function(e,t){e.exports="data:image/png;base64,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"},"52bb":function(e,t){e.exports="data:image/png;base64,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"},"5b09":function(e,t,i){"use strict";i("3106")},"621f":function(e,t,i){"use strict";i("abd4")},"68b6":function(e,t,i){},abd4:function(e,t,i){},bed0:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.$t("device.allot-record.155854-0"),visible:e.open,width:"1000px"},on:{"update:visible":function(t){e.open=t}}},[i("div",{staticClass:"allot-recor-dialog"},[i("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{prop:"distributeType"}},[i("el-select",{attrs:{placeholder:e.$t("device.allot-record.155854-16"),filterable:"",size:"small",clearable:""},model:{value:e.queryParams.distributeType,callback:function(t){e.$set(e.queryParams,"distributeType",t)},expression:"queryParams.distributeType"}},[i("el-option",{attrs:{label:e.$t("device.index.105953-14"),value:1}}),i("el-option",{attrs:{label:e.$t("device.index.105953-15"),value:2}})],1)],1),i("el-form-item",{attrs:{prop:"productId"}},[i("el-select",{attrs:{placeholder:e.$t("device.allot-record.155854-2"),filterable:"",size:"small",clearable:""},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",{staticStyle:{width:"205px"},attrs:{prop:"operateDeptId"}},[i("treeselect",{attrs:{options:e.deptOptions,"show-count":!0,placeholder:e.$t("device.allot-record.155854-1")},model:{value:e.queryParams.operateDeptId,callback:function(t){e.$set(e.queryParams,"operateDeptId",t)},expression:"queryParams.operateDeptId"}})],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"small",border:!1}},[i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"190"}}),i("el-table-column",{attrs:{align:"left",prop:"operateDeptName","min-width":"160"}},[i("template",{slot:"header"},[i("span",[e._v(e._s(e.$t("device.allot-record.155854-1")))]),i("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:e.$t("device.allot-record.155854-5"),placement:"top"}},[i("i",{staticClass:"el-icon-warning-outline"})])],1)],2),i("el-table-column",{attrs:{align:"left",prop:"targetDeptName","min-width":"170"}},[i("template",{slot:"header"},[i("span",[e._v(e._s(e.$t("device.allot-import-dialog.060657-2")))]),i("el-tooltip",{staticClass:"item",staticStyle:{"margin-left":"10px"},attrs:{effect:"dark",content:e.$t("device.allot-record.155854-7"),placement:"top"}},[i("i",{staticClass:"el-icon-warning-outline"})])],1)],2),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-8"),align:"center",prop:"total","min-width":"85"}}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-9"),align:"center",prop:"successQuantity","min-width":"85"}}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-10"),align:"center",prop:"failQuantity","min-width":"85"}}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-11"),align:"center",prop:"status","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("dict-tag",{attrs:{options:e.dict.type.common_status_type,value:t.row.status,size:"small"}})]}}])}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-12"),align:"center",prop:"distributeTypeDesc","min-width":"90"}}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-13"),align:"center",prop:"createTime",width:"150"}}),i("el-table-column",{attrs:{label:e.$t("opation"),align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:record:export"],expression:"['iot:device:record:export']"}],attrs:{type:"text",size:"small",icon:"el-icon-download"},on:{click:function(i){return e.handleDownLoad(t.row)}}},[e._v(" "+e._s(e.$t("device.allot-record.155854-15"))+" ")])]}}])})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)])},r=[],o=i("5530"),l=(i("d81d"),i("9b9c")),s=i("584f"),n=i("c0c7"),c=i("ca17"),d=i.n(c),u=(i("542c"),{name:"allotRecord",dicts:["common_status_type"],components:{Treeselect:d.a},data:function(){return{loading:!0,total:0,open:!1,productList:[],dataList:[],daterangeTime:[],deptOptions:[],queryParams:{pageNum:1,pageSize:10,type:3}}},created:function(){this.getProductList(),this.getDeptTree()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999,showSenior:!0};Object(l["g"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(s["r"])(this.queryParams).then((function(t){e.dataList=t.rows,e.total=t.total,e.loading=!1}))},handleDownLoad:function(e){var t={parentId:e.id,type:4};this.download("iot/record/export",Object(o["a"])({},t),"allot_".concat((new Date).getTime(),".xlsx"))},getDeptTree:function(){var e=this;Object(n["d"])().then((function(t){e.deptOptions=t.data}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}}),p=u,m=(i("cd0b"),i("2877")),v=Object(m["a"])(p,a,r,!1,null,"c56e6dfe",null);t["default"]=v.exports},c0c7:function(e,t,i){"use strict";i.d(t,"l",(function(){return o})),i.d(t,"o",(function(){return l})),i.d(t,"j",(function(){return s})),i.d(t,"i",(function(){return n})),i.d(t,"a",(function(){return c})),i.d(t,"q",(function(){return d})),i.d(t,"c",(function(){return u})),i.d(t,"m",(function(){return p})),i.d(t,"b",(function(){return m})),i.d(t,"h",(function(){return v})),i.d(t,"n",(function(){return h})),i.d(t,"k",(function(){return g})),i.d(t,"r",(function(){return f})),i.d(t,"s",(function(){return y})),i.d(t,"t",(function(){return b})),i.d(t,"f",(function(){return w})),i.d(t,"p",(function(){return x})),i.d(t,"d",(function(){return A})),i.d(t,"e",(function(){return P})),i.d(t,"g",(function(){return I}));var a=i("b775"),r=i("c38a");function o(e){return Object(a["a"])({url:"/system/user/list",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/system/user/listTerminal",method:"get",params:e})}function s(e){return Object(a["a"])({url:"/system/user/"+Object(r["f"])(e),method:"get"})}function n(e){return Object(a["a"])({url:"/system/dept/getRole?deptId="+e,method:"get"})}function c(e){return Object(a["a"])({url:"/system/user",method:"post",data:e})}function d(e){return Object(a["a"])({url:"/system/user",method:"put",data:e})}function u(e){return Object(a["a"])({url:"/system/user/"+e,method:"delete"})}function p(e,t){var i={userId:e,password:t};return Object(a["a"])({url:"/system/user/resetPwd",method:"put",data:i})}function m(e,t){var i={userId:e,status:t};return Object(a["a"])({url:"/system/user/changeStatus",method:"put",data:i})}function v(){return Object(a["a"])({url:"/wechat/getWxBindQr",method:"get"})}function h(e){return Object(a["a"])({url:"/wechat/cancelBind",method:"post",data:e})}function g(){return Object(a["a"])({url:"/system/user/profile",method:"get"})}function f(e){return Object(a["a"])({url:"/system/user/profile",method:"put",data:e})}function y(e,t){var i={oldPassword:e,newPassword:t};return Object(a["a"])({url:"/system/user/profile/updatePwd",method:"put",params:i})}function b(e){return Object(a["a"])({url:"/system/user/profile/avatar",method:"post",data:e})}function w(e){return Object(a["a"])({url:"/system/user/authRole/"+e,method:"get"})}function x(e){return Object(a["a"])({url:"/system/user/authRole",method:"put",params:e})}function A(){return Object(a["a"])({url:"/system/user/deptTree",method:"get"})}function P(e){return Object(a["a"])({url:"/system/user/deptTree?showOwner="+e,method:"get"})}function I(e){return Object(a["a"])({url:"/system/user/getByDeptId",method:"get",params:e})}},c572:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"iot-device"},[a("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"15px","border-radius":"8px",width:"100%","padding-bottom":"22.5px"}},[a("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-18px",height:"36px",padding:"3px 0 0 0"},attrs:{model:e.queryParams,inline:!0,"label-width":"75px"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{attrs:{prop:"deviceName"}},[a("el-input",{attrs:{placeholder:e.$t("device.index.105953-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceName,callback:function(t){e.$set(e.queryParams,"deviceName",t)},expression:"queryParams.deviceName"}})],1),a("el-form-item",{attrs:{prop:"serialNumber"}},[a("el-input",{attrs:{placeholder:e.$t("device.index.105953-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{attrs:{placeholder:e.$t("device.index.105953-5"),clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_device_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e.searchShow?[a("el-form-item",[a("el-select",{attrs:{placeholder:e.$t("device.index.105953-7"),clearable:""},model:{value:e.queryParams.groupId,callback:function(t){e.$set(e.queryParams,"groupId",t)},expression:"queryParams.groupId"}},e._l(e.myGroupList,(function(e){return a("el-option",{key:e.groupId,attrs:{label:e.groupName,value:e.groupId}})})),1)],1)]:e._e(),a("div",{staticStyle:{float:"right"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))]),a("el-button",{attrs:{type:"text"},on:{click:e.searchChange}},[a("span",{staticStyle:{color:"#486ff2","margin-left":"14px"}},[e._v(e._s(e.searchShow?e.$t("template.index.891112-113"):e.$t("template.index.891112-112")))]),a("i",{class:{"el-icon-arrow-down":!e.searchShow,"el-icon-arrow-up":e.searchShow},staticStyle:{color:"#486ff2","margin-left":"10px"}})])],1)],2)],1),a("el-card",[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],on:{command:function(t){return e.handleCommand(t)}}},[a("el-button",{attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-plus"}},[e._v(" "+e._s(e.$t("device.index.105953-10"))+" "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{command:"handleEditDevice"}},[e._v(e._s(e.$t("device.index.105953-11")))]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:add"],expression:"['iot:device:add']"}],attrs:{command:"handleBatchImport"}},[e._v(e._s(e.$t("device.index.105953-12")))])],1)],1)],1),"list"==e.showType?a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple},on:{click:e.handleDelete}},[e._v(e._s(e.$t("del")))])],1):e._e(),a("el-col",{attrs:{span:1.5}},[a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],on:{command:function(t){return e.handleCommand1(t)}}},[a("el-button",{staticClass:"btn-wrap",attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-folder-opened"}},[e._v(" "+e._s(e.$t("device.index.105953-13"))+" "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],attrs:{command:"handleSelectAllot"}},[e._v(e._s(e.$t("device.index.105953-14")))]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:assignment"],expression:"['iot:device:assignment']"}],attrs:{command:"handleImportAllot"}},[e._v(e._s(e.$t("device.index.105953-15")))])],1)],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:recovery"],expression:"['iot:device:recovery']"}],staticClass:"btn-wrap",attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-delete"},on:{click:e.recycleDevice}},[e._v(e._s(e.$t("device.index.105953-16")))])],1),a("el-col",{attrs:{span:1.5}},[a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:record:list","iot:device:batchGenerator"],expression:"['iot:device:record:list', 'iot:device:batchGenerator']"}],on:{command:function(t){return e.handleCommandMore(t)}}},[a("el-button",{staticClass:"btn-wrap",attrs:{size:"small",type:"primary",plain:"",icon:"el-icon-more-outline"}},[e._v(" "+e._s(e.$t("device.index.105953-48"))+" "),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:record:list"],expression:"['iot:device:record:list']"}],attrs:{command:"importRecord"}},[e._v(e._s(e.$t("device.index.105953-49")))]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:batchGenerator"],expression:"['iot:device:batchGenerator']"}],attrs:{command:"generateSerialNumber"}},[e._v(e._s(e.$t("device.index.105953-50")))]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:record:list"],expression:"['iot:device:record:list']"}],attrs:{command:"recycleRecord"}},[e._v(e._s(e.$t("device.index.105953-51")))]),a("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:record:list"],expression:"['iot:device:record:list']"}],attrs:{command:"allotRecord"}},[e._v(e._s(e.$t("device.index.105953-52")))])],1)],1)],1),a("el-col",{staticStyle:{"line-height":"32px",margin:"0px 10px"},attrs:{span:1.5}},[a("el-checkbox",{on:{change:e.handleQuery},model:{value:e.queryParams.showChild,callback:function(t){e.$set(e.queryParams,"showChild",t)},expression:"queryParams.showChild"}},[a("div",{staticStyle:{color:"#606266 !important","font-size":"14px"}},[e._v(e._s(e.$t("device.index.105953-18")))])]),a("el-tooltip",{attrs:{content:e.$t("device.index.105953-19"),placement:"top"}},[a("i",{staticClass:"el-icon-question",staticStyle:{color:"#909399","font-size":"16px"}})])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}},[a("div",{staticClass:"button-group"},[a("el-tooltip",{attrs:{effect:"dark",content:"切换到卡片视图",placement:"top"}},[a("el-button",{class:["toggle-button card-button",{active:e.isListView}],attrs:{size:"small",icon:"el-icon-s-claim"},on:{click:e.handleShowCard}})],1),a("div",{staticClass:"separator"}),a("el-tooltip",{attrs:{effect:"dark",content:"切换到列表视图",placement:"top"}},[a("el-button",{class:["toggle-button list-button",{active:!e.isListView}],attrs:{size:"small",icon:"el-icon-s-grid"},on:{click:e.handleShowList}})],1)],1)])],1),"list"==e.showType?a("div",{staticStyle:{"margin-top":"16px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceList,border:!1},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"55"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-20"),align:"center",prop:"deviceId",width:"60"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-0"),align:"left",prop:"deviceName","min-width":"190"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-2"),align:"left",prop:"serialNumber","min-width":"180"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-21"),align:"left",prop:"productName","min-width":"190"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-59"),align:"center",prop:"deptName","min-width":"190"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-22"),align:"center",prop:"transport","min-width":"90"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-23"),align:"center",prop:"protocolCode","min-width":"120"}}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-24"),align:"center",prop:"subDeviceCount",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.subDeviceCount)+" ")]}}],null,!1,1050122242)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-25"),align:"center",prop:"isShadow",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.isShadow?a("el-tag",{attrs:{type:"success",size:"small"}},[e._v(e._s(e.$t("device.index.105953-26")))]):a("el-tag",{attrs:{type:"info",size:"small"}},[e._v(e._s(e.$t("device.index.105953-27")))])]}}],null,!1,707737213)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-28"),align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status,size:"small"}})]}}],null,!1,3188204005)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-29"),align:"center",prop:"rssi",width:"60"},scopedSlots:e._u([{key:"default",fn:function(e){return[3==e.row.status&&e.row.rssi>="-55"?a("svg-icon",{attrs:{"icon-class":"wifi_4"}}):3==e.row.status&&e.row.rssi>="-70"&&e.row.rssi<"-55"?a("svg-icon",{attrs:{"icon-class":"wifi_3"}}):3==e.row.status&&e.row.rssi>="-85"&&e.row.rssi<"-70"?a("svg-icon",{attrs:{"icon-class":"wifi_2"}}):3==e.row.status&&e.row.rssi>="-100"&&e.row.rssi<"-85"?a("svg-icon",{attrs:{"icon-class":"wifi_1"}}):a("svg-icon",{attrs:{"icon-class":"wifi_0"}})]}}],null,!1,3608481156)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-30"),align:"center",prop:"locationWay",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_location_way,value:t.row.locationWay,size:"small"}})]}}],null,!1,1502323453)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-31"),align:"center",prop:"firmwareVersion",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{size:"mini",type:"info"}},[e._v("Ver "+e._s(t.row.firmwareVersion))])]}}],null,!1,1749219447)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-32"),align:"center",prop:"activeTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.activeTime,"{y}-{m}-{d}")))])]}}],null,!1,3799299972)}),a("el-table-column",{attrs:{label:e.$t("device.index.105953-33"),align:"center",prop:"createTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}],null,!1,3484357996)}),a("el-table-column",{attrs:{fixed:"right",label:e.$t("device.index.105953-34"),align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{type:"text",size:"small",icon:"el-icon-view"},on:{click:function(i){return e.handleEditDevice(t.row)}}},[e._v(e._s(e.$t("device.index.105953-60")))]),0!=e.form.deviceId?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{type:"text",size:"small",icon:"el-icon-picture-outline"},on:{click:function(i){return e.openSummaryDialog(t.row)}}},[e._v(" "+e._s(e.$t("device.index.105953-37"))+" ")]):e._e(),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(i){return e.handleDelete(t.row)}}},[e._v(" "+e._s(e.$t("del"))+" ")])]}}],null,!1,1450469486)})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize,pageSizes:[12,24,36,60]},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1):e._e(),"card"==e.showType?a("div",{staticStyle:{"margin-top":"20px"}},[a("el-row",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{gutter:20}},e._l(e.deviceList,(function(t,r){return a("el-col",{key:r,staticClass:"device-item",attrs:{xs:24,sm:12,md:12,lg:8,xl:6}},[a("el-card",{staticClass:"card-item",attrs:{"body-style":{padding:"0px"},shadow:"always"}},[a("div",{staticClass:"item-title"},[a("div",[null!=t.imgUrl&&""!=t.imgUrl?a("el-image",{staticClass:"img",attrs:{lazy:"","preview-src-list":[e.baseUrl+t.imgUrl],src:e.baseUrl+t.imgUrl,fit:"cover"}}):2==t.deviceType?a("el-image",{staticClass:"img",attrs:{"preview-src-list":[i("4efc")],src:i("4efc"),fit:"cover"}}):3==t.deviceType?a("el-image",{staticClass:"img",attrs:{"preview-src-list":[i("c59e")],src:i("c59e"),fit:"cover"}}):a("el-image",{staticClass:"img",attrs:{"preview-src-list":[i("52bb")],src:i("52bb"),fit:"cover"}})],1),a("div",{staticClass:"title"},[a("div",{staticClass:"name",on:{click:function(i){return e.handleDeviceDetail(t)}}},[e._v(e._s(t.deviceName))]),a("div",{staticClass:"tag-wrap"},[a("dict-tag",{staticStyle:{"line-height":"15px"},attrs:{options:e.dict.type.iot_device_status,value:t.status,size:"mini"}}),t.protocolCode?a("el-tag",{staticStyle:{"margin-left":"6px"},attrs:{type:"info",size:"mini"}},[e._v(e._s(t.protocolCode))]):e._e(),t.transport?a("el-tag",{staticStyle:{"margin-left":"6px"},attrs:{type:"info",size:"mini"}},[e._v(e._s(t.transport))]):e._e(),t.alertCount?a("el-tag",{staticStyle:{"margin-left":"6px"},attrs:{type:"danger",size:"mini"}},[e._v(e._s(e.$t("device.index.105953-53"))+e._s(t.alertCount.unprocessedCount))]):e._e()],1)]),a("div",{staticStyle:{width:"45px"}}),a("div",{staticClass:"status"},[a("div",{staticClass:"icon-wrap"},[a("svg-icon",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticClass:"item",staticStyle:{cursor:"pointer"},attrs:{"icon-class":"qrcode"},on:{click:function(i){return e.openSummaryDialog(t)}}}),3==t.status&&t.rssi>="-55"?a("svg-icon",{staticClass:"item",attrs:{"icon-class":"wifi_4"}}):3==t.status&&t.rssi>="-70"&&t.rssi<"-55"?a("svg-icon",{staticClass:"item",attrs:{"icon-class":"wifi_3"}}):3==t.status&&t.rssi>="-85"&&t.rssi<"-70"?a("svg-icon",{staticClass:"item",attrs:{"icon-class":"wifi_2"}}):3==t.status&&t.rssi>="-100"&&t.rssi<"-85"?a("svg-icon",{staticClass:"item",attrs:{"icon-class":"wifi_1"}}):a("svg-icon",{staticClass:"item",attrs:{"icon-class":"wifi_0"}})],1)])]),a("el-row",{attrs:{gutter:10}},[a("el-col",{staticStyle:{padding:"0px 0 0 30px","margin-bottom":"-10px"},attrs:{span:14}},[a("el-descriptions",{staticStyle:{"margin-top":"10px","white-space":"nowrap",overflow:"hidden"},attrs:{column:1,size:"small"}},[a("el-descriptions-item",{attrs:{label:e.$t("device.index.105953-20")}},[e._v(" "+e._s(t.serialNumber)+" ")])],1)],1),a("el-col",{staticStyle:{padding:"0px 30px 0 0px","margin-bottom":"-10px"},attrs:{span:10}},[a("el-descriptions",{staticStyle:{"margin-top":"10px","white-space":"nowrap"},attrs:{column:1,size:"small"}},[a("el-descriptions-item",{attrs:{label:e.$t("device.index.105953-59")}},[e._v(" "+e._s(t.deptName)+" ")])],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{staticStyle:{padding:"0px 0 0 30px"},attrs:{span:14}},[a("el-descriptions",{staticStyle:{"margin-top":"10px","white-space":"nowrap"},attrs:{column:1,size:"small"}},[a("el-descriptions-item",{attrs:{label:e.$t("device.index.105953-39")}},[e._v(" "+e._s(t.productName)+" ")])],1)],1),a("el-col",{staticStyle:{padding:"0px 30px 0 0px"},attrs:{span:10}},[a("el-descriptions",{staticStyle:{"margin-top":"10px","white-space":"nowrap"},attrs:{column:1,size:"small"}},[a("el-descriptions-item",{attrs:{label:e.$t("device.index.105953-32")}},[e._v(" "+e._s(e.parseTime(t.activeTime,"{y}-{m}-{d}")||"xxxx-xx-xx")+" ")])],1)],1)],1),a("el-divider",{staticClass:"divider"}),a("div",{staticStyle:{height:"39px",padding:"0 10px",display:"flex","align-items":"center","justify-content":"space-evenly"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleEditDevice(t,"basic")}}},[e._v(e._s(e.$t("device.index.105953-36")))]),a("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],staticStyle:{width:"1px",margin:"0px 10px","font-size":"16px",color:"#dcdfe6"}},[e._v("|")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:query"],expression:"['iot:device:query']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleRunDevice(t)}}},[e._v(" "+e._s(e.$t("device.index.105953-40"))+" ")]),a("span",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],staticStyle:{width:"1px",margin:"0px 10px","font-size":"16px",color:"#dcdfe6"}},[e._v("|")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:remove"],expression:"['iot:device:remove']"}],attrs:{size:"mini",type:"text"},on:{click:function(i){return e.handleDelete(t)}}},[e._v(e._s(e.$t("device.index.105953-35")))])],1)],1)],1)})),1),0==e.total?a("el-empty",{attrs:{description:e.$t("device.index.105953-41")}}):e._e(),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 20px 0"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize,pageSizes:[12,24,36,60]},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1):e._e()],1),a("el-dialog",{attrs:{visible:e.openSummary,width:"300px","append-to-body":""},on:{"update:visible":function(t){e.openSummary=t}}},[a("div",{staticStyle:{border:"1px solid #ccc",width:"220px","text-align":"center",margin:"0 auto","margin-top":"-15px"}},[a("vue-qr",{attrs:{text:e.qrText,size:200}}),a("div",{staticStyle:{"padding-bottom":"10px"}},[e._v(e._s(e.$t("device.index.105953-42")))])],1)]),a("el-dialog",{attrs:{title:e.$t("device.index.105953-54"),visible:e.openGenerate,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.openGenerate=t}}},[a("el-form",{model:{value:e.elForm,callback:function(t){e.elForm=t},expression:"elForm"}},[a("el-form-item",{attrs:{"label-width":"95px",label:e.$t("device.index.105953-55")}},[a("el-input",{staticStyle:{width:"310px"},attrs:{type:"number",max:200,oninput:"if(value>200)value=200;if(value<0)value=0"},model:{value:e.elForm.count,callback:function(t){e.$set(e.elForm,"count",t)},expression:"elForm.count"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:device:batchGenerator"],expression:"['iot:device:batchGenerator']"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("device.index.105953-56")))]),a("el-button",{on:{click:function(t){e.openGenerate=!1}}},[e._v(e._s(e.$t("device.index.105953-57")))])],1)],1),a("batchImport",{ref:"batchImport",on:{save:e.saveDialog}}),a("allotImport",{ref:"allotImport",on:{save:e.saveAllotDialog}}),a("importRecord",{ref:"importRecord"}),a("recycleRecord",{ref:"recycleRecord"}),a("allotRecord",{ref:"allotRecord"}),a("deviceAdd",{ref:"deviceAdd"})],1)},r=[],o=i("5530"),l=i("c7eb"),s=i("1da1"),n=(i("d81d"),i("14d9"),i("e9c4"),i("a9e3"),i("b64b"),i("d3b7"),i("25f0"),i("658f5")),c=i.n(n),d=i("584f"),u=i("10f3"),p=i("f5a7"),m=i("dce4"),v=i("ca17"),h=i.n(v),g=(i("542c"),i("3aec")),f=i("f4c2"),y=i("06d9"),b=i("f1cb"),w=i("bed0"),x=i("3568"),A={name:"Device",dicts:["iot_device_status","iot_is_enable","iot_location_way","iot_transport_type"],components:{vueQr:c.a,Treeselect:h.a,batchImport:g["default"],allotImport:f["default"],importRecord:y["default"],recycleRecord:b["default"],allotRecord:w["default"],deviceAdd:x["default"]},data:function(){return{qrText:"fastbee",openSummary:!1,openGenerate:!1,showSearch:!0,searchShow:!1,showType:"card",ids:[],loading:!0,single:!0,multiple:!0,total:0,deviceList:[],myGroupList:[],baseUrl:"/prod-api",isListView:!0,queryParams:{pageNum:1,pageSize:12,showChild:!0,deviceName:null,productId:null,groupId:null,productName:null,userId:null,userName:null,tenantId:null,tenantName:null,serialNumber:null,status:null,networkAddress:null,activeTime:null},form:{productId:0,status:1,locationWay:1,firmwareVersion:1,serialNumber:"",deviceType:1,isSimulate:0,productName:""},productList:[],elForm:{count:1},isSubDev:!1}},created:function(){var e=this.$route.query.productId;null!=e&&(this.queryParams.productId=Number(e),this.queryParams.groupId=null,this.queryParams.serialNumber=null);var t=this.$route.query.groupId;null!=t&&(this.queryParams.groupId=Number(t),this.queryParams.productId=null,this.queryParams.serialNumber=null);var i=this.$route.query.sn;null!=i&&(this.queryParams.serialNumber=i,this.queryParams.productId=null,this.queryParams.groupId=null),this.connectMqtt()},activated:function(){var e=this.$route.query.t;if(null!=e&&e!=this.uniqueId){this.uniqueId=e;var t=this.$route.query.pageNum;null!=t&&(this.queryParams.pageNum=Number(t));var i=this.$route.query.productId;null!=i&&(this.queryParams.productId=Number(i),this.queryParams.groupId=null,this.queryParams.serialNumber=null);var a=this.$route.query.groupId;null!=a&&(this.queryParams.groupId=Number(a),this.queryParams.productId=null,this.queryParams.serialNumber=null);var r=this.$route.query.sn;null!=r&&(this.queryParams.serialNumber=r,this.queryParams.productId=null,this.queryParams.groupId=null),this.getList()}},methods:{connectMqtt:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.$mqttTool.client){t.next=3;break}return t.next=3,e.$mqttTool.connect();case 3:e.mqttCallback(),e.getList();case 5:case"end":return t.stop()}}),t)})))()},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,i,a){var r=t.split("/"),o=(r[1],r[2]);if(i=JSON.parse(i.toString()),i&&"status"==r[3]){console.log(e.$t("device.index.105953-43"),t),console.log(e.$t("device.index.105953-44"),i);for(var l=0;l<e.deviceList.length;l++)if(e.deviceList[l].serialNumber==o)return e.deviceList[l].status=i.status,e.deviceList[l].isShadow=i.isShadow,void(e.deviceList[l].rssi=i.rssi)}}))},handleCommand:function(e){switch(e){case"handleEditDevice":this.handleAddDevice();break;case"handleBatchImport":this.handleBatchImport();break;default:break}},handleAddDevice:function(){this.$refs.deviceAdd.open=!0,this.$refs.deviceAdd.reset()},handleBatchImport:function(){this.$refs.batchImport.upload.importDeviceDialog=!0,this.$refs.batchImport.importForm.productName=null},handleImportAllot:function(){this.$refs.allotImport.upload.importAllotDialog=!0,this.$refs.allotImport.allotForm.productName=null,this.$refs.allotImport.allotForm.deptId=null},saveDialog:function(){this.getList()},saveAllotDialog:function(){this.getList()},handleCommand1:function(e){switch(e){case"handleSelectAllot":this.handleSelectAllot();break;case"handleImportAllot":this.handleImportAllot();break;default:break}},handleSelectAllot:function(){this.$router.push({path:"/iot/device-select-allot"})},recycleDevice:function(){this.$router.push({path:"/iot/device-recycle"})},handleCommandMore:function(e){switch(e){case"importRecord":this.handleImportRecord();break;case"generateSerialNumber":this.generateSerialNumber();break;case"recycleRecord":this.handleRecycleRecord();break;case"allotRecord":this.handleAllotRecord();break;default:break}},handleImportRecord:function(){this.$refs.importRecord.open=!0,this.$refs.importRecord.getList()},handleRecycleRecord:function(){this.$refs.recycleRecord.open=!0,this.$refs.recycleRecord.getList()},handleAllotRecord:function(){this.$refs.allotRecord.open=!0,this.$refs.allotRecord.getList()},generateSerialNumber:function(){this.openGenerate=!0},submitForm:function(){this.download("iot/device/batchGenerator",Object(o["a"])({},this.elForm),"batchGenerator_".concat((new Date).getTime(),".xlsx")),this.openGenerate=!1},openSummaryDialog:function(e){var t={type:1,deviceNumber:e.serialNumber,productId:e.productId,productName:e.productName};this.qrText=JSON.stringify(t),this.openSummary=!0},mqttSubscribe:function(e){for(var t=[],i=0;i<e.length;i++){var a="/+/"+e[i].serialNumber+"/status/post";t.push(a)}this.$mqttTool.subscribe(t)},getGroupList:function(){var e=this;this.loading=!0;var t={pageSize:30,pageNum:1,userId:this.$store.state.user.userId};Object(u["e"])(t).then((function(t){e.myGroupList=t.rows}))},getList:function(){var e=this;this.loading=!0,this.queryParams.params={},this.getGroupList(),Object(d["p"])(this.queryParams).then((function(t){e.deviceList=t.rows,e.total=t.total,e.deviceList&&e.deviceList.length>0&&e.mqttSubscribe(e.deviceList),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.queryParams.productId=null,this.queryParams.groupId=null,this.queryParams.serialNumber=null,this.resetForm("queryForm"),this.handleQuery()},handleChangeShowType:function(){this.showType="card"==this.showType?"list":"card"},handleShowList:function(){this.isListView=!1,this.showType="list",console.log("切换到列表视图")},handleShowCard:function(){this.isListView=!0,this.showType="card",console.log("切换到卡片视图")},handleDeviceDetail:function(e){m["a"].hasPermi("iot:device:query")&&this.handleEditDevice(e)},handleEditDevice:function(e,t){var i=0,a=0;0!=e&&(i=e.deviceId||this.ids,a=e.subDeviceCount>0?1:0),this.$router.push({path:"/iot/device-edit",query:{deviceId:i,isSubDev:a,pageNum:this.queryParams.pageNum,activeName:t}})},handleRunDevice:function(e){var t=0,i=0;0!=e&&(t=e.deviceId||this.ids,i=e.subDeviceCount>0?1:0),3===e.deviceType?this.$router.push({path:"/iot/device-edit",query:{deviceId:t,isSubDev:i,pageNum:this.queryParams.pageNum,activeName:"sipChannel"}}):this.$router.push({path:"/iot/device-edit",query:{deviceId:t,isSubDev:i,pageNum:this.queryParams.pageNum,activeName:"runningStatus"}})},handleDelete:function(e){var t=this,i=this.$loading({lock:!0}),a=e.deviceId||this.ids;this.$modal.confirm(this.$t("device.index.105953-45",[a])).then((function(){return 3===e.deviceType&&Object(p["a"])(e.serialNumber),Object(d["b"])(a)})).then((function(){i.close(),t.getList(),t.$modal.msgSuccess(t.$t("device.index.105953-47"))})).catch((function(){i.close()}))},shadowUnEnable:function(e,t){return 3!=e.status&&0==e.isShadow||!!t.isReadonly},searchChange:function(){this.searchShow=!this.searchShow},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.deviceId})),this.single=1!==e.length,this.multiple=!e.length}}},P=A,I=(i("621f"),i("2877")),N=Object(I["a"])(P,a,r,!1,null,"08be8e04",null);t["default"]=N.exports},c59e:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAYAAADimHc4AAAAAXNSR0IArs4c6QAABo9JREFUeF7tnU2IFEcUx/+vqmdmP+LHblxQNBIQcggYCMRbIIeElRX3ZshC/AAlixg3F70JQoQcPIQISgSDghEht4QsRFzw4CGwEMGbYEAwoFGiWXXNuh/d9V6o2t1xZtwdZ2bXqe6x6jAMPdX1qv6/etVV1d3zCAsmof7Be+3PctxekHxOsdIzipTNyglRm/3iPkIqKjAFTAFQkYg9lmdhVmymaSbuiNXk8Jl1kwC530oTVR7o3XW/U6/OdRErHeRdPgVEsTGP40cjF9ZOLApg674n3bqNVyyf2VBSpQJmSj29fHbV2PzxogcE8ZvXWUohOAB22IlWFtY0rwrBUjI+/dAORwQI9Q2NrQ9jfnM7hb0mXDrZfZf6B//uMLn2nuaaD9asAjqefEAff3nnzQI63wiSNF+BaUz8R9sO/LMWlCs033ywCImnafv+h+tZ6yjI0XwFlDEJ9R74962IlFvlhtRcBRJhDgCaq3mZNQfgk8GxjfkcvbAl4bFer43pmViEtg+ObeQAwAt05QB8NbaRTfAAHwSUDgB86F60GQB4lR8IAAIAzwp4Nh88IADwrIBn88EDAgDPCng2HzwgAPCsgGfzwQMCAM8KeDYfPCAA8KyAZ/PBAwKA2hUQok2i1XcA3gPoio4xJJQ8q72E9OXMjgfkcl0sPAxgQ4mMN5XhnRB5kD5pa6tRJgAQkzZ5dR7Ahws065YyPJBVCJkAwDo6BJKhKn0qsxDSD0BHfUxyugaHziSEdAOI6B2G+hlAZw0AbJbMQUgvAJKVrKNfAbxdo/jz2TIFIbUATKROE6ivTvEzByG1AETRFiE6D6KOVoaQOgBCegOJuWNFfx0gpAvA3EWXIN9SwudmIeheIZwGodFXZlN9TUgNgLnF1giATVZ4ghx7DoG2CamTrQghNQAQ6c0M2K2GYiLIECVu+8EORy0JIT0AiHpYqdHyXk6JEgzBJJdaFUJ6ANherqPdQnKsfNZTAUHrz4RwvMGZUeoWa6kCMHfRPSIKX1SFEKm9AjraChBSB8CKaiJ1jEC7KyEQ8z5ivupAtQiEVAKwM6IkT6cWWAlPKNAeJMm1VoGQSgBuGmrvAeTUORA+qhhqyiFofVAIh7M6HKUWgIMgUYfJ8Y8AffASCEeFsDeLEFINwAk6uytqt6TdAq0kTZAxO0lw3Q1HWmcSgncAQnifSW9SkoySkNsDep50QSL0k+GrrNVPC0B4QIYHSORWViF4BVAx7y/r0XOzocMEtd8uxoTNnzILofJfXcogLDyDqmdwkt9Vwp/Xc8ZS8noFwFpdB1FXSQN+U4k54Hoz6Q2icQVAAZhdjNUCocoMqmadVGLqvQlUc9mVGf0CiPTtsgoJRpUxA3O9v/KGjJv9CBta5D5B0ROWBkGuqYR3NKxonSemCwAwDpEbILJbz1sq2zK/OVflPsFtZfhT+4hKYxDkOhkamr8fUaeWDWVPG4BqjRhVyax3uCFq8d3R4v5/HRDGCThOibnYkIpLOCkbAASGhLcRy83SttYCAdAFjuTiAmuJWZDgXzRFXyOOHy1Bx4ZPzQQAEZzTxlTsks55wuJ7Qs89YeEF3S0yfIRERhtWbxlOTBuAaQDlz3kKRnVCR6s9hFtlY+6GjmmHPbdkVb1ZwKeiGXwvSswyaLikItIFoGQWVG+rqqyE/9Ax7ZmHIDn0IEn+qrf8V5W/ZQC48Xyx7QjBsDKm2rOlr0rfl5bbUgBemPUIjADnNScnIDT+UjU8ZGgpAFY/C4HzaoAhPcpghIRveNC1ZpN+AWg9DMLm4twe+EEn5puaa98CGb0CcK8cKXUCwLtCMhLF6lDWXzmqt094BVBvZVsxfwDgmWoAEAB4VsCz+eABAYBnBTybDx4QAHhWwLP54AEBgGcFPJsPHhAAeFbAs/ngAQGAZwU8mw8eEAB4VsCz+VkPCEF8vGFwQXxCGCtv+sOFsQqR9PwBCJH0/GnvLDsAIZinPwoumGcIZ+sPgAtnGwI6+wPgAjqHkOb+ALiQ5vaVuL6hsfXEqtF/pfLXggxbFsXm0snuuy6Mbe+u+53RysKaDLcnc1VPxqcfjlxYO1GMI7x135Nu3cYrMteSDFbYTKmnl8+uGrNVLwvkHCC8epql4r8AYH440qtzXeGasLww7JhvHseP7LBTWvIiocyF+gfvtT/LcXtB8jnFSs8oUvZEToja7Bf3EVJRgSlgCoCKROyxPAuzYjNNM3FHrCaHz6ybtP8+UqnY/xyrxXcDc8sGAAAAAElFTkSuQmCC"},cd0b:function(e,t,i){"use strict";i("68b6")},f1cb:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:e.$t("device.recycle-record.845969-0"),visible:e.open,width:"910px"},on:{"update:visible":function(t){e.open=t}}},[i("div",{staticClass:"recycle-recor-dialog"},[i("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[i("el-form-item",{attrs:{prop:"serialNumber"}},[i("el-input",{attrs:{placeholder:e.$t("device.device-edit.148398-7"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serialNumber,callback:function(t){e.$set(e.queryParams,"serialNumber",t)},expression:"queryParams.serialNumber"}})],1),i("el-form-item",{attrs:{prop:"operateDeptId"}},[i("treeselect",{staticStyle:{width:"205px"},attrs:{options:e.deptOptions,"show-count":!0,placeholder:e.$t("device.recycle-record.845969-1")},model:{value:e.queryParams.operateDeptId,callback:function(t){e.$set(e.queryParams,"operateDeptId",t)},expression:"queryParams.operateDeptId"}})],1),i("el-form-item",{attrs:{prop:"productId"}},[i("el-select",{attrs:{placeholder:e.$t("device.allot-record.155854-2"),filterable:!0,size:"small",clearable:""},model:{value:e.queryParams.productId,callback:function(t){e.$set(e.queryParams,"productId",t)},expression:"queryParams.productId"}},e._l(e.productList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),i("el-form-item",[i("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.recycle-record.845969-4")))]),i("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.recycle-record.845969-5")))])],1)],1),i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.dataList,size:"small",border:!1}},[i("el-table-column",{attrs:{label:e.$t("device.device-edit.148398-7"),align:"left",prop:"serialNumber","min-width":"160"}}),i("el-table-column",{attrs:{label:e.$t("device.device-edit.148398-1"),align:"left",prop:"deviceName","min-width":"150"}}),i("el-table-column",{attrs:{label:e.$t("device.recycle-record.845969-1"),align:"left",prop:"operateDeptName","min-width":"150"}}),i("el-table-column",{attrs:{label:e.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),i("el-table-column",{attrs:{label:e.$t("device.recycle-record.845969-6"),align:"left",prop:"targetDeptName","min-width":"220"}}),i("el-table-column",{attrs:{label:e.$t("device.recycle-record.845969-8"),align:"center",prop:"createTime",width:"140"}})],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)])},r=[],o=(i("d81d"),i("9b9c")),l=i("584f"),s=i("c0c7"),n=i("ca17"),c=i.n(n),d=(i("542c"),{name:"recycleRecord",dicts:[],components:{Treeselect:c.a},data:function(){return{loading:!0,total:0,open:!1,productList:[],dataList:[],daterangeTime:[],deptOptions:[],queryParams:{pageNum:1,pageSize:10,operateDeptId:null,productId:null,serialNumber:"",type:2}}},created:function(){this.getProductList(),this.getDeptTree()},methods:{getProductList:function(){var e=this;this.loading=!0;var t={pageSize:999,showSenior:!0};Object(o["g"])(t).then((function(t){e.productList=t.rows.map((function(e){return{value:e.productId,label:e.productName}})),e.loading=!1}))},getList:function(){var e=this;this.loading=!0,Object(l["r"])(this.queryParams).then((function(t){e.dataList=t.rows,e.total=t.total,e.loading=!1}))},getDeptTree:function(){var e=this;Object(s["d"])().then((function(t){e.deptOptions=t.data}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},closeDialog:function(){this.open=!1}}}),u=d,p=(i("5b09"),i("2877")),m=Object(p["a"])(u,a,r,!1,null,"58f83482",null);t["default"]=m.exports},f4c2:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("el-dialog",{attrs:{title:e.upload.title,visible:e.upload.importAllotDialog,width:"550px","append-to-body":""},on:{"update:visible":function(t){return e.$set(e.upload,"importAllotDialog",t)}}},[i("el-form",{ref:"allotForm",attrs:{"label-width":"100px",model:e.allotForm,rules:e.allotRules}},[i("el-form-item",{attrs:{label:"",prop:"productName"}},[i("template",{slot:"label"},[e._v(" "+e._s(e.$t("device.device-edit.148398-4"))+" ")]),i("el-input",{staticStyle:{width:"360px"},attrs:{readonly:"",placeholder:e.$t("device.device-edit.148398-5")},model:{value:e.allotForm.productName,callback:function(t){e.$set(e.allotForm,"productName",t)},expression:"allotForm.productName"}},[i("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v(e._s(e.$t("device.device-edit.148398-6")))])],1)],2),i("el-form-item",{attrs:{label:e.$t("device.allot-import-dialog.060657-2"),prop:"deptId"}},[i("treeselect",{staticStyle:{width:"360px"},attrs:{options:e.deptOptions,"show-count":!0,placeholder:e.$t("device.allot-import-dialog.060657-3")},model:{value:e.allotForm.deptId,callback:function(t){e.$set(e.allotForm,"deptId",t)},expression:"allotForm.deptId"}})],1),i("el-form-item",{attrs:{label:e.$t("uploadFile"),prop:"fileList"}},[i("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:e.upload.headers,action:e.upload.url+"?productId="+e.allotForm.productId+"&deptId="+e.allotForm.deptId,disabled:e.upload.isUploading,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,"auto-upload":!1,"on-change":e.handleChange,"on-remove":e.handleRemove,drag:""},model:{value:e.allotForm.fileList,callback:function(t){e.$set(e.allotForm,"fileList",t)},expression:"allotForm.fileList"}},[i("i",{staticClass:"el-icon-upload"}),i("div",{staticClass:"el-upload__text"},[e._v(" "+e._s(e.$t("dragFileTips"))+" "),i("em",[e._v(e._s(e.$t("clickFileTips")))])]),i("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[i("div",{staticStyle:{"line-height":"26px",width:"360px"}},[i("div",[e._v(e._s(e.$t("device.allot-import-dialog.060657-7")))]),i("div",[e._v(e._s(e.$t("device.allot-import-dialog.060657-8")))]),i("div",[e._v(e._s(e.$t("device.allot-import-dialog.060657-9")))])])])]),i("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:e.importAllotTemplate}},[i("i",{staticClass:"el-icon-download"}),e._v(" "+e._s(e.$t("device.allot-import-dialog.060657-10"))+" ")])],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:e.submitImportDevice}},[e._v(e._s(e.$t("device.allot-import-dialog.060657-12")))]),i("el-button",{on:{click:function(t){e.upload.importAllotDialog=!1}}},[e._v(e._s(e.$t("cancel")))])],1)],1),i("product-list",{ref:"productList",attrs:{productId:e.allotForm.productId},on:{productEvent:function(t){return e.getProductData(t)}}})],1)},r=[],o=i("5f87"),l=i("c0c7"),s=i("ca17"),n=i.n(s),c=i("e51f"),d=(i("542c"),{name:"allotImport",components:{Treeselect:n.a,productList:c["default"]},data:function(){return{type:1,allotForm:{productId:0,deptId:0,fileList:[],productName:""},productList:[],deptOptions:[],upload:{title:this.$t("device.allot-import-dialog.060657-13"),importAllotDialog:!1,isUploading:!1,headers:{Authorization:"Bearer "+Object(o["a"])()},url:"/prod-api/iot/device/importAssignmentData"},isSubDev:!1,allotRules:{productName:[{required:!0,message:this.$t("device.allot-import-dialog.060657-14"),trigger:"change"}],deptId:[{required:!0,message:this.$t("device.allot-import-dialog.060657-15"),trigger:"change"}],fileList:[{required:!0,message:this.$t("plzUploadFile"),trigger:"change"}]}}},created:function(){this.getDeptTree()},methods:{getDeptTree:function(){var e=this;Object(l["d"])().then((function(t){e.deptOptions=t.data}))},importAllotTemplate:function(){this.type=2,this.download("/iot/device/uploadTemplate?type="+this.type,{},"allot_device_".concat((new Date).getTime(),".xlsx"))},handleChange:function(e,t){this.allotForm.fileList=t,this.allotForm.fileList&&this.$refs.allotForm.clearValidate("fileList")},handleRemove:function(e,t){this.allotForm.fileList=t,this.$refs.allotForm.validateField("fileList")},handleFileUploadProgress:function(e,t,i){this.upload.isUploading=!0},handleFileSuccess:function(e,t,i){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0}),this.$parent.getList()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(e){this.allotForm.productId=e.productId,this.allotForm.productName=e.productName},submitImportDevice:function(){var e=this;this.$refs["allotForm"].validate((function(t){t&&(e.$refs.upload.submit(),e.upload.importAllotDialog=!1)}))}}}),u=d,p=i("2877"),m=Object(p["a"])(u,a,r,!1,null,null,null);t["default"]=m.exports},f5a7:function(e,t,i){"use strict";i.d(t,"b",(function(){return r})),i.d(t,"a",(function(){return o})),i.d(t,"c",(function(){return l}));var a=i("b775");function r(e){return Object(a["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function l(e,t,i){return Object(a["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:i})}}}]);