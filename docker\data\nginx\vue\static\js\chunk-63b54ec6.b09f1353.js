(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63b54ec6"],{"3b72":function(e,t,a){"use strict";a.d(t,"d",(function(){return l})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return o})),a.d(t,"b",(function(){return s}));var r=a("b775");function l(e){return Object(r["a"])({url:"/iot/alertLog/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/iot/alertLog/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/iot/alertLog",method:"post",data:e})}function o(e){return Object(r["a"])({url:"/iot/alertLog",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/iot/alertLog/"+e,method:"delete"})}},"7bce":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{prop:"alertName"}},[a("el-input",{attrs:{placeholder:e.$t("device.device-alert.309509-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.alertName,callback:function(t){e.$set(e.queryParams,"alertName",t)},expression:"queryParams.alertName"}})],1),a("el-form-item",{attrs:{prop:"alertLevel"}},[a("el-select",{attrs:{placeholder:e.$t("device.device-alert.309509-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.alertLevel,callback:function(t){e.$set(e.queryParams,"alertLevel",t)},expression:"queryParams.alertLevel"}},e._l(e.dict.type.iot_alert_level,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{prop:"status"}},[a("el-select",{attrs:{placeholder:e.$t("device.device-alert.309509-5"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.iot_process_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("device.device-alert.309509-6")))]),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("device.device-alert.309509-7")))])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.alertLogList,border:!1},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-0"),align:"left",prop:"alertName","min-width":"160"}}),a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-2"),align:"center",prop:"alertLevel","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_alert_level,value:t.row.alertLevel}})]}}])}),a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-8"),align:"left",prop:"createBy","min-width":"150"}}),a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-9"),align:"center",prop:"createTime",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-10"),align:"left",prop:"detail","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatDetail(t.row.detail))}})]}}])}),a("el-table-column",{attrs:{label:e.$t("device.device-alert.309509-11"),align:"center",prop:"status","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_process_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{fixed:"right",label:e.$t("device.device-alert.309509-12"),align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:alertLog:edit"],expression:"['iot:alertLog:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(e._s(e.$t("device.device-alert.309509-13")))])]}}])})],1),a("div",{staticStyle:{height:"60px"}},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"550px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:e.$t("device.device-alert.309509-14"),prop:"remark"}},[a("el-input",{staticStyle:{width:"350px"},attrs:{type:"textarea",placeholder:e.$t("device.device-alert.309509-15"),autosize:{minRows:4,maxRows:6}},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("device.device-alert.309509-16")))]),a("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("device.device-alert.309509-17")))])],1)],1)],1)},l=[],i=(a("d81d"),a("b64b"),a("3b72")),n={name:"DeviceAlertLog",dicts:["iot_alert_level","iot_process_status"],props:{device:{type:Object,default:null}},watch:{device:function(e,t){this.deviceInfo=e,this.deviceInfo&&0!=this.deviceInfo.deviceId&&(this.queryParams.serialNumber=this.deviceInfo.serialNumber,this.getList())}},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,alertLogList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,alertName:null,alertLevel:null,status:null,productId:null,productName:null,serialNumber:null,deviceName:null},form:{},rules:{remark:[{required:!0,message:this.$t("device.device-alert.309509-18"),trigger:"blur"}]}}},created:function(){this.queryParams.serialNumber=this.device.serialNumber,this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(i["d"])(this.queryParams).then((function(t){e.alertLogList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={alertLogId:null,alertName:null,alertLevel:null,status:null,productId:null,productName:null,deviceId:null,deviceName:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.alertLogId})),this.single=1!==e.length,this.multiple=!e.length},handleUpdate:function(e){var t=this;this.reset();var a=e.alertLogId||this.ids;Object(i["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title=t.$t("device.device-alert.309509-19")}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.alertLogId?Object(i["e"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("device.device-alert.309509-20")),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess(e.$t("device.device-alert.309509-21")),e.open=!1,e.getList()})))}))},formatDetail:function(e){if(null!=e&&""!=e){var t=JSON.parse(e),a='id：<span style="color:#F56C6C">'+t.id+"</span><br />";return a=a+'value：<span style="color:#F56C6C">'+t.value+"</span><br />",a=a+'remark：<span style="color:#F56C6C">'+t.remark+"</span>",a}}}},o=n,s=a("2877"),u=Object(s["a"])(o,r,l,!1,null,null,null);t["default"]=u.exports}}]);