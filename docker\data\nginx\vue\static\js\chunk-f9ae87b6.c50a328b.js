(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f9ae87b6","chunk-63f48a62"],{"172de":function(e,t,r){"use strict";r("7cb1")},"30fc":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:e.$t("sip.product-list.998536-0"),visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{prop:"productName"}},[r("el-input",{attrs:{placeholder:e.$t("product.index.091251-1"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.productName,callback:function(t){e.$set(e.queryParams,"productName",t)},expression:"queryParams.productName"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"singleTable",attrs:{data:e.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":e.rowClick}},[r("el-table-column",{attrs:{label:e.$t("select"),width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("input",{attrs:{type:"radio",name:"product"},domProps:{checked:e.row.isSelect}})]}}])}),r("el-table-column",{attrs:{label:e.$t("product.index.091251-0"),align:"left",prop:"productName","min-width":"160"}}),r("el-table-column",{attrs:{label:e.$t("product.index.091251-2"),align:"left",prop:"categoryName","min-width":"140"}}),r("el-table-column",{attrs:{label:e.$t("sip.product-list.998536-1"),align:"left",prop:"tenantName","min-width":"120"}}),r("el-table-column",{attrs:{label:e.$t("sip.product-list.998536-2"),align:"center",prop:"networkMethod","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.iot_network_method,value:t.row.networkMethod}})]}}])}),r("el-table-column",{attrs:{label:e.$t("sip.mediaServer.998535-4"),align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.confirmSelectProduct}},[e._v(e._s(e.$t("confirm")))]),r("el-button",{attrs:{type:"info"},on:{click:e.closeDialog}},[e._v(e._s(e.$t("close")))])],1)],1)},i=[],o=(r("a9e3"),r("9b9c")),a={name:"SipProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:3,networkMethod:null}}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0,Object(o["g"])(this.queryParams).then((function(t){for(var r=0;r<t.rows.length;r++)t.rows[r].isSelect=!1;e.productList=t.rows,e.total=t.total,0!=e.productId&&e.setRadioSelected(e.productId),e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(e){null!=e&&(this.setRadioSelected(e.productId),this.product=e)},setRadioSelected:function(e){for(var t=0;t<this.productList.length;t++)this.productList[t].productId==e?this.productList[t].isSelect=!0:this.productList[t].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},c=a,s=r("2877"),u=Object(s["a"])(c,n,i,!1,null,null,null);t["default"]=u.exports},"36eb":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"iot-sip"},[r("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"15px","border-radius":"8px",width:"100%"}},[r("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-18px",padding:"3px 0 0 0"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(e){e.preventDefault()}}},[r("el-form-item",{attrs:{prop:"deviceSipId"}},[r("el-input",{attrs:{placeholder:e.$t("sip.index.998533-1"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deviceSipId,callback:function(t){e.$set(e.queryParams,"deviceSipId",t)},expression:"queryParams.deviceSipId"}})],1),r("el-form-item",{attrs:{prop:"channelSipId"}},[r("el-input",{attrs:{placeholder:e.$t("sip.index.998533-3"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.channelSipId,callback:function(t){e.$set(e.queryParams,"channelSipId",t)},expression:"queryParams.channelSipId"}})],1),r("el-form-item",{attrs:{prop:"status"}},[r("el-select",{attrs:{placeholder:e.$t("sip.index.998533-5"),clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sip_gen_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("div",{staticStyle:{float:"right"}},[r("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))]),r("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v(e._s(e.$t("reset")))])],1)],1)],1),r("el-card",{staticStyle:{"border-radius":"8px"}},[r("el-row",{staticStyle:{"margin-bottom":"16px"},attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:video:add"],expression:"['iot:video:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small",disabled:e.isGeneralUser},on:{click:e.handleAdd}},[e._v(e._s(e.$t("sip.index.998533-6")))])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:video:remove"],expression:"['iot:video:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:e.multiple||e.isGeneralUser},on:{click:e.handleDelete}},[e._v(" "+e._s(e.$t("sip.index.998533-7"))+" ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.sipidList,border:!1},on:{"selection-change":e.handleSelectionChange,"cell-dblclick":e.celldblclick}},[r("el-table-column",{attrs:{type:"selection",selectable:e.selectable,width:"55",align:"center"}}),r("el-table-column",{attrs:{label:e.$t("device.device-edit.148398-7"),align:"center",prop:"deviceSipId","min-width":"195"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{underline:!1,type:"primary"},on:{click:function(r){return e.handleViewDevice(t.row.deviceSipId)}}},[e._v(e._s(t.row.deviceSipId))])]}}])}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-2"),align:"center",prop:"channelSipId","min-width":"195"}}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-4"),align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sip_gen_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-8"),align:"left",prop:"productName","min-width":"180"}}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-9"),align:"center",prop:"deviceType","min-width":"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.video_type,value:t.row.deviceType}})]}}])}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-15"),align:"center",prop:"channelType","min-width":"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.channel_type,value:t.row.channelType}})]}}])}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-10"),align:"center",prop:"cityCode","min-width":"185"}}),r("el-table-column",{attrs:{label:e.$t("sip.index.998533-11"),align:"center",prop:"registerTime",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.registerTime,"{y}-{m}-{d} {h}:{m}:{s}")))])]}}])}),r("el-table-column",{attrs:{fixed:"right",label:e.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleBinding(t.row)}}},[e._v(e._s(e.$t("sip.index.998533-12")))])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"createForm",attrs:{model:e.createForm,"label-width":"100px"}},[r("el-form-item",{attrs:{label:e.$t("sip.index.998533-13")}},[r("el-cascader",{staticStyle:{width:"400px"},attrs:{options:e.cityOptions,"change-on-select":""},on:{change:e.changeProvince},model:{value:e.createForm.city,callback:function(t){e.$set(e.createForm,"city",t)},expression:"createForm.city"}})],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-9"),prop:"deviceType"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("sip.index.998533-14")},model:{value:e.createForm.deviceType,callback:function(t){e.$set(e.createForm,"deviceType",t)},expression:"createForm.deviceType"}},e._l(e.dict.type.video_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-15"),prop:"channelType"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("sip.index.998533-16")},model:{value:e.createForm.channelType,callback:function(t){e.$set(e.createForm,"channelType",t)},expression:"createForm.channelType"}},e._l(e.dict.type.channel_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-8"),prop:"productName"}},[r("el-input",{staticStyle:{width:"400px"},attrs:{readonly:"",placeholder:e.$t("sip.index.998533-17")},model:{value:e.createForm.productName,callback:function(t){e.$set(e.createForm,"productName",t)},expression:"createForm.productName"}},[r("el-button",{attrs:{slot:"append"},on:{click:function(t){return e.selectProduct()}},slot:"append"},[e._v(e._s(e.$t("sip.index.998533-18")))])],1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-20"),prop:"createNum"}},[r("el-input-number",{staticStyle:{width:"400px"},attrs:{"controls-position":"right",max:10,placeholder:e.$t("sip.index.998533-19"),type:"number"},model:{value:e.createForm.createNum,callback:function(t){e.$set(e.createForm,"createNum",t)},expression:"createForm.createNum"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.$t("sip.index.998533-21")))]),r("el-button",{on:{click:e.cancel}},[e._v(e._s(e.$t("cancel")))])],1)],1),r("el-dialog",{attrs:{title:e.title,visible:e.bindingOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.bindingOpen=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px"}},[r("el-form-item",{attrs:{label:e.$t("sip.index.998533-22"),prop:"deviceId"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("sip.index.998533-23"),filterable:""},on:{change:function(t){return e.handleUpdateDeviceItem(e.scope.row,t)}},model:{value:e.form.reDeviceId,callback:function(t){e.$set(e.form,"reDeviceId",t)},expression:"form.reDeviceId"}},e._l(e.deviceList,(function(e,t){return r("el-option",{key:t,attrs:{label:e.deviceName,value:e.deviceId}})})),1)],1),r("el-form-item",{attrs:{label:e.$t("sip.index.998533-24"),prop:"sceneId"}},[r("el-select",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("sip.index.998533-25"),filterable:""},model:{value:e.form.reSceneModelId,callback:function(t){e.$set(e.form,"reSceneModelId",t)},expression:"form.reSceneModelId"}},e._l(e.sceneList,(function(e,t){return r("el-option",{key:t,attrs:{label:e.sceneModelName,value:e.sceneModelId}})})),1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:relation:add"],expression:"['iot:relation:add']"}],attrs:{type:"primary"},on:{click:e.submitFormBinding}},[e._v(e._s(e.$t("confirm")))]),r("el-button",{on:{click:e.cancelBinding}},[e._v(e._s(e.$t("cancel")))])],1)],1),r("product-list",{ref:"productList",on:{productEvent:function(t){return e.getProductData(t)}}})],1)},i=[],o=(r("d81d"),r("14d9"),r("584f")),a=r("ef6c"),c=r("e2de"),s=r("30fc"),u=r("7a7d"),l={name:"Sip",dicts:["video_type","channel_type","sip_gen_status"],components:{productList:s["default"]},props:{product:{type:Object,default:null}},watch:{product:function(e,t){this.productInfo=e,this.productInfo&&0!=this.productInfo.productId&&(this.queryParams.productId=this.productInfo.productId,this.deviceParams.productId=this.productInfo.productId,this.getList())}},data:function(){return{isGeneralUser:!0,loading:!0,showSearch:!0,searchShow:!1,ids:[],multiple:!0,total:0,sipidList:[],title:"",open:!1,bindingOpen:!1,deviceList:[],sceneList:[],queryParams:{pageNum:1,pageSize:10,deviceSipId:null,deviceChannelId:null,status:null},createForm:{city:"",deviceType:"",channelType:"",createNum:1,remark:"",area:""},form:{},productInfo:{},cityOptions:a["regionData"],city:"",rules:{protocol:[{required:!0,message:this.$t("sip.index.998533-26"),trigger:"blur"}],ip:[{required:!0,message:this.$t("sip.index.998533-27"),trigger:"blur"}],domainAlias:[{required:!0,message:this.$t("sip.index.998533-28"),trigger:"blur"}],secret:[{required:!0,message:this.$t("sip.index.998533-29"),trigger:"blur"}],portHttp:[{required:!0,message:this.$t("sip.index.998533-30"),trigger:"blur"}],portHttps:[{required:!0,message:this.$t("sip.index.998533-31"),trigger:"blur"}],portRtmp:[{required:!0,message:this.$t("sip.index.998533-32"),trigger:"blur"}],portRtsp:[{required:!0,message:this.$t("sip.index.998533-33"),trigger:"blur"}],rtpPortRange:[{required:!0,message:this.$t("sip.index.998533-34"),trigger:"blur"}],delFlag:[{required:!0,message:this.$t("sip.index.998533-35"),trigger:"blur"}],createBy:[{required:!0,message:this.$t("sip.index.998533-36"),trigger:"blur"}],createTime:[{required:!0,message:this.$t("sip.index.998533-37"),trigger:"blur"}]}}},created:function(){-1===this.$store.state.user.roles.indexOf("general")&&(this.isGeneralUser=!1),this.getList(),this.getDeviceList(),this.getSceneListDatas()},methods:{handleViewDevice:function(e){this.$router.push({path:"/iot/device",query:{t:Date.now(),sn:e}})},selectProduct:function(){this.open=!1,this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(e){this.open=!0,this.createForm.productId=e.productId,this.createForm.productName=e.productName,this.createForm.tenantId=e.tenantId,this.createForm.tenantName=e.tenantName},changeProvince:function(e){if(e&&null!=e[0]&&null!=e[1]&&null!=e[2]){var t=a["CodeToText"][e[0]]+"/"+a["CodeToText"][e[1]]+"/"+a["CodeToText"][e[2]];this.createForm.citycode=t}},getDeviceBySerialNumber:function(e){var t=this;this.openDevice=!0,Object(o["g"])(e).then((function(e){t.device=e.data}))},getList:function(){var e=this;Object(c["g"])(this.queryParams).then((function(t){e.sipidList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.createForm={id:null,deviceSipId:null,channelSipId:null,status:0,registertime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,createNum:1},this.resetForm("createForm")},handleQuery:function(){this.loading=!0,this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title=this.$t("sip.index.998533-38")},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;console.log(e),Object(c["e"])(r).then((function(e){t.createForm=e.data,t.open=!0,t.title=t.$t("sip.index.998533-39")}))},handleBinding:function(e){this.form=e,this.bindingOpen=!0,this.title=this.$t("sip.index.998533-40")},getDeviceList:function(){var e=this,t={showChild:!0,pageNum:1,pageSize:9999};Object(o["p"])(t).then((function(t){200===t.code?e.deviceList=t.rows:e.$message.error(t.msg)}))},getSceneListDatas:function(){var e=this,t={pageNum:1,pageSize:9999};Object(u["m"])(t).then((function(t){200===t.code?e.sceneList=t.rows:e.$message.error(t.msg)}))},cancelBinding:function(){this.bindingOpen=!1,this.reset()},submitFormBinding:function(){var e=this,t={channelId:this.form.channelSipId,reDeviceId:this.form.reDeviceId,reSceneModelId:this.form.reSceneModelId};Object(c["b"])(t).then((function(t){200===t.code?(e.bindingOpen=!1,e.$message.success(e.$t("sip.index.998533-41"))):e.$message.error(t.msg)}))},submitForm:function(){var e=this;this.createForm.createNum<1?this.$modal.alertError(this.$t("sip.index.998533-42")):this.createForm.productId&&0!=this.createForm.productId?(this.createForm.deviceSipId=this.createForm.city[2]+"0000"+this.createForm.deviceType+"0",this.createForm.channelSipId=this.createForm.city[2]+"0000"+this.createForm.channelType+"0",""!==this.createForm.deviceType&&""!==this.createForm.channelType&&3===this.createForm.city.length?(console.log(this.createForm),Object(c["a"])(this.createForm.createNum,this.createForm).then((function(t){e.$modal.msgSuccess(e.$t("addSuccess")),e.open=!1,e.getList()}))):this.$message({type:"error",message:this.$t("sip.index.998533-44")})):this.$modal.alertError(this.$t("sip.index.998533-43"))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm(this.$t("sip.index.998533-45")+r+this.$t("notify.channel.index.333541-18")).then((function(){return Object(c["d"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess(t.$t("delSuccess"))})).catch((function(){}))},selectable:function(e){return 2!=e.status&&!this.isGeneralUser},celldblclick:function(e,t,r,n){var i=this;this.$copyText(e[t.property]).then((function(e){i.onCopy()}),(function(e){this.onError()}))},onCopy:function(){this.$notify({title:this.$t("success"),message:this.$t("sip.index.998533-46"),type:"success",offset:50,duration:2e3})},onError:function(){this.$notify({title:this.$t("fail"),message:this.$t("sip.index.998533-47"),type:"error",offset:50,duration:2e3})},searchChange:function(){this.searchShow=!this.searchShow}}},d=l,p=(r("172de"),r("2877")),m=Object(p["a"])(d,n,i,!1,null,"041113d6",null);t["default"]=m.exports},"584f":function(e,t,r){"use strict";r.d(t,"n",(function(){return i})),r.d(t,"t",(function(){return o})),r.d(t,"o",(function(){return a})),r.d(t,"p",(function(){return c})),r.d(t,"m",(function(){return s})),r.d(t,"f",(function(){return u})),r.d(t,"c",(function(){return l})),r.d(t,"g",(function(){return d})),r.d(t,"i",(function(){return p})),r.d(t,"d",(function(){return m})),r.d(t,"u",(function(){return h})),r.d(t,"q",(function(){return f})),r.d(t,"r",(function(){return g})),r.d(t,"h",(function(){return b})),r.d(t,"a",(function(){return y})),r.d(t,"v",(function(){return v})),r.d(t,"b",(function(){return x})),r.d(t,"e",(function(){return $})),r.d(t,"k",(function(){return w})),r.d(t,"l",(function(){return O})),r.d(t,"j",(function(){return S})),r.d(t,"s",(function(){return k}));var n=r("b775");function i(e){return Object(n["a"])({url:"/iot/device/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/iot/device/shortList",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/iot/device/all",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/iot/device/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(n["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function p(){return Object(n["a"])({url:"/iot/device/statistic",method:"get"})}function m(e,t){return Object(n["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function h(e,t){return Object(n["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function f(e){return Object(n["a"])({url:"/iot/record/list",method:"get",params:e})}function g(e){return Object(n["a"])({url:"/iot/record/list",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/iot/device",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/iot/device",method:"put",data:e})}function x(e){return Object(n["a"])({url:"/iot/device/"+e,method:"delete"})}function $(e){return Object(n["a"])({url:"/iot/device/generator",method:"get",params:e})}function w(e){return Object(n["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function O(e){return Object(n["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function S(e){return Object(n["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function k(e){return Object(n["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},"7a7d":function(e,t,r){"use strict";r.d(t,"m",(function(){return i})),r.d(t,"b",(function(){return o})),r.d(t,"q",(function(){return a})),r.d(t,"e",(function(){return c})),r.d(t,"k",(function(){return s})),r.d(t,"i",(function(){return u})),r.d(t,"l",(function(){return l})),r.d(t,"a",(function(){return d})),r.d(t,"d",(function(){return p})),r.d(t,"p",(function(){return m})),r.d(t,"j",(function(){return h})),r.d(t,"h",(function(){return f})),r.d(t,"g",(function(){return g})),r.d(t,"o",(function(){return b})),r.d(t,"c",(function(){return y})),r.d(t,"r",(function(){return v})),r.d(t,"f",(function(){return x})),r.d(t,"n",(function(){return $}));var n=r("b775");function i(e){return Object(n["a"])({url:"/scene/model/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/scene/model",method:"post",data:e})}function a(e){return Object(n["a"])({url:"/scene/model",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/scene/model/"+e,method:"delete"})}function s(e){return Object(n["a"])({url:"/scene/model/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/scene/modelData/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/scene/modelDevice/list",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/scene/modelDevice",method:"post",data:e})}function p(e){return Object(n["a"])({url:"/scene/modelDevice/"+e,method:"delete"})}function m(e){return Object(n["a"])({url:"/scene/modelDevice",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/scene/modelData/listByType",method:"get",params:e})}function f(e){return Object(n["a"])({url:"/scene/modelDevice/editEnable",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/scene/modelData/editEnable",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/scene/modelTag/list",method:"get",params:e})}function y(e){return Object(n["a"])({url:"/scene/modelTag",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/scene/modelTag",method:"put",data:e})}function x(e){return Object(n["a"])({url:"/scene/modelTag/"+e,method:"delete"})}function $(e){return Object(n["a"])({url:"/scene/modelTag/"+e,method:"get"})}},"7cb1":function(e,t,r){},"9b9c":function(e,t,r){"use strict";r.d(t,"g",(function(){return i})),r.d(t,"h",(function(){return o})),r.d(t,"f",(function(){return a})),r.d(t,"a",(function(){return c})),r.d(t,"i",(function(){return s})),r.d(t,"e",(function(){return u})),r.d(t,"b",(function(){return l})),r.d(t,"d",(function(){return d})),r.d(t,"c",(function(){return p}));var n=r("b775");function i(e){return Object(n["a"])({url:"/iot/product/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/iot/product/shortList",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/iot/product/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/iot/product",method:"post",data:e})}function s(e){return Object(n["a"])({url:"/iot/product",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/iot/product/deviceCount/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/iot/product/status",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/iot/product/"+e,method:"delete"})}function p(e){return Object(n["a"])({url:"/iot/product/copy?productId="+e,method:"post"})}},e2de:function(e,t,r){"use strict";r.d(t,"g",(function(){return i})),r.d(t,"e",(function(){return o})),r.d(t,"a",(function(){return a})),r.d(t,"d",(function(){return c})),r.d(t,"k",(function(){return s})),r.d(t,"h",(function(){return u})),r.d(t,"c",(function(){return l})),r.d(t,"i",(function(){return d})),r.d(t,"b",(function(){return p})),r.d(t,"f",(function(){return m})),r.d(t,"j",(function(){return h})),r.d(t,"l",(function(){return f}));var n=r("b775");function i(e){return Object(n["a"])({url:"/sip/channel/list",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"get"})}function a(e,t){return Object(n["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function c(e){return Object(n["a"])({url:"/sip/channel/"+e,method:"delete"})}function s(e,t){return Object(n["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function u(e,t,r){return Object(n["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:r})}function l(e,t,r){return Object(n["a"])({url:"/sip/player/closeStream/"+e+"/"+t+"/"+r,method:"get"})}function d(e,t,r,i){return Object(n["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+r,method:"get",params:i})}function p(e){return Object(n["a"])({url:"/iot/relation/addOrUp",method:"post",data:e})}function m(e,t){return Object(n["a"])({url:"/sip/talk/getPushUrl/"+e+"/"+t,method:"get"})}function h(e,t){return Object(n["a"])({url:"/sip/talk/broadcast/"+e+"/"+t,method:"get"})}function f(e,t){return Object(n["a"])({url:"/sip/talk/broadcast/stop/"+e+"/"+t,method:"get"})}}}]);