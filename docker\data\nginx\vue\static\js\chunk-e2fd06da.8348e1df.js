(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e2fd06da","chunk-71776995"],{"01d1":function(e,t,a){"use strict";a("7f1d")},"3abb":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-card",{staticStyle:{margin:"6px","padding-bottom":"100px"}},[a("el-tabs",{staticStyle:{padding:"10px","min-height":"400px"},attrs:{"tab-position":"left"},on:{"tab-click":e.handleClick},model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[a("el-tab-pane",{attrs:{name:"play"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备直播")]),a("div",{staticClass:"components-container"},[a("div",[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("span",{staticStyle:{overflow:"auto"}},[e._v("开启直播录像：")]),a("el-switch",{attrs:{"active-color":"#13ce66","inactive-color":"#ff4949"},on:{change:e.startPlayRecordk},model:{value:e.playrecord,callback:function(t){e.playrecord=t},expression:"playrecord"}})],1)],1)]),a("player",{ref:"player",attrs:{playerinfo:e.playinfo}})],1),a("el-tab-pane",{attrs:{name:"playback"}},[a("span",{attrs:{slot:"label"},slot:"label"},[e._v("设备录像")]),a("div",{staticClass:"components-container"},[a("div",[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("span",{staticStyle:{overflow:"auto"}},[e._v("选择录像日期：")]),a("el-date-picker",{attrs:{type:"date",size:"small","value-format":"yyyy-MM-dd",clearable:"",placeholder:"选择日期"},on:{change:e.loadDevRecord},model:{value:e.queryDate,callback:function(t){e.queryDate=t},expression:"queryDate"}}),a("span",{staticStyle:{"margin-left":"10px",overflow:"auto"}},[e._v("转录的时间段：")]),a("el-button-group",[a("el-time-picker",{attrs:{size:"mini","is-range":"",align:"left","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},on:{change:e.timePickerChange},model:{value:e.timeRange,callback:function(t){e.timeRange=t},expression:"timeRange"}})],1),a("el-button-group",[a("el-button",{staticClass:"iconfont icon-xiazai1",attrs:{size:"mini",title:"下载选定录像"},on:{click:function(t){return e.downloadRecord()}}})],1)],1)],1)]),a("player",{ref:"playbacker",attrs:{playerinfo:e.playbackinfo}})],1),a("el-tab-pane",{attrs:{disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("div",{staticStyle:{"margin-top":"200px"}})])]),a("el-tab-pane",{attrs:{name:"device05",disabled:""}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(t){return e.goBack()}}},[e._v("返回设备")])],1)])],1)],1)},n=[],s=(a("b0c0"),a("d3b7"),a("97d6")),r=a("e2de"),o=a("6827"),l={name:"DevicePlayer",components:{player:s["default"]},data:function(){return{activeName:"play",deviceId:"",channelId:"",streamId:"",ssrc:"",playurl:"",queryDate:"",vodData:{},hisData:[],playinfo:{},playrecord:!1,playrecording:!1,playbackinfo:{},timeRange:null,startTime:null,endTime:null}},beforeDestroy:function(){},activated:function(){var e=this.$route.query.activeName;null!=e&&""!==e&&(this.activeName=e)},created:function(){if(this.$route.params){this.deviceId=this.$route.params.deviceId,this.channelId=this.$route.params.channelId;var e=this.$route.params.activeName;null!=e&&""!==e&&(this.activeName=e,"play"===this.activeName&&this.sendDevicePush()),this.playinfo={playtype:"play",deviceId:this.deviceId,channelId:this.channelId},this.playbackinfo={playtype:"playback",deviceId:this.deviceId,channelId:this.channelId}}},destroyed:function(){this.closeStream(),this.$refs.player.destroy(),this.$refs.playbacker.destroy()},methods:{startPlayRecordk:function(){var e=this;this.$refs.player.destroy(),this.playrecord?(this.closeStream(),Object(o["f"])(this.deviceId,this.channelId).then((function(t){console.log("开始录像："+e.deviceId+" : "+e.channelId),e.playrecording=!0;var a=t.data;e.streamId=a.streamId,e.playurl=a.playurl,e.$refs.player.isInit||e.$refs.player.init(),e.$refs.player.play(a.playurl)}))):(this.playrecording=!1,this.closeStream(),this.sendDevicePush())},handleClick:function(e,t){this.closeStream(),"play"===e.name?(this.$refs.playbacker.destroy(),this.sendDevicePush()):(this.$refs.player.destroy(),this.$refs.playbacker.registercallback("playbackSeek",this.seekPlay))},sendDevicePush:function(){var e=this;console.log("通知设备推流1："+this.deviceId+" : "+this.channelId),Object(r["k"])(this.deviceId,this.channelId).then((function(t){console.log("开始播放："+e.deviceId+" : "+e.channelId);var a=t.data;e.streamId=a.streamId,e.playurl=a.playurl,e.$refs.player.isInit||e.$refs.player.init(),e.$refs.player.play(a.playurl)}))},initUrl:function(e){e?(this.streamId=e.ssrc,this.ssrc=e.ssrc,this.playurl=e.playurl):(this.streamId="",this.ssrc="",this.playurl="")},loadDevRecord:function(){var e=this;if(this.deviceId&&this.channelId){var t=this.queryDate?new Date(this.queryDate).getTime():new Date((new Date).toLocaleDateString()).getTime(),a=t/1e3,i=Math.floor((t+864e5-1)/1e3),n={start:a,end:i};this.vodData={start:a,end:i,base:a},this.setTime(this.queryDate+" 00:00:00",this.queryDate+" 23:59:59"),Object(o["a"])(this.deviceId,this.channelId,n).then((function(t){if(e.hisData=t.data.recordItems,t.data.recordItems){var n=e.hisData.length;n>0?(e.hisData[0].start<a?(e.hisData[0].start=a,e.vodData.start=a):e.vodData.start=e.hisData[0].start,e.hisData[0].end<i&&(e.vodData.end=e.hisData[0].end),e.playback()):e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}else e.$message({type:"warning",message:"请确认设备是否支持录像，或者设备SD卡是否正确插入！"})}))}},playback:function(){var e=this;if(this.ssrc)Object(r["b"])(this.deviceId,this.channelId,this.ssrc).then((function(t){var a={start:e.vodData.start,end:e.vodData.end};Object(r["f"])(e.deviceId,e.channelId,a).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))}));else{var t={start:this.vodData.start,end:this.vodData.end};Object(r["f"])(this.deviceId,this.channelId,t).then((function(t){e.playing=!0,e.initUrl(t.data)})).finally((function(){e.triggerPlay(e.hisData)}))}},triggerPlay:function(e){this.$refs.playbacker.playback(this.playurl,e)},seekPlay:function(e){var t=this.vodData.base+3600*e.hour+60*e.min+e.second,a=t-this.vodData.start;if(this.ssrc){var i={seek:a},n=this;Object(r["i"])(this.deviceId,this.channelId,this.streamId,i).then((function(e){n.$refs.playbacker.setPlaybackStartTime(t)}))}},closeStream:function(){var e=this;"play"===this.activeName&&!0===this.playrecording||this.streamId&&Object(r["b"])(this.deviceId,this.streamId).then((function(t){e.streamId="",e.ssrc="",e.playurl=""}))},timePickerChange:function(e){this.setTime(e[0],e[1])},setTime:function(e,t){this.startTime=e,this.endTime=t,this.timeRange=[e,t]},downloadRecord:function(){var e=this,t=new Date(this.startTime).getTime()/1e3,a=new Date(this.endTime).getTime()/1e3,i={startTime:t,endTime:a,speed:"4"};Object(o["e"])(this.deviceId,this.channelId,i).then((function(t){console.log("开始下载录像："+e.deviceId+" : "+e.channelId)}))},goBack:function(){var e={path:"/iot/device",query:{t:Date.now(),pageNum:this.$route.query.pageNum}};this.$tab.closeOpenPage(e),this.reset()}}},c=l,d=(a("47f8"),a("2877")),u=Object(d["a"])(c,i,n,!1,null,"a450cd26",null);t["default"]=u.exports},"47f8":function(e,t,a){"use strict";a("fad8")},6827:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return o})),a.d(t,"f",(function(){return l})),a.d(t,"e",(function(){return c}));var i=a("b775");function n(e,t,a){return Object(i["a"])({url:"/sip/record/devquery/"+e+"/"+t,method:"get",params:a})}function s(e){return Object(i["a"])({url:"/sip/record/serverRecord/list",method:"get",params:e})}function r(e){return Object(i["a"])({url:"/sip/record/serverRecord/date/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/sip/record/serverRecord/file/list",method:"get",params:e})}function l(e,t){return Object(i["a"])({url:"/sip/record/play/"+e+"/"+t,method:"get"})}function c(e,t,a){return Object(i["a"])({url:"/sip/record/download/"+e+"/"+t,method:"get",params:a})}},"7f1d":function(e,t,a){},"97d6":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"root"},[a("div",{staticClass:"container-shell"},[a("div",{ref:"container",attrs:{id:"container"}})])])},n=[],s=a("c7eb"),r=a("1da1"),o=(a("a9e3"),a("ac1f"),a("00b4"),a("f5a7")),l={},c={name:"player",props:{playerinfo:{type:Object,default:null}},mounted:function(){console.log(this._uid)},watch:{playerinfo:function(e,t){console.log("playerinfo 发生变化"),this.playinfo=e,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype)}},jessibuca:null,data:function(){return{isPlaybackPause:!1,useWebGPU:!1,isInit:!1,playinfo:{},playtype:"play",operateBtns:{}}},beforeDestroy:function(){},created:function(){this.playinfo=this.playerinfo,this.playinfo&&""!==this.playinfo.playtype&&(this.playtype=this.playinfo.playtype),this.init()},methods:{init:function(){var e=this,t="gpu"in navigator;t?(console.log("支持webGPU"),this.useWebGPU=!0):(console.log("暂不支持webGPU，降级到webgl渲染"),this.useWebGPU=!1);var a=this.isMobile()||this.isPad();a&&window.VConsole&&new window.VConsole,this.$nextTick((function(){e.initplayer()}))},initplayer:function(){this.isPlaybackPause=!1,this.initconf(),l[this._uid]=new window.JessibucaPro({container:this.$refs.container,decoder:"/js/jessibuca-pro/decoder-pro.js",videoBuffer:Number(.2),isResize:!1,useWCS:!1,useMSE:!1,useSIMD:!0,wcsUseVideoRender:!1,loadingText:"加载中",debug:!1,showBandwidth:!0,showPlaybackOperate:!0,operateBtns:this.operateBtns,forceNoOffscreen:!0,isNotMute:!1,showPerformance:!1,playbackForwardMaxRateDecodeIFrame:4,useWebGPU:this.useWebGPU});var e=l[this._uid];this.initcallback(e),this.isInit=!0},initconf:function(){"play"===this.playtype?this.operateBtns={fullscreen:!0,zoom:!0,ptz:!0,play:!0}:this.operateBtns={fullscreen:!0,zoom:!0,play:!0,ptz:!1}},initcallback:function(e){var t=this;e.on("error",(function(e){console.log("error"),console.log(e),t.destroy()})),e.on("pause",(function(e){console.log("pause success!"),console.log(e)})),e.on("stats",(function(e){console.log("stats is",e)})),e.on("timeout",(function(){console.log("timeout")})),e.on("playbackPreRateChange",(function(t){e.forward(t)}));var a=0,i=0;e.on("timeUpdate",(function(e){i=parseInt(e/6e4),a!==i&&a++})),e.on(JessibucaPro.EVENTS.ptz,(function(e){console.log("ptz arrow",e),t.handlePtz(e)}))},registercallback:function(e,t){l[this._uid]&&l[this._uid].on(e,t)},isMobile:function(){return/iphone|ipad|android.*mobile|windows.*phone|blackberry.*mobile/i.test(window.navigator.userAgent.toLowerCase())},isPad:function(){return/ipad|android(?!.*mobile)|tablet|kindle|silk/i.test(window.navigator.userAgent.toLowerCase())},play:function(e){l[this._uid]&&l[this._uid].play(e)},pause:function(){l[this._uid]&&l[this._uid].pause()},replay:function(e){var t=this;l[this._uid]?l[this._uid].destroy().then((function(){t.initplayer(),t.play(e)})):(this.initplayer(),this.play(e))},handlePtz:function(e){var t=0,a=0;"left"===e?t=2:"right"===e?t=1:"up"===e?a=1:"down"===e&&(a=2);var i={leftRight:t,upDown:a,moveSpeed:125};this.playinfo&&""!==this.playinfo.playtype&&Object(o["c"])(this.playinfo.deviceId,this.playinfo.channelId,i).then(function(){var e=Object(r["a"])(Object(s["a"])().mark((function e(t){return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},playback:function(e,t){l[this._uid]&&(l[this._uid].playback(e,{playList:t,fps:25,showControl:!0,showRateBtn:!0,isUseFpsRender:!0,isCacheBeforeDecodeForFpsRender:!1,supportWheel:!0,rateConfig:[{label:"正常",value:1},{label:"2倍",value:2},{label:"4倍",value:4},{label:"8倍",value:8}]}),this.isPlaybackPause=!1)},playbackPause:function(){l[this._uid]&&(l[this._uid].playbackPause(),this.isPlaybackPause=!0)},replayback:function(e,t){var a=this;l[this._uid]?l[this._uid].destroy().then((function(){a.initplayer(),a.playback(e,t)})):(this.initplayer(),this.playback(e,t))},destroy:function(){var e=this;l[this._uid]&&l[this._uid].destroy().then((function(){e.initplayer()}))},close:function(){l[this._uid]&&l[this._uid].close()}}},d=c,u=(a("01d1"),a("2877")),p=Object(u["a"])(d,i,n,!1,null,"6be8f187",null);t["default"]=p.exports},e2de:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return s})),a.d(t,"a",(function(){return r})),a.d(t,"c",(function(){return o})),a.d(t,"k",(function(){return l})),a.d(t,"f",(function(){return c})),a.d(t,"b",(function(){return d})),a.d(t,"g",(function(){return u})),a.d(t,"h",(function(){return p})),a.d(t,"i",(function(){return h})),a.d(t,"j",(function(){return f}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/channel/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"get"})}function r(e,t){return Object(i["a"])({url:"/sip/channel/"+e,method:"post",data:t})}function o(e){return Object(i["a"])({url:"/sip/channel/"+e,method:"delete"})}function l(e,t){return Object(i["a"])({url:"/sip/player/play/"+e+"/"+t,method:"get"})}function c(e,t,a){return Object(i["a"])({url:"/sip/player/playback/"+e+"/"+t,method:"get",params:a})}function d(e,t){return Object(i["a"])({url:"/sip/player/closeStream/"+e+"/"+t,method:"get"})}function u(e,t,a){return Object(i["a"])({url:"/sip/player/playbackPause/"+e+"/"+t+"/"+a,method:"get"})}function p(e,t,a){return Object(i["a"])({url:"/sip/player/playbackReplay/"+e+"/"+t+"/"+a,method:"get"})}function h(e,t,a,n){return Object(i["a"])({url:"/sip/player/playbackSeek/"+e+"/"+t+"/"+a,method:"get",params:n})}function f(e,t,a,n){return Object(i["a"])({url:"/sip/player/playbackSpeed/"+e+"/"+t+"/"+a,method:"get",params:n})}},f5a7:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return o}));var i=a("b775");function n(e){return Object(i["a"])({url:"/sip/device/listchannel/"+e,method:"get"})}function s(e){return Object(i["a"])({url:"/sip/device/sipid/"+e,method:"delete"})}function r(e,t,a){return Object(i["a"])({url:"/sip/ptz/direction/"+e+"/"+t,method:"post",data:a})}function o(e,t,a){return Object(i["a"])({url:"/sip/ptz/scale/"+e+"/"+t,method:"post",data:a})}},fad8:function(e,t,a){}}]);