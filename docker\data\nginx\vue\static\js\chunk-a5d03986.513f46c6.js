(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a5d03986"],{"0433":function(t,e,o){},"3eac":function(t,e,o){"use strict";o.r(e);var s=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"monitor-job"},[o("el-card",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],staticClass:"search-card"},[o("el-form",{ref:"queryForm",staticClass:"search-form",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{prop:"jobName"}},[o("el-input",{attrs:{placeholder:t.$t("system.job.356378-1"),clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.jobName,callback:function(e){t.$set(t.queryParams,"jobName",e)},expression:"queryParams.jobName"}})],1),o("el-form-item",{attrs:{prop:"jobGroup"}},[o("el-select",{attrs:{placeholder:t.$t("system.job.356378-3"),clearable:""},model:{value:t.queryParams.jobGroup,callback:function(e){t.$set(t.queryParams,"jobGroup",e)},expression:"queryParams.jobGroup"}},t._l(t.dict.type.sys_job_group,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),o("el-form-item",{attrs:{prop:"status"}},[o("el-select",{attrs:{placeholder:t.$t("system.job.356378-5"),clearable:""},model:{value:t.queryParams.status,callback:function(e){t.$set(t.queryParams,"status",e)},expression:"queryParams.status"}},t._l(t.dict.type.sys_job_status,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),o("div",{staticStyle:{float:"right"}},[o("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("search")))]),o("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("reset")))])],1)],1)],1),o("el-card",[o("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:add"],expression:"['monitor:job:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"small"},on:{click:t.handleAdd}},[t._v(t._s(t.$t("add")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:edit"],expression:"['monitor:job:edit']"}],attrs:{plain:"",icon:"el-icon-edit",size:"small",disabled:t.single},on:{click:t.handleUpdate}},[t._v(t._s(t.$t("update")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{plain:"",icon:"el-icon-delete",size:"small",disabled:t.multiple},on:{click:t.handleDelete}},[t._v(t._s(t.$t("del")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{plain:"",icon:"el-icon-download",size:"small"},on:{click:t.handleExport}},[t._v(t._s(t.$t("export")))])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{plain:"",icon:"el-icon-s-operation",size:"small"},on:{click:t.handleJobLog}},[t._v(t._s(t.$t("system.job.356378-6")))])],1),o("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.jobList,border:!1},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-7"),width:"100",align:"center",prop:"jobId"}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-0"),align:"left",prop:"jobName","show-overflow-tooltip":!0,"min-width":"200"}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-2"),align:"center",prop:"jobGroup","min-width":"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.sys_job_group,value:e.row.jobGroup}})]}}])}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-8"),align:"left",prop:"invokeTarget","show-overflow-tooltip":!0,"min-width":"200"}}),o("el-table-column",{attrs:{label:t.$t("system.job.356378-9"),align:"left",prop:"cronExpression","show-overflow-tooltip":!0,"min-width":"180"}}),o("el-table-column",{attrs:{label:t.$t("status"),align:"center",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-switch",{attrs:{"active-value":0,"inactive-value":1},on:{change:function(o){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(o){t.$set(e.row,"status",o)},expression:"scope.row.status"}})]}}])}),o("el-table-column",{attrs:{fixed:"right",label:t.$t("opation"),align:"center","class-name":"small-padding fixed-width",width:"170"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:edit"],expression:"['monitor:job:edit']"}],attrs:{size:"small",type:"text",icon:"el-icon-edit"},on:{click:function(o){return t.handleUpdate(e.row)}}},[t._v(t._s(t.$t("update")))]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{size:"small",type:"text",icon:"el-icon-delete"},on:{click:function(o){return t.handleDelete(e.row)}}},[t._v(t._s(t.$t("del")))]),o("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:changeStatus","monitor:job:query"],expression:"['monitor:job:changeStatus', 'monitor:job:query']"}],attrs:{size:"small"},on:{command:function(o){return t.handleCommand(o,e.row)}}},[o("el-button",{attrs:{size:"small",type:"text",icon:"el-icon-d-arrow-right"}},[t._v(t._s(t.$t("user.index.098976-14")))]),o("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[o("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:run"],expression:"['monitor:job:run']"}],attrs:{command:"handleRun",icon:"el-icon-caret-right"}},[t._v(t._s(t.$t("system.job.356378-10")))]),o("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{command:"handleView",icon:"el-icon-view"}},[t._v(t._s(t.$t("system.job.356378-11")))]),o("el-dropdown-item",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{command:"handleJobLog",icon:"el-icon-s-operation"}},[t._v(t._s(t.$t("system.job.356378-12")))])],1)],1)]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticStyle:{"margin-bottom":"20px"},attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1),o("el-dialog",{attrs:{title:t.title,visible:t.open,width:"810px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[o("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"125px"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-0"),prop:"jobName"}},[o("el-input",{staticStyle:{width:"250px"},attrs:{placeholder:t.$t("system.job.356378-1")},model:{value:t.form.jobName,callback:function(e){t.$set(t.form,"jobName",e)},expression:"form.jobName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-13"),prop:"jobGroup"}},[o("el-select",{staticStyle:{width:"220px"},attrs:{placeholder:t.$t("system.job.356378-14")},model:{value:t.form.jobGroup,callback:function(e){t.$set(t.form,"jobGroup",e)},expression:"form.jobGroup"}},t._l(t.dict.type.sys_job_group,(function(t){return o("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{prop:"invokeTarget"}},[o("span",{attrs:{slot:"label"},slot:"label"},[t._v(" "+t._s(t.$t("system.job.356378-15"))+" "),o("el-tooltip",{attrs:{placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[t._v(" "+t._s(t.$t("system.job.356378-16"))+" "),o("br"),t._v(" "+t._s(t.$t("system.job.356378-17"))+" "),o("br"),t._v(" "+t._s(t.$t("system.job.356378-18"))+" ")]),o("i",{staticClass:"el-icon-question"})])],1),o("el-input",{staticStyle:{width:"604px"},attrs:{placeholder:t.$t("system.job.356378-8")},model:{value:t.form.invokeTarget,callback:function(e){t.$set(t.form,"invokeTarget",e)},expression:"form.invokeTarget"}})],1)],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-19"),prop:"cronExpression"}},[o("el-input",{staticStyle:{width:"604px"},attrs:{placeholder:t.$t("system.job.356378-20")},model:{value:t.form.cronExpression,callback:function(e){t.$set(t.form,"cronExpression",e)},expression:"form.cronExpression"}},[o("el-button",{staticStyle:{"border-top-left-radius":"0","border-bottom-left-radius":"0"},attrs:{slot:"append",type:"primary"},on:{click:t.handleShowCron},slot:"append"},[t._v(" "+t._s(t.$t("system.job.356378-21"))+" "),o("i",{staticClass:"el-icon-time el-icon--right"})])],1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-22"),prop:"misfirePolicy"}},[o("el-radio-group",{attrs:{size:"small"},model:{value:t.form.misfirePolicy,callback:function(e){t.$set(t.form,"misfirePolicy",e)},expression:"form.misfirePolicy"}},[o("el-radio-button",{attrs:{label:"1"}},[t._v(t._s(t.$t("system.job.356378-23")))]),o("el-radio-button",{attrs:{label:"2"}},[t._v(t._s(t.$t("system.job.356378-10")))]),o("el-radio-button",{attrs:{label:"3"}},[t._v(t._s(t.$t("system.job.356378-24")))])],1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("status")}},[o("el-radio-group",{model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},t._l(t.dict.type.sys_job_status,(function(e){return o("el-radio",{key:e.value,attrs:{label:Number(e.value)}},[t._v(t._s(e.label))])})),1)],1)],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-25"),prop:"concurrent"}},[o("el-radio-group",{attrs:{size:"small"},model:{value:t.form.concurrent,callback:function(e){t.$set(t.form,"concurrent",e)},expression:"form.concurrent"}},[o("el-radio-button",{attrs:{label:"0"}},[t._v(t._s(t.$t("system.job.356378-26")))]),o("el-radio-button",{attrs:{label:"1"}},[t._v(t._s(t.$t("system.job.356378-27")))])],1)],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v(t._s(t.$t("confirm")))]),o("el-button",{on:{click:t.cancel}},[t._v(t._s(t.$t("cancel")))])],1)],1),o("el-dialog",{staticClass:"scrollbar",attrs:{title:t.$t("system.job.356378-28"),visible:t.openCron,"append-to-body":"","destroy-on-close":""},on:{"update:visible":function(e){t.openCron=e}}},[o("crontab",{attrs:{expression:t.expression},on:{hide:function(e){t.openCron=!1},fill:t.crontabFill}})],1),o("el-dialog",{attrs:{title:t.$t("system.job.356378-11"),visible:t.openView,width:"710px","append-to-body":""},on:{"update:visible":function(e){t.openView=e}}},[o("el-form",{ref:"form",attrs:{model:t.form,"label-width":"135px",size:"mini"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-29")}},[t._v(t._s(t.form.jobId))])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-31")}},[t._v(t._s(t.jobGroupFormat(t.form)))])],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-30")}},[t._v(t._s(t.form.jobName))])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-32")}},[t._v(t._s(t.form.createTime))])],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-33")}},[t._v(t._s(t.form.cronExpression))])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-34")}},[t._v(t._s(t.parseTime(t.form.nextValidTime)))])],1)],1),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-35")}},[t._v(t._s(t.form.invokeTarget))])],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-36")}},[0==t.form.status?o("div",[t._v(t._s(t.$t("system.job.356378-37")))]):1==t.form.status?o("div",[t._v(t._s(t.$t("system.job.356378-38")))]):t._e()])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-25")}},[0==t.form.concurrent?o("div",[t._v(t._s(t.$t("system.job.356378-26")))]):1==t.form.concurrent?o("div",[t._v(t._s(t.$t("system.job.356378-27")))]):t._e()])],1)],1),o("el-row",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:t.$t("system.job.356378-39")}},[0==t.form.misfirePolicy?o("div",[t._v(t._s(t.$t("system.job.356378-40")))]):1==t.form.misfirePolicy?o("div",[t._v(t._s(t.$t("system.job.356378-23")))]):2==t.form.misfirePolicy?o("div",[t._v(t._s(t.$t("system.job.356378-10")))]):3==t.form.misfirePolicy?o("div",[t._v(t._s(t.$t("system.job.356378-24")))]):t._e()])],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(e){t.openView=!1}}},[t._v(t._s(t.$t("close")))])],1)],1)],1)},r=[],a=o("5530"),n=(o("d81d"),o("14d9"),o("a159")),i=o("bdd0"),l={components:{Crontab:i["a"]},name:"Job",dicts:["sys_job_group","sys_job_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,jobList:[],title:"",open:!1,openView:!1,openCron:!1,expression:"",queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0},form:{},rules:{jobName:[{required:!0,message:this.$t("system.job.356378-41"),trigger:"blur"}],invokeTarget:[{required:!0,message:this.$t("system.job.356378-42"),trigger:"blur"}],cronExpression:[{required:!0,message:this.$t("system.job.356378-43"),trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(n["e"])(this.queryParams).then((function(e){t.jobList=e.rows,t.total=e.total,t.loading=!1}))},jobGroupFormat:function(t,e){return this.selectDictLabel(this.dict.type.sys_job_group,t.jobGroup)},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={jobId:void 0,jobName:void 0,jobGroup:void 0,invokeTarget:void 0,cronExpression:void 0,misfirePolicy:1,concurrent:1,status:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.jobId})),this.single=1!=t.length,this.multiple=!t.length},handleCommand:function(t,e){switch(t){case"handleRun":this.handleRun(e);break;case"handleView":this.handleView(e);break;case"handleJobLog":this.handleJobLog(e);break;default:break}},handleStatusChange:function(t){var e=this,o=0===t.status?this.$t("simulate.index.111543-54"):this.$t("simulate.index.111543-55");this.$modal.confirm(this.$t("system.job.356378-44")+o+'""'+t.jobName+this.$t("system.job.356378-45")).then((function(){return Object(n["b"])(t.jobId,t.status)})).then((function(){e.$modal.msgSuccess(o+e.$t("success"))})).catch((function(){t.status=0===t.status?1:0}))},handleRun:function(t){var e=this;this.$modal.confirm(this.$t("system.job.356378-46")+t.jobName+this.$t("system.job.356378-45")).then((function(){return Object(n["f"])(t.jobId,t.jobGroup)})).then((function(){e.$modal.msgSuccess(e.$t("system.job.356378-47"))})).catch((function(){}))},handleView:function(t){var e=this;Object(n["d"])(t.jobId).then((function(t){e.form=t.data,e.openView=!0}))},handleShowCron:function(){this.expression=this.form.cronExpression,this.openCron=!0},crontabFill:function(t){this.form.cronExpression=t},handleJobLog:function(t){var e=t.jobId||0;this.$router.push("/monitor/job-log/index/"+e)},handleAdd:function(){this.reset(),this.open=!0,this.title=this.$t("system.job.356378-48")},handleUpdate:function(t){var e=this;this.reset();var o=t.jobId||this.ids;Object(n["d"])(o).then((function(t){e.form=t.data,e.open=!0,e.title=e.$t("system.job.356378-49")}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(void 0!=t.form.jobId?Object(n["g"])(t.form).then((function(e){t.$modal.msgSuccess(t.$t("updateSuccess")),t.open=!1,t.getList()})):Object(n["a"])(t.form).then((function(e){t.$modal.msgSuccess(t.$t("addSuccess")),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,o=t.jobId||this.ids;this.$modal.confirm(this.$t("system.job.356378-50",[o])).then((function(){return Object(n["c"])(o)})).then((function(){e.getList(),e.$modal.msgSuccess(e.$t("delSuccess"))})).catch((function(){}))},handleExport:function(){this.download("monitor/job/export",Object(a["a"])({},this.queryParams),"job_".concat((new Date).getTime(),".xlsx"))}}},m=l,c=(o("41cc"),o("2877")),u=Object(c["a"])(m,s,r,!1,null,"0aeb54a6",null);e["default"]=u.exports},"41cc":function(t,e,o){"use strict";o("0433")},a159:function(t,e,o){"use strict";o.d(e,"e",(function(){return r})),o.d(e,"d",(function(){return a})),o.d(e,"a",(function(){return n})),o.d(e,"g",(function(){return i})),o.d(e,"c",(function(){return l})),o.d(e,"b",(function(){return m})),o.d(e,"f",(function(){return c}));var s=o("b775");function r(t){return Object(s["a"])({url:"/monitor/job/list",method:"get",params:t})}function a(t){return Object(s["a"])({url:"/monitor/job/"+t,method:"get"})}function n(t){return Object(s["a"])({url:"/monitor/job",method:"post",data:t})}function i(t){return Object(s["a"])({url:"/monitor/job",method:"put",data:t})}function l(t){return Object(s["a"])({url:"/monitor/job/"+t,method:"delete"})}function m(t,e){var o={jobId:t,status:e};return Object(s["a"])({url:"/monitor/job/changeStatus",method:"put",data:o})}function c(t,e){var o={jobId:t,jobGroup:e};return Object(s["a"])({url:"/monitor/job/run",method:"put",data:o})}}}]);