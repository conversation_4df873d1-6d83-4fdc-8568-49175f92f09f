(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-37ca88dc"],{16263:function(e,t,r){"use strict";r("2ab9")},"2ab9":function(e,t,r){},7803:function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"register"},[r("el-row",[r("el-col",{attrs:{xs:24}},[r("div",{staticStyle:{color:"#fff","background-color":"#0f73ee",width:"100%",height:"200px","text-align":"center",padding:"15px","font-family":"'微软雅黑'"}},[r("div",{staticStyle:{"font-size":"42px","padding-top":"40px",width:"300px",margin:"0 auto"}},[r("img",{staticStyle:{width:"100px",height:"100px",float:"left"},attrs:{src:e.logo,alt:"logo"}}),r("div",{staticStyle:{float:"left","margin-top":"13px",width:"200px","text-align":"left"}},[r("div",[e._v("FastBee")]),r("div",{staticStyle:{"letter-spacing":"1.5px","font-size":"20px","font-weight":"600","margin-top":"-8px","margin-left":"3px"}},[e._v(" 开源物联网平台")])])])]),r("el-form",{ref:"registerForm",staticClass:"register-form",staticStyle:{"z-index":"1000"},attrs:{model:e.registerForm,rules:e.registerRules}},[r("el-form-item",{attrs:{prop:"username"}},[r("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.registerForm.username,callback:function(t){e.$set(e.registerForm,"username",t)},expression:"registerForm.username"}},[r("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),r("el-form-item",{attrs:{prop:"phonenumber"}},[r("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"手机号码"},model:{value:e.registerForm.phonenumber,callback:function(t){e.$set(e.registerForm,"phonenumber",t)},expression:"registerForm.phonenumber"}},[r("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"phone"},slot:"prefix"})],1)],1),r("el-form-item",{attrs:{prop:"password"}},[r("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleRegister(t)}},model:{value:e.registerForm.password,callback:function(t){e.$set(e.registerForm,"password",t)},expression:"registerForm.password"}},[r("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),r("el-form-item",{attrs:{prop:"confirmPassword"}},[r("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"确认密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleRegister(t)}},model:{value:e.registerForm.confirmPassword,callback:function(t){e.$set(e.registerForm,"confirmPassword",t)},expression:"registerForm.confirmPassword"}},[r("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),e.captchaOnOff?r("el-form-item",{attrs:{prop:"code"}},[r("el-input",{staticStyle:{width:"63%"},attrs:{"auto-complete":"off",placeholder:"验证码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleRegister(t)}},model:{value:e.registerForm.code,callback:function(t){e.$set(e.registerForm,"code",t)},expression:"registerForm.code"}},[r("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"validCode"},slot:"prefix"})],1),r("div",{staticClass:"register-code"},[r("img",{attrs:{src:e.codeUrl},on:{click:e.getCode}})])],1):e._e(),r("el-form-item",{staticStyle:{width:"100%"}},[e.bindAccount?r("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleRegister(t)}}},[e.loading?r("span",[e._v("绑 定 中...")]):r("span",[e._v("注 册 绑 定")])]):r("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleRegister(t)}}},[e.loading?r("span",[e._v("注 册 中...")]):r("span",[e._v("注 册")])])],1),r("el-form-item",[r("el-link",{staticStyle:{float:"left"},attrs:{href:"https://fastbee.cn/",underline:!1,target:"_blank"}},[e._v("返回官网")]),r("el-link",{staticStyle:{float:"left","margin-left":"20px"},attrs:{href:"https://fastbee.cn/doc",underline:!1,target:"_blank"}},[e._v("查看文档")]),r("router-link",{staticStyle:{float:"left","margin-left":"20px"},attrs:{to:{path:"/login",query:this.$route.query}}},[e._v("账号登录")])],1)],1)],1)],1),e._m(0)],1)},i=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"el-register-footer"},[r("span",[e._v(" Copyright © 2023 "),r("a",{attrs:{target:"_blank",href:"http://fastbee.cn"}},[e._v("FastBee")]),e._v(" All Rights Reserved. ")])])}],o=(r("d9e2"),r("14d9"),r("e05f"),r("4309")),s=r.n(o),a=r("7ded"),c=r("bb86"),l={name:"Register",data:function(){var e=this,t=function(t,r,n){e.registerForm.password!==r?n(new Error("两次输入的密码不一致")):n()};return{logo:s.a,codeUrl:"",registerForm:{username:"",phonenumber:"",password:"",confirmPassword:"",code:"",uuid:"",bindId:"",sourceType:1},registerRules:{username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],phonenumber:[{required:!0,trigger:"blur",message:"请输入您的手机号码"},{min:11,max:11,message:"手机号码长度为11",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:t,trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},loading:!1,captchaOnOff:!0,bindAccount:!1}},created:function(){this.checkBind(),this.getCode()},methods:{checkBind:function(){var e=this,t=this.$route.query,r=t.bindId;void 0===r||null===r?this.bindAccount=!1:(this.bindAccount=!0,Object(a["d"])(r).then((function(t){e.bindAccount=void 0===t.bindAccount||t.bindAccount,e.bindAccount?e.registerForm.bindId=r:(e.registerForm.bindId="",e.$router.push({query:{}}))})))},getCode:function(){var e=this;Object(a["f"])().then((function(t){e.captchaOnOff=void 0===t.captchaOnOff||t.captchaOnOff,e.captchaOnOff&&(e.codeUrl="data:image/gif;base64,"+t.img,e.registerForm.uuid=t.uuid)}))},qqLogin:function(){window.location.href="http://localhost:8080/auth/render/qq"},authLogin:function(){var e=this;this.$alert("第三方登录正在集成中...","提示消息",{confirmButtonText:"确定",callback:function(t){e.$message({type:"info",message:"action: ".concat(t)})}})},handleRegister:function(){var e=this;this.$refs.registerForm.validate((function(t){t&&(e.loading=!0,e.bindAccount?Object(a["c"])(e.registerForm).then((function(t){e.innerRegister(t)})).catch((function(){e.loading=!1,e.captchaOnOff&&e.getCode()})):Object(c["d"])(e.registerForm).then((function(t){e.innerRegister(t)})).catch((function(){e.loading=!1,e.captchaOnOff&&e.getCode()})))}))},innerRegister:function(e){var t=this,r=this.registerForm.username;this.$alert("<font color='red'>恭喜你，您的账号 "+r+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then((function(){t.$router.push("/login")})).catch((function(){}))}}},u=l,d=(r("16263"),r("2877")),f=Object(d["a"])(u,n,i,!1,null,null,null);t["default"]=f.exports},bb86:function(e,t,r){"use strict";r.d(t,"d",(function(){return i})),r.d(t,"c",(function(){return o})),r.d(t,"b",(function(){return s})),r.d(t,"a",(function(){return a})),r.d(t,"e",(function(){return c}));var n=r("b775");r("bc3a"),r("5c96"),r("21a6"),r("5f87"),r("c38a");function i(e){return Object(n["a"])({url:"/iot/tool/register",headers:{isToken:!1},method:"post",data:e})}function o(e){return Object(n["a"])({url:"/iot/tool/userList",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/iot/tool/getTopics",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/iot/tool/decode",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/iot/tool/simulate",method:"get",params:e})}}}]);