(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0a3715"],{"01ca":function(t,n,e){"use strict";e.d(n,"f",(function(){return r})),e.d(n,"d",(function(){return u})),e.d(n,"g",(function(){return c})),e.d(n,"a",(function(){return d})),e.d(n,"e",(function(){return i})),e.d(n,"i",(function(){return a})),e.d(n,"c",(function(){return m})),e.d(n,"b",(function(){return f})),e.d(n,"h",(function(){return l}));var o=e("b775");function r(t){return Object(o["a"])({url:"/iot/model/list",method:"get",params:t})}function u(t){return Object(o["a"])({url:"/iot/model/"+t,method:"get"})}function c(t){return Object(o["a"])({url:"/iot/model/permList/"+t,method:"get"})}function d(t){return Object(o["a"])({url:"/iot/model",method:"post",data:t})}function i(t){return Object(o["a"])({url:"/iot/model/import",method:"post",data:t})}function a(t){return Object(o["a"])({url:"/iot/model",method:"put",data:t})}function m(t){return Object(o["a"])({url:"/iot/model/"+t,method:"delete"})}function f(t){return Object(o["a"])({url:"/iot/model/cache/"+t,method:"get"})}function l(t){return Object(o["a"])({url:"/iot/model/synchron",method:"post",data:t})}}}]);