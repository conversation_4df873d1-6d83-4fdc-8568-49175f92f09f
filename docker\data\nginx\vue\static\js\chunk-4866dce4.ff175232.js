(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4866dce4"],{"25ca":function(e,r,t){"use strict";t.d(r,"a",(function(){return ew})),t.d(r,"c",(function(){return cw})),t.d(r,"b",(function(){return Cw}));
/*! xlsx.js (C) 2013-present SheetJS -- http://sheetjs.com */
var a={version:"0.18.5"},n=1200,s=1252,i=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],o={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},c=function(e){-1!=i.indexOf(e)&&(s=o[0]=e)};function l(){c(1252)}var f=function(e){n=e,c(e)};function h(){f(1200),l()}function u(e){for(var r=[],t=0,a=e.length;t<a;++t)r[t]=e.charCodeAt(t);return r}function d(e){for(var r=[],t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t)+(e.charCodeAt(2*t+1)<<8));return r.join("")}function p(e){for(var r=[],t=0;t<e.length>>1;++t)r[t]=String.fromCharCode(e.charCodeAt(2*t+1)+(e.charCodeAt(2*t)<<8));return r.join("")}var m,b=function(e){var r=e.charCodeAt(0),t=e.charCodeAt(1);return 255==r&&254==t?d(e.slice(2)):254==r&&255==t?p(e.slice(2)):65279==r?e.slice(1):e},v=function(e){return String.fromCharCode(e)},g=function(e){return String.fromCharCode(e)};var w=null,k=!0,T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function E(e){for(var r="",t=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)t=e.charCodeAt(l++),s=t>>2,a=e.charCodeAt(l++),i=(3&t)<<4|a>>4,n=e.charCodeAt(l++),o=(15&a)<<2|n>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),r+=T.charAt(s)+T.charAt(i)+T.charAt(o)+T.charAt(c);return r}function S(e){var r="",t=0,a=0,n=0,s=0,i=0,o=0,c=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var l=0;l<e.length;)s=T.indexOf(e.charAt(l++)),i=T.indexOf(e.charAt(l++)),t=s<<2|i>>4,r+=String.fromCharCode(t),o=T.indexOf(e.charAt(l++)),a=(15&i)<<4|o>>2,64!==o&&(r+=String.fromCharCode(a)),c=T.indexOf(e.charAt(l++)),n=(3&o)<<6|c,64!==c&&(r+=String.fromCharCode(n));return r}var y=function(){return"undefined"!==typeof Buffer&&"undefined"!==typeof process&&"undefined"!==typeof process.versions&&!!process.versions.node}(),_=function(){if("undefined"!==typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(r){e=!0}return e?function(e,r){return r?new Buffer(e,r):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function A(e){return y?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function x(e){return y?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var C=function(e){return y?_(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function R(e){if("undefined"===typeof ArrayBuffer)return C(e);for(var r=new ArrayBuffer(e.length),t=new Uint8Array(r),a=0;a!=e.length;++a)t[a]=255&e.charCodeAt(a);return r}function O(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var r=[],t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function I(e){if("undefined"===typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}function N(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return N(new Uint8Array(e));for(var r=new Array(e.length),t=0;t<e.length;++t)r[t]=e[t];return r}var D=y?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:_(e)})))}:function(e){if("undefined"!==typeof Uint8Array){var r=0,t=0;for(r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t),n=0;for(r=0,t=0;r<e.length;t+=n,++r)if(n=e[r].length,e[r]instanceof Uint8Array)a.set(e[r],t);else{if("string"==typeof e[r])throw"wtf";a.set(new Uint8Array(e[r]),t)}return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};function F(e){for(var r=[],t=0,a=e.length+250,n=A(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[t++]=i;else if(i<2048)n[t++]=192|i>>6&31,n[t++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var o=1023&e.charCodeAt(++s);n[t++]=240|i>>8&7,n[t++]=128|i>>2&63,n[t++]=128|o>>6&15|(3&i)<<4,n[t++]=128|63&o}else n[t++]=224|i>>12&15,n[t++]=128|i>>6&63,n[t++]=128|63&i;t>a&&(r.push(n.slice(0,t)),t=0,n=A(65535),a=65530)}return r.push(n.slice(0,t)),D(r)}var P=/\u0000/g,L=/[\u0001-\u0006]/g;function M(e){var r="",t=e.length-1;while(t>=0)r+=e.charAt(t--);return r}function U(e,r){var t=""+e;return t.length>=r?t:kr("0",r-t.length)+t}function B(e,r){var t=""+e;return t.length>=r?t:kr(" ",r-t.length)+t}function W(e,r){var t=""+e;return t.length>=r?t:t+kr(" ",r-t.length)}function H(e,r){var t=""+Math.round(e);return t.length>=r?t:kr("0",r-t.length)+t}function V(e,r){var t=""+e;return t.length>=r?t:kr("0",r-t.length)+t}var z=Math.pow(2,32);function G(e,r){if(e>z||e<-z)return H(e,r);var t=Math.round(e);return V(t,r)}function j(e,r){return r=r||0,e.length>=7+r&&103===(32|e.charCodeAt(r))&&101===(32|e.charCodeAt(r+1))&&110===(32|e.charCodeAt(r+2))&&101===(32|e.charCodeAt(r+3))&&114===(32|e.charCodeAt(r+4))&&97===(32|e.charCodeAt(r+5))&&108===(32|e.charCodeAt(r+6))}var X=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],Y=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function K(e){return e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e}var J={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},q={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},Z={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function Q(e,r,t){var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,h=Math.floor(n);while(l<r){if(h=Math.floor(n),o=h*i+s,f=h*l+c,n-h<5e-8)break;n=1/(n-h),s=i,i=o,c=l,l=f}if(f>r&&(l>r?(f=c,o=s):(f=l,o=i)),!t)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function ee(e,r,t){if(e>2958465||e<0)return null;var a=0|e,n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),r&&r.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=t?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=t?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),a<60&&(s=(s+6)%7),t&&(s=he(c,i))}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}var re=new Date(1899,11,31,0,0,0),te=re.getTime(),ae=new Date(1900,2,1,0,0,0);function ne(e,r){var t=e.getTime();return r?t-=1262304e5:e>=ae&&(t+=864e5),(t-(te+6e4*(e.getTimezoneOffset()-re.getTimezoneOffset())))/864e5}function se(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function ie(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}function oe(e){var r=e<0?12:11,t=se(e.toFixed(12));return t.length<=r?t:(t=e.toPrecision(10),t.length<=r?t:e.toExponential(5))}function ce(e){var r=se(e.toFixed(11));return r.length>(e<0?12:11)||"0"===r||"-0"===r?e.toPrecision(6):r}function le(e){var r,t=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return r=t>=-4&&t<=-1?e.toPrecision(10+t):Math.abs(t)<=9?oe(e):10===t?e.toFixed(10).substr(0,12):ce(e),se(ie(r.toUpperCase()))}function fe(e,r){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):le(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Ve(14,ne(e,r&&r.date1904),r)}throw new Error("unsupported value in General format: "+e)}function he(e,r){r[0]-=581;var t=e.getDay();return e<60&&(t=(t+6)%7),t}function ue(e,r,t,a){var n,s="",i=0,o=0,c=t.y,l=0;switch(e){case 98:c=t.y+543;case 121:switch(r.length){case 1:case 2:n=c%100,l=2;break;default:n=c%1e4,l=4;break}break;case 109:switch(r.length){case 1:case 2:n=t.m,l=r.length;break;case 3:return Y[t.m-1][1];case 5:return Y[t.m-1][0];default:return Y[t.m-1][2]}break;case 100:switch(r.length){case 1:case 2:n=t.d,l=r.length;break;case 3:return X[t.q][0];default:return X[t.q][1]}break;case 104:switch(r.length){case 1:case 2:n=1+(t.H+11)%12,l=r.length;break;default:throw"bad hour format: "+r}break;case 72:switch(r.length){case 1:case 2:n=t.H,l=r.length;break;default:throw"bad hour format: "+r}break;case 77:switch(r.length){case 1:case 2:n=t.M,l=r.length;break;default:throw"bad minute format: "+r}break;case 115:if("s"!=r&&"ss"!=r&&".0"!=r&&".00"!=r&&".000"!=r)throw"bad second format: "+r;return 0!==t.u||"s"!=r&&"ss"!=r?(o=a>=2?3===a?1e3:100:1===a?10:1,i=Math.round(o*(t.S+t.u)),i>=60*o&&(i=0),"s"===r?0===i?"0":""+i/o:(s=U(i,2+a),"ss"===r?s.substr(0,2):"."+s.substr(2,r.length-1))):U(t.S,r.length);case 90:switch(r){case"[h]":case"[hh]":n=24*t.D+t.H;break;case"[m]":case"[mm]":n=60*(24*t.D+t.H)+t.M;break;case"[s]":case"[ss]":n=60*(60*(24*t.D+t.H)+t.M)+Math.round(t.S+t.u);break;default:throw"bad abstime format: "+r}l=3===r.length?1:2;break;case 101:n=c,l=1;break}var f=l>0?U(n,l):"";return f}function de(e){var r=3;if(e.length<=r)return e;for(var t=e.length%r,a=e.substr(0,t);t!=e.length;t+=r)a+=(a.length>0?",":"")+e.substr(t,r);return a}var pe=/%/g;function me(e,r,t){var a=r.replace(pe,""),n=r.length-a.length;return Fe(e,a,t*Math.pow(10,2*n))+kr("%",n)}function be(e,r,t){var a=r.length-1;while(44===r.charCodeAt(a-1))--a;return Fe(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function ve(e,r){var t,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+ve(e,-r);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%n;if(s<0&&(s+=n),t=(r/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),-1===t.indexOf("e")){var i=Math.floor(Math.log(r)*Math.LOG10E);-1===t.indexOf(".")?t=t.charAt(0)+"."+t.substr(1)+"E+"+(i-t.length+s):t+="E+"+(i-s);while("0."===t.substr(0,2))t=t.charAt(0)+t.substr(2,n)+"."+t.substr(2+n),t=t.replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,r,t,a){return r+t+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else t=r.toExponential(a);return e.match(/E\+00$/)&&t.match(/e[+-]\d$/)&&(t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1)),e.match(/E\-/)&&t.match(/e\+/)&&(t=t.replace(/e\+/,"e")),t.replace("e","E")}var ge=/# (\?+)( ?)\/( ?)(\d+)/;function we(e,r,t){var a=parseInt(e[4],10),n=Math.round(r*a),s=Math.floor(n/a),i=n-s*a,o=a;return t+(0===s?"":""+s)+" "+(0===i?kr(" ",e[1].length+1+e[4].length):B(i,e[1].length)+e[2]+"/"+e[3]+U(o,e[4].length))}function ke(e,r,t){return t+(0===r?"":""+r)+kr(" ",e[1].length+2+e[4].length)}var Te=/^#*0*\.([0#]+)/,Ee=/\).*[0#]/,Se=/\(###\) ###\\?-####/;function ye(e){for(var r,t="",a=0;a!=e.length;++a)switch(r=e.charCodeAt(a)){case 35:break;case 63:t+=" ";break;case 48:t+="0";break;default:t+=String.fromCharCode(r)}return t}function _e(e,r){var t=Math.pow(10,r);return""+Math.round(e*t)/t}function Ae(e,r){var t=e-Math.floor(e),a=Math.pow(10,r);return r<(""+Math.round(t*a)).length?0:Math.round(t*a)}function xe(e,r){return r<(""+Math.round((e-Math.floor(e))*Math.pow(10,r))).length?1:0}function Ce(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}function Re(e,r,t){if(40===e.charCodeAt(0)&&!r.match(Ee)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return t>=0?Re("n",a,t):"("+Re("n",a,-t)+")"}if(44===r.charCodeAt(r.length-1))return be(e,r,t);if(-1!==r.indexOf("%"))return me(e,r,t);if(-1!==r.indexOf("E"))return ve(r,t);if(36===r.charCodeAt(0))return"$"+Re(e,r.substr(" "==r.charAt(1)?2:1),t);var n,s,i,o,c=Math.abs(t),l=t<0?"-":"";if(r.match(/^00+$/))return l+G(c,r.length);if(r.match(/^[#?]+$/))return n=G(t,0),"0"===n&&(n=""),n.length>r.length?n:ye(r.substr(0,r.length-n.length))+n;if(s=r.match(ge))return we(s,c,l);if(r.match(/^#+0+$/))return l+G(c,r.length-r.indexOf("0"));if(s=r.match(Te))return n=_e(t,s[1].length).replace(/^([^\.]+)$/,"$1."+ye(s[1])).replace(/\.$/,"."+ye(s[1])).replace(/\.(\d*)$/,(function(e,r){return"."+r+kr("0",ye(s[1]).length-r.length)})),-1!==r.indexOf("0.")?n:n.replace(/^0\./,".");if(r=r.replace(/^#+([0.])/,"$1"),s=r.match(/^(0*)\.(#*)$/))return l+_e(c,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=r.match(/^#{1,3},##0(\.?)$/))return l+de(G(c,0));if(s=r.match(/^#,##0\.([#0]*0)$/))return t<0?"-"+Re(e,r,-t):de(""+(Math.floor(t)+xe(t,s[1].length)))+"."+U(Ae(t,s[1].length),s[1].length);if(s=r.match(/^#,#*,#0/))return Re(e,r.replace(/^#,#*,/,""),t);if(s=r.match(/^([0#]+)(\\?-([0#]+))+$/))return n=M(Re(e,r.replace(/[\\-]/g,""),t)),i=0,M(M(r.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(r.match(Se))return n=Re(e,"##########",t),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=Q(c,Math.pow(10,i)-1,!1),n=""+l,f=Fe("n",s[1],o[1])," "==f.charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],f=W(o[2],i),f.length<s[4].length&&(f=ye(s[4].substr(s[4].length-f.length))+f),n+=f,n;if(s=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),o=Q(c,Math.pow(10,i)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?B(o[1],i)+s[2]+"/"+s[3]+W(o[2],i):kr(" ",2*i+1+s[2].length+s[3].length));if(s=r.match(/^[#0?]+$/))return n=G(t,0),r.length<=n.length?n:ye(r.substr(0,r.length-n.length))+n;if(s=r.match(/^([#0?]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=r.indexOf(".")-i,u=r.length-n.length-h;return ye(r.substr(0,h)+n+r.substr(r.length-u))}if(s=r.match(/^00,000\.([#0]*0)$/))return i=Ae(t,s[1].length),t<0?"-"+Re(e,r,-t):de(Ce(t)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?U(0,3-e.length):"")+e}))+"."+U(i,s[1].length);switch(r){case"###,##0.00":return Re(e,"#,##0.00",t);case"###,###":case"##,###":case"#,###":var d=de(G(c,0));return"0"!==d?l+d:"";case"###,###.00":return Re(e,"###,##0.00",t).replace(/^0\./,".");case"#,###.00":return Re(e,"#,##0.00",t).replace(/^0\./,".");default:}throw new Error("unsupported format |"+r+"|")}function Oe(e,r,t){var a=r.length-1;while(44===r.charCodeAt(a-1))--a;return Fe(e,r.substr(0,a),t/Math.pow(10,3*(r.length-a)))}function Ie(e,r,t){var a=r.replace(pe,""),n=r.length-a.length;return Fe(e,a,t*Math.pow(10,2*n))+kr("%",n)}function Ne(e,r){var t,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+Ne(e,-r);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(r)*Math.LOG10E)%n;if(s<0&&(s+=n),t=(r/Math.pow(10,s)).toPrecision(a+1+(n+s)%n),!t.match(/[Ee]/)){var i=Math.floor(Math.log(r)*Math.LOG10E);-1===t.indexOf(".")?t=t.charAt(0)+"."+t.substr(1)+"E+"+(i-t.length+s):t+="E+"+(i-s),t=t.replace(/\+-/,"-")}t=t.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,r,t,a){return r+t+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else t=r.toExponential(a);return e.match(/E\+00$/)&&t.match(/e[+-]\d$/)&&(t=t.substr(0,t.length-1)+"0"+t.charAt(t.length-1)),e.match(/E\-/)&&t.match(/e\+/)&&(t=t.replace(/e\+/,"e")),t.replace("e","E")}function De(e,r,t){if(40===e.charCodeAt(0)&&!r.match(Ee)){var a=r.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return t>=0?De("n",a,t):"("+De("n",a,-t)+")"}if(44===r.charCodeAt(r.length-1))return Oe(e,r,t);if(-1!==r.indexOf("%"))return Ie(e,r,t);if(-1!==r.indexOf("E"))return Ne(r,t);if(36===r.charCodeAt(0))return"$"+De(e,r.substr(" "==r.charAt(1)?2:1),t);var n,s,i,o,c=Math.abs(t),l=t<0?"-":"";if(r.match(/^00+$/))return l+U(c,r.length);if(r.match(/^[#?]+$/))return n=""+t,0===t&&(n=""),n.length>r.length?n:ye(r.substr(0,r.length-n.length))+n;if(s=r.match(ge))return ke(s,c,l);if(r.match(/^#+0+$/))return l+U(c,r.length-r.indexOf("0"));if(s=r.match(Te))return n=(""+t).replace(/^([^\.]+)$/,"$1."+ye(s[1])).replace(/\.$/,"."+ye(s[1])),n=n.replace(/\.(\d*)$/,(function(e,r){return"."+r+kr("0",ye(s[1]).length-r.length)})),-1!==r.indexOf("0.")?n:n.replace(/^0\./,".");if(r=r.replace(/^#+([0.])/,"$1"),s=r.match(/^(0*)\.(#*)$/))return l+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=r.match(/^#{1,3},##0(\.?)$/))return l+de(""+c);if(s=r.match(/^#,##0\.([#0]*0)$/))return t<0?"-"+De(e,r,-t):de(""+t)+"."+kr("0",s[1].length);if(s=r.match(/^#,#*,#0/))return De(e,r.replace(/^#,#*,/,""),t);if(s=r.match(/^([0#]+)(\\?-([0#]+))+$/))return n=M(De(e,r.replace(/[\\-]/g,""),t)),i=0,M(M(r.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(r.match(Se))return n=De(e,"##########",t),"("+n.substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=r.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=Q(c,Math.pow(10,i)-1,!1),n=""+l,f=Fe("n",s[1],o[1])," "==f.charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],f=W(o[2],i),f.length<s[4].length&&(f=ye(s[4].substr(s[4].length-f.length))+f),n+=f,n;if(s=r.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),o=Q(c,Math.pow(10,i)-1,!0),l+(o[0]||(o[1]?"":"0"))+" "+(o[1]?B(o[1],i)+s[2]+"/"+s[3]+W(o[2],i):kr(" ",2*i+1+s[2].length+s[3].length));if(s=r.match(/^[#0?]+$/))return n=""+t,r.length<=n.length?n:ye(r.substr(0,r.length-n.length))+n;if(s=r.match(/^([#0]+)\.([#0]+)$/)){n=""+t.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=r.indexOf(".")-i,u=r.length-n.length-h;return ye(r.substr(0,h)+n+r.substr(r.length-u))}if(s=r.match(/^00,000\.([#0]*0)$/))return t<0?"-"+De(e,r,-t):de(""+t).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?U(0,3-e.length):"")+e}))+"."+U(0,s[1].length);switch(r){case"###,###":case"##,###":case"#,###":var d=de(""+c);return"0"!==d?l+d:"";default:if(r.match(/\.[0#?]*$/))return De(e,r.slice(0,r.lastIndexOf(".")),t)+ye(r.slice(r.lastIndexOf(".")))}throw new Error("unsupported format |"+r+"|")}function Fe(e,r,t){return(0|t)===t?De(e,r,t):Re(e,r,t)}function Pe(e){for(var r=[],t=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:t=!t;break;case 95:case 42:case 92:++a;break;case 59:r[r.length]=e.substr(n,a-n),n=a+1}if(r[r.length]=e.substr(n),!0===t)throw new Error("Format |"+e+"| unterminated string ");return r}var Le=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function Me(e){var r=0,t="",a="";while(r<e.length)switch(t=e.charAt(r)){case"G":j(e,r)&&(r+=6),r++;break;case'"':for(;34!==e.charCodeAt(++r)&&r<e.length;);++r;break;case"\\":r+=2;break;case"_":r+=2;break;case"@":++r;break;case"B":case"b":if("1"===e.charAt(r+1)||"2"===e.charAt(r+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(r,3).toUpperCase())return!0;if("AM/PM"===e.substr(r,5).toUpperCase())return!0;if("上午/下午"===e.substr(r,5).toUpperCase())return!0;++r;break;case"[":a=t;while("]"!==e.charAt(r++)&&r<e.length)a+=e.charAt(r);if(a.match(Le))return!0;break;case".":case"0":case"#":while(r<e.length&&("0#?.,E+-%".indexOf(t=e.charAt(++r))>-1||"\\"==t&&"-"==e.charAt(r+1)&&"0#".indexOf(e.charAt(r+2))>-1));break;case"?":while(e.charAt(++r)===t);break;case"*":++r," "!=e.charAt(r)&&"*"!=e.charAt(r)||++r;break;case"(":case")":++r;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":while(r<e.length&&"0123456789".indexOf(e.charAt(++r))>-1);break;case" ":++r;break;default:++r;break}return!1}function Ue(e,r,t,a){var n,s,i,o=[],c="",l=0,f="",h="t",u="H";while(l<e.length)switch(f=e.charAt(l)){case"G":if(!j(e,l))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(c="";34!==(i=e.charCodeAt(++l))&&l<e.length;)c+=String.fromCharCode(i);o[o.length]={t:"t",v:c},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:r},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&(n=ee(r,t,"2"===e.charAt(l+1)),null==n))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(r<0)return"";if(null==n&&(n=ee(r,t),null==n))return"";c=f;while(++l<e.length&&e.charAt(l).toLowerCase()===f)c+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:c},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=ee(r,t)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?"P":"A"),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":c=f;while("]"!==e.charAt(l++)&&l<e.length)c+=e.charAt(l);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(Le)){if(null==n&&(n=ee(r,t),null==n))return"";o[o.length]={t:"Z",v:c.toLowerCase()},h=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",Me(e)||(o[o.length]={t:"t",v:c}));break;case".":if(null!=n){c=f;while(++l<e.length&&"0"===(f=e.charAt(l)))c+=f;o[o.length]={t:"s",v:c};break}case"0":case"#":c=f;while(++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1)c+=f;o[o.length]={t:"n",v:c};break;case"?":c=f;while(e.charAt(++l)===f)c+=f;o[o.length]={t:f,v:c},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":c=f;while(l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1)c+=e.charAt(l);o[o.length]={t:"D",v:c};break;case" ":o[o.length]={t:f,v:f},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++l;break}var b,v=0,g=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",v<1&&(v=1);break;case"s":(b=o[l].v.match(/\.0+$/))&&(g=Math.max(g,b[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":h=o[l].t;break;case"m":"s"===h&&(o[l].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[l].v.match(/[Hh]/)&&(v=1),v<2&&o[l].v.match(/[Mm]/)&&(v=2),v<3&&o[l].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H);break;case 2:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M);break}var w,k="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=ue(o[l].t.charCodeAt(0),o[l].v,n,g),o[l].t="t";break;case"n":case"?":w=l+1;while(null!=o[w]&&("?"===(f=o[w].t)||"D"===f||(" "===f||"t"===f)&&null!=o[w+1]&&("?"===o[w+1].t||"t"===o[w+1].t&&"/"===o[w+1].v)||"("===o[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[w].v||" "===o[w].v&&null!=o[w+1]&&"?"==o[w+1].t)))o[l].v+=o[w].v,o[w]={v:"",t:";"},++w;k+=o[l].v,l=w-1;break;case"G":o[l].t="t",o[l].v=fe(r,t);break}var T,E,S="";if(k.length>0){40==k.charCodeAt(0)?(T=r<0&&45===k.charCodeAt(0)?-r:r,E=Fe("n",k,T)):(T=r<0&&a>1?-r:r,E=Fe("n",k,T),T<0&&o[0]&&"t"==o[0].t&&(E=E.substr(1),o[0].v="-"+o[0].v)),w=E.length-1;var y=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){y=l;break}var _=o.length;if(y===o.length&&-1===E.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(w>=o[l].v.length-1?(w-=o[l].v.length,o[l].v=E.substr(w+1,o[l].v.length)):w<0?o[l].v="":(o[l].v=E.substr(0,w+1),w=-1),o[l].t="t",_=l);w>=0&&_<o.length&&(o[_].v=E.substr(0,w+1)+o[_].v)}else if(y!==o.length&&-1===E.indexOf("E")){for(w=E.indexOf(".")-1,l=y;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(s=o[l].v.indexOf(".")>-1&&l===y?o[l].v.indexOf(".")-1:o[l].v.length-1,S=o[l].v.substr(s+1);s>=0;--s)w>=0&&("0"===o[l].v.charAt(s)||"#"===o[l].v.charAt(s))&&(S=E.charAt(w--)+S);o[l].v=S,o[l].t="t",_=l}for(w>=0&&_<o.length&&(o[_].v=E.substr(0,w+1)+o[_].v),w=E.indexOf(".")+1,l=y;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===y)){for(s=o[l].v.indexOf(".")>-1&&l===y?o[l].v.indexOf(".")+1:0,S=o[l].v.substr(0,s);s<o[l].v.length;++s)w<E.length&&(S+=E.charAt(w++));o[l].v=S,o[l].t="t",_=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(T=a>1&&r<0&&l>0&&"-"===o[l-1].v?-r:r,o[l].v=Fe(o[l].t,o[l].v,T),o[l].t="t");var A="";for(l=0;l!==o.length;++l)null!=o[l]&&(A+=o[l].v);return A}var Be=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function We(e,r){if(null==r)return!1;var t=parseFloat(r[2]);switch(r[1]){case"=":if(e==t)return!0;break;case">":if(e>t)return!0;break;case"<":if(e<t)return!0;break;case"<>":if(e!=t)return!0;break;case">=":if(e>=t)return!0;break;case"<=":if(e<=t)return!0;break}return!1}function He(e,r){var t=Pe(e),a=t.length,n=t[a-1].indexOf("@");if(a<4&&n>-1&&--a,t.length>4)throw new Error("cannot find right format for |"+t.join("|")+"|");if("number"!==typeof r)return[4,4===t.length||n>-1?t[t.length-1]:"@"];switch(t.length){case 1:t=n>-1?["General","General","General",t[0]]:[t[0],t[0],t[0],"@"];break;case 2:t=n>-1?[t[0],t[0],t[0],t[1]]:[t[0],t[1],t[0],"@"];break;case 3:t=n>-1?[t[0],t[1],t[0],t[2]]:[t[0],t[1],t[2],"@"];break;case 4:break}var s=r>0?t[0]:r<0?t[1]:t[2];if(-1===t[0].indexOf("[")&&-1===t[1].indexOf("["))return[a,s];if(null!=t[0].match(/\[[=<>]/)||null!=t[1].match(/\[[=<>]/)){var i=t[0].match(Be),o=t[1].match(Be);return We(r,i)?[a,t[0]]:We(r,o)?[a,t[1]]:[a,t[null!=i&&null!=o?2:1]]}return[a,s]}function Ve(e,r,t){null==t&&(t={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&t.dateNF?t.dateNF:e;break;case"number":a=14==e&&t.dateNF?t.dateNF:(null!=t.table?t.table:J)[e],null==a&&(a=t.table&&t.table[q[e]]||J[q[e]]),null==a&&(a=Z[e]||"General");break}if(j(a,0))return fe(r,t);r instanceof Date&&(r=ne(r,t.date1904));var n=He(a,r);if(j(n[1]))return fe(r,t);if(!0===r)r="TRUE";else if(!1===r)r="FALSE";else if(""===r||null==r)return"";return Ue(n[1],r,t,n[0])}function ze(e,r){if("number"!=typeof r){r=+r||-1;for(var t=0;t<392;++t)if(void 0!=J[t]){if(J[t]==e){r=t;break}}else r<0&&(r=t);r<0&&(r=391)}return J[r]=e,r}function Ge(e){for(var r=0;392!=r;++r)void 0!==e[r]&&ze(e[r],r)}function je(){J=K()}var Xe={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},$e=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;function Ye(e){var r="number"==typeof e?J[e]:e;return r=r.replace($e,"(\\d+)"),new RegExp("^"+r+"$")}function Ke(e,r,t){var a=-1,n=-1,s=-1,i=-1,o=-1,c=-1;(r.match($e)||[]).forEach((function(e,r){var l=parseInt(t[r+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":c=l;break;case"m":i>=0?o=l:n=l;break}})),c>=0&&-1==o&&n>=0&&(o=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return-1==i&&-1==o&&-1==c?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}var Je=function(){var e={};function r(){for(var e=0,r=new Array(256),t=0;256!=t;++t)e=t,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,e=1&e?-306674912^e>>>1:e>>>1,r[t]=e;return"undefined"!==typeof Int32Array?new Int32Array(r):r}e.version="1.2.0";var t=r();function a(e){var r=0,t=0,a=0,n="undefined"!==typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(t=e[a],r=256+a;r<4096;r+=256)t=n[r]=t>>>8^e[255&t];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!==typeof Int32Array?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}var n=a(t),s=n[0],i=n[1],o=n[2],c=n[3],l=n[4],f=n[5],h=n[6],u=n[7],d=n[8],p=n[9],m=n[10],b=n[11],v=n[12],g=n[13],w=n[14];function k(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[255&(a^e.charCodeAt(n++))];return~a}function T(e,r){for(var a=-1^r,n=e.length-15,k=0;k<n;)a=w[e[k++]^255&a]^g[e[k++]^a>>8&255]^v[e[k++]^a>>16&255]^b[e[k++]^a>>>24]^m[e[k++]]^p[e[k++]]^d[e[k++]]^u[e[k++]]^h[e[k++]]^f[e[k++]]^l[e[k++]]^c[e[k++]]^o[e[k++]]^i[e[k++]]^s[e[k++]]^t[e[k++]];n+=15;while(k<n)a=a>>>8^t[255&(a^e[k++])];return~a}function E(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)i=e.charCodeAt(n++),i<128?a=a>>>8^t[255&(a^i)]:i<2048?(a=a>>>8^t[255&(a^(192|i>>6&31))],a=a>>>8^t[255&(a^(128|63&i))]):i>=55296&&i<57344?(i=64+(1023&i),o=1023&e.charCodeAt(n++),a=a>>>8^t[255&(a^(240|i>>8&7))],a=a>>>8^t[255&(a^(128|i>>2&63))],a=a>>>8^t[255&(a^(128|o>>6&15|(3&i)<<4))],a=a>>>8^t[255&(a^(128|63&o))]):(a=a>>>8^t[255&(a^(224|i>>12&15))],a=a>>>8^t[255&(a^(128|i>>6&63))],a=a>>>8^t[255&(a^(128|63&i))]);return~a}return e.table=t,e.bstr=k,e.buf=T,e.str=E,e}(),qe=function(){var e,r={};function t(e,r){for(var t=e.split("/"),a=r.split("/"),n=0,s=0,i=Math.min(t.length,a.length);n<i;++n){if(s=t[n].length-a[n].length)return s;if(t[n]!=a[n])return t[n]<a[n]?-1:1}return t.length-a.length}function a(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:a(e.slice(0,-1));var r=e.lastIndexOf("/");return-1===r?e:e.slice(0,r+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var r=e.lastIndexOf("/");return-1===r?e:e.slice(r+1)}function s(e,r){"string"===typeof r&&(r=new Date(r));var t=r.getHours();t=t<<6|r.getMinutes(),t=t<<5|r.getSeconds()>>>1,e.write_shift(2,t);var a=r.getFullYear()-1980;a=a<<4|r.getMonth()+1,a=a<<5|r.getDate(),e.write_shift(2,a)}function i(e){var r=65535&e.read_shift(2),t=65535&e.read_shift(2),a=new Date,n=31&t;t>>>=5;var s=15&t;t>>>=4,a.setMilliseconds(0),a.setFullYear(t+1980),a.setMonth(s-1),a.setDate(n);var i=31&r;r>>>=5;var o=63&r;return r>>>=6,a.setHours(r),a.setMinutes(o),a.setSeconds(i<<1),a}function o(e){ha(e,0);var r={},t=0;while(e.l<=e.length-4){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};switch(a){case 21589:t=e.read_shift(1),1&t&&(i.mtime=e.read_shift(4)),n>5&&(2&t&&(i.atime=e.read_shift(4)),4&t&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));break}e.l=s,r[a]=i}return r}function c(){return e||(e={})}function l(e,r){if(80==e[0]&&75==e[1])return Ie(e,r);if(109==(32|e[0])&&105==(32|e[1]))return We(e,r);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var t=3,a=512,n=0,s=0,i=0,o=0,c=0,l=[],p=e.slice(0,512);ha(p,0);var b=f(p);switch(t=b[0],t){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==b[1])return Ie(e,r);default:throw new Error("Major Version: Expected 3 or 4 saw "+t)}512!==a&&(p=e.slice(0,a),ha(p,28));var w=e.slice(0,a);h(p,t);var k=p.read_shift(4,"i");if(3===t&&0!==k)throw new Error("# Directory Sectors: Expected 0 saw "+k);p.l+=4,i=p.read_shift(4,"i"),p.l+=4,p.chk("00100000","Mini Stream Cutoff Size: "),o=p.read_shift(4,"i"),n=p.read_shift(4,"i"),c=p.read_shift(4,"i"),s=p.read_shift(4,"i");for(var T=-1,E=0;E<109;++E){if(T=p.read_shift(4,"i"),T<0)break;l[E]=T}var S=u(e,a);m(c,s,S,a,l);var y=v(S,i,l,a);y[i].name="!Directory",n>0&&o!==B&&(y[o].name="!MiniFAT"),y[l[0]].name="!FAT",y.fat_addrs=l,y.ssz=a;var _={},A=[],x=[],C=[];g(i,y,S,A,n,_,x,o),d(x,C,A),A.shift();var R={FileIndex:x,FullPaths:C};return r&&r.raw&&(R.raw={header:w,sectors:S}),R}function f(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(W,"Header Signature: "),e.l+=16;var r=e.read_shift(2,"u");return[e.read_shift(2,"u"),r]}function h(e,r){var t=9;switch(e.l+=2,t=e.read_shift(2)){case 9:if(3!=r)throw new Error("Sector Shift: Expected 9 saw "+t);break;case 12:if(4!=r)throw new Error("Sector Shift: Expected 12 saw "+t);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+t)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}function u(e,r){for(var t=Math.ceil(e.length/r)-1,a=[],n=1;n<t;++n)a[n-1]=e.slice(n*r,(n+1)*r);return a[t-1]=e.slice(t*r),a}function d(e,r,t){for(var a=0,n=0,s=0,i=0,o=0,c=t.length,l=[],f=[];a<c;++a)l[a]=f[a]=a,r[a]=t[a];for(;o<f.length;++o)a=f[o],n=e[a].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<c;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<c;++a)if(0!==e[a].type){if(o=a,o!=l[o])do{o=l[o],r[a]=r[o]+"/"+r[a]}while(0!==o&&-1!==l[o]&&o!=l[o]);l[a]=-1}for(r[0]+="/",a=1;a<c;++a)2!==e[a].type&&(r[a]+="/")}function p(e,r,t){var a=e.start,n=e.size,s=[],i=a;while(t&&n>0&&i>=0)s.push(r.slice(i*U,i*U+U)),n-=U,i=aa(t,4*i);return 0===s.length?da(0):D(s).slice(0,e.size)}function m(e,r,t,a,n){var s=B;if(e===B){if(0!==r)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=t[e],o=(a>>>2)-1;if(!i)return;for(var c=0;c<o;++c){if((s=aa(i,4*c))===B)break;n.push(s)}m(aa(i,a-4),r-1,t,a,n)}}function b(e,r,t,a,n){var s=[],i=[];n||(n=[]);var o=a-1,c=0,l=0;for(c=r;c>=0;){n[c]=!0,s[s.length]=c,i.push(e[c]);var f=t[Math.floor(4*c/a)];if(l=4*c&o,a<4+l)throw new Error("FAT boundary crossed: "+c+" 4 "+a);if(!e[f])break;c=aa(e[f],l)}return{nodes:s,data:Nt([i])}}function v(e,r,t,a){var n=e.length,s=[],i=[],o=[],c=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],u=f+r,u>=n&&(u-=n),!i[u]){c=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,c.push(e[h]);var m=t[Math.floor(4*h/a)];if(d=4*h&l,a<4+d)throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(h=aa(e[m],d),p[h])break}s[u]={nodes:o,data:Nt([c])}}return s}function g(e,r,t,a,n,s,i,o){for(var c,l=0,f=a.length?2:0,h=r[e].data,u=0,d=0;u<h.length;u+=128){var m=h.slice(u,u+128);ha(m,64),d=m.read_shift(2),c=Ft(m,0,d-f),a.push(c);var v={name:c,type:m.read_shift(1),color:m.read_shift(1),L:m.read_shift(4,"i"),R:m.read_shift(4,"i"),C:m.read_shift(4,"i"),clsid:m.read_shift(16),state:m.read_shift(4,"i"),start:0,size:0},g=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==g&&(v.ct=w(m,m.l-8));var k=m.read_shift(2)+m.read_shift(2)+m.read_shift(2)+m.read_shift(2);0!==k&&(v.mt=w(m,m.l-8)),v.start=m.read_shift(4,"i"),v.size=m.read_shift(4,"i"),v.size<0&&v.start<0&&(v.size=v.type=0,v.start=B,v.name=""),5===v.type?(l=v.start,n>0&&l!==B&&(r[l].name="!StreamData")):v.size>=4096?(v.storage="fat",void 0===r[v.start]&&(r[v.start]=b(t,v.start,r.fat_addrs,r.ssz)),r[v.start].name=v.name,v.content=r[v.start].data.slice(0,v.size)):(v.storage="minifat",v.size<0?v.size=0:l!==B&&v.start!==B&&r[l]&&(v.content=p(v,r[l].data,(r[o]||{}).data))),v.content&&ha(v.content,0),s[c]=v,i.push(v)}}function w(e,r){return new Date(1e3*(ta(e,r+4)/1e7*Math.pow(2,32)+ta(e,r)/1e7-11644473600))}function k(r,t){return c(),l(e.readFileSync(r),t)}function T(e,r){var t=r&&r.type;switch(t||y&&Buffer.isBuffer(e)&&(t="buffer"),t||"base64"){case"file":return k(e,r);case"base64":return l(C(S(e)),r);case"binary":return l(C(e),r)}return l(e,r)}function R(e,r){var t=r||{},a=t.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),t.CLSID&&(e.FileIndex[0].clsid=t.CLSID),O(e)}function O(e){var r="Sh33tJ5";if(!qe.find(e,"/"+r)){var t=da(4);t[0]=55,t[1]=t[3]=50,t[2]=54,e.FileIndex.push({name:r,type:2,content:t,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+r),I(e)}}function I(e,r){R(e);for(var s=!1,i=!1,o=e.FullPaths.length-1;o>=0;--o){var c=e.FileIndex[o];switch(c.type){case 0:i?s=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:i=!0,isNaN(c.R*c.L*c.C)&&(s=!0),c.R>-1&&c.L>-1&&c.R==c.L&&(s=!0);break;default:s=!0;break}}if(s||r){var l=new Date(1987,1,19),f=0,h=Object.create?Object.create(null):{},u=[];for(o=0;o<e.FullPaths.length;++o)h[e.FullPaths[o]]=!0,0!==e.FileIndex[o].type&&u.push([e.FullPaths[o],e.FileIndex[o]]);for(o=0;o<u.length;++o){var d=a(u[o][0]);i=h[d],i||(u.push([d,{name:n(d).replace("/",""),type:1,clsid:V,ct:l,mt:l,content:null}]),h[d]=!0)}for(u.sort((function(e,r){return t(e[0],r[0])})),e.FullPaths=[],e.FileIndex=[],o=0;o<u.length;++o)e.FullPaths[o]=u[o][0],e.FileIndex[o]=u[o][1];for(o=0;o<u.length;++o){var p=e.FileIndex[o],m=e.FullPaths[o];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||V,0===o)p.C=u.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(f=o+1;f<u.length;++f)if(a(e.FullPaths[f])==m)break;for(p.C=f>=u.length?-1:f,f=o+1;f<u.length;++f)if(a(e.FullPaths[f])==a(m))break;p.R=f>=u.length?-1:f,p.type=1}else a(e.FullPaths[o+1]||"")==a(m)&&(p.R=o+1),p.type=2}}}function N(e,r){var t=r||{};if("mad"==t.fileType)return He(e,t);switch(I(e),t.fileType){case"zip":return De(e,t)}var a=function(e){for(var r=0,t=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?r+=s+63>>6:t+=s+511>>9)}}var i=e.FullPaths.length+3>>2,o=r+7>>3,c=r+127>>7,l=o+t+i+c,f=l+127>>7,h=f<=109?0:Math.ceil((f-109)/127);while(l+f+h+127>>7>f)h=++f<=109?0:Math.ceil((f-109)/127);var u=[1,h,f,c,i,t,r,0];return e.FileIndex[0].size=r<<6,u[7]=(e.FileIndex[0].start=u[0]+u[1]+u[2]+u[3]+u[4]+u[5])+(u[6]+7>>3),u}(e),n=da(a[7]<<9),s=0,i=0;for(s=0;s<8;++s)n.write_shift(1,H[s]);for(s=0;s<8;++s)n.write_shift(2,0);for(n.write_shift(2,62),n.write_shift(2,3),n.write_shift(2,65534),n.write_shift(2,9),n.write_shift(2,6),s=0;s<3;++s)n.write_shift(2,0);for(n.write_shift(4,0),n.write_shift(4,a[2]),n.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),n.write_shift(4,0),n.write_shift(4,4096),n.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:B),n.write_shift(4,a[3]),n.write_shift(-4,a[1]?a[0]-1:B),n.write_shift(4,a[1]),s=0;s<109;++s)n.write_shift(-4,s<a[2]?a[1]+s:-1);if(a[1])for(i=0;i<a[1];++i){for(;s<236+127*i;++s)n.write_shift(-4,s<a[2]?a[1]+s:-1);n.write_shift(-4,i===a[1]-1?B:i+1)}var o=function(e){for(i+=e;s<i-1;++s)n.write_shift(-4,s+1);e&&(++s,n.write_shift(-4,B))};for(i=s=0,i+=a[1];s<i;++s)n.write_shift(-4,z.DIFSECT);for(i+=a[2];s<i;++s)n.write_shift(-4,z.FATSECT);o(a[3]),o(a[4]);for(var c=0,l=0,f=e.FileIndex[0];c<e.FileIndex.length;++c)f=e.FileIndex[c],f.content&&(l=f.content.length,l<4096||(f.start=i,o(l+511>>9)));o(a[6]+7>>3);while(511&n.l)n.write_shift(-4,z.ENDOFCHAIN);for(i=s=0,c=0;c<e.FileIndex.length;++c)f=e.FileIndex[c],f.content&&(l=f.content.length,!l||l>=4096||(f.start=i,o(l+63>>6)));while(511&n.l)n.write_shift(-4,z.ENDOFCHAIN);for(s=0;s<a[4]<<2;++s){var h=e.FullPaths[s];if(h&&0!==h.length){f=e.FileIndex[s],0===s&&(f.start=f.size?f.start-1:B);var u=0===s&&t.root||f.name;if(l=2*(u.length+1),n.write_shift(64,u,"utf16le"),n.write_shift(2,l),n.write_shift(1,f.type),n.write_shift(1,f.color),n.write_shift(-4,f.L),n.write_shift(-4,f.R),n.write_shift(-4,f.C),f.clsid)n.write_shift(16,f.clsid,"hex");else for(c=0;c<4;++c)n.write_shift(4,0);n.write_shift(4,f.state||0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,0),n.write_shift(4,f.start),n.write_shift(4,f.size),n.write_shift(4,0)}else{for(c=0;c<17;++c)n.write_shift(4,0);for(c=0;c<3;++c)n.write_shift(4,-1);for(c=0;c<12;++c)n.write_shift(4,0)}}for(s=1;s<e.FileIndex.length;++s)if(f=e.FileIndex[s],f.size>=4096)if(n.l=f.start+1<<9,y&&Buffer.isBuffer(f.content))f.content.copy(n,n.l,0,f.size),n.l+=f.size+511&-512;else{for(c=0;c<f.size;++c)n.write_shift(1,f.content[c]);for(;511&c;++c)n.write_shift(1,0)}for(s=1;s<e.FileIndex.length;++s)if(f=e.FileIndex[s],f.size>0&&f.size<4096)if(y&&Buffer.isBuffer(f.content))f.content.copy(n,n.l,0,f.size),n.l+=f.size+63&-64;else{for(c=0;c<f.size;++c)n.write_shift(1,f.content[c]);for(;63&c;++c)n.write_shift(1,0)}if(y)n.l=n.length;else while(n.l<n.length)n.write_shift(1,0);return n}function F(e,r){var t=e.FullPaths.map((function(e){return e.toUpperCase()})),a=t.map((function(e){var r=e.split("/");return r[r.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===r.charCodeAt(0)?(n=!0,r=t[0].slice(0,-1)+r):n=-1!==r.indexOf("/");var s=r.toUpperCase(),i=!0===n?t.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(L);for(s=s.replace(P,""),o&&(s=s.replace(L,"!")),i=0;i<t.length;++i){if((o?t[i].replace(L,"!"):t[i]).replace(P,"")==s)return e.FileIndex[i];if((o?a[i].replace(L,"!"):a[i]).replace(P,"")==s)return e.FileIndex[i]}return null}r.version="1.2.1";var M,U=64,B=-2,W="d0cf11e0a1b11ae1",H=[208,207,17,224,161,177,26,225],V="00000000000000000000000000000000",z={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:B,FREESECT:-1,HEADER_SIGNATURE:W,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:V,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function G(r,t,a){c();var n=N(r,a);e.writeFileSync(t,n)}function j(e){for(var r=new Array(e.length),t=0;t<e.length;++t)r[t]=String.fromCharCode(e[t]);return r.join("")}function X(r,t){var a=N(r,t);switch(t&&t.type||"buffer"){case"file":return c(),e.writeFileSync(t.filename,a),a;case"binary":return"string"==typeof a?a:j(a);case"base64":return E("string"==typeof a?a:j(a));case"buffer":if(y)return Buffer.isBuffer(a)?a:_(a);case"array":return"string"==typeof a?C(a):a}return a}function $(e){try{var r=e.InflateRaw,t=new r;if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");M=e}catch(a){console.error("cannot use native zlib: "+(a.message||a))}}function Y(e,r){if(!M)return Re(e,r);var t=M.InflateRaw,a=new t,n=a._processChunk(e.slice(e.l),a._finishFlushFlag);return e.l+=a.bytesRead,n}function K(e){return M?M.deflateRawSync(e):Te(e)}var J=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],q=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],Z=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function Q(e){var r=139536&(e<<1|e<<11)|558144&(e<<5|e<<15);return 255&(r>>16|r>>8|r)}for(var ee="undefined"!==typeof Uint8Array,re=ee?new Uint8Array(256):[],te=0;te<256;++te)re[te]=Q(te);function ae(e,r){var t=re[255&e];return r<=8?t>>>8-r:(t=t<<8|re[e>>8&255],r<=16?t>>>16-r:(t=t<<8|re[e>>16&255],t>>>24-r))}function ne(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=6?0:e[a+1]<<8))>>>t&3}function se(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=5?0:e[a+1]<<8))>>>t&7}function ie(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=4?0:e[a+1]<<8))>>>t&15}function oe(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=3?0:e[a+1]<<8))>>>t&31}function ce(e,r){var t=7&r,a=r>>>3;return(e[a]|(t<=1?0:e[a+1]<<8))>>>t&127}function le(e,r,t){var a=7&r,n=r>>>3,s=(1<<t)-1,i=e[n]>>>a;return t<8-a?i&s:(i|=e[n+1]<<8-a,t<16-a?i&s:(i|=e[n+2]<<16-a,t<24-a||(i|=e[n+3]<<24-a),i&s))}function fe(e,r,t){var a=7&r,n=r>>>3;return a<=5?e[n]|=(7&t)<<a:(e[n]|=t<<a&255,e[n+1]=(7&t)>>8-a),r+3}function he(e,r,t){var a=7&r,n=r>>>3;return t=(1&t)<<a,e[n]|=t,r+1}function ue(e,r,t){var a=7&r,n=r>>>3;return t<<=a,e[n]|=255&t,t>>>=8,e[n+1]=t,r+8}function de(e,r,t){var a=7&r,n=r>>>3;return t<<=a,e[n]|=255&t,t>>>=8,e[n+1]=255&t,e[n+2]=t>>>8,r+16}function pe(e,r){var t=e.length,a=2*t>r?2*t:r+5,n=0;if(t>=r)return e;if(y){var s=x(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(ee){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<t;++n)i[n]=e[n];return i}return e.length=a,e}function me(e){for(var r=new Array(e),t=0;t<e;++t)r[t]=0;return r}function be(e,r,t){var a=1,n=0,s=0,i=0,o=0,c=e.length,l=ee?new Uint16Array(32):me(32);for(s=0;s<32;++s)l[s]=0;for(s=c;s<t;++s)e[s]=0;c=e.length;var f=ee?new Uint16Array(c):me(c);for(s=0;s<c;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=o=o+l[s-1]<<1;for(s=0;s<c;++s)o=e[s],0!=o&&(f[s]=l[o+16]++);var h=0;for(s=0;s<c;++s)if(h=e[s],0!=h)for(o=ae(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)r[o|i<<h]=15&h|s<<4;return a}var ve=ee?new Uint16Array(512):me(512),ge=ee?new Uint16Array(32):me(32);if(!ee){for(var we=0;we<512;++we)ve[we]=0;for(we=0;we<32;++we)ge[we]=0}(function(){for(var e=[],r=0;r<32;r++)e.push(5);be(e,ge,32);var t=[];for(r=0;r<=143;r++)t.push(8);for(;r<=255;r++)t.push(9);for(;r<=279;r++)t.push(7);for(;r<=287;r++)t.push(8);be(t,ve,288)})();var ke=function(){for(var e=ee?new Uint8Array(32768):[],r=0,t=0;r<Z.length-1;++r)for(;t<Z[r+1];++t)e[t]=r;for(;t<32768;++t)e[t]=29;var a=ee?new Uint8Array(259):[];for(r=0,t=0;r<q.length-1;++r)for(;t<q[r+1];++t)a[t]=r;function n(e,r){var t=0;while(t<e.length){var a=Math.min(65535,e.length-t),n=t+a==e.length;r.write_shift(1,+n),r.write_shift(2,a),r.write_shift(2,65535&~a);while(a-- >0)r[r.l++]=e[t++]}return r.l}function s(r,t){var n=0,s=0,i=ee?new Uint16Array(32768):[];while(s<r.length){var o=Math.min(65535,r.length-s);if(o<10){n=fe(t,n,+!(s+o!=r.length)),7&n&&(n+=8-(7&n)),t.l=n/8|0,t.write_shift(2,o),t.write_shift(2,65535&~o);while(o-- >0)t[t.l++]=r[s++];n=8*t.l}else{n=fe(t,n,+!(s+o!=r.length)+2);var c=0;while(o-- >0){var l=r[s];c=32767&(c<<5^l);var f=-1,h=0;if((f=i[c])&&(f|=-32768&s,f>s&&(f-=32768),f<s))while(r[f+h]==r[s+h]&&h<250)++h;if(h>2){l=a[h],l<=22?n=ue(t,n,re[l+1]>>1)-1:(ue(t,n,3),n+=5,ue(t,n,re[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(de(t,n,h-q[l]),n+=u),l=e[s-f],n=ue(t,n,re[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(de(t,n,s-f-Z[l]),n+=d);for(var p=0;p<h;++p)i[c]=32767&s,c=32767&(c<<5^r[s]),++s;o-=h-1}else l<=143?l+=48:n=he(t,n,1),n=ue(t,n,re[l]),i[c]=32767&s,++s}n=ue(t,n,0)-1}}return t.l=(n+7)/8|0,t.l}return function(e,r){return e.length<8?n(e,r):s(e,r)}}();function Te(e){var r=da(50+Math.floor(1.1*e.length)),t=ke(e,r);return r.slice(0,t)}var Ee=ee?new Uint16Array(32768):me(32768),Se=ee?new Uint16Array(32768):me(32768),ye=ee?new Uint16Array(128):me(128),_e=1,Ae=1;function xe(e,r){var t=oe(e,r)+257;r+=5;var a=oe(e,r)+1;r+=5;var n=ie(e,r)+4;r+=4;for(var s=0,i=ee?new Uint8Array(19):me(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=1,l=ee?new Uint8Array(8):me(8),f=ee?new Uint8Array(8):me(8),h=i.length,u=0;u<n;++u)i[J[u]]=s=se(e,r),c<s&&(c=s),l[s]++,r+=3;var d=0;for(l[0]=0,u=1;u<=c;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(o[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(p=i[u],0!=p){d=re[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)ye[d|m<<p]=7&p|u<<3}var b=[];for(c=1;b.length<t+a;)switch(d=ye[ce(e,r)],r+=7&d,d>>>=3){case 16:s=3+ne(e,r),r+=2,d=b[b.length-1];while(s-- >0)b.push(d);break;case 17:s=3+se(e,r),r+=3;while(s-- >0)b.push(0);break;case 18:s=11+ce(e,r),r+=7;while(s-- >0)b.push(0);break;default:b.push(d),c<d&&(c=d);break}var v=b.slice(0,t),g=b.slice(t);for(u=t;u<286;++u)v[u]=0;for(u=a;u<30;++u)g[u]=0;return _e=be(v,Ee,286),Ae=be(g,Se,30),r}function Ce(e,r){if(3==e[0]&&!(3&e[1]))return[A(r),2];var t=0,a=0,n=x(r||1<<18),s=0,i=n.length>>>0,o=0,c=0;while(0==(1&a))if(a=se(e,t),t+=3,a>>>1!=0)for(a>>1==1?(o=9,c=5):(t=xe(e,t),o=_e,c=Ae);;){!r&&i<s+32767&&(n=pe(n,s+32767),i=n.length);var l=le(e,t,o),f=a>>>1==1?ve[l]:Ee[l];if(t+=15&f,f>>>=4,0===(f>>>8&255))n[s++]=f;else{if(256==f)break;f-=257;var h=f<8?0:f-4>>2;h>5&&(h=0);var u=s+q[f];h>0&&(u+=le(e,t,h),t+=h),l=le(e,t,c),f=a>>>1==1?ge[l]:Se[l],t+=15&f,f>>>=4;var d=f<4?0:f-2>>1,p=Z[f];d>0&&(p+=le(e,t,d),t+=d),!r&&i<u&&(n=pe(n,u+100),i=n.length);while(s<u)n[s]=n[s-p],++s}}else{7&t&&(t+=8-(7&t));var m=e[t>>>3]|e[1+(t>>>3)]<<8;if(t+=32,m>0){!r&&i<s+m&&(n=pe(n,s+m),i=n.length);while(m-- >0)n[s++]=e[t>>>3],t+=8}}return r?[n,t+7>>>3]:[n.slice(0,s),t+7>>>3]}function Re(e,r){var t=e.slice(e.l||0),a=Ce(t,r);return e.l+=a[1],a[0]}function Oe(e,r){if(!e)throw new Error(r);"undefined"!==typeof console&&console.error(r)}function Ie(e,r){var t=e;ha(t,0);var a=[],n=[],s={FileIndex:a,FullPaths:n};R(s,{root:r.root});var i=t.length-4;while((80!=t[i]||75!=t[i+1]||5!=t[i+2]||6!=t[i+3])&&i>=0)--i;t.l=i+4,t.l+=4;var c=t.read_shift(2);t.l+=6;var l=t.read_shift(4);for(t.l=l,i=0;i<c;++i){t.l+=20;var f=t.read_shift(4),h=t.read_shift(4),u=t.read_shift(2),d=t.read_shift(2),p=t.read_shift(2);t.l+=8;var m=t.read_shift(4),b=o(t.slice(t.l+u,t.l+u+d));t.l+=u+d+p;var v=t.l;t.l=m+4,Ne(t,f,h,s,b),t.l=v}return s}function Ne(e,r,t,a,n){e.l+=2;var s=e.read_shift(2),c=e.read_shift(2),l=i(e);if(8257&s)throw new Error("Unsupported ZIP encryption");for(var f=e.read_shift(4),h=e.read_shift(4),u=e.read_shift(4),d=e.read_shift(2),p=e.read_shift(2),m="",b=0;b<d;++b)m+=String.fromCharCode(e[e.l++]);if(p){var v=o(e.slice(e.l,e.l+p));(v[21589]||{}).mt&&(l=v[21589].mt),((n||{})[21589]||{}).mt&&(l=n[21589].mt)}e.l+=p;var g=e.slice(e.l,e.l+h);switch(c){case 8:g=Y(e,u);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+c)}var w=!1;8&s&&(f=e.read_shift(4),134695760==f&&(f=e.read_shift(4),w=!0),h=e.read_shift(4),u=e.read_shift(4)),h!=r&&Oe(w,"Bad compressed size: "+r+" != "+h),u!=t&&Oe(w,"Bad uncompressed size: "+t+" != "+u),ze(a,m,g,{unsafe:!0,mt:l})}function De(e,r){var t=r||{},a=[],n=[],i=da(1),o=t.compression?8:0,c=0,l=!1;l&&(c|=8);var f=0,h=0,u=0,d=0,p=e.FullPaths[0],m=p,b=e.FileIndex[0],v=[],g=0;for(f=1;f<e.FullPaths.length;++f)if(m=e.FullPaths[f].slice(p.length),b=e.FileIndex[f],b.size&&b.content&&"Sh33tJ5"!=m){var w=u,k=da(m.length);for(h=0;h<m.length;++h)k.write_shift(1,127&m.charCodeAt(h));k=k.slice(0,k.l),v[d]=Je.buf(b.content,0);var T=b.content;8==o&&(T=K(T)),i=da(30),i.write_shift(4,67324752),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),b.mt?s(i,b.mt):i.write_shift(4,0),i.write_shift(-4,8&c?0:v[d]),i.write_shift(4,8&c?0:T.length),i.write_shift(4,8&c?0:b.content.length),i.write_shift(2,k.length),i.write_shift(2,0),u+=i.length,a.push(i),u+=k.length,a.push(k),u+=T.length,a.push(T),8&c&&(i=da(12),i.write_shift(-4,v[d]),i.write_shift(4,T.length),i.write_shift(4,b.content.length),u+=i.l,a.push(i)),i=da(46),i.write_shift(4,33639248),i.write_shift(2,0),i.write_shift(2,20),i.write_shift(2,c),i.write_shift(2,o),i.write_shift(4,0),i.write_shift(-4,v[d]),i.write_shift(4,T.length),i.write_shift(4,b.content.length),i.write_shift(2,k.length),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(4,0),i.write_shift(4,w),g+=i.l,n.push(i),g+=k.length,n.push(k),++d}return i=da(22),i.write_shift(4,101010256),i.write_shift(2,0),i.write_shift(2,0),i.write_shift(2,d),i.write_shift(2,d),i.write_shift(4,g),i.write_shift(4,u),i.write_shift(2,0),D([D(a),D(n),i])}var Fe={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function Pe(e,r){if(e.ctype)return e.ctype;var t=e.name||"",a=t.match(/\.([^\.]+)$/);return a&&Fe[a[1]]||r&&(a=(t=r).match(/[\.\\]([^\.\\])+$/),a&&Fe[a[1]])?Fe[a[1]]:"application/octet-stream"}function Le(e){for(var r=E(e),t=[],a=0;a<r.length;a+=76)t.push(r.slice(a,a+76));return t.join("\r\n")+"\r\n"}function Me(e){var r=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var r=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==r.length?"0"+r:r)}));r=r.replace(/ $/gm,"=20").replace(/\t$/gm,"=09"),"\n"==r.charAt(0)&&(r="=0D"+r.slice(1)),r=r.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A");for(var t=[],a=r.split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var o=76,c=s.slice(i,i+o);"="==c.charAt(o-1)?o--:"="==c.charAt(o-2)?o-=2:"="==c.charAt(o-3)&&(o-=3),c=s.slice(i,i+o),i+=o,i<s.length&&(c+="="),t.push(c)}else t.push("")}return t.join("\r\n")}function Ue(e){for(var r=[],t=0;t<e.length;++t){var a=e[t];while(t<=e.length&&"="==a.charAt(a.length-1))a=a.slice(0,a.length-1)+e[++t];r.push(a)}for(var n=0;n<r.length;++n)r[n]=r[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return C(r.join("\r\n"))}function Be(e,r,t){for(var a,n="",s="",i="",o=0;o<10;++o){var c=r[o];if(!c||c.match(/^\s*$/))break;var l=c.match(/^(.*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim();break}}switch(++o,s.toLowerCase()){case"base64":a=C(S(r.slice(o).join("")));break;case"quoted-printable":a=Ue(r.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=ze(e,n.slice(t.length),a,{unsafe:!0});i&&(f.ctype=i)}function We(e,r){if("mime-version:"!=j(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var t=r&&r.root||"",a=(y&&Buffer.isBuffer(e)?e.toString("binary"):j(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),t||(t=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,t.length)!=t))while(t.length>0)if(t=t.slice(0,t.length-1),t=t.slice(0,t.lastIndexOf("/")+1),s.slice(0,t.length)==t)break;var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var o="--"+(i[1]||""),c=[],l=[],f={FileIndex:c,FullPaths:l};R(f);var h,u=0;for(n=0;n<a.length;++n){var d=a[n];d!==o&&d!==o+"--"||(u++&&Be(f,a.slice(h,n),t),h=n)}return f}function He(e,r){var t=r||{},a=t.boundary||"SheetJS";a="------="+a;for(var n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+a.slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),o=e.FileIndex[c],o.size&&o.content&&"Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=o.content,f=y&&Buffer.isBuffer(l)?l.toString("binary"):j(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(t.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+Pe(o,i)),n.push(""),n.push(m?Me(f):Le(f))}return n.push(a+"--\r\n"),n.join("\r\n")}function Ve(e){var r={};return R(r,e),r}function ze(e,r,t,a){var s=a&&a.unsafe;s||R(e);var i=!s&&qe.find(e,r);if(!i){var o=e.FullPaths[0];r.slice(0,o.length)==o?o=r:("/"!=o.slice(-1)&&(o+="/"),o=(o+r).replace("//","/")),i={name:n(r),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||qe.utils.cfb_gc(e)}return i.content=t,i.size=t?t.length:0,a&&(a.CLSID&&(i.clsid=a.CLSID),a.mt&&(i.mt=a.mt),a.ct&&(i.ct=a.ct)),i}function Ge(e,r){R(e);var t=qe.find(e,r);if(t)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==t)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1}function je(e,r,t){R(e);var a=qe.find(e,r);if(a)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==a)return e.FileIndex[s].name=n(t),e.FullPaths[s]=t,!0;return!1}function Xe(e){I(e,!0)}return r.find=F,r.read=T,r.parse=l,r.write=X,r.writeFile=G,r.utils={cfb_new:Ve,cfb_add:ze,cfb_del:Ge,cfb_mov:je,cfb_gc:Xe,ReadShift:sa,CheckField:fa,prep_blob:ha,bconcat:D,use_zlib:$,_deflateRaw:Te,_inflateRaw:Re,consts:z},r}();let Ze=void 0;function Qe(e){return"string"===typeof e?R(e):Array.isArray(e)?I(e):e}function er(e,r,t){if("undefined"!==typeof Ze&&Ze.writeFileSync)return t?Ze.writeFileSync(e,r,t):Ze.writeFileSync(e,r);if("undefined"!==typeof Deno){if(t&&"string"==typeof r)switch(t){case"utf8":r=new TextEncoder(t).encode(r);break;case"binary":r=R(r);break;default:throw new Error("Unsupported encoding "+t)}return Deno.writeFileSync(e,r)}var a="utf8"==t?ht(r):r;if("undefined"!==typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!==typeof Blob){var n=new Blob([Qe(a)],{type:"application/octet-stream"});if("undefined"!==typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if("undefined"!==typeof saveAs)return saveAs(n,e);if("undefined"!==typeof URL&&"undefined"!==typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(n);if("object"===typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var i=document.createElement("a");if(null!=i.download)return i.download=e,i.href=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),s}}if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(r)&&(r=O(r)),o.write(r),o.close(),r}catch(c){if(!c.message||!c.message.match(/onstruct/))throw c}throw new Error("cannot save file "+e)}function rr(e){if("undefined"!==typeof Ze)return Ze.readFileSync(e);if("undefined"!==typeof Deno)return Deno.readFileSync(e);if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var r=File(e);r.open("r"),r.encoding="binary";var t=r.read();return r.close(),t}catch(a){if(!a.message||!a.message.match(/onstruct/))throw a}throw new Error("Cannot access file "+e)}function tr(e){for(var r=Object.keys(e),t=[],a=0;a<r.length;++a)Object.prototype.hasOwnProperty.call(e,r[a])&&t.push(r[a]);return t}function ar(e,r){for(var t=[],a=tr(e),n=0;n!==a.length;++n)null==t[e[a[n]][r]]&&(t[e[a[n]][r]]=a[n]);return t}function nr(e){for(var r=[],t=tr(e),a=0;a!==t.length;++a)r[e[t[a]]]=t[a];return r}function sr(e){for(var r=[],t=tr(e),a=0;a!==t.length;++a)r[e[t[a]]]=parseInt(t[a],10);return r}function ir(e){for(var r=[],t=tr(e),a=0;a!==t.length;++a)null==r[e[t[a]]]&&(r[e[t[a]]]=[]),r[e[t[a]]].push(t[a]);return r}var or=new Date(1899,11,30,0,0,0);function cr(e,r){var t=e.getTime();r&&(t-=1263168e5);var a=or.getTime()+6e4*(e.getTimezoneOffset()-or.getTimezoneOffset());return(t-a)/864e5}var lr=new Date,fr=or.getTime()+6e4*(lr.getTimezoneOffset()-or.getTimezoneOffset()),hr=lr.getTimezoneOffset();function ur(e){var r=new Date;return r.setTime(24*e*60*60*1e3+fr),r.getTimezoneOffset()!==hr&&r.setTime(r.getTime()+6e4*(r.getTimezoneOffset()-hr)),r}function dr(e){var r=0,t=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(t=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":t*=24;case"H":t*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");t*=60;case"S":break}r+=t*parseInt(n[s],10)}return r}var pr=new Date("2017-02-19T19:06:09.000Z"),mr=isNaN(pr.getFullYear())?new Date("2/19/17"):pr,br=2017==mr.getFullYear();function vr(e,r){var t=new Date(e);if(br)return r>0?t.setTime(t.getTime()+60*t.getTimezoneOffset()*1e3):r<0&&t.setTime(t.getTime()-60*t.getTimezoneOffset()*1e3),t;if(e instanceof Date)return e;if(1917==mr.getFullYear()&&!isNaN(t.getFullYear())){var a=t.getFullYear();return e.indexOf(""+a)>-1||t.setFullYear(t.getFullYear()+100),t}var n=e.match(/\d+/g)||["2017","2","19","0","0","0"],s=new Date(+n[0],+n[1]-1,+n[2],+n[3]||0,+n[4]||0,+n[5]||0);return e.indexOf("Z")>-1&&(s=new Date(s.getTime()-60*s.getTimezoneOffset()*1e3)),s}function gr(e,r){if(y&&Buffer.isBuffer(e)){if(r){if(255==e[0]&&254==e[1])return ht(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return ht(p(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!==typeof TextDecoder)try{if(r){if(255==e[0]&&254==e[1])return ht(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return ht(new TextDecoder("utf-16be").decode(e.slice(2)))}var t={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return t[e]||e}))}catch(s){}for(var a=[],n=0;n!=e.length;++n)a.push(String.fromCharCode(e[n]));return a.join("")}function wr(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var r={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=wr(e[t]));return r}function kr(e,r){var t="";while(t.length<r)t+=e;return t}function Tr(e){var r=Number(e);if(!isNaN(r))return isFinite(r)?r:NaN;if(!/\d/.test(e))return r;var t=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return t*=100,""}));return isNaN(r=Number(a))?(a=a.replace(/[(](.*)[)]/,(function(e,r){return t=-t,r})),isNaN(r=Number(a))?r:r/t):r/t}var Er=["january","february","march","april","may","june","july","august","september","october","november","december"];function Sr(e){var r=new Date(e),t=new Date(NaN),a=r.getYear(),n=r.getMonth(),s=r.getDate();if(isNaN(s))return t;var i=e.toLowerCase();if(i.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if(i=i.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,""),i.length>3&&-1==Er.indexOf(i))return t}else if(i.match(/[a-z]/))return t;return a<0||a>8099?t:(n>0||s>1)&&101!=a?r:e.match(/[^-0-9:,\/\\]/)?t:r}var yr=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(r,t,a){if(e||"string"==typeof t)return r.split(t);for(var n=r.split(t),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function _r(e){return e?e.content&&e.type?gr(e.content,!0):e.data?b(e.data):e.asNodeBuffer&&y?b(e.asNodeBuffer().toString("binary")):e.asBinary?b(e.asBinary()):e._data&&e._data.getContent?b(gr(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function Ar(e){if(!e)return null;if(e.data)return u(e.data);if(e.asNodeBuffer&&y)return e.asNodeBuffer();if(e._data&&e._data.getContent){var r=e._data.getContent();return"string"==typeof r?u(r):Array.prototype.slice.call(r)}return e.content&&e.type?e.content:null}function xr(e){return e&&".bin"===e.name.slice(-4)?Ar(e):_r(e)}function Cr(e,r){for(var t=e.FullPaths||tr(e.files),a=r.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<t.length;++s){var i=t[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[t[s]]:e.FileIndex[s]}return null}function Rr(e,r){var t=Cr(e,r);if(null==t)throw new Error("Cannot find file "+r+" in zip");return t}function Or(e,r,t){if(!t)return xr(Rr(e,r));if(!r)return null;try{return Or(e,r)}catch(a){return null}}function Ir(e,r,t){if(!t)return _r(Rr(e,r));if(!r)return null;try{return Ir(e,r)}catch(a){return null}}function Nr(e,r,t){if(!t)return Ar(Rr(e,r));if(!r)return null;try{return Nr(e,r)}catch(a){return null}}function Dr(e){for(var r=e.FullPaths||tr(e.files),t=[],a=0;a<r.length;++a)"/"!=r[a].slice(-1)&&t.push(r[a].replace(/^Root Entry[\/]/,""));return t.sort()}function Fr(e,r,t){if(e.FullPaths){var a;if("string"==typeof t)return a=y?_(t):F(t),qe.utils.cfb_add(e,r,a);qe.utils.cfb_add(e,r,t)}else e.file(r,t)}function Pr(){return qe.utils.cfb_new()}function Lr(e,r){switch(r.type){case"base64":return qe.read(e,{type:"base64"});case"binary":return qe.read(e,{type:"binary"});case"buffer":case"array":return qe.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+r.type)}function Mr(e,r){if("/"==e.charAt(0))return e.slice(1);var t=r.split("/");"/"!=r.slice(-1)&&t.pop();var a=e.split("/");while(0!==a.length){var n=a.shift();".."===n?t.pop():"."!==n&&t.push(n)}return t.join("/")}var Ur='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',Br=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,Wr=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s*[\/\?]?>/gm,Hr=/<[^>]*>/g,Vr=Ur.match(Wr)?Wr:Hr,zr=/<\w*:/,Gr=/<(\/?)\w+:/;function jr(e,r,t){for(var a={},n=0,s=0;n!==e.length;++n)if(32===(s=e.charCodeAt(n))||10===s||13===s)break;if(r||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(Br),o=0,c="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l],s=0;s!=h.length;++s)if(61===h.charCodeAt(s))break;f=h.slice(0,s).trim();while(32==h.charCodeAt(s+1))++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,c=h.slice(s+1+u,h.length-u),o=0;o!=f.length;++o)if(58===f.charCodeAt(o))break;if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,t||(a[f.toLowerCase()]=c);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=c,t||(a[d.toLowerCase()]=c)}}return a}function Xr(e){return e.replace(Gr,"<$1")}var $r={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Yr=nr($r),Kr=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,r=/_x([\da-fA-F]{4})_/gi;return function t(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,r){return $r[e]||String.fromCharCode(parseInt(r,e.indexOf("x")>-1?16:10))||e})).replace(r,(function(e,r){return String.fromCharCode(parseInt(r,16))}));var i=n.indexOf("]]>");return t(n.slice(0,s))+n.slice(s+9,i)+t(n.slice(i+3))}}(),Jr=/[&<>'"]/g,qr=/[\u0000-\u0008\u000b-\u001f]/g;function Zr(e){var r=e+"";return r.replace(Jr,(function(e){return Yr[e]})).replace(qr,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function Qr(e){return Zr(e).replace(/ /g,"_x0020_")}var et=/[\u0000-\u001f]/g;function rt(e){var r=e+"";return r.replace(Jr,(function(e){return Yr[e]})).replace(/\n/g,"<br/>").replace(et,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}function tt(e){var r=e+"";return r.replace(Jr,(function(e){return Yr[e]})).replace(et,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var at=function(){var e=/&#(\d+);/g;function r(e,r){return String.fromCharCode(parseInt(r,10))}return function(t){return t.replace(e,r)}}();function nt(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")}function st(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function it(e){var r="",t=0,a=0,n=0,s=0,i=0,o=0;while(t<e.length)a=e.charCodeAt(t++),a<128?r+=String.fromCharCode(a):(n=e.charCodeAt(t++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,r+=String.fromCharCode(i)):(s=e.charCodeAt(t++),a<240?r+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(i=e.charCodeAt(t++),o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&i)-65536,r+=String.fromCharCode(55296+(o>>>10&1023)),r+=String.fromCharCode(56320+(1023&o)))));return r}function ot(e){var r,t,a,n=A(2*e.length),s=1,i=0,o=0;for(t=0;t<e.length;t+=s)s=1,(a=e.charCodeAt(t))<128?r=a:a<224?(r=64*(31&a)+(63&e.charCodeAt(t+1)),s=2):a<240?(r=4096*(15&a)+64*(63&e.charCodeAt(t+1))+(63&e.charCodeAt(t+2)),s=3):(s=4,r=262144*(7&a)+4096*(63&e.charCodeAt(t+1))+64*(63&e.charCodeAt(t+2))+(63&e.charCodeAt(t+3)),r-=65536,o=55296+(r>>>10&1023),r=56320+(1023&r)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=r%256,n[i++]=r>>>8;return n.slice(0,i).toString("ucs2")}function ct(e){return _(e,"binary").toString("utf8")}var lt="foo bar bazâð£",ft=y&&(ct(lt)==it(lt)&&ct||ot(lt)==it(lt)&&ot)||it,ht=y?function(e){return _(e,"utf8").toString("binary")}:function(e){var r=[],t=0,a=0,n=0;while(t<e.length)switch(a=e.charCodeAt(t++),!0){case a<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6))),r.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(t++)-56320+(a<<10),r.push(String.fromCharCode(240+(n>>18&7))),r.push(String.fromCharCode(144+(n>>12&63))),r.push(String.fromCharCode(128+(n>>6&63))),r.push(String.fromCharCode(128+(63&n)));break;default:r.push(String.fromCharCode(224+(a>>12))),r.push(String.fromCharCode(128+(a>>6&63))),r.push(String.fromCharCode(128+(63&a)))}return r.join("")},ut=function(){var e={};return function(r,t){var a=r+"|"+(t||"");return e[a]?e[a]:e[a]=new RegExp("<(?:\\w+:)?"+r+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+r+">",t||"")}}(),dt=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(r){for(var t=r.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),a=0;a<e.length;++a)t=t.replace(e[a][0],e[a][1]);return t}}(),pt=function(){var e={};return function(r){return void 0!==e[r]?e[r]:e[r]=new RegExp("<(?:vt:)?"+r+">([\\s\\S]*?)</(?:vt:)?"+r+">","g")}}(),mt=/<\/?(?:vt:)?variant>/g,bt=/<(?:vt:)([^>]*)>([\s\S]*)</;function vt(e,r){var t=jr(e),a=e.match(pt(t.baseType))||[],n=[];if(a.length!=t.size){if(r.WTF)throw new Error("unexpected vector length "+a.length+" != "+t.size);return n}return a.forEach((function(e){var r=e.replace(mt,"").match(bt);r&&n.push({v:ft(r[2]),t:r[1]})})),n}var gt=/(^\s|\s$|\n)/;function wt(e,r){return"<"+e+(r.match(gt)?' xml:space="preserve"':"")+">"+r+"</"+e+">"}function kt(e){return tr(e).map((function(r){return" "+r+'="'+e[r]+'"'})).join("")}function Tt(e,r,t){return"<"+e+(null!=t?kt(t):"")+(null!=r?(r.match(gt)?' xml:space="preserve"':"")+">"+r+"</"+e:"/")+">"}function Et(e,r){try{return e.toISOString().replace(/\.\d*/,"")}catch(t){if(r)throw t}return""}function St(e,r){switch(typeof e){case"string":var t=Tt("vt:lpwstr",Zr(e));return r&&(t=t.replace(/&quot;/g,"_x0022_")),t;case"number":return Tt((0|e)==e?"vt:i4":"vt:r8",Zr(String(e)));case"boolean":return Tt("vt:bool",e?"true":"false")}if(e instanceof Date)return Tt("vt:filetime",Et(e));throw new Error("Unable to serialize "+e)}function yt(e){if(y&&Buffer.isBuffer(e))return e.toString("utf8");if("string"===typeof e)return e;if("undefined"!==typeof Uint8Array&&e instanceof Uint8Array)return ft(O(N(e)));throw new Error("Bad input format: expected Buffer or string")}var _t=/<(\/?)([^\s?><!\/:]*:|)([^\s?<>:\/]+)(?:[\s?:\/][^>]*)?>/gm,At={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},xt=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],Ct={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};function Rt(e,r){for(var t=1-2*(e[r+7]>>>7),a=((127&e[r+7])<<4)+(e[r+6]>>>4&15),n=15&e[r+6],s=5;s>=0;--s)n=256*n+e[r+s];return 2047==a?0==n?t*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),t*Math.pow(2,a-52)*n)}function Ot(e,r,t){var a=(r<0||1/r==-1/0?1:0)<<7,n=0,s=0,i=a?-r:r;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(r)?26985:0);for(var o=0;o<=5;++o,s/=256)e[t+o]=255&s;e[t+6]=(15&n)<<4|15&s,e[t+7]=n>>4|a}var It=function(e){for(var r=[],t=10240,a=0;a<e[0].length;++a)if(e[0][a])for(var n=0,s=e[0][a].length;n<s;n+=t)r.push.apply(r,e[0][a].slice(n,n+t));return r},Nt=y?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:_(e)}))):It(e)}:It,Dt=function(e,r,t){for(var a=[],n=r;n<t;n+=2)a.push(String.fromCharCode(ea(e,n)));return a.join("").replace(P,"")},Ft=y?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf16le",r,t).replace(P,""):Dt(e,r,t)}:Dt,Pt=function(e,r,t){for(var a=[],n=r;n<r+t;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},Lt=y?function(e,r,t){return Buffer.isBuffer(e)?e.toString("hex",r,r+t):Pt(e,r,t)}:Pt,Mt=function(e,r,t){for(var a=[],n=r;n<t;n++)a.push(String.fromCharCode(Qt(e,n)));return a.join("")},Ut=y?function(e,r,t){return Buffer.isBuffer(e)?e.toString("utf8",r,t):Mt(e,r,t)}:Mt,Bt=function(e,r){var t=ta(e,r);return t>0?Ut(e,r+4,r+4+t-1):""},Wt=Bt,Ht=function(e,r){var t=ta(e,r);return t>0?Ut(e,r+4,r+4+t-1):""},Vt=Ht,zt=function(e,r){var t=2*ta(e,r);return t>0?Ut(e,r+4,r+4+t-1):""},Gt=zt,jt=function(e,r){var t=ta(e,r);return t>0?Ft(e,r+4,r+4+t):""},Xt=jt,$t=function(e,r){var t=ta(e,r);return t>0?Ut(e,r+4,r+4+t):""},Yt=$t,Kt=function(e,r){return Rt(e,r)},Jt=Kt,qt=function(e){return Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array};function Zt(){Ft=function(e,r,t){return m.utils.decode(1200,e.slice(r,t)).replace(P,"")},Ut=function(e,r,t){return m.utils.decode(65001,e.slice(r,t))},Wt=function(e,r){var t=ta(e,r);return t>0?m.utils.decode(s,e.slice(r+4,r+4+t-1)):""},Vt=function(e,r){var t=ta(e,r);return t>0?m.utils.decode(n,e.slice(r+4,r+4+t-1)):""},Gt=function(e,r){var t=2*ta(e,r);return t>0?m.utils.decode(1200,e.slice(r+4,r+4+t-1)):""},Xt=function(e,r){var t=ta(e,r);return t>0?m.utils.decode(1200,e.slice(r+4,r+4+t)):""},Yt=function(e,r){var t=ta(e,r);return t>0?m.utils.decode(65001,e.slice(r+4,r+4+t)):""}}y&&(Wt=function(e,r){if(!Buffer.isBuffer(e))return Bt(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},Vt=function(e,r){if(!Buffer.isBuffer(e))return Ht(e,r);var t=e.readUInt32LE(r);return t>0?e.toString("utf8",r+4,r+4+t-1):""},Gt=function(e,r){if(!Buffer.isBuffer(e))return zt(e,r);var t=2*e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t-1)},Xt=function(e,r){if(!Buffer.isBuffer(e))return jt(e,r);var t=e.readUInt32LE(r);return e.toString("utf16le",r+4,r+4+t)},Yt=function(e,r){if(!Buffer.isBuffer(e))return $t(e,r);var t=e.readUInt32LE(r);return e.toString("utf8",r+4,r+4+t)},Jt=function(e,r){return Buffer.isBuffer(e)?e.readDoubleLE(r):Kt(e,r)},qt=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array}),"undefined"!==typeof m&&Zt();var Qt=function(e,r){return e[r]},ea=function(e,r){return 256*e[r+1]+e[r]},ra=function(e,r){var t=256*e[r+1]+e[r];return t<32768?t:-1*(65535-t+1)},ta=function(e,r){return e[r+3]*(1<<24)+(e[r+2]<<16)+(e[r+1]<<8)+e[r]},aa=function(e,r){return e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]},na=function(e,r){return e[r]<<24|e[r+1]<<16|e[r+2]<<8|e[r+3]};function sa(e,r){var t,a,s,i,o,c,l="",f=[];switch(r){case"dbcs":if(c=this.l,y&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(o=0;o<e;++o)l+=String.fromCharCode(ea(this,c)),c+=2;e*=2;break;case"utf8":l=Ut(this,this.l,this.l+e);break;case"utf16le":e*=2,l=Ft(this,this.l,this.l+e);break;case"wstr":if("undefined"===typeof m)return sa.call(this,e,"dbcs");l=m.utils.decode(n,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":l=Wt(this,this.l),e=4+ta(this,this.l);break;case"lpstr-cp":l=Vt(this,this.l),e=4+ta(this,this.l);break;case"lpwstr":l=Gt(this,this.l),e=4+2*ta(this,this.l);break;case"lpp4":e=4+ta(this,this.l),l=Xt(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+ta(this,this.l),l=Yt(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":e=0,l="";while(0!==(s=Qt(this,this.l+e++)))f.push(v(s));l=f.join("");break;case"_wstr":e=0,l="";while(0!==(s=ea(this,this.l+e)))f.push(v(s)),e+=2;e+=2,l=f.join("");break;case"dbcs-cont":for(l="",c=this.l,o=0;o<e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=Qt(this,c),this.l=c+1,i=sa.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(v(ea(this,c))),c+=2}l=f.join(""),e*=2;break;case"cpstr":if("undefined"!==typeof m){l=m.utils.decode(n,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(l="",c=this.l,o=0;o!=e;++o){if(this.lens&&-1!==this.lens.indexOf(c))return s=Qt(this,c),this.l=c+1,i=sa.call(this,e-o,s?"dbcs-cont":"sbcs-cont"),f.join("")+i;f.push(v(Qt(this,c))),c+=1}l=f.join("");break;default:switch(e){case 1:return t=Qt(this,this.l),this.l++,t;case 2:return t=("i"===r?ra:ea)(this,this.l),this.l+=2,t;case 4:case-4:return"i"===r||0===(128&this[this.l+3])?(t=(e>0?aa:na)(this,this.l),this.l+=4,t):(a=ta(this,this.l),this.l+=4,a);case 8:case-8:if("f"===r)return a=8==e?Jt(this,this.l):Jt([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,a;e=8;case 16:l=Lt(this,this.l,e);break}}return this.l+=e,l}var ia=function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255,e[t+2]=r>>>16&255,e[t+3]=r>>>24&255},oa=function(e,r,t){e[t]=255&r,e[t+1]=r>>8&255,e[t+2]=r>>16&255,e[t+3]=r>>24&255},ca=function(e,r,t){e[t]=255&r,e[t+1]=r>>>8&255};function la(e,r,t){var a=0,n=0;if("dbcs"===t){for(n=0;n!=r.length;++n)ca(this,r.charCodeAt(n),this.l+2*n);a=2*r.length}else if("sbcs"===t){if("undefined"!==typeof m&&874==s)for(n=0;n!=r.length;++n){var i=m.utils.encode(s,r.charAt(n));this[this.l+n]=i[0]}else for(r=r.replace(/[^\x00-\x7F]/g,"_"),n=0;n!=r.length;++n)this[this.l+n]=255&r.charCodeAt(n);a=r.length}else{if("hex"===t){for(;n<e;++n)this[this.l++]=parseInt(r.slice(2*n,2*n+2),16)||0;return this}if("utf16le"===t){var o=Math.min(this.l+e,this.length);for(n=0;n<Math.min(r.length,e);++n){var c=r.charCodeAt(n);this[this.l++]=255&c,this[this.l++]=c>>8}while(this.l<o)this[this.l++]=0;return this}switch(e){case 1:a=1,this[this.l]=255&r;break;case 2:a=2,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r;break;case 3:a=3,this[this.l]=255&r,r>>>=8,this[this.l+1]=255&r,r>>>=8,this[this.l+2]=255&r;break;case 4:a=4,ia(this,r,this.l);break;case 8:if(a=8,"f"===t){Ot(this,r,this.l);break}case 16:break;case-4:a=4,oa(this,r,this.l);break}}return this.l+=a,this}function fa(e,r){var t=Lt(this,this.l,e.length>>1);if(t!==e)throw new Error(r+"Expected "+e+" saw "+t);this.l+=e.length>>1}function ha(e,r){e.l=r,e.read_shift=sa,e.chk=fa,e.write_shift=la}function ua(e,r){e.l+=r}function da(e){var r=A(e);return ha(r,0),r}function pa(e,r,t){if(e){var a,n,s;ha(e,e.l||0);var i=e.length,o=0,c=0;while(e.l<i){o=e.read_shift(1),128&o&&(o=(127&o)+((127&e.read_shift(1))<<7));var l=uv[o]||uv[65535];for(a=e.read_shift(1),s=127&a,n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,t);if(e.l=c,r(f,l,o))return}}}function ma(){var e=[],r=y?256:2048,t=function(e){var r=da(e);return ha(r,0),r},a=t(r),n=function(){a&&(a.length>a.l&&(a=a.slice(0,a.l),a.l=a.length),a.length>0&&e.push(a),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=t(Math.max(e+1,r)))},i=function(){return n(),D(e)},o=function(e){n(),a=e,null==a.l&&(a.l=a.length),s(r)};return{next:s,push:o,end:i,_bufs:e}}function ba(e,r,t,a){var n,s=+r;if(!isNaN(s)){a||(a=uv[s].p||(t||[]).length||0),n=1+(s>=128?1:0)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,128+(127&s)),i.write_shift(1,s>>7));for(var o=0;4!=o;++o){if(!(a>=128)){i.write_shift(1,a);break}i.write_shift(1,128+(127&a)),a>>=7}a>0&&qt(t)&&e.push(t)}}function va(e,r,t){var a=wr(e);if(r.s?(a.cRel&&(a.c+=r.s.c),a.rRel&&(a.r+=r.s.r)):(a.cRel&&(a.c+=r.c),a.rRel&&(a.r+=r.r)),!t||t.biff<12){while(a.c>=256)a.c-=256;while(a.r>=65536)a.r-=65536}return a}function ga(e,r,t){var a=wr(e);return a.s=va(a.s,r.s,t),a.e=va(a.e,r.s,t),a}function wa(e,r){if(e.cRel&&e.c<0){e=wr(e);while(e.c<0)e.c+=r>8?16384:256}if(e.rRel&&e.r<0){e=wr(e);while(e.r<0)e.r+=r>8?1048576:r>5?65536:16384}var t=Ia(e);return e.cRel||null==e.cRel||(t=xa(t)),e.rRel||null==e.rRel||(t=Sa(t)),t}function ka(e,r){return 0!=e.s.r||e.s.rRel||e.e.r!=(r.biff>=12?1048575:r.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(r.biff>=12?16383:255)||e.e.cRel?wa(e.s,r.biff)+":"+wa(e.e,r.biff):(e.s.rRel?"":"$")+Ea(e.s.r)+":"+(e.e.rRel?"":"$")+Ea(e.e.r):(e.s.cRel?"":"$")+Aa(e.s.c)+":"+(e.e.cRel?"":"$")+Aa(e.e.c)}function Ta(e){return parseInt(ya(e),10)-1}function Ea(e){return""+(e+1)}function Sa(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}function ya(e){return e.replace(/\$(\d+)$/,"$1")}function _a(e){for(var r=Ca(e),t=0,a=0;a!==r.length;++a)t=26*t+r.charCodeAt(a)-64;return t-1}function Aa(e){if(e<0)throw new Error("invalid column "+e);var r="";for(++e;e;e=Math.floor((e-1)/26))r=String.fromCharCode((e-1)%26+65)+r;return r}function xa(e){return e.replace(/^([A-Z])/,"$$$1")}function Ca(e){return e.replace(/^\$([A-Z])/,"$1")}function Ra(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Oa(e){for(var r=0,t=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?r=10*r+(n-48):n>=65&&n<=90&&(t=26*t+(n-64))}return{c:t-1,r:r-1}}function Ia(e){for(var r=e.c+1,t="";r;r=(r-1)/26|0)t=String.fromCharCode((r-1)%26+65)+t;return t+(e.r+1)}function Na(e){var r=e.indexOf(":");return-1==r?{s:Oa(e),e:Oa(e)}:{s:Oa(e.slice(0,r)),e:Oa(e.slice(r+1))}}function Da(e,r){return"undefined"===typeof r||"number"===typeof r?Da(e.s,e.e):("string"!==typeof e&&(e=Ia(e)),"string"!==typeof r&&(r=Ia(r)),e==r?e:e+":"+r)}function Fa(e){var r={s:{c:0,r:0},e:{c:0,r:0}},t=0,a=0,n=0,s=e.length;for(t=0;a<s;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}for(r.s.c=--t,t=0;a<s;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}if(r.s.r=--t,a===s||10!=n)return r.e.c=r.s.c,r.e.r=r.s.r,r;for(++a,t=0;a!=s;++a){if((n=e.charCodeAt(a)-64)<1||n>26)break;t=26*t+n}for(r.e.c=--t,t=0;a!=s;++a){if((n=e.charCodeAt(a)-48)<0||n>9)break;t=10*t+n}return r.e.r=--t,r}function Pa(e,r){var t="d"==e.t&&r instanceof Date;if(null!=e.z)try{return e.w=Ve(e.z,t?cr(r):r)}catch(a){}try{return e.w=Ve((e.XF||{}).numFmtId||(t?14:0),t?cr(r):r)}catch(a){return""+r}}function La(e,r,t){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&t&&t.dateNF&&(e.z=t.dateNF),"e"==e.t?zn[e.v]||e.v:Pa(e,void 0==r?e.v:r))}function Ma(e,r){var t=r&&r.sheet?r.sheet:"Sheet1",a={};return a[t]=e,{SheetNames:[t],Sheets:a}}function Ua(e,r,t){var a=t||{},n=e?Array.isArray(e):a.dense;null!=w&&null==n&&(n=w);var s=e||(n?[]:{}),i=0,o=0;if(s&&null!=a.origin){if("number"==typeof a.origin)i=a.origin;else{var c="string"==typeof a.origin?Oa(a.origin):a.origin;i=c.r,o=c.c}s["!ref"]||(s["!ref"]="A1:A1")}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=Fa(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=f.e.r+1)}for(var h=0;h!=r.length;++h)if(r[h]){if(!Array.isArray(r[h]))throw new Error("aoa_to_sheet expects an array of arrays");for(var u=0;u!=r[h].length;++u)if("undefined"!==typeof r[h][u]){var d={v:r[h][u]},p=i+h,m=o+u;if(l.s.r>p&&(l.s.r=p),l.s.c>m&&(l.s.c=m),l.e.r<p&&(l.e.r=p),l.e.c<m&&(l.e.c=m),!r[h][u]||"object"!==typeof r[h][u]||Array.isArray(r[h][u])||r[h][u]instanceof Date)if(Array.isArray(d.v)&&(d.f=r[h][u][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(a.nullError)d.t="e",d.v=0;else{if(!a.sheetStubs)continue;d.t="z"}else"number"===typeof d.v?d.t="n":"boolean"===typeof d.v?d.t="b":d.v instanceof Date?(d.z=a.dateNF||J[14],a.cellDates?(d.t="d",d.w=Ve(d.z,cr(d.v))):(d.t="n",d.v=cr(d.v),d.w=Ve(d.z,d.v))):d.t="s";else d=r[h][u];if(n)s[p]||(s[p]=[]),s[p][m]&&s[p][m].z&&(d.z=s[p][m].z),s[p][m]=d;else{var b=Ia({c:m,r:p});s[b]&&s[b].z&&(d.z=s[b].z),s[b]=d}}}return l.s.c<1e7&&(s["!ref"]=Da(l)),s}function Ba(e,r){return Ua(null,e,r)}function Wa(e){return e.read_shift(4,"i")}function Ha(e,r){return r||(r=da(4)),r.write_shift(4,e),r}function Va(e){var r=e.read_shift(4);return 0===r?"":e.read_shift(r,"dbcs")}function za(e,r){var t=!1;return null==r&&(t=!0,r=da(4+2*e.length)),r.write_shift(4,e.length),e.length>0&&r.write_shift(0,e,"dbcs"),t?r.slice(0,r.l):r}function Ga(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function ja(e,r){return r||(r=da(4)),r.write_shift(2,e.ich||0),r.write_shift(2,e.ifnt||0),r}function Xa(e,r){var t=e.l,a=e.read_shift(1),n=Va(e),s=[],i={t:n,h:n};if(0!==(1&a)){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push(Ga(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=t+r,i}function $a(e,r){var t=!1;return null==r&&(t=!0,r=da(15+4*e.t.length)),r.write_shift(1,0),za(e.t,r),t?r.slice(0,r.l):r}var Ya=Xa;function Ka(e,r){var t=!1;return null==r&&(t=!0,r=da(23+4*e.t.length)),r.write_shift(1,1),za(e.t,r),r.write_shift(4,1),ja({ich:0,ifnt:0},r),t?r.slice(0,r.l):r}function Ja(e){var r=e.read_shift(4),t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:r,iStyleRef:t}}function qa(e,r){return null==r&&(r=da(8)),r.write_shift(-4,e.c),r.write_shift(3,e.iStyleRef||e.s),r.write_shift(1,0),r}function Za(e){var r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:r}}function Qa(e,r){return null==r&&(r=da(4)),r.write_shift(3,e.iStyleRef||e.s),r.write_shift(1,0),r}var en=Va,rn=za;function tn(e){var r=e.read_shift(4);return 0===r||4294967295===r?"":e.read_shift(r,"dbcs")}function an(e,r){var t=!1;return null==r&&(t=!0,r=da(127)),r.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&r.write_shift(0,e,"dbcs"),t?r.slice(0,r.l):r}var nn=Va,sn=tn,on=an;function cn(e){var r=e.slice(e.l,e.l+4),t=1&r[0],a=2&r[0];e.l+=4;var n=0===a?Jt([0,0,0,0,252&r[0],r[1],r[2],r[3]],0):aa(r,0)>>2;return t?n/100:n}function ln(e,r){null==r&&(r=da(4));var t=0,a=0,n=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?a=1:n==(0|n)&&n>=-(1<<29)&&n<1<<29&&(a=1,t=1),!a)throw new Error("unsupported RkNumber "+e);r.write_shift(-4,((t?n:e)<<2)+(t+2))}function fn(e){var r={s:{},e:{}};return r.s.r=e.read_shift(4),r.e.r=e.read_shift(4),r.s.c=e.read_shift(4),r.e.c=e.read_shift(4),r}function hn(e,r){return r||(r=da(16)),r.write_shift(4,e.s.r),r.write_shift(4,e.e.r),r.write_shift(4,e.s.c),r.write_shift(4,e.e.c),r}var un=fn,dn=hn;function pn(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function mn(e,r){return(r||da(8)).write_shift(8,e,"f")}function bn(e){var r={},t=e.read_shift(1),a=t>>>1,n=e.read_shift(1),s=e.read_shift(2,"i"),i=e.read_shift(1),o=e.read_shift(1),c=e.read_shift(1);switch(e.l++,a){case 0:r.auto=1;break;case 1:r.index=n;var l=Vn[n];l&&(r.rgb=bl(l));break;case 2:r.rgb=bl([i,o,c]);break;case 3:r.theme=n;break}return 0!=s&&(r.tint=s>0?s/32767:s/32768),r}function vn(e,r){if(r||(r=da(8)),!e||e.auto)return r.write_shift(4,0),r.write_shift(4,0),r;null!=e.index?(r.write_shift(1,2),r.write_shift(1,e.index)):null!=e.theme?(r.write_shift(1,6),r.write_shift(1,e.theme)):(r.write_shift(1,5),r.write_shift(1,0));var t=e.tint||0;if(t>0?t*=32767:t<0&&(t*=32768),r.write_shift(2,t),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),r.write_shift(1,parseInt(a.slice(0,2),16)),r.write_shift(1,parseInt(a.slice(2,4),16)),r.write_shift(1,parseInt(a.slice(4,6),16)),r.write_shift(1,255)}else r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r}function gn(e){var r=e.read_shift(1);e.l++;var t={fBold:1&r,fItalic:2&r,fUnderline:4&r,fStrikeout:8&r,fOutline:16&r,fShadow:32&r,fCondense:64&r,fExtend:128&r};return t}function wn(e,r){r||(r=da(2));var t=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);return r.write_shift(1,t),r.write_shift(1,0),r}function kn(e,r){var t={2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"},a=e.read_shift(4);switch(a){case 0:return"";case 4294967295:case 4294967294:return t[e.read_shift(4)]||""}if(a>400)throw new Error("Unsupported Clipboard: "+a.toString(16));return e.l-=4,e.read_shift(0,1==r?"lpstr":"lpwstr")}function Tn(e){return kn(e,1)}function En(e){return kn(e,2)}var Sn=2,yn=3,_n=11,An=12,xn=19,Cn=64,Rn=65,On=71,In=4108,Nn=4126,Dn=80,Fn=81,Pn=[Dn,Fn],Ln={1:{n:"CodePage",t:Sn},2:{n:"Category",t:Dn},3:{n:"PresentationFormat",t:Dn},4:{n:"ByteCount",t:yn},5:{n:"LineCount",t:yn},6:{n:"ParagraphCount",t:yn},7:{n:"SlideCount",t:yn},8:{n:"NoteCount",t:yn},9:{n:"HiddenCount",t:yn},10:{n:"MultimediaClipCount",t:yn},11:{n:"ScaleCrop",t:_n},12:{n:"HeadingPairs",t:In},13:{n:"TitlesOfParts",t:Nn},14:{n:"Manager",t:Dn},15:{n:"Company",t:Dn},16:{n:"LinksUpToDate",t:_n},17:{n:"CharacterCount",t:yn},19:{n:"SharedDoc",t:_n},22:{n:"HyperlinksChanged",t:_n},23:{n:"AppVersion",t:yn,p:"version"},24:{n:"DigSig",t:Rn},26:{n:"ContentType",t:Dn},27:{n:"ContentStatus",t:Dn},28:{n:"Language",t:Dn},29:{n:"Version",t:Dn},255:{},2147483648:{n:"Locale",t:xn},2147483651:{n:"Behavior",t:xn},1919054434:{}},Mn={1:{n:"CodePage",t:Sn},2:{n:"Title",t:Dn},3:{n:"Subject",t:Dn},4:{n:"Author",t:Dn},5:{n:"Keywords",t:Dn},6:{n:"Comments",t:Dn},7:{n:"Template",t:Dn},8:{n:"LastAuthor",t:Dn},9:{n:"RevNumber",t:Dn},10:{n:"EditTime",t:Cn},11:{n:"LastPrinted",t:Cn},12:{n:"CreatedDate",t:Cn},13:{n:"ModifiedDate",t:Cn},14:{n:"PageCount",t:yn},15:{n:"WordCount",t:yn},16:{n:"CharCount",t:yn},17:{n:"Thumbnail",t:On},18:{n:"Application",t:Dn},19:{n:"DocSecurity",t:yn},255:{},2147483648:{n:"Locale",t:xn},2147483651:{n:"Behavior",t:xn},1919054434:{}},Un={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Bn=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Wn(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var Hn=Wn([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),Vn=wr(Hn),zn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Gn={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},jn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Xn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function $n(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""}}function Yn(e){var r=$n();if(!e||!e.match)return r;var t={};if((e.match(Vr)||[]).forEach((function(e){var a=jr(e);switch(a[0].replace(zr,"<")){case"<?xml":break;case"<Types":r.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":t[a.Extension]=a.ContentType;break;case"<Override":void 0!==r[jn[a.ContentType]]&&r[jn[a.ContentType]].push(a.PartName);break}})),r.xmlns!==At.CT)throw new Error("Unknown Namespace: "+r.xmlns);return r.calcchain=r.calcchains.length>0?r.calcchains[0]:"",r.sst=r.strs.length>0?r.strs[0]:"",r.style=r.styles.length>0?r.styles[0]:"",r.defaults=t,delete r.calcchains,r}function Kn(e,r){var t,a=ir(jn),n=[];n[n.length]=Ur,n[n.length]=Tt("Types",null,{xmlns:At.CT,"xmlns:xsd":At.xsd,"xmlns:xsi":At.xsi}),n=n.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return Tt("Default",null,{Extension:e[0],ContentType:e[1]})})));var s=function(a){e[a]&&e[a].length>0&&(t=e[a][0],n[n.length]=Tt("Override",null,{PartName:("/"==t[0]?"":"/")+t,ContentType:Xn[a][r.bookType]||Xn[a]["xlsx"]}))},i=function(t){(e[t]||[]).forEach((function(e){n[n.length]=Tt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:Xn[t][r.bookType]||Xn[t]["xlsx"]})}))},o=function(r){(e[r]||[]).forEach((function(e){n[n.length]=Tt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:a[r][0]})}))};return s("workbooks"),i("sheets"),i("charts"),o("themes"),["strs","styles"].forEach(s),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),i("metadata"),o("people"),n.length>2&&(n[n.length]="</Types>",n[1]=n[1].replace("/>",">")),n.join("")}var Jn={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function qn(e){var r=e.lastIndexOf("/");return e.slice(0,r+1)+"_rels/"+e.slice(r+1)+".rels"}function Zn(e,r){var t={"!id":{}};if(!e)return t;"/"!==r.charAt(0)&&(r="/"+r);var a={};return(e.match(Vr)||[]).forEach((function(e){var n=jr(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=n.Target,s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:Mr(n.Target,r);t[i]=s,a[n.Id]=s}})),t["!id"]=a,t}function Qn(e){var r=[Ur,Tt("Relationships",null,{xmlns:At.RELS})];return tr(e["!id"]).forEach((function(t){r[r.length]=Tt("Relationship",null,e["!id"][t])})),r.length>2&&(r[r.length]="</Relationships>",r[1]=r[1].replace("/>",">")),r.join("")}function es(e,r,t,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),r<0)for(r=e["!idx"];e["!id"]["rId"+r];++r);if(e["!idx"]=r+1,n.Id="rId"+r,n.Type=a,n.Target=t,s?n.TargetMode=s:[Jn.HLINK,Jn.XPATH,Jn.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+r);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,r}var rs="application/vnd.oasis.opendocument.spreadsheet";function ts(e,r){var t,a,n=yt(e);while(t=_t.exec(n))switch(t[3]){case"manifest":break;case"file-entry":if(a=jr(t[0],!1),"/"==a.path&&a.type!==rs)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(r&&r.WTF)throw t}}function as(e){var r=[Ur];r.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),r.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var t=0;t<e.length;++t)r.push('  <manifest:file-entry manifest:full-path="'+e[t][0]+'" manifest:media-type="'+e[t][1]+'"/>\n');return r.push("</manifest:manifest>"),r.join("")}function ns(e,r,t){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(t||"odf")+"#"+r+'"/>\n',"  </rdf:Description>\n"].join("")}function ss(e,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")}function is(e){var r=[Ur];r.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var t=0;t!=e.length;++t)r.push(ns(e[t][0],e[t][1])),r.push(ss("",e[t][0]));return r.push(ns("","Document","pkg")),r.push("</rdf:RDF>"),r.join("")}function os(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+a.version+"</meta:generator></office:meta></office:document-meta>"}var cs=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]],ls=function(){for(var e=new Array(cs.length),r=0;r<cs.length;++r){var t=cs[r],a="(?:"+t[0].slice(0,t[0].indexOf(":"))+":)"+t[0].slice(t[0].indexOf(":")+1);e[r]=new RegExp("<"+a+"[^>]*>([\\s\\S]*?)</"+a+">")}return e}();function fs(e){var r={};e=ft(e);for(var t=0;t<cs.length;++t){var a=cs[t],n=e.match(ls[t]);null!=n&&n.length>0&&(r[a[1]]=Kr(n[1])),"date"===a[2]&&r[a[1]]&&(r[a[1]]=vr(r[a[1]]))}return r}function hs(e,r,t,a,n){null==n[e]&&null!=r&&""!==r&&(n[e]=r,r=Zr(r),a[a.length]=t?Tt(e,r,t):wt(e,r))}function us(e,r){var t=r||{},a=[Ur,Tt("cp:coreProperties",null,{"xmlns:cp":At.CORE_PROPS,"xmlns:dc":At.dc,"xmlns:dcterms":At.dcterms,"xmlns:dcmitype":At.dcmitype,"xmlns:xsi":At.xsi})],n={};if(!e&&!t.Props)return a.join("");e&&(null!=e.CreatedDate&&hs("dcterms:created","string"===typeof e.CreatedDate?e.CreatedDate:Et(e.CreatedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&hs("dcterms:modified","string"===typeof e.ModifiedDate?e.ModifiedDate:Et(e.ModifiedDate,t.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=cs.length;++s){var i=cs[s],o=t.Props&&null!=t.Props[i[1]]?t.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&hs(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var ds=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],ps=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ms(e,r,t,a){var n=[];if("string"==typeof e)n=vt(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof r?vt(r,a).map((function(e){return e.v})):r,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":t.Worksheets=c,t.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":t.NamedRanges=c,t.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":t.Chartsheets=c,t.ChartNames=i.slice(o,o+c);break}o+=c}}function bs(e,r,t){var a={};return r||(r={}),e=ft(e),ds.forEach((function(t){var n=(e.match(ut(t[0]))||[])[1];switch(t[2]){case"string":n&&(r[t[1]]=Kr(n));break;case"bool":r[t[1]]="true"===n;break;case"raw":var s=e.match(new RegExp("<"+t[0]+"[^>]*>([\\s\\S]*?)</"+t[0]+">"));s&&s.length>0&&(a[t[1]]=s[1]);break}})),a.HeadingPairs&&a.TitlesOfParts&&ms(a.HeadingPairs,a.TitlesOfParts,r,t),r}function vs(e){var r=[],t=Tt;return e||(e={}),e.Application="SheetJS",r[r.length]=Ur,r[r.length]=Tt("Properties",null,{xmlns:At.EXT_PROPS,"xmlns:vt":At.vt}),ds.forEach((function(a){if(void 0!==e[a[1]]){var n;switch(a[2]){case"string":n=Zr(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false";break}void 0!==n&&(r[r.length]=t(a[0],n))}})),r[r.length]=t("HeadingPairs",t("vt:vector",t("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+t("vt:variant",t("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),r[r.length]=t("TitlesOfParts",t("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+Zr(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),r.length>2&&(r[r.length]="</Properties>",r[1]=r[1].replace("/>",">")),r.join("")}var gs=/<[^>]+>[^<]*/g;function ws(e,r){var t={},a="",n=e.match(gs);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=jr(i);switch(o[0]){case"<?xml":break;case"<Properties":break;case"<property":a=Kr(o.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":t[a]=Kr(f);break;case"bool":t[a]=st(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":t[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":t[a]=parseFloat(f);break;case"filetime":case"date":t[a]=vr(f);break;case"cy":case"error":t[a]=Kr(f);break;default:if("/"==l.slice(-1))break;r.WTF&&"undefined"!==typeof console&&console.warn("Unexpected",i,l,c)}}else if("</"===i.slice(0,2));else if(r.WTF)throw new Error(i)}}return t}function ks(e){var r=[Ur,Tt("Properties",null,{xmlns:At.CUST_PROPS,"xmlns:vt":At.vt})];if(!e)return r.join("");var t=1;return tr(e).forEach((function(a){++t,r[r.length]=Tt("property",St(e[a],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:t,name:Zr(a)})})),r.length>2&&(r[r.length]="</Properties>",r[1]=r[1].replace("/>",">")),r.join("")}var Ts,Es={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Ss(e,r,t){Ts||(Ts=nr(Es)),r=Ts[r]||r,e[r]=t}function ys(e,r){var t=[];return tr(Es).map((function(e){for(var r=0;r<cs.length;++r)if(cs[r][1]==e)return cs[r];for(r=0;r<ds.length;++r)if(ds[r][1]==e)return ds[r];throw e})).forEach((function(a){if(null!=e[a[1]]){var n=r&&r.Props&&null!=r.Props[a[1]]?r.Props[a[1]]:e[a[1]];switch(a[2]){case"date":n=new Date(n).toISOString().replace(/\.\d*Z/,"Z");break}"number"==typeof n?n=String(n):!0===n||!1===n?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),t.push(wt(Es[a[1]]||a[1],n))}})),Tt("DocumentProperties",t.join(""),{xmlns:Ct.o})}function _s(e,r){var t=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&tr(e).forEach((function(r){if(Object.prototype.hasOwnProperty.call(e,r)){for(var a=0;a<cs.length;++a)if(r==cs[a][1])return;for(a=0;a<ds.length;++a)if(r==ds[a][1])return;for(a=0;a<t.length;++a)if(r==t[a])return;var s=e[r],i="string";"number"==typeof s?(i="float",s=String(s)):!0===s||!1===s?(i="boolean",s=s?"1":"0"):s=String(s),n.push(Tt(Qr(r),s,{"dt:dt":i}))}})),r&&tr(r).forEach((function(t){if(Object.prototype.hasOwnProperty.call(r,t)&&(!e||!Object.prototype.hasOwnProperty.call(e,t))){var a=r[t],s="string";"number"==typeof a?(s="float",a=String(a)):!0===a||!1===a?(s="boolean",a=a?"1":"0"):a instanceof Date?(s="dateTime.tz",a=a.toISOString()):a=String(a),n.push(Tt(Qr(t),a,{"dt:dt":s}))}})),"<"+a+' xmlns="'+Ct.o+'">'+n.join("")+"</"+a+">"}function As(e){var r=e.read_shift(4),t=e.read_shift(4);return new Date(1e3*(t/1e7*Math.pow(2,32)+r/1e7-11644473600)).toISOString().replace(/\.000/,"")}function xs(e){var r="string"==typeof e?new Date(Date.parse(e)):e,t=r.getTime()/1e3+11644473600,a=t%Math.pow(2,32),n=(t-a)/Math.pow(2,32);a*=1e7,n*=1e7;var s=a/Math.pow(2,32)|0;s>0&&(a%=Math.pow(2,32),n+=s);var i=da(8);return i.write_shift(4,a),i.write_shift(4,n),i}function Cs(e,r,t){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(t)while(e.l-a&3)++e.l;return n}function Rs(e,r,t){var a=e.read_shift(0,"lpwstr");return t&&(e.l+=4-(a.length+1&3)&3),a}function Os(e,r,t){return 31===r?Rs(e):Cs(e,r,t)}function Is(e,r,t){return Os(e,r,!1===t?0:4)}function Ns(e,r){if(!r)throw new Error("VtUnalignedString must have positive length");return Os(e,r,0)}function Ds(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a){var n=e.l;t[a]=e.read_shift(0,"lpwstr").replace(P,""),e.l-n&2&&(e.l+=2)}return t}function Fs(e){for(var r=e.read_shift(4),t=[],a=0;a!=r;++a)t[a]=e.read_shift(0,"lpstr-cp").replace(P,"");return t}function Ps(e){var r=e.l,t=Ws(e,Fn);0==e[e.l]&&0==e[e.l+1]&&e.l-r&2&&(e.l+=2);var a=Ws(e,yn);return[t,a]}function Ls(e){for(var r=e.read_shift(4),t=[],a=0;a<r/2;++a)t.push(Ps(e));return t}function Ms(e,r){for(var t=e.read_shift(4),a={},n=0;n!=t;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===r?"utf16le":"utf8").replace(P,"").replace(L,"!"),1200===r&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Us(e){var r=e.read_shift(4),t=e.slice(e.l,e.l+r);return e.l+=r,(3&r)>0&&(e.l+=4-(3&r)&3),t}function Bs(e){var r={};return r.Size=e.read_shift(4),e.l+=r.Size+3-(r.Size-1)%4,r}function Ws(e,r,t){var a,n=e.read_shift(2),s=t||{};if(e.l+=2,r!==An&&n!==r&&-1===Pn.indexOf(r)&&(4126!=(65534&r)||4126!=(65534&n)))throw new Error("Expected type "+r+" saw "+n);switch(r===An?n:r){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return a=e.read_shift(4,"i"),a;case 11:return 0!==e.read_shift(4);case 19:return a=e.read_shift(4),a;case 30:return Cs(e,n,4).replace(P,"");case 31:return Rs(e);case 64:return As(e);case 65:return Us(e);case 71:return Bs(e);case 80:return Is(e,n,!s.raw).replace(P,"");case 81:return Ns(e,n).replace(P,"");case 4108:return Ls(e);case 4126:case 4127:return 4127==n?Ds(e):Fs(e);default:throw new Error("TypedPropertyValue unrecognized type "+r+" "+n)}}function Hs(e,r){var t=da(4),a=da(4);switch(t.write_shift(4,80==e?31:e),e){case 3:a.write_shift(-4,r);break;case 5:a=da(8),a.write_shift(8,r,"f");break;case 11:a.write_shift(4,r?1:0);break;case 64:a=xs(r);break;case 31:case 80:a=da(4+2*(r.length+1)+(r.length%2?0:2)),a.write_shift(4,r.length+1),a.write_shift(0,r,"dbcs");while(a.l!=a.length)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+r)}return D([t,a])}function Vs(e,r){var t=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,c=-1,l={};for(i=0;i!=n;++i){var h=e.read_shift(4),u=e.read_shift(4);s[i]=[h,u+t]}s.sort((function(e,r){return e[1]-r[1]}));var d={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&r)switch(r[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:e.l<=s[i][1]&&(e.l=s[i][1],p=!1);break;case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1);break}if((!r||0==i)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(r){var m=r[s[i][0]];if(d[m.n]=Ws(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:f(o=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===s[i][0]){if(o=d.CodePage=Ws(e,Sn),f(o),-1!==c){var b=e.l;e.l=s[c][1],l=Ms(e,o),e.l=b}}else if(0===s[i][0]){if(0===o){c=i,e.l=s[i+1][1];continue}l=Ms(e,o)}else{var v,g=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,v=Us(e);break;case 30:e.l+=4,v=Is(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 31:e.l+=4,v=Is(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,v=e.read_shift(4,"i");break;case 19:e.l+=4,v=e.read_shift(4);break;case 5:e.l+=4,v=e.read_shift(8,"f");break;case 11:e.l+=4,v=qs(e,4);break;case 64:e.l+=4,v=vr(As(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[g]=v}}return e.l=t+a,d}var zs=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function Gs(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64;break}return-1}function js(e,r,t){var a=da(8),n=[],s=[],i=8,o=0,c=da(8),l=da(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!r){l=da(8),l.write_shift(4,0),n.unshift(l);var f=[da(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];c=da(8+2*(h.length+1)+(h.length%2?0:2)),c.write_shift(4,o+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");while(c.l!=c.length)c.write_shift(1,0);f.push(c)}c=D(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if((!r||r[e[o][0]])&&!(zs.indexOf(e[o][0])>-1||ps.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(r){d=+r[e[o][0]];var p=t[d];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}c=Hs(p.t,u)}else{var b=Gs(u);-1==b&&(b=31,u=String(u)),c=Hs(b,u)}s.push(c),l=da(8),l.write_shift(4,r?d:2+o),n.push(l),i+=8+c.length}var v=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,v),v+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),D([a].concat(n).concat(s))}function Xs(e,r,t){var a=e.content;if(!a)return{};ha(a,0);var n,s,i,o,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==qe.utils.consts.HEADER_CLSID&&f!==t)throw new Error("Bad PropertySet CLSID "+f);if(n=a.read_shift(4),1!==n&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),o=a.read_shift(4),1===n&&o!==a.l)throw new Error("Length mismatch: "+o+" !== "+a.l);2===n&&(i=a.read_shift(16),c=a.read_shift(4));var h,u=Vs(a,r),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);try{h=Vs(a,null)}catch(m){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function $s(e,r,t,a,n,s){var i=da(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,qe.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,r,"hex"),i.write_shift(4,n?68:48);var c=js(e,t,a);if(o.push(c),n){var l=js(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return D(o)}function Ys(e,r){return e.read_shift(r),null}function Ks(e,r){r||(r=da(e));for(var t=0;t<e;++t)r.write_shift(1,0);return r}function Js(e,r,t){var a=[],n=e.l+r;while(e.l<n)a.push(t(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}function qs(e,r){return 1===e.read_shift(r)}function Zs(e,r){return r||(r=da(2)),r.write_shift(2,+!!e),r}function Qs(e){return e.read_shift(2,"u")}function ei(e,r){return r||(r=da(2)),r.write_shift(2,e),r}function ri(e,r){return Js(e,r,Qs)}function ti(e){var r=e.read_shift(1),t=e.read_shift(1);return 1===t?r:1===r}function ai(e,r,t){return t||(t=da(2)),t.write_shift(1,"e"==r?+e:+!!e),t.write_shift(1,"e"==r?1:0),t}function ni(e,r,t){var a=e.read_shift(t&&t.biff>=12?2:1),s="sbcs-cont",i=n;if(t&&t.biff>=8&&(n=1200),t&&8!=t.biff)12==t.biff&&(s="wstr");else{var o=e.read_shift(1);o&&(s="dbcs-cont")}t.biff>=2&&t.biff<=5&&(s="cpstr");var c=a?e.read_shift(a,s):"";return n=i,c}function si(e){var r=n;n=1200;var t,a=e.read_shift(2),s=e.read_shift(1),i=4&s,o=8&s,c=1+(1&s),l=0,f={};o&&(l=e.read_shift(2)),i&&(t=e.read_shift(4));var h=2==c?"dbcs-cont":"sbcs-cont",u=0===a?"":e.read_shift(a,h);return o&&(e.l+=4*l),i&&(e.l+=t),f.t=u,o||(f.raw="<t>"+f.t+"</t>",f.r=f.t),n=r,f}function ii(e){var r=e.t||"",t=1,a=da(3+(t>1?2:0));a.write_shift(2,r.length),a.write_shift(1,1|(t>1?8:0)),t>1&&a.write_shift(2,t);var n=da(2*r.length);n.write_shift(2*r.length,r,"utf16le");var s=[a,n];return D(s)}function oi(e,r,t){var a;if(t){if(t.biff>=2&&t.biff<=5)return e.read_shift(r,"cpstr");if(t.biff>=12)return e.read_shift(r,"dbcs-cont")}var n=e.read_shift(1);return a=0===n?e.read_shift(r,"sbcs-cont"):e.read_shift(r,"dbcs-cont"),a}function ci(e,r,t){var a=e.read_shift(t&&2==t.biff?1:2);return 0===a?(e.l++,""):oi(e,a,t)}function li(e,r,t){if(t.biff>5)return ci(e,r,t);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,t.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function fi(e,r,t){return t||(t=da(3+2*e.length)),t.write_shift(2,e.length),t.write_shift(1,1),t.write_shift(31,e,"utf16le"),t}function hi(e){var r=e.read_shift(1);e.l++;var t=e.read_shift(2);return e.l+=2,[r,t]}function ui(e){var r=e.read_shift(4),t=e.l,a=!1;r>24&&(e.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=t);var n=e.read_shift((a?r-24:r)>>1,"utf16le").replace(P,"");return a&&(e.l+=24),n}function di(e){var r=e.read_shift(2),t="";while(r-- >0)t+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");var n=e.read_shift(4);if(0===n)return t+a.replace(/\\/g,"/");var s=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");var i=e.read_shift(s>>1,"utf16le").replace(P,"");return t+i}function pi(e,r){var t=e.read_shift(16);switch(r-=16,t){case"e0c9ea79f9bace118c8200aa004ba90b":return ui(e,r);case"0303000000000000c000000000000046":return di(e,r);default:throw new Error("Unsupported Moniker "+t)}}function mi(e){var r=e.read_shift(4),t=r>0?e.read_shift(r,"utf16le").replace(P,""):"";return t}function bi(e,r){r||(r=da(6+2*e.length)),r.write_shift(4,1+e.length);for(var t=0;t<e.length;++t)r.write_shift(2,e.charCodeAt(t));return r.write_shift(2,0),r}function vi(e,r){var t=e.l+r,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l,f,h="";16&n&&(s=mi(e,t-e.l)),128&n&&(i=mi(e,t-e.l)),257===(257&n)&&(o=mi(e,t-e.l)),1===(257&n)&&(c=pi(e,t-e.l)),8&n&&(h=mi(e,t-e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=As(e)),e.l=t;var u=i||o||c||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}function gi(e){var r=da(512),t=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3;break}r.write_shift(4,2),r.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(t=0;t<i.length;++t)r.write_shift(4,i[t]);if(28==s)a=a.slice(1),bi(a,r);else if(2&s){for(i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),t=0;t<i.length;++t)r.write_shift(1,parseInt(i[t],16));var o=n>-1?a.slice(0,n):a;for(r.write_shift(4,2*(o.length+1)),t=0;t<o.length;++t)r.write_shift(2,o.charCodeAt(t));r.write_shift(2,0),8&s&&bi(n>-1?a.slice(n+1):"",r)}else{for(i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),t=0;t<i.length;++t)r.write_shift(1,parseInt(i[t],16));var c=0;while("../"==a.slice(3*c,3*c+3)||"..\\"==a.slice(3*c,3*c+3))++c;for(r.write_shift(2,c),r.write_shift(4,a.length-3*c+1),t=0;t<a.length-3*c;++t)r.write_shift(1,255&a.charCodeAt(t+3*c));for(r.write_shift(1,0),r.write_shift(2,65535),r.write_shift(2,57005),t=0;t<6;++t)r.write_shift(4,0)}return r.slice(0,r.l)}function wi(e){var r=e.read_shift(1),t=e.read_shift(1),a=e.read_shift(1),n=e.read_shift(1);return[r,t,a,n]}function ki(e,r){var t=wi(e,r);return t[3]=0,t}function Ti(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2);return{r:r,c:t,ixfe:a}}function Ei(e,r,t,a){return a||(a=da(6)),a.write_shift(2,e),a.write_shift(2,r),a.write_shift(2,t||0),a}function Si(e){var r=e.read_shift(2),t=e.read_shift(2);return e.l+=8,{type:r,flags:t}}function yi(e,r,t){return 0===r?"":li(e,r,t)}function _i(e,r,t){var a=t.biff>8?4:2,n=e.read_shift(a),s=e.read_shift(a,"i"),i=e.read_shift(a,"i");return[n,s,i]}function Ai(e){var r=e.read_shift(2),t=cn(e);return[r,t]}function xi(e,r,t){e.l+=4,r-=4;var a=e.l+r,n=ni(e,r,t),s=e.read_shift(2);if(a-=e.l,s!==a)throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}function Ci(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2);return{s:{c:a,r:r},e:{c:n,r:t}}}function Ri(e,r){return r||(r=da(8)),r.write_shift(2,e.s.r),r.write_shift(2,e.e.r),r.write_shift(2,e.s.c),r.write_shift(2,e.e.c),r}function Oi(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(1),n=e.read_shift(1);return{s:{c:a,r:r},e:{c:n,r:t}}}var Ii=Oi;function Ni(e){e.l+=4;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[t,r,a]}function Di(e){var r={};return e.l+=4,e.l+=16,r.fSharedNote=e.read_shift(2),e.l+=4,r}function Fi(e){var r={};return e.l+=4,e.cf=e.read_shift(2),r}function Pi(e){e.l+=2,e.l+=e.read_shift(2)}var Li={0:Pi,4:Pi,5:Pi,6:Pi,7:Fi,8:Pi,9:Pi,10:Pi,11:Pi,12:Pi,13:Di,14:Pi,15:Pi,16:Pi,17:Pi,18:Pi,19:Pi,20:Pi,21:Ni};function Mi(e,r){var t=e.l+r,a=[];while(e.l<t){var n=e.read_shift(2);e.l-=2;try{a.push(Li[n](e,t-e.l))}catch(s){return e.l=t,a}}return e.l!=t&&(e.l=t),a}function Ui(e,r){var t={BIFFVer:0,dt:0};switch(t.BIFFVer=e.read_shift(2),r-=2,r>=2&&(t.dt=e.read_shift(2),e.l-=2),t.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(r>6)throw new Error("Unexpected BIFF Ver "+t.BIFFVer)}return e.read_shift(r),t}function Bi(e,r,t){var a=1536,n=16;switch(t.bookType){case"biff8":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}var s=da(n);return s.write_shift(2,a),s.write_shift(2,r),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function Wi(e,r){return 0===r||e.read_shift(2),1200}function Hi(e,r,t){if(t.enc)return e.l+=r,"";var a=e.l,n=li(e,0,t);return e.read_shift(r+a-e.l),n}function Vi(e,r){var t=!r||8==r.biff,a=da(t?112:54);a.write_shift(8==r.biff?2:1,7),t&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(t?0:536870912));while(a.l<a.length)a.write_shift(1,t?0:32);return a}function zi(e,r,t){var a=t&&8==t.biff||2==r?e.read_shift(2):(e.l+=r,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}function Gi(e,r,t){var a=e.read_shift(4),n=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule";break}var i=ni(e,0,t);return 0===i.length&&(i="Sheet1"),{pos:a,hs:n,dt:s,name:i}}function ji(e,r){var t=!r||r.biff>=8?2:1,a=da(8+t*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),r.biff>=8&&a.write_shift(1,1),a.write_shift(t*e.name.length,e.name,r.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function Xi(e,r){for(var t=e.l+r,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<t;++i)s.push(si(e));return s.Count=a,s.Unique=n,s}function $i(e,r){var t=da(8);t.write_shift(4,e.Count),t.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=ii(e[n],r);var s=D([t].concat(a));return s.parts=[t.length].concat(a.map((function(e){return e.length}))),s}function Yi(e,r){var t={};return t.dsst=e.read_shift(2),e.l+=r-2,t}function Ki(e){var r={};r.r=e.read_shift(2),r.c=e.read_shift(2),r.cnt=e.read_shift(2)-r.c;var t=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(r.level=7&a),32&a&&(r.hidden=!0),64&a&&(r.hpt=t/20),r}function Ji(e){var r=Si(e);if(2211!=r.type)throw new Error("Invalid Future Record "+r.type);var t=e.read_shift(4);return 0!==t}function qi(e){return e.read_shift(2),e.read_shift(4)}function Zi(e,r,t){var a=0;t&&2==t.biff||(a=e.read_shift(2));var n=e.read_shift(2);t&&2==t.biff&&(a=1-(n>>15),n&=32767);var s={Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3};return[s,n]}function Qi(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=e.read_shift(2),c=e.read_shift(2),l=e.read_shift(2);return{Pos:[r,t],Dim:[a,n],Flags:s,CurTab:i,FirstTab:o,Selected:c,TabRatio:l}}function eo(){var e=da(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}function ro(e,r,t){if(t&&t.biff>=2&&t.biff<5)return{};var a=e.read_shift(2);return{RTL:64&a}}function to(e){var r=da(18),t=1718;return e&&e.RTL&&(t|=64),r.write_shift(2,t),r.write_shift(4,0),r.write_shift(4,64),r.write_shift(4,0),r.write_shift(4,0),r}function ao(){}function no(e,r,t){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(t&&t.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10;break}return a.name=ni(e,0,t),a}function so(e,r){var t=e.name||"Arial",a=r&&5==r.biff,n=a?15+t.length:16+2*t.length,s=da(n);return s.write_shift(2,20*(e.sz||12)),s.write_shift(4,0),s.write_shift(2,400),s.write_shift(4,0),s.write_shift(2,0),s.write_shift(1,t.length),a||s.write_shift(1,1),s.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le"),s}function io(e){var r=Ti(e);return r.isst=e.read_shift(4),r}function oo(e,r,t,a){var n=da(10);return Ei(e,r,a,n),n.write_shift(4,t),n}function co(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5);var a=e.l+r,n=Ti(e,6);2==t.biff&&e.l++;var s=ci(e,a-e.l,t);return n.val=s,n}function lo(e,r,t,a,n){var s=!n||8==n.biff,i=da(+s+8+(1+s)*t.length);return Ei(e,r,a,i),i.write_shift(2,t.length),s&&i.write_shift(1,1),i.write_shift((1+s)*t.length,t,s?"utf16le":"sbcs"),i}function fo(e,r,t){var a=e.read_shift(2),n=li(e,0,t);return[a,n]}function ho(e,r,t,a){var n=t&&5==t.biff;a||(a=da(n?3+r.length:5+2*r.length)),a.write_shift(2,e),a.write_shift(n?1:2,r.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}var uo=li;function po(e,r,t){var a=e.l+r,n=8!=t.biff&&t.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:c}}}function mo(e,r){var t=8!=r.biff&&r.biff?2:4,a=da(2*t+6);return a.write_shift(t,e.s.r),a.write_shift(t,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}function bo(e){var r=e.read_shift(2),t=e.read_shift(2),a=Ai(e);return{r:r,c:t,ixfe:a[0],rknum:a[1]}}function vo(e,r){var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];while(e.l<t)s.push(Ai(e));if(e.l!==t)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}function go(e,r){var t=e.l+r-2,a=e.read_shift(2),n=e.read_shift(2),s=[];while(e.l<t)s.push(e.read_shift(2));if(e.l!==t)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}function wo(e,r,t,a){var n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2);return n.patternType=Bn[o>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&c,n.icvBack=c>>7&127,n.fsxButton=c>>14&1,n):n}function ko(e,r,t){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,r-=6,a.data=wo(e,r,a.fStyle,t),a}function To(e,r,t,a){var n=t&&5==t.biff;a||(a=da(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,r<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Eo(e){e.l+=4;var r=[e.read_shift(2),e.read_shift(2)];if(0!==r[0]&&r[0]--,0!==r[1]&&r[1]--,r[0]>7||r[1]>7)throw new Error("Bad Gutters: "+r.join("|"));return r}function So(e){var r=da(8);return r.write_shift(4,0),r.write_shift(2,e[0]?e[0]+1:0),r.write_shift(2,e[1]?e[1]+1:0),r}function yo(e,r,t){var a=Ti(e,6);2!=t.biff&&9!=r||++e.l;var n=ti(e,2);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}function _o(e,r,t,a,n,s){var i=da(8);return Ei(e,r,a,i),ai(t,s,i),i}function Ao(e,r,t){t.biffguess&&2==t.biff&&(t.biff=5);var a=Ti(e,6),n=pn(e,8);return a.val=n,a}function xo(e,r,t,a){var n=da(14);return Ei(e,r,a,n),mn(t,n),n}var Co=yi;function Ro(e,r,t){var a=e.l+r,n=e.read_shift(2),s=e.read_shift(2);if(t.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);var i=oi(e,s),o=[];while(a>e.l)o.push(ci(e));return[s,n,i,o]}function Oo(e,r,t){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===t.sbcch&&(a=xi(e,r-2,t)),s.body=a||e.read_shift(r-2),"string"===typeof a&&(s.Name=a),s}var Io=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function No(e,r,t){var a=e.l+r,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(t&&2==t.biff?1:2),c=0;(!t||t.biff>=5)&&(5!=t.biff&&(e.l+=2),c=e.read_shift(2),5==t.biff&&(e.l+=2),e.l+=4);var l=oi(e,i,t);32&n&&(l=Io[l.charCodeAt(0)]);var f=a-e.l;t&&2==t.biff&&--f;var h=a!=e.l&&0!==o&&f>0?ld(e,f,t,o):[];return{chKey:s,Name:l,itab:c,rgce:h}}function Do(e,r,t){if(t.biff<8)return Fo(e,r,t);var a=[],n=e.l+r,s=e.read_shift(t.biff>8?4:2);while(0!==s--)a.push(_i(e,t.biff>8?12:6,t));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Fo(e,r,t){3==e[e.l+1]&&e[e.l]++;var a=ni(e,r,t);return 3==a.charCodeAt(0)?a.slice(1):a}function Po(e,r,t){if(!(t.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=oi(e,a,t),i=oi(e,n,t);return[s,i]}e.l+=r}function Lo(e,r,t){var a=Oi(e,6);e.l++;var n=e.read_shift(1);return r-=8,[fd(e,r,t),n,a]}function Mo(e,r,t){var a=Ii(e,6);switch(t.biff){case 2:e.l++,r-=7;break;case 3:case 4:e.l+=2,r-=8;break;default:e.l+=6,r-=12}return[a,od(e,r,t,a)]}function Uo(e){var r=0!==e.read_shift(4),t=0!==e.read_shift(4),a=e.read_shift(4);return[r,t,a]}function Bo(e,r,t){if(!(t.biff<8)){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2),o=li(e,0,t);return t.biff<8&&e.read_shift(1),[{r:a,c:n},o,i,s]}}function Wo(e,r,t){return Bo(e,r,t)}function Ho(e,r){var t=[],a=e.read_shift(2);while(a--)t.push(Ci(e,r));return t}function Vo(e){var r=da(2+8*e.length);r.write_shift(2,e.length);for(var t=0;t<e.length;++t)Ri(e[t],r);return r}function zo(e,r,t){if(t&&t.biff<8)return jo(e,r,t);var a=Ni(e,22),n=Mi(e,r-22,a[1]);return{cmo:a,ft:n}}var Go={8:function(e,r){var t=e.l+r;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=t,{fmt:a}}};function jo(e,r,t){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,r-=36;var i=[];return i.push((Go[a]||ua)(e,r,t)),{cmo:[n,a,s],ft:i}}function Xo(e,r,t){var a=e.l,n="";try{e.l+=4;var s=(t.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:hi(e,6,t);var i=e.read_shift(2);e.read_shift(2),Qs(e,2);var o=e.read_shift(2);e.l+=o;for(var c=1;c<e.lens.length-1;++c){if(e.l-a!=e.lens[c])throw new Error("TxO: bad continue record");var l=e[e.l],f=oi(e,e.lens[c+1]-e.lens[c]-1);if(n+=f,n.length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+r,{t:n}}catch(h){return e.l=a+r,{t:n}}}function $o(e,r){var t=Ci(e,8);e.l+=16;var a=vi(e,r-24);return[t,a]}function Yo(e){var r=da(24),t=Oa(e[0]);r.write_shift(2,t.r),r.write_shift(2,t.r),r.write_shift(2,t.c),r.write_shift(2,t.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)r.write_shift(1,parseInt(a[n],16));return D([r,gi(e[1])])}function Ko(e,r){e.read_shift(2);var t=Ci(e,8),a=e.read_shift((r-10)/2,"dbcs-cont");return a=a.replace(P,""),[t,a]}function Jo(e){var r=e[1].Tooltip,t=da(10+2*(r.length+1));t.write_shift(2,2048);var a=Oa(e[0]);t.write_shift(2,a.r),t.write_shift(2,a.r),t.write_shift(2,a.c),t.write_shift(2,a.c);for(var n=0;n<r.length;++n)t.write_shift(2,r.charCodeAt(n));return t.write_shift(2,0),t}function qo(e){var r,t=[0,0];return r=e.read_shift(2),t[0]=Un[r]||r,r=e.read_shift(2),t[1]=Un[r]||r,t}function Zo(e){return e||(e=da(4)),e.write_shift(2,1),e.write_shift(2,1),e}function Qo(e){var r=e.read_shift(2),t=[];while(r-- >0)t.push(ki(e,8));return t}function ec(e){var r=e.read_shift(2),t=[];while(r-- >0)t.push(ki(e,8));return t}function rc(e){e.l+=2;var r={cxfs:0,crc:0};return r.cxfs=e.read_shift(2),r.crc=e.read_shift(4),r}function tc(e,r,t){if(!t.cellStyles)return ua(e,r);var a=t&&t.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(t.biff>=5||!t.biff)&&(l.level=c>>8&7),l}function ac(e,r){var t=da(12);t.write_shift(2,r),t.write_shift(2,r),t.write_shift(2,256*e.width),t.write_shift(2,0);var a=0;return e.hidden&&(a|=1),t.write_shift(1,a),a=e.level||0,t.write_shift(1,a),t.write_shift(2,0),t}function nc(e,r){var t={};return r<32||(e.l+=16,t.header=pn(e,8),t.footer=pn(e,8),e.l+=2),t}function sc(e,r,t){var a={area:!1};if(5!=t.biff)return e.l+=r,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}function ic(e){for(var r=da(2*e),t=0;t<e;++t)r.write_shift(2,t+1);return r}var oc=Ti,cc=ri,lc=ci;function fc(e){var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n={fmt:r,env:t,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}function hc(e,r,t){t.biffguess&&5==t.biff&&(t.biff=2);var a=Ti(e,6);++e.l;var n=li(e,r-7,t);return a.t="str",a.val=n,a}function uc(e){var r=Ti(e,6);++e.l;var t=pn(e,8);return r.t="n",r.val=t,r}function dc(e,r,t){var a=da(15);return bv(a,e,r),a.write_shift(8,t,"f"),a}function pc(e){var r=Ti(e,6);++e.l;var t=e.read_shift(2);return r.t="n",r.val=t,r}function mc(e,r,t){var a=da(9);return bv(a,e,r),a.write_shift(2,t),a}function bc(e){var r=e.read_shift(1);return 0===r?(e.l++,""):e.read_shift(r,"sbcs-cont")}function vc(e,r){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=r-13}function gc(e,r,t){var a=e.l+r,n=Ti(e,6),s=e.read_shift(2),i=oi(e,s,t);return e.l=a,n.t="str",n.val=i,n}var wc=[2,3,48,49,131,139,140,245],kc=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},r=nr({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function t(r,t){var a=[],n=A(1);switch(t.type){case"base64":n=C(S(r));break;case"binary":n=C(r);break;case"buffer":case"array":n=r;break}ha(n,0);var s=n.read_shift(1),i=!!(136&s),o=!1,c=!1;switch(s){case 2:break;case 3:break;case 48:o=!0,i=!0;break;case 49:o=!0,i=!0;break;case 131:break;case 139:break;case 140:c=!0;break;case 245:break;default:throw new Error("DBF Unsupported Version: "+s.toString(16))}var l=0,f=521;2==s&&(l=n.read_shift(2)),n.l+=3,2!=s&&(l=n.read_shift(4)),l>1048576&&(l=1e6),2!=s&&(f=n.read_shift(2));var h=n.read_shift(2),u=t.codepage||1252;2!=s&&(n.l+=16,n.read_shift(1),0!==n[n.l]&&(u=e[n[n.l]]),n.l+=1,n.l+=2),c&&(n.l+=36);var d=[],p={},b=Math.min(n.length,2==s?521:f-10-(o?264:0)),v=c?32:11;while(n.l<b&&13!=n[n.l])switch(p={},p.name=m.utils.decode(u,n.slice(n.l,n.l+v)).replace(/[\u0000\r\n].*$/g,""),n.l+=v,p.type=String.fromCharCode(n.read_shift(1)),2==s||c||(p.offset=n.read_shift(4)),p.len=n.read_shift(1),2==s&&(p.offset=n.read_shift(2)),p.dec=n.read_shift(1),p.name.length&&d.push(p),2!=s&&(n.l+=c?13:14),p.type){case"B":o&&8==p.len||!t.WTF||console.log("Skipping "+p.name+":"+p.type);break;case"G":case"P":t.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+p.type)}if(13!==n[n.l]&&(n.l=f-1),13!==n.read_shift(1))throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=f;var g=0,w=0;for(a[0]=[],w=0;w!=d.length;++w)a[0][w]=d[w].name;while(l-- >0)if(42!==n[n.l])for(++n.l,a[++g]=[],w=0,w=0;w!=d.length;++w){var k=n.slice(n.l,n.l+d[w].len);n.l+=d[w].len,ha(k,0);var T=m.utils.decode(u,k);switch(d[w].type){case"C":T.trim().length&&(a[g][w]=T.replace(/\s+$/,""));break;case"D":8===T.length?a[g][w]=new Date(+T.slice(0,4),+T.slice(4,6)-1,+T.slice(6,8)):a[g][w]=T;break;case"F":a[g][w]=parseFloat(T.trim());break;case"+":case"I":a[g][w]=c?2147483648^k.read_shift(-4,"i"):k.read_shift(4,"i");break;case"L":switch(T.trim().toUpperCase()){case"Y":case"T":a[g][w]=!0;break;case"N":case"F":a[g][w]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+T+"|")}break;case"M":if(!i)throw new Error("DBF Unexpected MEMO for type "+s.toString(16));a[g][w]="##MEMO##"+(c?parseInt(T.trim(),10):k.read_shift(4));break;case"N":T=T.replace(/\u0000/g,"").trim(),T&&"."!=T&&(a[g][w]=+T||0);break;case"@":a[g][w]=new Date(k.read_shift(-8,"f")-621356832e5);break;case"T":a[g][w]=new Date(864e5*(k.read_shift(4)-2440588)+k.read_shift(4));break;case"Y":a[g][w]=k.read_shift(4,"i")/1e4+k.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":a[g][w]=-k.read_shift(-8,"f");break;case"B":if(o&&8==d[w].len){a[g][w]=k.read_shift(8,"f");break}case"G":case"P":k.l+=d[w].len;break;case"0":if("_NullFlags"===d[w].name)break;default:throw new Error("DBF Unsupported data type "+d[w].type)}}else n.l+=h;if(2!=s&&n.l<n.length&&26!=n[n.l++])throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));return t&&t.sheetRows&&(a=a.slice(0,t.sheetRows)),t.DBF=d,a}function a(e,r){var a=r||{};a.dateNF||(a.dateNF="yyyymmdd");var n=Ba(t(e,a),a);return n["!cols"]=a.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete a.DBF,n}function n(e,r){try{return Ma(a(e,r),r)}catch(t){if(r&&r.WTF)throw t}return{SheetNames:[],Sheets:{}}}var i={B:8,C:250,L:1,D:8,"?":0,"":0};function o(e,t){var a=t||{};if(+a.codepage>=0&&f(+a.codepage),"string"==a.type)throw new Error("Cannot write DBF to JS string");var n=ma(),o=fw(e,{header:1,raw:!0,cellDates:!0}),c=o[0],l=o.slice(1),h=e["!cols"]||[],u=0,d=0,p=0,m=1;for(u=0;u<c.length;++u)if(((h[u]||{}).DBF||{}).name)c[u]=h[u].DBF.name,++p;else if(null!=c[u]){if(++p,"number"===typeof c[u]&&(c[u]=c[u].toString(10)),"string"!==typeof c[u])throw new Error("DBF Invalid column name "+c[u]+" |"+typeof c[u]+"|");if(c.indexOf(c[u])!==u)for(d=0;d<1024;++d)if(-1==c.indexOf(c[u]+"_"+d)){c[u]+="_"+d;break}}var b=Fa(e["!ref"]),v=[],g=[],w=[];for(u=0;u<=b.e.c-b.s.c;++u){var k="",T="",E=0,S=[];for(d=0;d<l.length;++d)null!=l[d][u]&&S.push(l[d][u]);if(0!=S.length&&null!=c[u]){for(d=0;d<S.length;++d){switch(typeof S[d]){case"number":T="B";break;case"string":T="C";break;case"boolean":T="L";break;case"object":T=S[d]instanceof Date?"D":"C";break;default:T="C"}E=Math.max(E,String(S[d]).length),k=k&&k!=T?"C":T}E>250&&(E=250),T=((h[u]||{}).DBF||{}).type,"C"==T&&h[u].DBF.len>E&&(E=h[u].DBF.len),"B"==k&&"N"==T&&(k="N",w[u]=h[u].DBF.dec,E=h[u].DBF.len),g[u]="C"==k||"N"==T?E:i[k]||0,m+=g[u],v[u]=k}else v[u]="?"}var y=n.next(32);for(y.write_shift(4,318902576),y.write_shift(4,l.length),y.write_shift(2,296+32*p),y.write_shift(2,m),u=0;u<4;++u)y.write_shift(4,0);for(y.write_shift(4,0|(+r[s]||3)<<8),u=0,d=0;u<c.length;++u)if(null!=c[u]){var _=n.next(32),A=(c[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);_.write_shift(1,A,"sbcs"),_.write_shift(1,"?"==v[u]?"C":v[u],"sbcs"),_.write_shift(4,d),_.write_shift(1,g[u]||i[v[u]]||0),_.write_shift(1,w[u]||0),_.write_shift(1,2),_.write_shift(4,0),_.write_shift(1,0),_.write_shift(4,0),_.write_shift(4,0),d+=g[u]||i[v[u]]||0}var x=n.next(264);for(x.write_shift(4,13),u=0;u<65;++u)x.write_shift(4,0);for(u=0;u<l.length;++u){var C=n.next(m);for(C.write_shift(1,0),d=0;d<c.length;++d)if(null!=c[d])switch(v[d]){case"L":C.write_shift(1,null==l[u][d]?63:l[u][d]?84:70);break;case"B":C.write_shift(8,l[u][d]||0,"f");break;case"N":var R="0";for("number"==typeof l[u][d]&&(R=l[u][d].toFixed(w[d]||0)),p=0;p<g[d]-R.length;++p)C.write_shift(1,32);C.write_shift(1,R,"sbcs");break;case"D":l[u][d]?(C.write_shift(4,("0000"+l[u][d].getFullYear()).slice(-4),"sbcs"),C.write_shift(2,("00"+(l[u][d].getMonth()+1)).slice(-2),"sbcs"),C.write_shift(2,("00"+l[u][d].getDate()).slice(-2),"sbcs")):C.write_shift(8,"00000000","sbcs");break;case"C":var O=String(null!=l[u][d]?l[u][d]:"").slice(0,g[d]);for(C.write_shift(1,O,"sbcs"),p=0;p<g[d]-O.length;++p)C.write_shift(1,32);break}}return n.next(1).write_shift(1,26),n.end()}return{to_workbook:n,to_sheet:a,from_sheet:o}}(),Tc=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},r=new RegExp("N("+tr(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),t=function(r,t){var a=e[t];return"number"==typeof a?g(a):a},a=function(e,r,t){var a=r.charCodeAt(0)-32<<4|t.charCodeAt(0)-48;return 59==a?e:g(a)};function n(e,r){switch(r.type){case"base64":return s(S(e),r);case"binary":return s(e,r);case"buffer":return s(y&&Buffer.isBuffer(e)?e.toString("binary"):O(e),r);case"array":return s(gr(e),r)}throw new Error("Unrecognized type "+r.type)}function s(e,n){var s,i=e.split(/[\n\r]+/),o=-1,c=-1,l=0,h=0,u=[],d=[],p=null,b={},v=[],g=[],w=[],k=0;for(+n.codepage>=0&&f(+n.codepage);l!==i.length;++l){k=0;var T,E=i[l].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,a).replace(r,t),S=E.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),y=S[0];if(E.length>0)switch(y){case"ID":break;case"E":break;case"B":break;case"O":break;case"W":break;case"P":"P"==S[1].charAt(0)&&d.push(E.slice(3).replace(/;;/g,";"));break;case"C":var _=!1,A=!1,x=!1,C=!1,R=-1,O=-1;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"A":break;case"X":c=parseInt(S[h].slice(1))-1,A=!0;break;case"Y":for(o=parseInt(S[h].slice(1))-1,A||(c=0),s=u.length;s<=o;++s)u[s]=[];break;case"K":T=S[h].slice(1),'"'===T.charAt(0)?T=T.slice(1,T.length-1):"TRUE"===T?T=!0:"FALSE"===T?T=!1:isNaN(Tr(T))?isNaN(Sr(T).getDate())||(T=vr(T)):(T=Tr(T),null!==p&&Me(p)&&(T=ur(T))),"undefined"!==typeof m&&"string"==typeof T&&"string"!=(n||{}).type&&(n||{}).codepage&&(T=m.utils.decode(n.codepage,T)),_=!0;break;case"E":C=!0;var I=_h(S[h].slice(1),{r:o,c:c});u[o][c]=[u[o][c],I];break;case"S":x=!0,u[o][c]=[u[o][c],"S5S"];break;case"G":break;case"R":R=parseInt(S[h].slice(1))-1;break;case"C":O=parseInt(S[h].slice(1))-1;break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}if(_&&(u[o][c]&&2==u[o][c].length?u[o][c][0]=T:u[o][c]=T,p=null),x){if(C)throw new Error("SYLK shared formula cannot have own formula");var N=R>-1&&u[R][O];if(!N||!N[1])throw new Error("SYLK shared formula cannot find base");u[o][c][1]=Ch(N[1],{r:o-R,c:c-O})}break;case"F":var D=0;for(h=1;h<S.length;++h)switch(S[h].charAt(0)){case"X":c=parseInt(S[h].slice(1))-1,++D;break;case"Y":for(o=parseInt(S[h].slice(1))-1,s=u.length;s<=o;++s)u[s]=[];break;case"M":k=parseInt(S[h].slice(1))/20;break;case"F":break;case"G":break;case"P":p=d[parseInt(S[h].slice(1))];break;case"S":break;case"D":break;case"N":break;case"W":for(w=S[h].slice(1).split(" "),s=parseInt(w[0],10);s<=parseInt(w[1],10);++s)k=parseInt(w[2],10),g[s-1]=0===k?{hidden:!0}:{wch:k},Rl(g[s-1]);break;case"C":c=parseInt(S[h].slice(1))-1,g[c]||(g[c]={});break;case"R":o=parseInt(S[h].slice(1))-1,v[o]||(v[o]={}),k>0?(v[o].hpt=k,v[o].hpx=Dl(k)):0===k&&(v[o].hidden=!0);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}D<1&&(p=null);break;default:if(n&&n.WTF)throw new Error("SYLK bad record "+E)}}return v.length>0&&(b["!rows"]=v),g.length>0&&(b["!cols"]=g),n&&n.sheetRows&&(u=u.slice(0,n.sheetRows)),[u,b]}function i(e,r){var t=n(e,r),a=t[0],s=t[1],i=Ba(a,r);return tr(s).forEach((function(e){i[e]=s[e]})),i}function o(e,r){return Ma(i(e,r),r)}function c(e,r,t,a){var n="C;Y"+(t+1)+";X"+(a+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+xh(e.f,{r:t,c:a}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"';break}return n}function l(e,r){r.forEach((function(r,t){var a="F;W"+(t+1)+" "+(t+1)+" ";r.hidden?a+="0":("number"!=typeof r.width||r.wpx||(r.wpx=yl(r.width)),"number"!=typeof r.wpx||r.wch||(r.wch=_l(r.wpx)),"number"==typeof r.wch&&(a+=Math.round(r.wch)))," "!=a.charAt(a.length-1)&&e.push(a)}))}function h(e,r){r.forEach((function(r,t){var a="F;";r.hidden?a+="M0;":r.hpt?a+="M"+20*r.hpt+";":r.hpx&&(a+="M"+20*Nl(r.hpx)+";"),a.length>2&&e.push(a+"R"+(t+1))}))}function u(e,r){var t,a=["ID;PWXL;N;E"],n=[],s=Fa(e["!ref"]),i=Array.isArray(e),o="\r\n";a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&l(a,e["!cols"]),e["!rows"]&&h(a,e["!rows"]),a.push("B;Y"+(s.e.r-s.s.r+1)+";X"+(s.e.c-s.s.c+1)+";D"+[s.s.c,s.s.r,s.e.c,s.e.r].join(" "));for(var f=s.s.r;f<=s.e.r;++f)for(var u=s.s.c;u<=s.e.c;++u){var d=Ia({r:f,c:u});t=i?(e[f]||[])[u]:e[d],t&&(null!=t.v||t.f&&!t.F)&&n.push(c(t,e,f,u,r))}return a.join(o)+o+n.join(o)+o+"E"+o}return e["|"]=254,{to_workbook:o,to_sheet:i,from_sheet:u}}(),Ec=function(){function e(e,t){switch(t.type){case"base64":return r(S(e),t);case"binary":return r(e,t);case"buffer":return r(y&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return r(gr(e),t)}throw new Error("Unrecognized type "+t.type)}function r(e,r){for(var t=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==t.length;++s)if("BOT"!==t[s].trim()){if(!(a<0)){var o=t[s].trim().split(","),c=o[0],l=o[1];++s;var f=t[s]||"";while(1&(f.match(/["]/g)||[]).length&&s<t.length-1)f+="\n"+t[++s];switch(f=f.trim(),+c){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(Tr(l))?isNaN(Sr(l).getDate())?i[a][n]=l:i[a][n]=vr(l):i[a][n]=Tr(l),++n;break;case 1:f=f.slice(1,f.length-1),f=f.replace(/""/g,'"'),k&&f&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null;break}if("EOD"===f)break}}else i[++a]=[],n=0;return r&&r.sheetRows&&(i=i.slice(0,r.sheetRows)),i}function t(r,t){return Ba(e(r,t),t)}function a(e,r){return Ma(t(e,r),r)}var n=function(){var e=function(e,r,t,a,n){e.push(r),e.push(t+","+a),e.push('"'+n.replace(/"/g,'""')+'"')},r=function(e,r,t,a){e.push(r+","+t),e.push(1==r?'"'+a.replace(/"/g,'""')+'"':a)};return function(t){var a,n=[],s=Fa(t["!ref"]),i=Array.isArray(t);e(n,"TABLE",0,1,"sheetjs"),e(n,"VECTORS",0,s.e.r-s.s.r+1,""),e(n,"TUPLES",0,s.e.c-s.s.c+1,""),e(n,"DATA",0,0,"");for(var o=s.s.r;o<=s.e.r;++o){r(n,-1,0,"BOT");for(var c=s.s.c;c<=s.e.c;++c){var l=Ia({r:o,c:c});if(a=i?(t[o]||[])[c]:t[l],a)switch(a.t){case"n":var f=k?a.w:a.v;f||null==a.v||(f=a.v),null==f?k&&a.f&&!a.F?r(n,1,0,"="+a.f):r(n,1,0,""):r(n,0,f,"V");break;case"b":r(n,0,a.v?1:0,a.v?"TRUE":"FALSE");break;case"s":r(n,1,0,!k||isNaN(a.v)?a.v:'="'+a.v+'"');break;case"d":a.w||(a.w=Ve(a.z||J[14],cr(vr(a.v)))),k?r(n,0,a.w,"V"):r(n,1,0,a.w);break;default:r(n,1,0,"")}else r(n,1,0,"")}}r(n,-1,0,"EOD");var h="\r\n",u=n.join(h);return u}}();return{to_workbook:a,to_sheet:t,from_sheet:n}}(),Sc=function(){function e(e){return e.replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n")}function r(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(r,t){for(var a=r.split("\n"),n=-1,s=-1,i=0,o=[];i!==a.length;++i){var c=a[i].trim().split(":");if("cell"===c[0]){var l=Oa(c[1]);if(o.length<=l.r)for(n=o.length;n<=l.r;++n)o[n]||(o[n]=[]);switch(n=l.r,s=l.c,c[2]){case"t":o[n][s]=e(c[3]);break;case"v":o[n][s]=+c[3];break;case"vtf":var f=c[c.length-1];case"vtc":switch(c[3]){case"nl":o[n][s]=!!+c[4];break;default:o[n][s]=+c[4];break}"vtf"==c[2]&&(o[n][s]=[o[n][s],f])}}}return t&&t.sheetRows&&(o=o.slice(0,t.sheetRows)),o}function a(e,r){return Ba(t(e,r),r)}function n(e,r){return Ma(a(e,r),r)}var s=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),i=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",o=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),c="--SocialCalcSpreadsheetControlSave--";function l(e){if(!e||!e["!ref"])return"";for(var t,a=[],n=[],s="",i=Na(e["!ref"]),o=Array.isArray(e),c=i.s.r;c<=i.e.r;++c)for(var l=i.s.c;l<=i.e.c;++l)if(s=Ia({r:c,c:l}),t=o?(e[c]||[])[l]:e[s],t&&null!=t.v&&"z"!==t.t){switch(n=["cell",s,"t"],t.t){case"s":case"str":n.push(r(t.v));break;case"n":t.f?(n[2]="vtf",n[3]="n",n[4]=t.v,n[5]=r(t.f)):(n[2]="v",n[3]=t.v);break;case"b":n[2]="vt"+(t.f?"f":"c"),n[3]="nl",n[4]=t.v?"1":"0",n[5]=r(t.f||(t.v?"TRUE":"FALSE"));break;case"d":var f=cr(vr(t.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=t.w||Ve(t.z||J[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}function f(e){return[s,i,o,i,l(e),c].join("\n")}return{to_workbook:n,to_sheet:a,from_sheet:f}}(),yc=function(){function e(e,r,t,a,n){n.raw?r[t][a]=e:""===e||("TRUE"===e?r[t][a]=!0:"FALSE"===e?r[t][a]=!1:isNaN(Tr(e))?isNaN(Sr(e).getDate())?r[t][a]=e:r[t][a]=vr(e):r[t][a]=Tr(e))}function r(r,t){var a=t||{},n=[];if(!r||0===r.length)return n;var s=r.split(/[\r\n]/),i=s.length-1;while(i>=0&&0===s[i].length)--i;for(var o=10,c=0,l=0;l<=i;++l)c=s[l].indexOf(" "),-1==c?c=s[l].length:c++,o=Math.max(o,c);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,o).trim(),n,l,f,a),f=1;f<=(s[l].length-o)/10+1;++f)e(s[l].slice(o+10*(f-1),o+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}var t={44:",",9:"\t",59:";",124:"|"},a={44:3,9:2,59:1,124:0};function n(e){for(var r={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(r[i]=(r[i]||0)+1);for(s in i=[],r)Object.prototype.hasOwnProperty.call(r,s)&&i.push([r[s],s]);if(!i.length)for(s in r=a,r)Object.prototype.hasOwnProperty.call(r,s)&&i.push([r[s],s]);return i.sort((function(e,r){return e[0]-r[0]||a[e[1]]-a[r[1]]})),t[i.pop()[1]]||44}function s(e,r){var t=r||{},a="";null!=w&&null==t.dense&&(t.dense=w);var s=t.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(a=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(a=e.charAt(4),e=e.slice(6)):a=n(e.slice(0,1024)):a=t&&t.FS?t.FS:n(e.slice(0,1024));var o=0,c=0,l=0,f=0,h=0,u=a.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var b=null!=t.dateNF?Ye(t.dateNF):null;function v(){var r=e.slice(f,h),a={};if('"'==r.charAt(0)&&'"'==r.charAt(r.length-1)&&(r=r.slice(1,-1).replace(/""/g,'"')),0===r.length)a.t="z";else if(t.raw)a.t="s",a.v=r;else if(0===r.trim().length)a.t="s",a.v=r;else if(61==r.charCodeAt(0))34==r.charCodeAt(1)&&34==r.charCodeAt(r.length-1)?(a.t="s",a.v=r.slice(2,-1).replace(/""/g,'"')):Oh(r)?(a.t="n",a.f=r.slice(1)):(a.t="s",a.v=r);else if("TRUE"==r)a.t="b",a.v=!0;else if("FALSE"==r)a.t="b",a.v=!1;else if(isNaN(l=Tr(r)))if(!isNaN(Sr(r).getDate())||b&&r.match(b)){a.z=t.dateNF||J[14];var n=0;b&&r.match(b)&&(r=Ke(r,t.dateNF,r.match(b)||[]),n=1),t.cellDates?(a.t="d",a.v=vr(r,n)):(a.t="n",a.v=cr(vr(r,n))),!1!==t.cellText&&(a.w=Ve(a.z,a.v instanceof Date?cr(a.v):a.v)),t.cellNF||delete a.z}else a.t="s",a.v=r;else a.t="n",!1!==t.cellText&&(a.w=r),a.v=l;if("z"==a.t||(t.dense?(s[o]||(s[o]=[]),s[o][c]=a):s[Ia({c:c,r:o})]=a),f=h+1,m=e.charCodeAt(f),i.e.c<c&&(i.e.c=c),i.e.r<o&&(i.e.r=o),p==u)++c;else if(c=0,++o,t.sheetRows&&t.sheetRows<=o)return!0}e:for(;h<e.length;++h)switch(p=e.charCodeAt(h)){case 34:34===m&&(d=!d);break;case u:case 10:case 13:if(!d&&v())break e;break;default:break}return h-f>0&&v(),s["!ref"]=Da(i),s}function i(e,t){return t&&t.PRN?t.FS||"sep="==e.slice(0,4)||e.indexOf("\t")>=0||e.indexOf(",")>=0||e.indexOf(";")>=0?s(e,t):Ba(r(e,t),t):s(e,t)}function o(e,r){var t="",a="string"==r.type?[0,0,0,0]:Xg(e,r);switch(r.type){case"base64":t=S(e);break;case"binary":t=e;break;case"buffer":t=65001==r.codepage?e.toString("utf8"):r.codepage&&"undefined"!==typeof m?m.utils.decode(r.codepage,e):y&&Buffer.isBuffer(e)?e.toString("binary"):O(e);break;case"array":t=gr(e);break;case"string":t=e;break;default:throw new Error("Unrecognized type "+r.type)}return 239==a[0]&&187==a[1]&&191==a[2]?t=ft(t.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?t=ft(t):"binary"==r.type&&"undefined"!==typeof m&&r.codepage&&(t=m.utils.decode(r.codepage,m.utils.encode(28591,t))),"socialcalc:version:"==t.slice(0,19)?Sc.to_sheet("string"==r.type?t:ft(t),r):i(t,r)}function c(e,r){return Ma(o(e,r),r)}function l(e){for(var r,t=[],a=Fa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var c=Ia({r:s,c:o});if(r=n?(e[s]||[])[o]:e[c],r&&null!=r.v){var l=(r.w||(La(r),r.w)||"").slice(0,10);while(l.length<10)l+=" ";i.push(l+(0===o?" ":""))}else i.push("          ")}t.push(i.join(""))}return t.join("\n")}return{to_workbook:c,to_sheet:o,from_sheet:l}}();function _c(e,r){var t=r||{},a=!!t.WTF;t.WTF=!0;try{var n=Tc.to_workbook(e,t);return t.WTF=a,n}catch(s){if(t.WTF=a,!s.message.match(/SYLK bad record ID/)&&a)throw s;return yc.to_workbook(e,r)}}var Ac=function(){function e(e,r,t){if(e){ha(e,e.l||0);var a=t.Enum||W;while(e.l<e.length){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,c=s.f&&s.f(e,i,t);if(e.l=o,r(c,s,n))return}}}function r(e,r){switch(r.type){case"base64":return t(C(S(e)),r);case"binary":return t(C(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}function t(r,t){if(!r)return r;var a=t||{};null!=w&&null==a.dense&&(a.dense=w);var n=a.dense?[]:{},s="Sheet1",i="",o=0,c={},l=[],f=[],h={s:{r:0,c:0},e:{r:0,c:0}},u=a.sheetRows||0;if(0==r[2]&&(8==r[3]||9==r[3])&&r.length>=16&&5==r[14]&&108===r[15])throw new Error("Unsupported Works 3 for Mac file");if(2==r[2])a.Enum=W,e(r,(function(e,r,t){switch(t){case 0:a.vers=e,e>=4096&&(a.qpro=!0);break;case 6:h=e;break;case 204:e&&(i=e);break;case 222:i=e;break;case 15:case 51:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==t&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=a.dateNF||J[14],a.cellDates&&(e[1].t="d",e[1].v=ur(e[1].v))),a.qpro&&e[3]>o&&(n["!ref"]=Da(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s=i||"Sheet"+(o+1),i="");var f=a.dense?(n[e[0].r]||[])[e[0].c]:n[Ia(e[0])];if(f){f.t=e[1].t,f.v=e[1].v,null!=e[1].z&&(f.z=e[1].z),null!=e[1].f&&(f.f=e[1].f);break}a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Ia(e[0])]=e[1];break;default:}}),a);else{if(26!=r[2]&&14!=r[2])throw new Error("Unrecognized LOTUS BOF "+r[2]);a.Enum=H,14==r[2]&&(a.qpro=!0,r.l=0),e(r,(function(e,r,t){switch(t){case 204:s=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(n["!ref"]=Da(h),c[s]=n,l.push(s),n=a.dense?[]:{},h={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],s="Sheet"+(o+1)),u>0&&e[0].r>=u)break;a.dense?(n[e[0].r]||(n[e[0].r]=[]),n[e[0].r][e[0].c]=e[1]):n[Ia(e[0])]=e[1],h.e.c<e[0].c&&(h.e.c=e[0].c),h.e.r<e[0].r&&(h.e.r=e[0].r);break;case 27:e[14e3]&&(f[e[14e3][0]]=e[14e3][1]);break;case 1537:f[e[0]]=e[1],e[0]==o&&(s=e[1]);break;default:break}}),a)}if(n["!ref"]=Da(h),c[i||s]=n,l.push(i||s),!f.length)return{SheetNames:l,Sheets:c};for(var d={},p=[],m=0;m<f.length;++m)c[l[m]]?(p.push(f[m]||l[m]),d[f[m]]=c[f[m]]||c[l[m]]):(p.push(f[m]),d[f[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function a(e,r){var t=r||{};if(+t.codepage>=0&&f(+t.codepage),"string"==t.type)throw new Error("Cannot write WK1 to JS string");var a=ma(),n=Fa(e["!ref"]),i=Array.isArray(e),o=[];pv(a,0,s(1030)),pv(a,6,c(n));for(var l=Math.min(n.e.r,8191),h=n.s.r;h<=l;++h)for(var d=Ea(h),m=n.s.c;m<=n.e.c;++m){h===n.s.r&&(o[m]=Aa(m));var v=o[m]+d,g=i?(e[h]||[])[m]:e[v];if(g&&"z"!=g.t)if("n"==g.t)(0|g.v)==g.v&&g.v>=-32768&&g.v<=32767?pv(a,13,p(h,m,g.v)):pv(a,14,b(h,m,g.v));else{var w=La(g);pv(a,15,u(h,m,w.slice(0,239)))}}return pv(a,1),a.end()}function n(e,r){var t=r||{};if(+t.codepage>=0&&f(+t.codepage),"string"==t.type)throw new Error("Cannot write WK3 to JS string");var a=ma();pv(a,0,i(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&pv(a,27,B(e.SheetNames[n],s++));var o=0;for(n=0;n<e.SheetNames.length;++n){var c=e.Sheets[e.SheetNames[n]];if(c&&c["!ref"]){for(var l=Fa(c["!ref"]),h=Array.isArray(c),u=[],d=Math.min(l.e.r,8191),p=l.s.r;p<=d;++p)for(var m=Ea(p),b=l.s.c;b<=l.e.c;++b){p===l.s.r&&(u[b]=Aa(b));var v=u[b]+m,g=h?(c[p]||[])[b]:c[v];if(g&&"z"!=g.t)if("n"==g.t)pv(a,23,O(p,b,o,g.v));else{var w=La(g);pv(a,22,A(p,b,o,w.slice(0,239)))}}++o}}return pv(a,1),a.end()}function s(e){var r=da(2);return r.write_shift(2,e),r}function i(e){var r=da(26);r.write_shift(2,4096),r.write_shift(2,4),r.write_shift(4,0);for(var t=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var c=Na(o["!ref"]);t<c.e.r&&(t=c.e.r),a<c.e.c&&(a=c.e.c)}}return t>8191&&(t=8191),r.write_shift(2,t),r.write_shift(1,n),r.write_shift(1,a),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,1),r.write_shift(1,2),r.write_shift(4,0),r.write_shift(4,0),r}function o(e,r,t){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==r&&t.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==r&&t.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}function c(e){var r=da(8);return r.write_shift(2,e.s.c),r.write_shift(2,e.s.r),r.write_shift(2,e.e.c),r.write_shift(2,e.e.r),r}function l(e,r,t){var a=[{c:0,r:0},{t:"n",v:0},0,0];return t.qpro&&20768!=t.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function h(e,r,t){var a=e.l+r,n=l(e,r,t);if(n[1].t="s",20768==t.vers){e.l++;var s=e.read_shift(1);return n[1].v=e.read_shift(s,"utf8"),n}return t.qpro&&e.l++,n[1].v=e.read_shift(a-e.l,"cstr"),n}function u(e,r,t){var a=da(7+t.length);a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=t.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function d(e,r,t){var a=l(e,r,t);return a[1].v=e.read_shift(2,"i"),a}function p(e,r,t){var a=da(7);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(2,t,"i"),a}function m(e,r,t){var a=l(e,r,t);return a[1].v=e.read_shift(8,"f"),a}function b(e,r,t){var a=da(13);return a.write_shift(1,255),a.write_shift(2,r),a.write_shift(2,e),a.write_shift(8,t,"f"),a}function v(e,r,t){var a=e.l+r,n=l(e,r,t);if(n[1].v=e.read_shift(8,"f"),t.qpro)e.l=a;else{var s=e.read_shift(2);E(e.slice(e.l,e.l+s),n),e.l+=s}return n}function g(e,r,t){var a=32768&r;return r&=-32769,r=(a?e:0)+(r>=8192?r-16384:r),(a?"":"$")+(t?Aa(r):Ea(r))}var k={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},T=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function E(e,r){ha(e,0);var t=[],a=0,n="",s="",i="",o="";while(e.l<e.length){var c=e[e.l++];switch(c){case 0:t.push(e.read_shift(8,"f"));break;case 1:s=g(r[0].c,e.read_shift(2),!0),n=g(r[0].r,e.read_shift(2),!1),t.push(s+n);break;case 2:var l=g(r[0].c,e.read_shift(2),!0),f=g(r[0].r,e.read_shift(2),!1);s=g(r[0].c,e.read_shift(2),!0),n=g(r[0].r,e.read_shift(2),!1),t.push(l+f+":"+s+n);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:t.push("("+t.pop()+")");break;case 5:t.push(e.read_shift(2));break;case 6:var h="";while(c=e[e.l++])h+=String.fromCharCode(c);t.push('"'+h.replace(/"/g,'""')+'"');break;case 8:t.push("-"+t.pop());break;case 23:t.push("+"+t.pop());break;case 22:t.push("NOT("+t.pop()+")");break;case 20:case 21:o=t.pop(),i=t.pop(),t.push(["AND","OR"][c-20]+"("+i+","+o+")");break;default:if(c<32&&T[c])o=t.pop(),i=t.pop(),t.push(i+T[c]+o);else{if(!k[c])return c<=7?console.error("WK1 invalid opcode "+c.toString(16)):c<=24?console.error("WK1 unsupported op "+c.toString(16)):c<=30?console.error("WK1 invalid opcode "+c.toString(16)):c<=115?console.error("WK1 unsupported function opcode "+c.toString(16)):console.error("WK1 unrecognized opcode "+c.toString(16));if(a=k[c][1],69==a&&(a=e[e.l++]),a>t.length)return void console.error("WK1 bad formula parse 0x"+c.toString(16)+":|"+t.join("|")+"|");var u=t.slice(-a);t.length-=a,t.push(k[c][0]+"("+u.join(",")+")")}}}1==t.length?r[1].f=""+t[0]:console.error("WK1 bad formula parse |"+t.join("|")+"|")}function y(e){var r=[{c:0,r:0},{t:"n",v:0},0];return r[0].r=e.read_shift(2),r[3]=e[e.l++],r[0].c=e[e.l++],r}function _(e,r){var t=y(e,r);return t[1].t="s",t[1].v=e.read_shift(r-4,"cstr"),t}function A(e,r,t,a){var n=da(6+a.length);n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function x(e,r){var t=y(e,r);t[1].v=e.read_shift(2);var a=t[1].v>>1;if(1&t[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64;break}return t[1].v=a,t}function R(e,r){var t=y(e,r),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(t[1].t="e",t[1].v=15):0===a&&3489660928===n?(t[1].t="e",t[1].v=42):t[1].v=0,t;var i=32768&s;return s=(32767&s)-16446,t[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),t}function O(e,r,t,a){var n=da(14);if(n.write_shift(2,e),n.write_shift(1,t),n.write_shift(1,r),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s=0,i=0,o=0,c=0;return a<0&&(s=1,a=-a),i=0|Math.log2(a),a/=Math.pow(2,i-31),c=a>>>0,0==(2147483648&c)&&(a/=2,++i,c=a>>>0),a-=c,c|=2147483648,c>>>=0,a*=Math.pow(2,32),o=a>>>0,n.write_shift(4,o),n.write_shift(4,c),i+=16383+(s?32768:0),n.write_shift(2,i),n}function I(e,r){var t=R(e,14);return e.l+=r-14,t}function N(e,r){var t=y(e,r),a=e.read_shift(4);return t[1].v=a>>6,t}function D(e,r){var t=y(e,r),a=e.read_shift(8,"f");return t[1].v=a,t}function F(e,r){var t=D(e,14);return e.l+=r-10,t}function P(e,r){return 0==e[e.l+r-1]?e.read_shift(r,"cstr"):""}function L(e,r){var t=e[e.l++];t>r-1&&(t=r-1);var a="";while(a.length<t)a+=String.fromCharCode(e[e.l++]);return a}function M(e,r,t){if(t.qpro&&!(r<21)){var a=e.read_shift(1);e.l+=17,e.l+=1,e.l+=2;var n=e.read_shift(r-21,"cstr");return[a,n]}}function U(e,r){var t={},a=e.l+r;while(e.l<a){var n=e.read_shift(2);if(14e3==n){t[n]=[0,""],t[n][0]=e.read_shift(2);while(e[e.l])t[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return t}function B(e,r){var t=da(5+e.length);t.write_shift(2,14e3),t.write_shift(2,r);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);t[t.l++]=n>127?95:n}return t[t.l++]=0,t}var W={0:{n:"BOF",f:Qs},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:o},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:d},14:{n:"NUMBER",f:m},15:{n:"LABEL",f:h},16:{n:"FORMULA",f:v},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:h},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:P},222:{n:"SHEETNAMELP",f:L},65535:{n:""}},H={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:_},23:{n:"NUMBER17",f:R},24:{n:"NUMBER18",f:x},25:{n:"FORMULA19",f:I},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:U},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:N},38:{n:"??"},39:{n:"NUMBER27",f:D},40:{n:"FORMULA28",f:F},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:P},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:M},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:a,book_to_wk3:n,to_workbook:r}}();function xc(e){var r={},t=e.match(Vr),a=0,n=!1;if(t)for(;a!=t.length;++a){var s=jr(t[a]);switch(s[0].replace(/\w*:/g,"")){case"<condense":break;case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":r.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;r.cp=o[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":r.outline=1;break;case"</outline>":break;case"<rFont":r.name=s.val;break;case"<sz":r.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":r.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":r.uval="double";break;case"singleAccounting":r.uval="single-accounting";break;case"doubleAccounting":r.uval="double-accounting";break}case"<u>":case"<u/>":r.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":r.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":r.i=1;break;case"</i>":break;case"<color":s.rgb&&(r.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":r.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":r.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":break;case"<scheme":break;case"<scheme>":case"<scheme/>":case"</scheme>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw new Error("Unrecognized rich format "+s[0])}}return r}var Cc=function(){var e=ut("t"),r=ut("rPr");function t(t){var a=t.match(e);if(!a)return{t:"s",v:""};var n={t:"s",v:Kr(a[1])},s=t.match(r);return s&&(n.s=xc(s[1])),n}var a=/<(?:\w+:)?r>/g,n=/<\/(?:\w+:)?r>/;return function(e){return e.replace(a,"").split(n).map(t).filter((function(e){return e.v}))}}(),Rc=function(){var e=/(\r\n|\n)/g;function r(e,r,t){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),r.push('<span style="'+a.join("")+'">'),e.b&&(r.push("<b>"),t.push("</b>")),e.i&&(r.push("<i>"),t.push("</i>")),e.strike&&(r.push("<s>"),t.push("</s>"));var n=e.valign||"";return"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(r.push("<"+n+">"),t.push("</"+n+">")),t.push("</span>"),e}function t(t){var a=[[],t.v,[]];return t.v?(t.s&&r(t.s,a[0],a[2]),a[0].join("")+a[1].replace(e,"<br/>")+a[2].join("")):""}return function(e){return e.map(t).join("")}}(),Oc=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,Ic=/<(?:\w+:)?r>/,Nc=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function Dc(e,r){var t=!r||r.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Kr(ft(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),a.r=ft(e),t&&(a.h=rt(a.t))):e.match(Ic)&&(a.r=ft(e),a.t=Kr(ft((e.replace(Nc,"").match(Oc)||[]).join("").replace(Vr,""))),t&&(a.h=Rc(Cc(a.r)))),a):{t:""}}var Fc=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,Pc=/<(?:\w+:)?(?:si|sstItem)>/g,Lc=/<\/(?:\w+:)?(?:si|sstItem)>/;function Mc(e,r){var t=[],a="";if(!e)return t;var n=e.match(Fc);if(n){a=n[2].replace(Pc,"").split(Lc);for(var s=0;s!=a.length;++s){var i=Dc(a[s].trim(),r);null!=i&&(t[t.length]=i)}n=jr(n[1]),t.Count=n.count,t.Unique=n.uniqueCount}return t}var Uc=/^\s|\s$|[\t\n\r]/;function Bc(e,r){if(!r.bookSST)return"";var t=[Ur];t[t.length]=Tt("sst",null,{xmlns:xt[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),n.t.match(Uc)&&(s+=' xml:space="preserve"'),s+=">"+Zr(n.t)+"</t>"),s+="</si>",t[t.length]=s}return t.length>2&&(t[t.length]="</sst>",t[1]=t[1].replace("/>",">")),t.join("")}function Wc(e){return[e.read_shift(4),e.read_shift(4)]}function Hc(e,r){var t=[],a=!1;return pa(e,(function(e,n,s){switch(s){case 159:t.Count=e[0],t.Unique=e[1];break;case 19:t.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||r.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),t}function Vc(e,r){return r||(r=da(8)),r.write_shift(4,e.Count),r.write_shift(4,e.Unique),r}var zc=$a;function Gc(e){var r=ma();ba(r,159,Vc(e));for(var t=0;t<e.length;++t)ba(r,19,zc(e[t]));return ba(r,160),r.end()}function jc(e){if("undefined"!==typeof m)return m.utils.encode(s,e);for(var r=[],t=e.split(""),a=0;a<t.length;++a)r[a]=t[a].charCodeAt(0);return r}function Xc(e,r){var t={};return t.Major=e.read_shift(2),t.Minor=e.read_shift(2),r>=4&&(e.l+=r-4),t}function $c(e){var r={};return r.id=e.read_shift(0,"lpp4"),r.R=Xc(e,4),r.U=Xc(e,4),r.W=Xc(e,4),r}function Yc(e){var r=e.read_shift(4),t=e.l+r-4,a={},n=e.read_shift(4),s=[];while(n-- >0)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=t)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+t);return a}function Kc(e){var r=[];e.l+=4;var t=e.read_shift(4);while(t-- >0)r.push(Yc(e));return r}function Jc(e){var r=[];e.l+=4;var t=e.read_shift(4);while(t-- >0)r.push(e.read_shift(0,"lpp4"));return r}function qc(e){var r={};return e.read_shift(4),e.l+=4,r.id=e.read_shift(0,"lpp4"),r.name=e.read_shift(0,"lpp4"),r.R=Xc(e,4),r.U=Xc(e,4),r.W=Xc(e,4),r}function Zc(e){var r=qc(e);if(r.ename=e.read_shift(0,"8lpp4"),r.blksz=e.read_shift(4),r.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return r}function Qc(e,r){var t=e.l+r,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(t-e.l>>1,"utf16le"),e.l=t,a}function el(e,r){var t={},a=e.l+r;return e.l+=4,t.Salt=e.slice(e.l,e.l+16),e.l+=16,t.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),t.VerifierHash=e.slice(e.l,a),e.l=a,t}function rl(e){var r=Xc(e);switch(r.Minor){case 2:return[r.Minor,tl(e,r)];case 3:return[r.Minor,al(e,r)];case 4:return[r.Minor,nl(e,r)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+r.Minor)}function tl(e){var r=e.read_shift(4);if(36!=(63&r))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4),a=Qc(e,t),n=el(e,e.length-e.l);return{t:"Std",h:a,v:n}}function al(){throw new Error("File is password-protected: ECMA-376 Extensible")}function nl(e){var r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var t=e.read_shift(e.length-e.l,"utf8"),a={};return t.replace(Vr,(function(e){var t=jr(e);switch(Xr(t[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":r.forEach((function(e){a[e]=t[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=t.encryptedHmacKey,a.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":a.uri=t.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":a.encs.push(t);break;default:throw t[0]}})),a}function sl(e,r){var t={},a=t.EncryptionVersionInfo=Xc(e,4);if(r-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);t.Flags=e.read_shift(4),r-=4;var n=e.read_shift(4);return r-=4,t.EncryptionHeader=Qc(e,n),r-=n,t.EncryptionVerifier=el(e,r),t}function il(e){var r={},t=r.EncryptionVersionInfo=Xc(e,4);if(1!=t.Major||1!=t.Minor)throw"unrecognized version code "+t.Major+" : "+t.Minor;return r.Salt=e.read_shift(16),r.EncryptedVerifier=e.read_shift(16),r.EncryptedVerifierHash=e.read_shift(16),r}function ol(e){var r,t,a,n,s,i,o=0,c=jc(e),l=c.length+1;for(r=A(l),r[0]=c.length,t=1;t!=l;++t)r[t]=c[t-1];for(t=l-1;t>=0;--t)a=r[t],n=0===(16384&o)?0:1,s=o<<1&32767,i=n|s,o=i^a;return 52811^o}var cl=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],r=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],t=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e){return 255&(e/2|128*e)},n=function(e,r){return a(e^r)},s=function(e){for(var a=r[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=t[n]),i*=2,--n;return a};return function(r){for(var t,a,i,o=jc(r),c=s(o),l=o.length,f=A(16),h=0;16!=h;++h)f[h]=0;1===(1&l)&&(t=c>>8,f[l]=n(e[0],t),--l,t=255&c,a=o[o.length-1],f[l]=n(a,t));while(l>0)--l,t=c>>8,f[l]=n(o[l],t),--l,t=255&c,f[l]=n(o[l],t);l=15,i=15-o.length;while(i>0)t=c>>8,f[l]=n(e[i],t),--l,--i,t=255&c,f[l]=n(o[l],t),--l,--i;return f}}(),ll=function(e,r,t,a,n){var s,i;for(n||(n=r),a||(a=cl(e)),s=0;s!=r.length;++s)i=r[s],i^=a[t],i=255&(i>>5|i<<3),n[s]=i,++t;return[n,t,a]},fl=function(e){var r=0,t=cl(e);return function(e){var a=ll("",e,r,t);return r=a[1],a[0]}};function hl(e,r,t,a){var n={key:Qs(e),verificationBytes:Qs(e)};return t.password&&(n.verifier=ol(t.password)),a.valid=n.verificationBytes===n.verifier,a.valid&&(a.insitu=fl(t.password)),n}function ul(e,r,t){var a=t||{};return a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=il(e,r):a.Data=sl(e,r),a}function dl(e,r,t){var a={Type:t.biff>=8?e.read_shift(2):0};return a.Type?ul(e,r-2,a):hl(e,t.biff>=8?r:r-2,t,a),a}var pl=function(){function e(e,t){switch(t.type){case"base64":return r(S(e),t);case"binary":return r(e,t);case"buffer":return r(y&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return r(gr(e),t)}throw new Error("Unrecognized type "+t.type)}function r(e,r){var t=r||{},a=t.dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw new Error("RTF missing table");var s={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach((function(e,r){Array.isArray(a)&&(a[r]=[]);var t,n=/\\\w+\b/g,i=0,o=-1;while(t=n.exec(e)){switch(t[0]){case"\\cell":var c=e.slice(i,n.lastIndex-t[0].length);if(" "==c[0]&&(c=c.slice(1)),++o,c.length){var l={v:c,t:"s"};Array.isArray(a)?a[r][o]=l:a[Ia({r:r,c:o})]=l}break}i=n.lastIndex}o>s.e.c&&(s.e.c=o)})),a["!ref"]=Da(s),a}function t(r,t){return Ma(e(r,t),t)}function a(e){for(var r,t=["{\\rtf1\\ansi"],a=Fa(e["!ref"]),n=Array.isArray(e),s=a.s.r;s<=a.e.r;++s){t.push("\\trowd\\trautofit1");for(var i=a.s.c;i<=a.e.c;++i)t.push("\\cellx"+(i+1));for(t.push("\\pard\\intbl"),i=a.s.c;i<=a.e.c;++i){var o=Ia({r:s,c:i});r=n?(e[s]||[])[i]:e[o],r&&(null!=r.v||r.f&&!r.F)&&(t.push(" "+(r.w||(La(r),r.w))),t.push("\\cell"))}t.push("\\pard\\intbl\\row")}return t.join("")+"}"}return{to_workbook:t,to_sheet:e,from_sheet:a}}();function ml(e){var r=e.slice("#"===e[0]?1:0).slice(0,6);return[parseInt(r.slice(0,2),16),parseInt(r.slice(2,4),16),parseInt(r.slice(4,6),16)]}function bl(e){for(var r=0,t=1;3!=r;++r)t=256*t+(e[r]>255?255:e[r]<0?0:e[r]);return t.toString(16).toUpperCase().slice(1)}function vl(e){var r=e[0]/255,t=e[1]/255,a=e[2]/255,n=Math.max(r,t,a),s=Math.min(r,t,a),i=n-s;if(0===i)return[0,0,r];var o=0,c=0,l=n+s;switch(c=i/(l>1?2-l:l),n){case r:o=((t-a)/i+6)%6;break;case t:o=(a-r)/i+2;break;case a:o=(r-t)/i+4;break}return[o/6,c,l/2]}function gl(e){var r,t=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,o=[i,i,i],c=6*t;if(0!==a)switch(0|c){case 0:case 6:r=s*c,o[0]+=s,o[1]+=r;break;case 1:r=s*(2-c),o[0]+=r,o[1]+=s;break;case 2:r=s*(c-2),o[1]+=s,o[2]+=r;break;case 3:r=s*(4-c),o[1]+=r,o[2]+=s;break;case 4:r=s*(c-4),o[2]+=s,o[0]+=r;break;case 5:r=s*(6-c),o[2]+=r,o[0]+=s;break}for(var l=0;3!=l;++l)o[l]=Math.round(255*o[l]);return o}function wl(e,r){if(0===r)return e;var t=vl(ml(e));return t[2]=r<0?t[2]*(1+r):1-(1-t[2])*(1-r),bl(gl(t))}var kl=6,Tl=15,El=1,Sl=kl;function yl(e){return Math.floor((e+Math.round(128/Sl)/256)*Sl)}function _l(e){return Math.floor((e-5)/Sl*100+.5)/100}function Al(e){return Math.round((e*Sl+5)/Sl*256)/256}function xl(e){return Al(_l(yl(e)))}function Cl(e){var r=Math.abs(e-xl(e)),t=Sl;if(r>.005)for(Sl=El;Sl<Tl;++Sl)Math.abs(e-xl(e))<=r&&(r=Math.abs(e-xl(e)),t=Sl);Sl=t}function Rl(e){e.width?(e.wpx=yl(e.width),e.wch=_l(e.wpx),e.MDW=Sl):e.wpx?(e.wch=_l(e.wpx),e.width=Al(e.wch),e.MDW=Sl):"number"==typeof e.wch&&(e.width=Al(e.wch),e.wpx=yl(e.width),e.MDW=Sl),e.customWidth&&delete e.customWidth}var Ol=96,Il=Ol;function Nl(e){return 96*e/Il}function Dl(e){return e*Il/96}var Fl={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};function Pl(e,r,t,a){r.Borders=[];var n={},s=!1;(e[0].match(Vr)||[]).forEach((function(e){var t=jr(e);switch(Xr(t[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":n={},t.diagonalUp&&(n.diagonalUp=st(t.diagonalUp)),t.diagonalDown&&(n.diagonalDown=st(t.diagonalDown)),r.Borders.push(n);break;case"</border>":break;case"<left/>":break;case"<left":case"<left>":break;case"</left>":break;case"<right/>":break;case"<right":case"<right>":break;case"</right>":break;case"<top/>":break;case"<top":case"<top>":break;case"</top>":break;case"<bottom/>":break;case"<bottom":case"<bottom>":break;case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":break;case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":break;case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":break;case"</vertical>":break;case"<start":case"<start>":case"<start/>":break;case"</start>":break;case"<end":case"<end>":case"<end/>":break;case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+t[0]+" in borders")}}))}function Ll(e,r,t,a){r.Fills=[];var n={},s=!1;(e[0].match(Vr)||[]).forEach((function(e){var t=jr(e);switch(Xr(t[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":n={},r.Fills.push(n);break;case"</fill>":break;case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":r.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":t.patternType&&(n.patternType=t.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":n.bgColor||(n.bgColor={}),t.indexed&&(n.bgColor.indexed=parseInt(t.indexed,10)),t.theme&&(n.bgColor.theme=parseInt(t.theme,10)),t.tint&&(n.bgColor.tint=parseFloat(t.tint)),t.rgb&&(n.bgColor.rgb=t.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":n.fgColor||(n.fgColor={}),t.theme&&(n.fgColor.theme=parseInt(t.theme,10)),t.tint&&(n.fgColor.tint=parseFloat(t.tint)),null!=t.rgb&&(n.fgColor.rgb=t.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":break;case"</stop>":break;case"<color":case"<color/>":break;case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+t[0]+" in fills")}}))}function Ml(e,r,t,a){r.Fonts=[];var n={},s=!1;(e[0].match(Vr)||[]).forEach((function(e){var i=jr(e);switch(Xr(i[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":r.Fonts.push(n),n={};break;case"<name":i.val&&(n.name=ft(i.val));break;case"<name/>":case"</name>":break;case"<b":n.bold=i.val?st(i.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=i.val?st(i.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(i.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34;break}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=i.val?st(i.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=i.val?st(i.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=i.val?st(i.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=i.val?st(i.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=i.val?st(i.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":i.val&&(n.sz=+i.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":i.val&&(n.vertAlign=i.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":i.val&&(n.family=parseInt(i.val,10));break;case"<family/>":case"</family>":break;case"<scheme":i.val&&(n.scheme=i.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if("1"==i.val)break;i.codepage=o[parseInt(i.val,10)];break;case"<color":if(n.color||(n.color={}),i.auto&&(n.color.auto=st(i.auto)),i.rgb)n.color.rgb=i.rgb.slice(-6);else if(i.indexed){n.color.index=parseInt(i.indexed,10);var c=Vn[n.color.index];81==n.color.index&&(c=Vn[1]),c||(c=Vn[1]),n.color.rgb=c[0].toString(16)+c[1].toString(16)+c[2].toString(16)}else i.theme&&(n.color.theme=parseInt(i.theme,10),i.tint&&(n.color.tint=parseFloat(i.tint)),i.theme&&t.themeElements&&t.themeElements.clrScheme&&(n.color.rgb=wl(t.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<color/>":case"</color>":break;case"<AlternateContent":s=!0;break;case"</AlternateContent>":s=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+i[0]+" in fonts")}}))}function Ul(e,r,t){r.NumberFmt=[];for(var a=tr(J),n=0;n<a.length;++n)r.NumberFmt[a[n]]=J[a[n]];var s=e[0].match(Vr);if(s)for(n=0;n<s.length;++n){var i=jr(s[n]);switch(Xr(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":var o=Kr(ft(i.formatCode)),c=parseInt(i.numFmtId,10);if(r.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60;--c)if(null==r.NumberFmt[c])break;r.NumberFmt[c]=o}ze(o,c)}break;case"</numFmt>":break;default:if(t.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}function Bl(e){var r=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(t){for(var a=t[0];a<=t[1];++a)null!=e[a]&&(r[r.length]=Tt("numFmt",null,{numFmtId:a,formatCode:Zr(e[a])}))})),1===r.length?"":(r[r.length]="</numFmts>",r[0]=Tt("numFmts",null,{count:r.length-2}).replace("/>",">"),r.join(""))}var Wl=["numFmtId","fillId","fontId","borderId","xfId"],Hl=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];function Vl(e,r,t){var a;r.CellXf=[];var n=!1;(e[0].match(Vr)||[]).forEach((function(e){var s=jr(e),i=0;switch(Xr(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(a=s,delete a[0],i=0;i<Wl.length;++i)a[Wl[i]]&&(a[Wl[i]]=parseInt(a[Wl[i]],10));for(i=0;i<Hl.length;++i)a[Hl[i]]&&(a[Hl[i]]=st(a[Hl[i]]));if(r.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(r.NumberFmt[a.numFmtId]==r.NumberFmt[i]){a.numFmtId=i;break}r.CellXf.push(a);break;case"</xf>":break;case"<alignment":case"<alignment/>":var o={};s.vertical&&(o.vertical=s.vertical),s.horizontal&&(o.horizontal=s.horizontal),null!=s.textRotation&&(o.textRotation=s.textRotation),s.indent&&(o.indent=s.indent),s.wrapText&&(o.wrapText=st(s.wrapText)),a.alignment=o;break;case"</alignment>":break;case"<protection":break;case"</protection>":case"<protection/>":break;case"<AlternateContent":n=!0;break;case"</AlternateContent>":n=!1;break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(t&&t.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}function zl(e){var r=[];return r[r.length]=Tt("cellXfs",null),e.forEach((function(e){r[r.length]=Tt("xf",null,e)})),r[r.length]="</cellXfs>",2===r.length?"":(r[0]=Tt("cellXfs",null,{count:r.length-2}).replace("/>",">"),r.join(""))}var Gl=function(){var e=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,r=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,t=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,a=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,n=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/;return function(s,i,o){var c,l={};return s?(s=s.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,""),(c=s.match(e))&&Ul(c,l,o),(c=s.match(a))&&Ml(c,l,i,o),(c=s.match(t))&&Ll(c,l,i,o),(c=s.match(n))&&Pl(c,l,i,o),(c=s.match(r))&&Vl(c,l,o),l):l}}();function jl(e,r){var t,a=[Ur,Tt("styleSheet",null,{xmlns:xt[0],"xmlns:vt":At.vt})];return e.SSF&&null!=(t=Bl(e.SSF))&&(a[a.length]=t),a[a.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',a[a.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',a[a.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',a[a.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(t=zl(r.cellXfs))&&(a[a.length]=t),a[a.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',a[a.length]='<dxfs count="0"/>',a[a.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',a.length>2&&(a[a.length]="</styleSheet>",a[1]=a[1].replace("/>",">")),a.join("")}function Xl(e,r){var t=e.read_shift(2),a=Va(e,r-2);return[t,a]}function $l(e,r,t){t||(t=da(6+4*r.length)),t.write_shift(2,e),za(r,t);var a=t.length>t.l?t.slice(0,t.l):t;return null==t.l&&(t.l=t.length),a}function Yl(e,r,t){var a={};a.sz=e.read_shift(2)/20;var n=gn(e,2,t);n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1);var s=e.read_shift(2);switch(700===s&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript";break}var i=e.read_shift(1);0!=i&&(a.underline=i);var o=e.read_shift(1);o>0&&(a.family=o);var c=e.read_shift(1);switch(c>0&&(a.charset=c),e.l++,a.color=bn(e,8),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor";break}return a.name=Va(e,r-21),a}function Kl(e,r){r||(r=da(153)),r.write_shift(2,20*e.sz),wn(e,r),r.write_shift(2,e.bold?700:400);var t=0;"superscript"==e.vertAlign?t=1:"subscript"==e.vertAlign&&(t=2),r.write_shift(2,t),r.write_shift(1,e.underline||0),r.write_shift(1,e.family||0),r.write_shift(1,e.charset||0),r.write_shift(1,0),vn(e.color,r);var a=0;return"major"==e.scheme&&(a=1),"minor"==e.scheme&&(a=2),r.write_shift(1,a),za(e.name,r),r.length>r.l?r.slice(0,r.l):r}var Jl,ql=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Zl=ua;function Ql(e,r){r||(r=da(84)),Jl||(Jl=nr(ql));var t=Jl[e.patternType];null==t&&(t=40),r.write_shift(4,t);var a=0;if(40!=t)for(vn({auto:1},r),vn({auto:1},r);a<12;++a)r.write_shift(4,0);else{for(;a<4;++a)r.write_shift(4,0);for(;a<12;++a)r.write_shift(4,0)}return r.length>r.l?r.slice(0,r.l):r}function ef(e,r){var t=e.l+r,a=e.read_shift(2),n=e.read_shift(2);return e.l=t,{ixfe:a,numFmtId:n}}function rf(e,r,t){t||(t=da(16)),t.write_shift(2,r||0),t.write_shift(2,e.numFmtId||0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);var a=0;return t.write_shift(1,a),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(1,0),t}function tf(e,r){return r||(r=da(10)),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(4,0),r}var af=ua;function nf(e,r){return r||(r=da(51)),r.write_shift(1,0),tf(null,r),tf(null,r),tf(null,r),tf(null,r),tf(null,r),r.length>r.l?r.slice(0,r.l):r}function sf(e,r){return r||(r=da(52)),r.write_shift(4,e.xfId),r.write_shift(2,1),r.write_shift(1,+e.builtinId),r.write_shift(1,0),an(e.name||"",r),r.length>r.l?r.slice(0,r.l):r}function of(e,r,t){var a=da(2052);return a.write_shift(4,e),an(r,a),an(t,a),a.length>a.l?a.slice(0,a.l):a}function cf(e,r,t){var a={NumberFmt:[]};for(var n in J)a.NumberFmt[n]=J[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return pa(e,(function(e,n,o){switch(o){case 44:a.NumberFmt[e[0]]=e[1],ze(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&r&&r.themeElements&&r.themeElements.clrScheme&&(e.color.rgb=wl(r.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:break;case 45:break;case 46:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(o);else if(n.T<0)s.pop();else if(!i||t.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}})),a}function lf(e,r){if(r){var t=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var a=e[0];a<=e[1];++a)null!=r[a]&&++t})),0!=t&&(ba(e,615,Ha(t)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(t){for(var a=t[0];a<=t[1];++a)null!=r[a]&&ba(e,44,$l(a,r[a]))})),ba(e,616))}}function ff(e){var r=1;0!=r&&(ba(e,611,Ha(r)),ba(e,43,Kl({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),ba(e,612))}function hf(e){var r=2;0!=r&&(ba(e,603,Ha(r)),ba(e,45,Ql({patternType:"none"})),ba(e,45,Ql({patternType:"gray125"})),ba(e,604))}function uf(e){var r=1;0!=r&&(ba(e,613,Ha(r)),ba(e,46,nf({})),ba(e,614))}function df(e){var r=1;ba(e,626,Ha(r)),ba(e,47,rf({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),ba(e,627)}function pf(e,r){ba(e,617,Ha(r.length)),r.forEach((function(r){ba(e,47,rf(r,0))})),ba(e,618)}function mf(e){var r=1;ba(e,619,Ha(r)),ba(e,48,sf({xfId:0,builtinId:0,name:"Normal"})),ba(e,620)}function bf(e){var r=0;ba(e,505,Ha(r)),ba(e,506)}function vf(e){var r=0;ba(e,508,of(r,"TableStyleMedium9","PivotStyleMedium4")),ba(e,509)}function gf(){}function wf(e,r){var t=ma();return ba(t,278),lf(t,e.SSF),ff(t,e),hf(t,e),uf(t,e),df(t,e),pf(t,r.cellXfs),mf(t,e),bf(t,e),vf(t,e),gf(t,e),ba(t,279),t.end()}var kf=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Tf(e,r,t){r.themeElements.clrScheme=[];var a={};(e[0].match(Vr)||[]).forEach((function(e){var n=jr(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(r.themeElements.clrScheme[kf.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(t&&t.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}function Ef(){}function Sf(){}var yf=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,_f=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,Af=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;function xf(e,r,t){var a;r.themeElements={},[["clrScheme",yf,Tf],["fontScheme",_f,Ef],["fmtScheme",Af,Sf]].forEach((function(n){if(!(a=e.match(n[1])))throw new Error(n[0]+" not found in themeElements");n[2](a,r,t)}))}var Cf=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Rf(e,r){var t;e&&0!==e.length||(e=Of());var a={};if(!(t=e.match(Cf)))throw new Error("themeElements not found in theme");return xf(t[0],a,r),a.raw=e,a}function Of(e,r){if(r&&r.themeXLSX)return r.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var t=[Ur];return t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',t[t.length]="<a:themeElements>",t[t.length]='<a:clrScheme name="Office">',t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',t[t.length]="</a:clrScheme>",t[t.length]='<a:fontScheme name="Office">',t[t.length]="<a:majorFont>",t[t.length]='<a:latin typeface="Cambria"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>',t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:majorFont>",t[t.length]="<a:minorFont>",t[t.length]='<a:latin typeface="Calibri"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Arial"/>',t[t.length]='<a:font script="Hebr" typeface="Arial"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Arial"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:minorFont>",t[t.length]="</a:fontScheme>",t[t.length]='<a:fmtScheme name="Office">',t[t.length]="<a:fillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="1"/>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="0"/>',t[t.length]="</a:gradFill>",t[t.length]="</a:fillStyleLst>",t[t.length]="<a:lnStyleLst>",t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]="</a:lnStyleLst>",t[t.length]="<a:effectStyleLst>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',t[t.length]="</a:effectStyle>",t[t.length]="</a:effectStyleLst>",t[t.length]="<a:bgFillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]="</a:bgFillStyleLst>",t[t.length]="</a:fmtScheme>",t[t.length]="</a:themeElements>",t[t.length]="<a:objectDefaults>",t[t.length]="<a:spDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',t[t.length]="</a:spDef>",t[t.length]="<a:lnDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',t[t.length]="</a:lnDef>",t[t.length]="</a:objectDefaults>",t[t.length]="<a:extraClrSchemeLst/>",t[t.length]="</a:theme>",t.join("")}function If(e,r,t){var a=e.l+r,n=e.read_shift(4);if(124226!==n)if(t.cellStyles){var s,i=e.slice(e.l);e.l=a;try{s=Lr(i,{type:"array"})}catch(c){return}var o=Ir(s,"theme/theme/theme1.xml",!0);if(o)return Rf(o,t)}else e.l=a}function Nf(e){return e.read_shift(4)}function Df(e){var r={};switch(r.xclrType=e.read_shift(2),r.nTintShade=e.read_shift(2),r.xclrType){case 0:e.l+=4;break;case 1:r.xclrValue=Ff(e,4);break;case 2:r.xclrValue=wi(e,4);break;case 3:r.xclrValue=Nf(e,4);break;case 4:e.l+=4;break}return e.l+=8,r}function Ff(e,r){return ua(e,r)}function Pf(e,r){return ua(e,r)}function Lf(e){var r=e.read_shift(2),t=e.read_shift(2)-4,a=[r];switch(r){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=Df(e,t);break;case 6:a[1]=Pf(e,t);break;case 14:case 15:a[1]=e.read_shift(1===t?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+r+" "+t)}return a}function Mf(e,r){var t=e.l+r;e.l+=2;var a=e.read_shift(2);e.l+=2;var n=e.read_shift(2),s=[];while(n-- >0)s.push(Lf(e,t-e.l));return{ixfe:a,ext:s}}function Uf(e,r){r.forEach((function(e){switch(e[0]){case 4:break;case 5:break;case 6:break;case 7:break;case 8:break;case 9:break;case 10:break;case 11:break;case 13:break;case 14:break;case 15:break}}))}function Bf(e,r){return{flags:e.read_shift(4),version:e.read_shift(4),name:Va(e,r-8)}}function Wf(e){var r=da(12+2*e.name.length);return r.write_shift(4,e.flags),r.write_shift(4,e.version),za(e.name,r),r.slice(0,r.l)}function Hf(e){var r=[],t=e.read_shift(4);while(t-- >0)r.push([e.read_shift(4),e.read_shift(4)]);return r}function Vf(e){var r=da(4+8*e.length);r.write_shift(4,e.length);for(var t=0;t<e.length;++t)r.write_shift(4,e[t][0]),r.write_shift(4,e[t][1]);return r}function zf(e,r){var t=da(8+2*r.length);return t.write_shift(4,e),za(r,t),t.slice(0,t.l)}function Gf(e){return e.l+=4,0!=e.read_shift(4)}function jf(e,r){var t=da(8);return t.write_shift(4,e),t.write_shift(4,r?1:0),t}function Xf(e,r,t){var a={Types:[],Cell:[],Value:[]},n=t||{},s=[],i=!1,o=2;return pa(e,(function(e,r,t){switch(t){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==o?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==o&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:o=e?1:0;break;case 338:o=2;break;case 35:s.push(t),i=!0;break;case 36:s.pop(),i=!1;break;default:if(r.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+t.toString(16))}})),a}function $f(){var e=ma();return ba(e,332),ba(e,334,Ha(1)),ba(e,335,Wf({name:"XLDAPR",version:12e4,flags:3496657072})),ba(e,336),ba(e,339,zf(1,"XLDAPR")),ba(e,52),ba(e,35,Ha(514)),ba(e,4096,Ha(0)),ba(e,4097,ei(1)),ba(e,36),ba(e,53),ba(e,340),ba(e,337,jf(1,!0)),ba(e,51,Vf([[1,0]])),ba(e,338),ba(e,333),e.end()}function Yf(e,r,t){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(Vr,(function(e){var r=jr(e);switch(Xr(r[0])){case"<?xml":break;case"<metadata":case"</metadata>":break;case"<metadataTypes":case"</metadataTypes>":break;case"<metadataType":a.Types.push({name:r.name});break;case"</metadataType>":break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==r.name&&(n=a.Types[o]);break;case"</futureMetadata>":break;case"<bk>":break;case"</bk>":break;case"<rc":1==i?a.Cell.push({type:a.Types[r.t-1].name,index:+r.v}):0==i&&a.Value.push({type:a.Types[r.t-1].name,index:+r.v});break;case"</rc>":break;case"<cellMetadata":i=1;break;case"</cellMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"</valueMetadata>":i=2;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+r.i);break;default:if(!s&&t.WTF)throw new Error("unrecognized "+r[0]+" in metadata")}return e})),a}function Kf(){var e=[Ur];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}function Jf(e){var r=[];if(!e)return r;var t=1;return(e.match(Vr)||[]).forEach((function(e){var a=jr(e);switch(a[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?t=a.i:a.i=t,r.push(a);break}})),r}function qf(e){var r={};r.i=e.read_shift(4);var t={};t.r=e.read_shift(4),t.c=e.read_shift(4),r.r=Ia(t);var a=e.read_shift(1);return 2&a&&(r.l="1"),8&a&&(r.a="1"),r}function Zf(e,r,t){var a=[],n=!1;return pa(e,(function(e,r,s){switch(s){case 63:a.push(e);break;default:if(r.T);else if(!n||t.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),a}function Qf(){}function eh(e,r,t,a){if(!e)return e;var n=a||{},s=!1,i=!1;pa(e,(function(e,r,t){if(!i)switch(t){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(r.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+t.toString(16))}}),n)}function rh(e,r){if(!e)return"??";var t=(e.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1];return r["!id"][t].Target}var th=1024;function ah(e,r){var t=[21600,21600],a=["m0,0l0",t[1],t[0],t[1],t[0],"0xe"].join(","),n=[Tt("xml",null,{"xmlns:v":Ct.v,"xmlns:o":Ct.o,"xmlns:x":Ct.x,"xmlns:mv":Ct.mv}).replace(/\/>/,">"),Tt("o:shapelayout",Tt("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Tt("v:shapetype",[Tt("v:stroke",null,{joinstyle:"miter"}),Tt("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:t.join(","),path:a})];while(th<1e3*e)th+=1e3;return r.forEach((function(e){var r=Oa(e[0]),t={color2:"#BEFF82",type:"gradient"};"gradient"==t.type&&(t.angle="-180");var a="gradient"==t.type?Tt("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,s=Tt("v:fill",a,t),i={on:"t",obscured:"t"};++th,n=n.concat(["<v:shape"+kt({id:"_x0000_s"+th,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",s,Tt("v:shadow",null,i),Tt("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",wt("x:Anchor",[r.c+1,0,r.r+1,0,r.c+3,20,r.r+5,20].join(",")),wt("x:AutoFill","False"),wt("x:Row",String(r.r)),wt("x:Column",String(r.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),n.push("</xml>"),n.join("")}function nh(e,r,t,a){var n,s=Array.isArray(e);r.forEach((function(r){var i=Oa(r.ref);if(s?(e[i.r]||(e[i.r]=[]),n=e[i.r][i.c]):n=e[r.ref],!n){n={t:"z"},s?e[i.r][i.c]=n:e[r.ref]=n;var o=Fa(e["!ref"]||"BDWGO1000001:A1");o.s.r>i.r&&(o.s.r=i.r),o.e.r<i.r&&(o.e.r=i.r),o.s.c>i.c&&(o.s.c=i.c),o.e.c<i.c&&(o.e.c=i.c);var c=Da(o);c!==e["!ref"]&&(e["!ref"]=c)}n.c||(n.c=[]);var l={a:r.author,t:r.t,r:r.r,T:t};r.h&&(l.h=r.h);for(var f=n.c.length-1;f>=0;--f){if(!t&&n.c[f].T)return;t&&!n.c[f].T&&n.c.splice(f,1)}if(t&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}))}function sh(e,r){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var t=[],a=[],n=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var r=e.match(/<(?:\w+:)?author[^>]*>(.*)/);r&&t.push(r[1])}}));var s=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^>]*>/);if(n){var s=jr(n[0]),i={author:s.authorId&&t[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},o=Oa(s.ref);if(!(r.sheetRows&&r.sheetRows<=o.r)){var c=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/),l=!!c&&!!c[1]&&Dc(c[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),r.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}function ih(e){var r=[Ur,Tt("comments",null,{xmlns:xt[0]})],t=[];return r.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var a=Zr(e.a);-1==t.indexOf(a)&&(t.push(a),r.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==t.indexOf("tc="+e.ID)&&(t.push("tc="+e.ID),r.push("<author>tc="+e.ID+"</author>"))}))})),0==t.length&&(t.push("SheetJ5"),r.push("<author>SheetJ5</author>")),r.push("</authors>"),r.push("<commentList>"),e.forEach((function(e){var a=0,n=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?a=t.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(a=t.indexOf(Zr(e.a))),n.push(e.t||"")})),r.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>'),n.length<=1)r.push(wt("t",Zr(n[0]||"")));else{for(var s="Comment:\n    "+n[0]+"\n",i=1;i<n.length;++i)s+="Reply:\n    "+n[i]+"\n";r.push(wt("t",Zr(s)))}r.push("</text></comment>")})),r.push("</commentList>"),r.length>2&&(r[r.length]="</comments>",r[1]=r[1].replace("/>",">")),r.join("")}function oh(e,r){var t=[],a=!1,n={},s=0;return e.replace(Vr,(function(i,o){var c=jr(i);switch(Xr(c[0])){case"<?xml":break;case"<ThreadedComments":break;case"</ThreadedComments>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=n.t&&t.push(n);break;case"<text>":case"<text":s=o+i.length;break;case"</text>":n.t=e.slice(s,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":a=!0;break;case"</mentions>":a=!1;break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments")}return i})),t}function ch(e,r,t){var a=[Ur,Tt("ThreadedComments",null,{xmlns:At.TCMNT}).replace(/[\/]>/,">")];return e.forEach((function(e){var n="";(e[1]||[]).forEach((function(s,i){if(s.T){s.a&&-1==r.indexOf(s.a)&&r.push(s.a);var o={ref:e[0],id:"{54EE7951-**************-"+("000000000000"+t.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-**************-"+("000000000000"+r.indexOf(s.a)).slice(-12)+"}"),a.push(Tt("threadedComment",wt("text",s.t||""),o))}else delete s.ID}))})),a.push("</ThreadedComments>"),a.join("")}function lh(e,r){var t=[],a=!1;return e.replace(Vr,(function(e){var n=jr(e);switch(Xr(n[0])){case"<?xml":break;case"<personList":break;case"</personList>":break;case"<person":t.push({name:n.displayname,id:n.id});break;case"</person>":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&r.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),t}function fh(e){var r=[Ur,Tt("personList",null,{xmlns:At.TCMNT,"xmlns:x":xt[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,t){r.push(Tt("person",null,{displayName:e,id:"{54EE7950-**************-"+("000000000000"+t).slice(-12)+"}",userId:e,providerId:"None"}))})),r.push("</personList>"),r.join("")}function hh(e){var r={};r.iauthor=e.read_shift(4);var t=un(e,16);return r.rfx=t.s,r.ref=Ia(t.s),e.l+=16,r}function uh(e,r){return null==r&&(r=da(36)),r.write_shift(4,e[1].iauthor),dn(e[0],r),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r}var dh=Va;function ph(e){return za(e.slice(0,54))}function mh(e,r){var t=[],a=[],n={},s=!1;return pa(e,(function(e,i,o){switch(o){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,r.sheetRows&&n.rfx&&r.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,t.push(n);break;case 3072:break;case 35:s=!0;break;case 36:s=!1;break;case 37:break;case 38:break;default:if(i.T);else if(!s||r.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}})),t}function bh(e){var r=ma(),t=[];return ba(r,628),ba(r,630),e.forEach((function(e){e[1].forEach((function(e){t.indexOf(e.a)>-1||(t.push(e.a.slice(0,54)),ba(r,632,ph(e.a)))}))})),ba(r,631),ba(r,633),e.forEach((function(e){e[1].forEach((function(a){a.iauthor=t.indexOf(a.a);var n={s:Oa(e[0]),e:Oa(e[0])};ba(r,635,uh([n,a])),a.t&&a.t.length>0&&ba(r,637,Ka(a)),ba(r,636),delete a.iauthor}))})),ba(r,634),ba(r,629),r.end()}var vh="application/vnd.ms-office.vbaProject";function gh(e){var r=qe.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(t,a){if("/"!==t.slice(-1)&&t.match(/_VBA_PROJECT_CUR/)){var n=t.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");qe.utils.cfb_add(r,n,e.FileIndex[a].content)}})),qe.write(r)}function wh(e,r){r.FullPaths.forEach((function(t,a){if(0!=a){var n=t.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&qe.utils.cfb_add(e,n,r.FileIndex[a].content)}}))}var kh=["xlsb","xlsm","xlam","biff8","xla"];function Th(){return{"!type":"dialog"}}function Eh(){return{"!type":"dialog"}}function Sh(){return{"!type":"macro"}}function yh(){return{"!type":"macro"}}var _h=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,r={r:0,c:0};function t(e,t,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,c=n.length>0?0|parseInt(n,10):0;return s?c+=r.c:--c,i?o+=r.r:--o,t+(s?"":"$")+Aa(c)+(i?"":"$")+Ea(o)}return function(a,n){return r=n,a.replace(e,t)}}(),Ah=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,xh=function(){return function(e,r){return e.replace(Ah,(function(e,t,a,n,s,i){var o=_a(n)-(a?0:r.c),c=Ta(i)-(s?0:r.r),l=0==c?"":s?c+1:"["+c+"]",f=0==o?"":a?o+1:"["+o+"]";return t+"R"+l+"C"+f}))}}();function Ch(e,r){return e.replace(Ah,(function(e,t,a,n,s,i){return t+("$"==a?a+n:Aa(_a(n)+r.c))+("$"==s?s+i:Ea(Ta(i)+r.r))}))}function Rh(e,r,t){var a=Na(r),n=a.s,s=Oa(t),i={r:s.r-n.r,c:s.c-n.c};return Ch(e,i)}function Oh(e){return 1!=e.length}function Ih(e){return e.replace(/_xlfn\./g,"")}function Nh(e){e.l+=1}function Dh(e,r){var t=e.read_shift(1==r?1:2);return[16383&t,t>>14&1,t>>15&1]}function Fh(e,r,t){var a=2;if(t){if(t.biff>=2&&t.biff<=5)return Ph(e,r,t);12==t.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=Dh(e,2),o=Dh(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function Ph(e){var r=Dh(e,2),t=Dh(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:r[0],c:a,cRel:r[1],rRel:r[2]},e:{r:t[0],c:n,cRel:t[1],rRel:t[2]}}}function Lh(e,r,t){if(t.biff<8)return Ph(e,r,t);var a=e.read_shift(12==t.biff?4:2),n=e.read_shift(12==t.biff?4:2),s=Dh(e,2),i=Dh(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}function Mh(e,r,t){if(t&&t.biff>=2&&t.biff<=5)return Uh(e,r,t);var a=e.read_shift(t&&12==t.biff?4:2),n=Dh(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function Uh(e){var r=Dh(e,2),t=e.read_shift(1);return{r:r[0],c:t,cRel:r[1],rRel:r[2]}}function Bh(e){var r=e.read_shift(2),t=e.read_shift(2);return{r:r,c:255&t,fQuoted:!!(16384&t),cRel:t>>15,rRel:t>>15}}function Wh(e,r,t){var a=t&&t.biff?t.biff:8;if(a>=2&&a<=5)return Hh(e,r,t);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,o=(32768&s)>>15;if(s&=16383,1==o)while(n>524287)n-=1048576;if(1==i)while(s>8191)s-=16384;return{r:n,c:s,cRel:i,rRel:o}}function Hh(e){var r=e.read_shift(2),t=e.read_shift(1),a=(32768&r)>>15,n=(16384&r)>>14;return r&=16383,1==a&&r>=8192&&(r-=16384),1==n&&t>=128&&(t-=256),{r:r,c:t,cRel:n,rRel:a}}function Vh(e,r,t){var a=(96&e[e.l++])>>5,n=Fh(e,t.biff>=2&&t.biff<=5?6:8,t);return[a,n]}function zh(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i"),s=8;if(t)switch(t.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}var i=Fh(e,s,t);return[a,n,i]}function Gh(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=t&&t.biff>8?12:t.biff<8?6:8,[a]}function jh(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(t)switch(t.biff){case 5:e.l+=12,s=6;break;case 12:s=12;break}return e.l+=s,[a,n]}function Xh(e,r,t){var a=(96&e[e.l++])>>5,n=Lh(e,r-1,t);return[a,n]}function $h(e,r,t){var a=(96&e[e.l++])>>5;return e.l+=2==t.biff?6:12==t.biff?14:7,[a]}function Yh(e){var r=1&e[e.l+1],t=1;return e.l+=4,[r,t]}function Kh(e,r,t){e.l+=2;for(var a=e.read_shift(t&&2==t.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(t&&2==t.biff?1:2));return n}function Jh(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}function qh(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(t&&2==t.biff?1:2)]}function Zh(e){var r=255&e[e.l+1]?1:0;return e.l+=2,[r,e.read_shift(2)]}function Qh(e,r,t){var a=255&e[e.l+1]?1:0;return e.l+=t&&2==t.biff?3:4,[a]}function eu(e){var r=e.read_shift(1),t=e.read_shift(1);return[r,t]}function ru(e){return e.read_shift(2),eu(e,2)}function tu(e){return e.read_shift(2),eu(e,2)}function au(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=Mh(e,0,t);return[a,n]}function nu(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=Wh(e,0,t);return[a,n]}function su(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);t&&5==t.biff&&(e.l+=12);var s=Mh(e,0,t);return[a,n,s]}function iu(e,r,t){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(t&&t.biff<=3?1:2);return[Ed[n],Td[n],a]}function ou(e,r,t){var a=e[e.l++],n=e.read_shift(1),s=t&&t.biff<=3?[88==a?-1:0,e.read_shift(1)]:cu(e);return[n,(0===s[0]?Td:kd)[s[1]]]}function cu(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}function lu(e,r,t){e.l+=t&&2==t.biff?3:4}function fu(e,r,t){if(e.l++,t&&12==t.biff)return[e.read_shift(4,"i"),0];var a=e.read_shift(2),n=e.read_shift(t&&2==t.biff?1:2);return[a,n]}function hu(e){return e.l++,zn[e.read_shift(1)]}function uu(e){return e.l++,e.read_shift(2)}function du(e){return e.l++,0!==e.read_shift(1)}function pu(e){return e.l++,pn(e,8)}function mu(e,r,t){return e.l++,ni(e,r-1,t)}function bu(e,r){var t=[e.read_shift(1)];if(12==r)switch(t[0]){case 2:t[0]=4;break;case 4:t[0]=16;break;case 0:t[0]=1;break;case 1:t[0]=2;break}switch(t[0]){case 4:t[1]=qs(e,1)?"TRUE":"FALSE",12!=r&&(e.l+=7);break;case 37:case 16:t[1]=zn[e[e.l]],e.l+=12==r?4:8;break;case 0:e.l+=8;break;case 1:t[1]=pn(e,8);break;case 2:t[1]=li(e,0,{biff:r>0&&r<8?2:r});break;default:throw new Error("Bad SerAr: "+t[0])}return t}function vu(e,r,t){for(var a=e.read_shift(12==t.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==t.biff?un:Ci)(e,8));return n}function gu(e,r,t){var a=0,n=0;12==t.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),t.biff>=2&&t.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=bu(e,t.biff);return i}function wu(e,r,t){var a=e.read_shift(1)>>>5&3,n=!t||t.biff>=8?4:2,s=e.read_shift(n);switch(t.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12;break}return[a,0,s]}function ku(e,r,t){if(5==t.biff)return Tu(e,r,t);var a=e.read_shift(1)>>>5&3,n=e.read_shift(2),s=e.read_shift(4);return[a,n,s]}function Tu(e){var r=e.read_shift(1)>>>5&3,t=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[r,t,a]}function Eu(e,r,t){var a=e.read_shift(1)>>>5&3;e.l+=t&&2==t.biff?3:4;var n=e.read_shift(t&&2==t.biff?1:2);return[a,n]}function Su(e,r,t){var a=e.read_shift(1)>>>5&3,n=e.read_shift(t&&2==t.biff?1:2);return[a,n]}function yu(e,r,t){var a=e.read_shift(1)>>>5&3;return e.l+=4,t.biff<8&&e.l--,12==t.biff&&(e.l+=2),[a]}function _u(e,r,t){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(t)switch(t.biff){case 5:s=15;break;case 12:s=6;break}return e.l+=s,[a,n]}var Au=ua,xu=ua,Cu=ua;function Ru(e,r,t){return e.l+=2,[Bh(e,4,t)]}function Ou(e){return e.l+=6,[]}var Iu=Ru,Nu=Ou,Du=Ou,Fu=Ru;function Pu(e){return e.l+=2,[Qs(e),1&e.read_shift(2)]}var Lu=Ru,Mu=Pu,Uu=Ou,Bu=Ru,Wu=Ru,Hu=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];function Vu(e){e.l+=2;var r=e.read_shift(2),t=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2),i=Hu[t>>2&31];return{ixti:r,coltype:3&t,rt:i,idx:a,c:n,C:s}}function zu(e){return e.l+=2,[e.read_shift(4)]}function Gu(e,r,t){return e.l+=5,e.l+=2,e.l+=2==t.biff?1:4,["PTGSHEET"]}function ju(e,r,t){return e.l+=2==t.biff?4:5,["PTGENDSHEET"]}function Xu(e){var r=e.read_shift(1)>>>5&3,t=e.read_shift(2);return[r,t]}function $u(e){var r=e.read_shift(1)>>>5&3,t=e.read_shift(2);return[r,t]}function Yu(e){return e.l+=4,[0,0]}var Ku={1:{n:"PtgExp",f:fu},2:{n:"PtgTbl",f:Cu},3:{n:"PtgAdd",f:Nh},4:{n:"PtgSub",f:Nh},5:{n:"PtgMul",f:Nh},6:{n:"PtgDiv",f:Nh},7:{n:"PtgPower",f:Nh},8:{n:"PtgConcat",f:Nh},9:{n:"PtgLt",f:Nh},10:{n:"PtgLe",f:Nh},11:{n:"PtgEq",f:Nh},12:{n:"PtgGe",f:Nh},13:{n:"PtgGt",f:Nh},14:{n:"PtgNe",f:Nh},15:{n:"PtgIsect",f:Nh},16:{n:"PtgUnion",f:Nh},17:{n:"PtgRange",f:Nh},18:{n:"PtgUplus",f:Nh},19:{n:"PtgUminus",f:Nh},20:{n:"PtgPercent",f:Nh},21:{n:"PtgParen",f:Nh},22:{n:"PtgMissArg",f:Nh},23:{n:"PtgStr",f:mu},26:{n:"PtgSheet",f:Gu},27:{n:"PtgEndSheet",f:ju},28:{n:"PtgErr",f:hu},29:{n:"PtgBool",f:du},30:{n:"PtgInt",f:uu},31:{n:"PtgNum",f:pu},32:{n:"PtgArray",f:$h},33:{n:"PtgFunc",f:iu},34:{n:"PtgFuncVar",f:ou},35:{n:"PtgName",f:wu},36:{n:"PtgRef",f:au},37:{n:"PtgArea",f:Vh},38:{n:"PtgMemArea",f:Eu},39:{n:"PtgMemErr",f:Au},40:{n:"PtgMemNoMem",f:xu},41:{n:"PtgMemFunc",f:Su},42:{n:"PtgRefErr",f:yu},43:{n:"PtgAreaErr",f:Gh},44:{n:"PtgRefN",f:nu},45:{n:"PtgAreaN",f:Xh},46:{n:"PtgMemAreaN",f:Xu},47:{n:"PtgMemNoMemN",f:$u},57:{n:"PtgNameX",f:ku},58:{n:"PtgRef3d",f:su},59:{n:"PtgArea3d",f:zh},60:{n:"PtgRefErr3d",f:_u},61:{n:"PtgAreaErr3d",f:jh},255:{}},Ju={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},qu={1:{n:"PtgElfLel",f:Pu},2:{n:"PtgElfRw",f:Bu},3:{n:"PtgElfCol",f:Iu},6:{n:"PtgElfRwV",f:Wu},7:{n:"PtgElfColV",f:Fu},10:{n:"PtgElfRadical",f:Lu},11:{n:"PtgElfRadicalS",f:Uu},13:{n:"PtgElfColS",f:Nu},15:{n:"PtgElfColSV",f:Du},16:{n:"PtgElfRadicalLel",f:Mu},25:{n:"PtgList",f:Vu},29:{n:"PtgSxName",f:zu},255:{}},Zu={0:{n:"PtgAttrNoop",f:Yu},1:{n:"PtgAttrSemi",f:Qh},2:{n:"PtgAttrIf",f:qh},4:{n:"PtgAttrChoose",f:Kh},8:{n:"PtgAttrGoto",f:Jh},16:{n:"PtgAttrSum",f:lu},32:{n:"PtgAttrBaxcel",f:Yh},33:{n:"PtgAttrBaxcel",f:Yh},64:{n:"PtgAttrSpace",f:ru},65:{n:"PtgAttrSpaceSemi",f:tu},128:{n:"PtgAttrIfError",f:Zh},255:{}};function Qu(e,r,t,a){if(a.biff<8)return ua(e,r);for(var n=e.l+r,s=[],i=0;i!==t.length;++i)switch(t[i][0]){case"PtgArray":t[i][1]=gu(e,0,a),s.push(t[i][1]);break;case"PtgMemArea":t[i][2]=vu(e,t[i][1],a),s.push(t[i][2]);break;case"PtgExp":a&&12==a.biff&&(t[i][1][1]=e.read_shift(4),s.push(t[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+t[i][0];default:break}return r=n-e.l,0!==r&&s.push(ua(e,r)),s}function ed(e,r,t){var a,n,s=e.l+r,i=[];while(s!=e.l)r=s-e.l,n=e[e.l],a=Ku[n]||Ku[Ju[n]],24!==n&&25!==n||(a=(24===n?qu:Zu)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,r,t)]):ua(e,r);return i}function rd(e){for(var r=[],t=0;t<e.length;++t){for(var a=e[t],n=[],s=0;s<a.length;++s){var i=a[s];if(i)switch(i[0]){case 2:n.push('"'+i[1].replace(/"/g,'""')+'"');break;default:n.push(i[1])}else n.push("")}r.push(n.join(","))}return r.join(";")}var td={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function ad(e,r){if(!e&&!(r&&r.biff<=5&&r.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}function nd(e,r,t){if(!e)return"SH33TJSERR0";if(t.biff>8&&(!e.XTI||!e.XTI[r]))return e.SheetNames[r];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[r];if(t.biff<8)return r>1e4&&(r-=65536),r<0&&(r=-r),0==r?"":e.XTI[r-1];if(!a)return"SH33TJSERR1";var n="";if(t.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=t.SID?e.SheetNames[t.SID]:"SH33TJSSAME"+e[a[0]][0];case 355:default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function sd(e,r,t){var a=nd(e,r,t);return"#REF"==a?a:ad(a,t)}function id(e,r,t,a,n){var s,i,o,c,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,b="",v=0,g=e[0].length;v<g;++v){var w=e[0][v];switch(w[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),m>=0){switch(e[0][m][1][0]){case 0:b=kr(" ",e[0][m][1][1]);break;case 1:b=kr("\r",e[0][m][1][1]);break;default:if(b="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}i+=b,m=-1}h.push(i+td[w[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push(i+":"+s);break;case"PtgAttrChoose":break;case"PtgAttrGoto":break;case"PtgAttrIf":break;case"PtgAttrIfError":break;case"PtgRef":o=va(w[1][1],f,n),h.push(wa(o,l));break;case"PtgRefN":o=t?va(w[1][1],t,n):w[1][1],h.push(wa(o,l));break;case"PtgRef3d":u=w[1][1],o=va(w[1][2],f,n),p=sd(a,u,n);h.push(p+"!"+wa(o,l));break;case"PtgFunc":case"PtgFuncVar":var k=w[1][0],T=w[1][1];k||(k=0),k&=127;var E=0==k?[]:h.slice(-k);h.length-=k,"User"===T&&(T=E.shift()),h.push(T+"("+E.join(",")+")");break;case"PtgBool":h.push(w[1]?"TRUE":"FALSE");break;case"PtgInt":h.push(w[1]);break;case"PtgNum":h.push(String(w[1]));break;case"PtgStr":h.push('"'+w[1].replace(/"/g,'""')+'"');break;case"PtgErr":h.push(w[1]);break;case"PtgAreaN":c=ga(w[1][1],t?{s:t}:f,n),h.push(ka(c,n));break;case"PtgArea":c=ga(w[1][1],f,n),h.push(ka(c,n));break;case"PtgArea3d":u=w[1][1],c=w[1][2],p=sd(a,u,n),h.push(p+"!"+ka(c,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":d=w[1][2];var S=(a.names||[])[d-1]||(a[0]||[])[d],y=S?S.Name:"SH33TJSNAME"+String(d);y&&"_xlfn."==y.slice(0,6)&&!n.xlfn&&(y=y.slice(6)),h.push(y);break;case"PtgNameX":var _,A=w[1][1];if(d=w[1][2],!(n.biff<=5)){var x="";if(14849==((a[A]||[])[0]||[])[0]||(1025==((a[A]||[])[0]||[])[0]?a[A][d]&&a[A][d].itab>0&&(x=a.SheetNames[a[A][d].itab-1]+"!"):x=a.SheetNames[d-1]+"!"),a[A]&&a[A][d])x+=a[A][d].Name;else if(a[0]&&a[0][d])x+=a[0][d].Name;else{var C=(nd(a,A,n)||"").split(";;");C[d-1]?x=C[d-1]:x+="SH33TJSERRX"}h.push(x);break}A<0&&(A=-A),a[A]&&(_=a[A][d]),_||(_={Name:"SH33TJSERRY"}),h.push(_.Name);break;case"PtgParen":var R="(",O=")";if(m>=0){switch(b="",e[0][m][1][0]){case 2:R=kr(" ",e[0][m][1][1])+R;break;case 3:R=kr("\r",e[0][m][1][1])+R;break;case 4:O=kr(" ",e[0][m][1][1])+O;break;case 5:O=kr("\r",e[0][m][1][1])+O;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}h.push(R+h.pop()+O);break;case"PtgRefErr":h.push("#REF!");break;case"PtgRefErr3d":h.push("#REF!");break;case"PtgExp":o={c:w[1][1],r:w[1][0]};var I={c:t.c,r:t.r};if(a.sharedf[Ia(o)]){var N=a.sharedf[Ia(o)];h.push(id(N,f,I,a,n))}else{var D=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(o.c<i[0].s.c||o.c>i[0].e.c)&&!(o.r<i[0].s.r||o.r>i[0].e.r)){h.push(id(i[1],f,I,a,n)),D=!0;break}D||h.push(w[1])}break;case"PtgArray":h.push("{"+rd(w[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgTbl":break;case"PtgMemErr":break;case"PtgMissArg":h.push("");break;case"PtgAreaErr":h.push("#REF!");break;case"PtgAreaErr3d":h.push("#REF!");break;case"PtgList":h.push("Table"+w[1].idx+"[#"+w[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":break;case"PtgMemFunc":break;case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");case"PtgSxName":throw new Error("Unrecognized Formula Token: "+String(w));default:throw new Error("Unrecognized Formula Token: "+String(w))}var F=["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"];if(3!=n.biff&&m>=0&&-1==F.indexOf(e[0][v][0])){w=e[0][m];var P=!0;switch(w[1][0]){case 4:P=!1;case 0:b=kr(" ",w[1][1]);break;case 5:P=!1;case 1:b=kr("\r",w[1][1]);break;default:if(b="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+w[1][0])}h.push((P?b:"")+h.pop()+(P?"":b)),m=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return h[0]}function od(e,r,t){var a,n=e.l+r,s=2==t.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],ua(e,r-2)];var o=ed(e,i,t);return r!==i+s&&(a=Qu(e,r-i-s,o,t)),e.l=n,[o,a]}function cd(e,r,t){var a,n=e.l+r,s=2==t.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],ua(e,r-2)];var o=ed(e,i,t);return r!==i+s&&(a=Qu(e,r-i-s,o,t)),e.l=n,[o,a]}function ld(e,r,t,a){var n,s=e.l+r,i=ed(e,a,t);return s!==e.l&&(n=Qu(e,s-e.l,i,t)),[i,n]}function fd(e,r,t){var a,n=e.l+r,s=e.read_shift(2),i=ed(e,s,t);return 65535==s?[[],ua(e,r-2)]:(r!==s+2&&(a=Qu(e,n-s-2,i,t)),[i,a])}function hd(e){var r;if(65535!==ea(e,e.l+6))return[pn(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return r=1===e[e.l+2],e.l+=8,[r,"b"];case 2:return r=e[e.l+2],e.l+=8,[r,"e"];case 3:return e.l+=8,["","s"]}return[]}function ud(e){if(null==e){var r=da(8);return r.write_shift(1,3),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,65535),r}return mn("number"==typeof e?e:0)}function dd(e,r,t){var a=e.l+r,n=Ti(e,6);2==t.biff&&++e.l;var s=hd(e,8),i=e.read_shift(1);2!=t.biff&&(e.read_shift(1),t.biff>=5&&e.read_shift(4));var o=cd(e,a-e.l,t);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function pd(e,r,t,a,n){var s=Ei(r,t,n),i=ud(e.v),o=da(6),c=33;o.write_shift(2,c),o.write_shift(4,0);for(var l=da(e.bf.length),f=0;f<e.bf.length;++f)l[f]=e.bf[f];var h=D([s,i,o,l]);return h}function md(e,r,t){var a=e.read_shift(4),n=ed(e,a,t),s=e.read_shift(4),i=s>0?Qu(e,s,n,t):null;return[n,i]}var bd=md,vd=md,gd=md,wd=md,kd={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Td={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Ed={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function Sd(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&(e=e.slice(1),61==e.charCodeAt(0)&&(e=e.slice(1))),e=e.replace(/COM\.MICROSOFT\./g,""),e=e.replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,r){return r.replace(/\./g,"")})),e=e.replace(/\[.(#[A-Z]*[?!])\]/g,"$1"),e.replace(/[;~]/g,",").replace(/\|/g,";")}function yd(e){var r="of:="+e.replace(Ah,"$1[.$2$3$4$5]").replace(/\]:\[/g,":");return r.replace(/;/g,"|").replace(/,/g,";")}function _d(e){var r=e.split(":"),t=r[0].split(".")[0];return[t,r[0].split(".")[1]+(r.length>1?":"+(r[1].split(".")[1]||r[1].split(".")[0]):"")]}function Ad(e){return e.replace(/\./,"!")}var xd={},Cd={},Rd="undefined"!==typeof Map;function Od(e,r,t){var a=0,n=e.length;if(t){if(Rd?t.has(r):Object.prototype.hasOwnProperty.call(t,r))for(var s=Rd?t.get(r):t[r];a<s.length;++a)if(e[s[a]].t===r)return e.Count++,s[a]}else for(;a<n;++a)if(e[a].t===r)return e.Count++,a;return e[n]={t:r},e.Count++,e.Unique++,t&&(Rd?(t.has(r)||t.set(r,[]),t.get(r).push(n)):(Object.prototype.hasOwnProperty.call(t,r)||(t[r]=[]),t[r].push(n))),n}function Id(e,r){var t={min:e+1,max:e+1},a=-1;return r.MDW&&(Sl=r.MDW),null!=r.width?t.customWidth=1:null!=r.wpx?a=_l(r.wpx):null!=r.wch&&(a=r.wch),a>-1?(t.width=Al(a),t.customWidth=1):null!=r.width&&(t.width=r.width),r.hidden&&(t.hidden=!0),null!=r.level&&(t.outlineLevel=t.level=r.level),t}function Nd(e,r){if(e){var t=[.7,.7,.75,.75,.3,.3];"xlml"==r&&(t=[1,1,1,1,.5,.5]),null==e.left&&(e.left=t[0]),null==e.right&&(e.right=t[1]),null==e.top&&(e.top=t[2]),null==e.bottom&&(e.bottom=t[3]),null==e.header&&(e.header=t[4]),null==e.footer&&(e.footer=t[5])}}function Dd(e,r,t){var a=t.revssf[null!=r.z?r.z:"General"],n=60,s=e.length;if(null==a&&t.ssf)for(;n<392;++n)if(null==t.ssf[n]){ze(r.z,n),t.ssf[n]=r.z,t.revssf[r.z]=a=n;break}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function Fd(e,r,t,a,n,s){try{a.cellNF&&(e.z=J[r])}catch(o){if(a.WTF)throw o}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"===typeof e.v&&(e.v=vr(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==J[r]&&ze(Xe[r]||"General",r),"e"===e.t)e.w=e.w||zn[e.v];else if(0===r)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=le(e.v);else if("d"===e.t){var i=cr(e.v);e.w=(0|i)===i?i.toString(10):le(i)}else{if(void 0===e.v)return"";e.w=fe(e.v,Cd)}else"d"===e.t?e.w=Ve(r,cr(e.v),Cd):e.w=Ve(r,e.v,Cd)}catch(o){if(a.WTF)throw o}if(a.cellStyles&&null!=t)try{e.s=s.Fills[t],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=wl(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=wl(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(o){if(a.WTF&&s.Fills)throw o}}}function Pd(e,r,t){if(e&&e["!ref"]){var a=Fa(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+t+"): "+e["!ref"])}}function Ld(e,r){var t=Fa(r);t.s.r<=t.e.r&&t.s.c<=t.e.c&&t.s.r>=0&&t.s.c>=0&&(e["!ref"]=Da(t))}var Md=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Ud=/<(?:\w+:)?sheetData[^>]*>([\s\S]*)<\/(?:\w+:)?sheetData>/,Bd=/<(?:\w:)?hyperlink [^>]*>/gm,Wd=/"(\w*:\w*)"/,Hd=/<(?:\w:)?col\b[^>]*[\/]?>/g,Vd=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,zd=/<(?:\w:)?pageMargins[^>]*\/>/g,Gd=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,jd=/<(?:\w:)?sheetPr[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetPr)>/,Xd=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function $d(e,r,t,a,n,s,i){if(!e)return e;a||(a={"!id":{}}),null!=w&&null==r.dense&&(r.dense=w);var o=r.dense?[]:{},c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=e.match(Ud);h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(Gd);u?Kd(u[0],o,n,t):(u=l.match(jd))&&Jd(u[0],u[1]||"",o,n,t,i,s);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(Wd);p&&Ld(o,p[1])}var m=l.match(Xd);m&&m[1]&&lp(m[1],n);var b=[];if(r.cellStyles){var v=l.match(Hd);v&&np(b,v)}h&&up(h[1],o,r,c,s,i);var g=f.match(Vd);g&&(o["!autofilter"]=ip(g[0]));var k=[],T=f.match(Md);if(T)for(d=0;d!=T.length;++d)k[d]=Fa(T[d].slice(T[d].indexOf('"')+1));var E=f.match(Bd);E&&rp(o,E,a);var S=f.match(zd);if(S&&(o["!margins"]=tp(jr(S[0]))),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=Da(c)),r.sheetRows>0&&o["!ref"]){var y=Fa(o["!ref"]);r.sheetRows<=+y.e.r&&(y.e.r=r.sheetRows-1,y.e.r>c.e.r&&(y.e.r=c.e.r),y.e.r<y.s.r&&(y.s.r=y.e.r),y.e.c>c.e.c&&(y.e.c=c.e.c),y.e.c<y.s.c&&(y.s.c=y.e.c),o["!fullref"]=o["!ref"],o["!ref"]=Da(y))}return b.length>0&&(o["!cols"]=b),k.length>0&&(o["!merges"]=k),o}function Yd(e){if(0===e.length)return"";for(var r='<mergeCells count="'+e.length+'">',t=0;t!=e.length;++t)r+='<mergeCell ref="'+Da(e[t])+'"/>';return r+"</mergeCells>"}function Kd(e,r,t,a){var n=jr(e);t.Sheets[a]||(t.Sheets[a]={}),n.codeName&&(t.Sheets[a].CodeName=Kr(ft(n.codeName)))}function Jd(e,r,t,a,n){Kd(e.slice(0,e.indexOf(">")),t,a,n)}function qd(e,r,t,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&r.vbaraw){var c=r.SheetNames[t];try{r.Workbook&&(c=r.Workbook.Sheets[t].CodeName||c)}catch(f){}s=!0,i.codeName=ht(Zr(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+Tt("outlinePr",null,l)}(s||o)&&(n[n.length]=Tt("sheetPr",o,i))}var Zd=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Qd=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function ep(e){var r={sheet:1};return Zd.forEach((function(t){null!=e[t]&&e[t]&&(r[t]="1")})),Qd.forEach((function(t){null==e[t]||e[t]||(r[t]="0")})),e.password&&(r.password=ol(e.password).toString(16).toUpperCase()),Tt("sheetProtection",null,r)}function rp(e,r,t){for(var a=Array.isArray(e),n=0;n!=r.length;++n){var s=jr(ft(r[n]),!0);if(!s.ref)return;var i=((t||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Kr(s.location))):(s.Target="#"+Kr(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=Fa(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=Ia({c:l,r:c});a?(e[c]||(e[c]=[]),e[c][l]||(e[c][l]={t:"z",v:void 0}),e[c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}function tp(e){var r={};return["left","right","top","bottom","header","footer"].forEach((function(t){e[t]&&(r[t]=parseFloat(e[t]))})),r}function ap(e){return Nd(e),Tt("pageMargins",null,e)}function np(e,r){for(var t=!1,a=0;a!=r.length;++a){var n=jr(r[a],!0);n.hidden&&(n.hidden=st(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!t&&n.width&&(t=!0,Cl(n.width)),Rl(n);while(s<=i)e[s++]=wr(n)}}function sp(e,r){for(var t,a=["<cols>"],n=0;n!=r.length;++n)(t=r[n])&&(a[a.length]=Tt("col",null,Id(n,t)));return a[a.length]="</cols>",a.join("")}function ip(e){var r={ref:(e.match(/ref="([^"]*)"/)||[])[1]};return r}function op(e,r,t,a){var n="string"==typeof e.ref?e.ref:Da(e.ref);t.Workbook||(t.Workbook={Sheets:[]}),t.Workbook.Names||(t.Workbook.Names=[]);var s=t.Workbook.Names,i=Na(n);i.s.r==i.e.r&&(i.e.r=Na(r["!ref"]).e.r,n=Da(i));for(var o=0;o<s.length;++o){var c=s[o];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref="'"+t.SheetNames[a]+"'!"+n;break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+n}),Tt("autoFilter",null,{ref:n})}var cp=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/?>/;function lp(e,r){r.Views||(r.Views=[{}]),(e.match(cp)||[]).forEach((function(e,t){var a=jr(e);r.Views[t]||(r.Views[t]={}),+a.zoomScale&&(r.Views[t].zoom=+a.zoomScale),st(a.rightToLeft)&&(r.Views[t].RTL=!0)}))}function fp(e,r,t,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),Tt("sheetViews",Tt("sheetView",null,n),{})}function hp(e,r,t,a){if(e.c&&t["!comments"].push([r,e.c]),void 0===e.v&&"string"!==typeof e.f||"z"===e.t&&!e.f)return"";var n="",s=e.t,i=e.v;if("z"!==e.t)switch(e.t){case"b":n=e.v?"1":"0";break;case"n":n=""+e.v;break;case"e":n=zn[e.v];break;case"d":a&&a.cellDates?n=vr(e.v,-1).toISOString():(e=wr(e),e.t="n",n=""+(e.v=cr(vr(e.v)))),"undefined"===typeof e.z&&(e.z=J[14]);break;default:n=e.v;break}var o=wt("v",Zr(n)),c={r:r},l=Dd(a.cellXfs,e,a);switch(0!==l&&(c.s=l),e.t){case"n":break;case"d":c.t="d";break;case"b":c.t="b";break;case"e":c.t="e";break;case"z":break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){o=wt("v",""+Od(a.Strings,e.v,a.revStrings)),c.t="s";break}c.t="str";break}if(e.t!=s&&(e.t=s,e.v=i),"string"==typeof e.f&&e.f){var f=e.F&&e.F.slice(0,r.length)==r?{t:"array",ref:e.F}:null;o=Tt("f",Zr(e.f),f)+(null!=e.v?o:"")}return e.l&&t["!links"].push([r,e.l]),e.D&&(c.cm=1),Tt("c",o,c)}var up=function(){var e=/<(?:\w+:)?c[ \/>]/,r=/<\/(?:\w+:)?row>/,t=/r=["']([^"']*)["']/,a=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,n=/ref=["']([^"']*)["']/,s=ut("v"),i=ut("f");return function(o,c,l,f,h,u){for(var d,p,m,b,v,g=0,w="",k=[],T=[],E=0,S=0,y=0,_="",A=0,x=0,C=0,R=0,O=Array.isArray(u.CellXf),I=[],N=[],D=Array.isArray(c),F=[],P={},L=!1,M=!!l.sheetStubs,U=o.split(r),B=0,W=U.length;B!=W;++B){w=U[B].trim();var H=w.length;if(0!==H){var V=0;e:for(g=0;g<H;++g)switch(w[g]){case">":if("/"!=w[g-1]){++g;break e}if(l&&l.cellStyles){if(p=jr(w.slice(V,g),!0),A=null!=p.r?parseInt(p.r,10):A+1,x=-1,l.sheetRows&&l.sheetRows<A)continue;P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=Dl(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[A-1]=P)}break;case"<":V=g;break}if(V>=g)break;if(p=jr(w.slice(V,g),!0),A=null!=p.r?parseInt(p.r,10):A+1,x=-1,!(l.sheetRows&&l.sheetRows<A)){f.s.r>A-1&&(f.s.r=A-1),f.e.r<A-1&&(f.e.r=A-1),l&&l.cellStyles&&(P={},L=!1,p.ht&&(L=!0,P.hpt=parseFloat(p.ht),P.hpx=Dl(P.hpt)),"1"==p.hidden&&(L=!0,P.hidden=!0),null!=p.outlineLevel&&(L=!0,P.level=+p.outlineLevel),L&&(F[A-1]=P)),k=w.slice(g).split(e);for(var z=0;z!=k.length;++z)if("<"!=k[z].trim().charAt(0))break;for(k=k.slice(z),g=0;g!=k.length;++g)if(w=k[g].trim(),0!==w.length){if(T=w.match(t),E=g,S=0,y=0,w="<c "+("<"==w.slice(0,1)?">":"")+w,null!=T&&2===T.length){for(E=0,_=T[1],S=0;S!=_.length;++S){if((y=_.charCodeAt(S)-64)<1||y>26)break;E=26*E+y}--E,x=E}else++x;for(S=0;S!=w.length;++S)if(62===w.charCodeAt(S))break;if(++S,p=jr(w.slice(0,S),!0),p.r||(p.r=Ia({r:A-1,c:x})),_=w.slice(S),d={t:""},null!=(T=_.match(s))&&""!==T[1]&&(d.v=Kr(T[1])),l.cellFormula){if(null!=(T=_.match(i))&&""!==T[1]){if(d.f=Kr(ft(T[1])).replace(/\r\n/g,"\n"),l.xlfn||(d.f=Ih(d.f)),T[0].indexOf('t="array"')>-1)d.F=(_.match(n)||[])[1],d.F.indexOf(":")>-1&&I.push([Fa(d.F),d.F]);else if(T[0].indexOf('t="shared"')>-1){b=jr(T[0]);var G=Kr(ft(T[1]));l.xlfn||(G=Ih(G)),N[parseInt(b.si,10)]=[b,G,p.r]}}else(T=_.match(/<f[^>]*\/>/))&&(b=jr(T[0]),N[b.si]&&(d.f=Rh(N[b.si][1],N[b.si][2],p.r)));var j=Oa(p.r);for(S=0;S<I.length;++S)j.r>=I[S][0].s.r&&j.r<=I[S][0].e.r&&j.c>=I[S][0].s.c&&j.c<=I[S][0].e.c&&(d.F=I[S][1])}if(null==p.t&&void 0===d.v)if(d.f||d.F)d.v=0,d.t="n";else{if(!M)continue;d.t="z"}else d.t=p.t||"n";switch(f.s.c>x&&(f.s.c=x),f.e.c<x&&(f.e.c=x),d.t){case"n":if(""==d.v||null==d.v){if(!M)continue;d.t="z"}else d.v=parseFloat(d.v);break;case"s":if("undefined"==typeof d.v){if(!M)continue;d.t="z"}else m=xd[parseInt(d.v,10)],d.v=m.t,d.r=m.r,l.cellHTML&&(d.h=m.h);break;case"str":d.t="s",d.v=null!=d.v?ft(d.v):"",l.cellHTML&&(d.h=rt(d.v));break;case"inlineStr":T=_.match(a),d.t="s",null!=T&&(m=Dc(T[1]))?(d.v=m.t,l.cellHTML&&(d.h=m.h)):d.v="";break;case"b":d.v=st(d.v);break;case"d":l.cellDates?d.v=vr(d.v,1):(d.v=cr(vr(d.v,1)),d.t="n");break;case"e":l&&!1===l.cellText||(d.w=d.v),d.v=Gn[d.v];break}if(C=R=0,v=null,O&&void 0!==p.s&&(v=u.CellXf[p.s],null!=v&&(null!=v.numFmtId&&(C=v.numFmtId),l.cellStyles&&null!=v.fillId&&(R=v.fillId))),Fd(d,C,R,l,h,u),l.cellDates&&O&&"n"==d.t&&Me(J[C])&&(d.t="d",d.v=ur(d.v)),p.cm&&l.xlmeta){var X=(l.xlmeta.Cell||[])[+p.cm-1];X&&"XLDAPR"==X.type&&(d.D=!0)}if(D){var $=Oa(p.r);c[$.r]||(c[$.r]=[]),c[$.r][$.c]=d}else c[p.r]=d}}}}F.length>0&&(c["!rows"]=F)}}();function dp(e,r,t,a){var n,s,i=[],o=[],c=Fa(e["!ref"]),l="",f="",h=[],u=0,d=0,p=e["!rows"],m=Array.isArray(e),b={r:f},v=-1;for(d=c.s.c;d<=c.e.c;++d)h[d]=Aa(d);for(u=c.s.r;u<=c.e.r;++u){for(o=[],f=Ea(u),d=c.s.c;d<=c.e.c;++d){n=h[d]+f;var g=m?(e[u]||[])[d]:e[n];void 0!==g&&(null!=(l=hp(g,n,e,r,t,a))&&o.push(l))}(o.length>0||p&&p[u])&&(b={r:f},p&&p[u]&&(s=p[u],s.hidden&&(b.hidden=1),v=-1,s.hpx?v=Nl(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(b.ht=v,b.customHeight=1),s.level&&(b.outlineLevel=s.level)),i[i.length]=Tt("row",o.join(""),b))}if(p)for(;u<p.length;++u)p&&p[u]&&(b={r:u+1},s=p[u],s.hidden&&(b.hidden=1),v=-1,s.hpx?v=Nl(s.hpx):s.hpt&&(v=s.hpt),v>-1&&(b.ht=v,b.customHeight=1),s.level&&(b.outlineLevel=s.level),i[i.length]=Tt("row","",b));return i.join("")}function pp(e,r,t,a){var n=[Ur,Tt("worksheet",null,{xmlns:xt[0],"xmlns:r":At.r})],s=t.SheetNames[e],i=0,o="",c=t.Sheets[s];null==c&&(c={});var l=c["!ref"]||"A1",f=Fa(l);if(f.e.c>16383||f.e.r>1048575){if(r.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575),l=Da(f)}a||(a={}),c["!comments"]=[];var h=[];qd(c,t,e,r,n),n[n.length]=Tt("dimension",null,{ref:l}),n[n.length]=fp(c,r,e,t),r.sheetFormat&&(n[n.length]=Tt("sheetFormatPr",null,{defaultRowHeight:r.sheetFormat.defaultRowHeight||"16",baseColWidth:r.sheetFormat.baseColWidth||"10",outlineLevelRow:r.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(n[n.length]=sp(c,c["!cols"])),n[i=n.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(o=dp(c,r,e,t,a),o.length>0&&(n[n.length]=o)),n.length>i+1&&(n[n.length]="</sheetData>",n[i]=n[i].replace("/>",">")),c["!protect"]&&(n[n.length]=ep(c["!protect"])),null!=c["!autofilter"]&&(n[n.length]=op(c["!autofilter"],c,t,e)),null!=c["!merges"]&&c["!merges"].length>0&&(n[n.length]=Yd(c["!merges"]));var u,d=-1,p=-1;return c["!links"].length>0&&(n[n.length]="<hyperlinks>",c["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(p=es(a,-1,Zr(e[1].Target).replace(/#.*$/,""),Jn.HLINK),u["r:id"]="rId"+p),(d=e[1].Target.indexOf("#"))>-1&&(u.location=Zr(e[1].Target.slice(d+1))),e[1].Tooltip&&(u.tooltip=Zr(e[1].Tooltip)),n[n.length]=Tt("hyperlink",null,u))})),n[n.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(n[n.length]=ap(c["!margins"])),r&&!r.ignoreEC&&void 0!=r.ignoreEC||(n[n.length]=wt("ignoredErrors",Tt("ignoredError",null,{numberStoredAsText:1,sqref:l}))),h.length>0&&(p=es(a,-1,"../drawings/drawing"+(e+1)+".xml",Jn.DRAW),n[n.length]=Tt("drawing",null,{"r:id":"rId"+p}),c["!drawing"]=h),c["!comments"].length>0&&(p=es(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Jn.VML),n[n.length]=Tt("legacyDrawing",null,{"r:id":"rId"+p}),c["!legacy"]=p),n.length>1&&(n[n.length]="</worksheet>",n[1]=n[1].replace("/>",">")),n.join("")}function mp(e,r){var t={},a=e.l+r;t.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(t.level=7&s),16&s&&(t.hidden=!0),32&s&&(t.hpt=n/20),t}function bp(e,r,t){var a=da(145),n=(t["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*Nl(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=0;f<16;++f)if(!(r.s.c>f+1<<10||r.e.c<f<<10)){for(var h=-1,u=-1,d=f<<10;d<f+1<<10;++d){l.c=d;var p=Array.isArray(t)?(t[l.r]||[])[l.c]:t[Ia(l)];p&&(h<0&&(h=d),u=d)}h<0||(++o,a.write_shift(4,h),a.write_shift(4,u))}var m=a.l;return a.l=c,a.write_shift(4,o),a.l=m,a.length>a.l?a.slice(0,a.l):a}function vp(e,r,t,a){var n=bp(a,t,r);(n.length>17||(r["!rows"]||[])[a])&&ba(e,0,n)}var gp=un,wp=dn;function kp(){}function Tp(e,r){var t={},a=e[e.l];return++e.l,t.above=!(64&a),t.left=!(128&a),e.l+=18,t.name=en(e,r-19),t}function Ep(e,r,t){null==t&&(t=da(84+4*e.length));var a=192;r&&(r.above&&(a&=-65),r.left&&(a&=-129)),t.write_shift(1,a);for(var n=1;n<3;++n)t.write_shift(1,0);return vn({auto:1},t),t.write_shift(-4,-1),t.write_shift(-4,-1),rn(e,t),t.slice(0,t.l)}function Sp(e){var r=Ja(e);return[r]}function yp(e,r,t){return null==t&&(t=da(8)),qa(r,t)}function _p(e){var r=Za(e);return[r]}function Ap(e,r,t){return null==t&&(t=da(4)),Qa(r,t)}function xp(e){var r=Ja(e),t=e.read_shift(1);return[r,t,"b"]}function Cp(e,r,t){return null==t&&(t=da(9)),qa(r,t),t.write_shift(1,e.v?1:0),t}function Rp(e){var r=Za(e),t=e.read_shift(1);return[r,t,"b"]}function Op(e,r,t){return null==t&&(t=da(5)),Qa(r,t),t.write_shift(1,e.v?1:0),t}function Ip(e){var r=Ja(e),t=e.read_shift(1);return[r,t,"e"]}function Np(e,r,t){return null==t&&(t=da(9)),qa(r,t),t.write_shift(1,e.v),t}function Dp(e){var r=Za(e),t=e.read_shift(1);return[r,t,"e"]}function Fp(e,r,t){return null==t&&(t=da(8)),Qa(r,t),t.write_shift(1,e.v),t.write_shift(2,0),t.write_shift(1,0),t}function Pp(e){var r=Ja(e),t=e.read_shift(4);return[r,t,"s"]}function Lp(e,r,t){return null==t&&(t=da(12)),qa(r,t),t.write_shift(4,r.v),t}function Mp(e){var r=Za(e),t=e.read_shift(4);return[r,t,"s"]}function Up(e,r,t){return null==t&&(t=da(8)),Qa(r,t),t.write_shift(4,r.v),t}function Bp(e){var r=Ja(e),t=pn(e);return[r,t,"n"]}function Wp(e,r,t){return null==t&&(t=da(16)),qa(r,t),mn(e.v,t),t}function Hp(e){var r=Za(e),t=pn(e);return[r,t,"n"]}function Vp(e,r,t){return null==t&&(t=da(12)),Qa(r,t),mn(e.v,t),t}function zp(e){var r=Ja(e),t=cn(e);return[r,t,"n"]}function Gp(e,r,t){return null==t&&(t=da(12)),qa(r,t),ln(e.v,t),t}function jp(e){var r=Za(e),t=cn(e);return[r,t,"n"]}function Xp(e,r,t){return null==t&&(t=da(8)),Qa(r,t),ln(e.v,t),t}function $p(e){var r=Ja(e),t=Xa(e);return[r,t,"is"]}function Yp(e){var r=Ja(e),t=Va(e);return[r,t,"str"]}function Kp(e,r,t){return null==t&&(t=da(12+4*e.v.length)),qa(r,t),za(e.v,t),t.length>t.l?t.slice(0,t.l):t}function Jp(e){var r=Za(e),t=Va(e);return[r,t,"str"]}function qp(e,r,t){return null==t&&(t=da(8+4*e.v.length)),Qa(r,t),za(e.v,t),t.length>t.l?t.slice(0,t.l):t}function Zp(e,r,t){var a=e.l+r,n=Ja(e);n.r=t["!row"];var s=e.read_shift(1),i=[n,s,"b"];if(t.cellFormula){e.l+=2;var o=vd(e,a-e.l,t);i[3]=id(o,null,n,t.supbooks,t)}else e.l=a;return i}function Qp(e,r,t){var a=e.l+r,n=Ja(e);n.r=t["!row"];var s=e.read_shift(1),i=[n,s,"e"];if(t.cellFormula){e.l+=2;var o=vd(e,a-e.l,t);i[3]=id(o,null,n,t.supbooks,t)}else e.l=a;return i}function em(e,r,t){var a=e.l+r,n=Ja(e);n.r=t["!row"];var s=pn(e),i=[n,s,"n"];if(t.cellFormula){e.l+=2;var o=vd(e,a-e.l,t);i[3]=id(o,null,n,t.supbooks,t)}else e.l=a;return i}function rm(e,r,t){var a=e.l+r,n=Ja(e);n.r=t["!row"];var s=Va(e),i=[n,s,"str"];if(t.cellFormula){e.l+=2;var o=vd(e,a-e.l,t);i[3]=id(o,null,n,t.supbooks,t)}else e.l=a;return i}var tm=un,am=dn;function nm(e,r){return null==r&&(r=da(4)),r.write_shift(4,e),r}function sm(e,r){var t=e.l+r,a=un(e,16),n=tn(e),s=Va(e),i=Va(e),o=Va(e);e.l=t;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}function im(e,r){var t=da(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));dn({s:Oa(e[0]),e:Oa(e[0])},t),on("rId"+r,t);var a=e[1].Target.indexOf("#"),n=-1==a?"":e[1].Target.slice(a+1);return za(n||"",t),za(e[1].Tooltip||"",t),za("",t),t.slice(0,t.l)}function om(){}function cm(e,r,t){var a=e.l+r,n=fn(e,16),s=e.read_shift(1),i=[n];if(i[2]=s,t.cellFormula){var o=bd(e,a-e.l,t);i[1]=o}else e.l=a;return i}function lm(e,r,t){var a=e.l+r,n=un(e,16),s=[n];if(t.cellFormula){var i=wd(e,a-e.l,t);s[1]=i,e.l=a}else e.l=a;return s}function fm(e,r,t){null==t&&(t=da(18));var a=Id(e,r);t.write_shift(-4,e),t.write_shift(-4,e),t.write_shift(4,256*(a.width||10)),t.write_shift(4,0);var n=0;return r.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),r.level&&(n|=r.level<<8),t.write_shift(2,n),t}var hm=["left","right","top","bottom","header","footer"];function um(e){var r={};return hm.forEach((function(t){r[t]=pn(e,8)})),r}function dm(e,r){return null==r&&(r=da(48)),Nd(e),hm.forEach((function(t){mn(e[t],r)})),r}function pm(e){var r=e.read_shift(2);return e.l+=28,{RTL:32&r}}function mm(e,r,t){null==t&&(t=da(30));var a=924;return(((r||{}).Views||[])[0]||{}).RTL&&(a|=32),t.write_shift(2,a),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,100),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(4,0),t}function bm(e){var r=da(24);return r.write_shift(4,4),r.write_shift(4,1),dn(e,r),r}function vm(e,r){return null==r&&(r=da(66)),r.write_shift(2,e.password?ol(e.password):0),r.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(t){t[1]?r.write_shift(4,null==e[t[0]]||e[t[0]]?0:1):r.write_shift(4,null!=e[t[0]]&&e[t[0]]?0:1)})),r}function gm(){}function wm(){}function km(e,r,t,a,n,s,i){if(!e)return e;var o=r||{};a||(a={"!id":{}}),null!=w&&null==o.dense&&(o.dense=w);var c,l,f,h,u,d,p,m,b,v,g=o.dense?[]:{},k={s:{r:2e6,c:2e6},e:{r:0,c:0}},T=[],E=!1,S=!1,y=[];o.biff=12,o["!row"]=0;var _=0,A=!1,x=[],C={},R=o.supbooks||n.supbooks||[[]];if(R.sharedf=C,R.arrayf=x,R.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!o.supbooks&&(o.supbooks=R,n.Names))for(var O=0;O<n.Names.length;++O)R[0][O+1]=n.Names[O];var I,N,D=[],F=[],P=!1;if(uv[16]={n:"BrtShortReal",f:Hp},pa(e,(function(e,r,w){if(!S)switch(w){case 148:c=e;break;case 0:l=e,o.sheetRows&&o.sheetRows<=l.r&&(S=!0),b=Ea(u=l.r),o["!row"]=l.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=Dl(e.hpt)),F[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(f={t:e[2]},e[2]){case"n":f.v=e[1];break;case"s":m=xd[e[1]],f.v=m.t,f.r=m.r;break;case"b":f.v=!!e[1];break;case"e":f.v=e[1],!1!==o.cellText&&(f.w=zn[f.v]);break;case"str":f.t="s",f.v=e[1];break;case"is":f.t="s",f.v=e[1].t;break}if((h=i.CellXf[e[0].iStyleRef])&&Fd(f,h.numFmtId,null,o,s,i),d=-1==e[0].c?d+1:e[0].c,o.dense?(g[u]||(g[u]=[]),g[u][d]=f):g[Aa(d)+b]=f,o.cellFormula){for(A=!1,_=0;_<x.length;++_){var O=x[_];l.r>=O[0].s.r&&l.r<=O[0].e.r&&d>=O[0].s.c&&d<=O[0].e.c&&(f.F=Da(O[0]),A=!0)}!A&&e.length>3&&(f.f=e[3])}if(k.s.r>l.r&&(k.s.r=l.r),k.s.c>d&&(k.s.c=d),k.e.r<l.r&&(k.e.r=l.r),k.e.c<d&&(k.e.c=d),o.cellDates&&h&&"n"==f.t&&Me(J[h.numFmtId])){var L=ee(f.v);L&&(f.t="d",f.v=new Date(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u))}I&&("XLDAPR"==I.type&&(f.D=!0),I=void 0),N&&(N=void 0);break;case 1:case 12:if(!o.sheetStubs||E)break;f={t:"z",v:void 0},d=-1==e[0].c?d+1:e[0].c,o.dense?(g[u]||(g[u]=[]),g[u][d]=f):g[Aa(d)+b]=f,k.s.r>l.r&&(k.s.r=l.r),k.s.c>d&&(k.s.c=d),k.e.r<l.r&&(k.e.r=l.r),k.e.c<d&&(k.e.c=d),I&&("XLDAPR"==I.type&&(f.D=!0),I=void 0),N&&(N=void 0);break;case 176:y.push(e);break;case 49:I=((o.xlmeta||{}).Cell||[])[e-1];break;case 494:var M=a["!id"][e.relId];for(M?(e.Target=M.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=M):""==e.relId&&(e.Target="#"+e.loc),u=e.rfx.s.r;u<=e.rfx.e.r;++u)for(d=e.rfx.s.c;d<=e.rfx.e.c;++d)o.dense?(g[u]||(g[u]=[]),g[u][d]||(g[u][d]={t:"z",v:void 0}),g[u][d].l=e):(p=Ia({c:d,r:u}),g[p]||(g[p]={t:"z",v:void 0}),g[p].l=e);break;case 426:if(!o.cellFormula)break;x.push(e),v=o.dense?g[u][d]:g[Aa(d)+b],v.f=id(e[1],k,{r:l.r,c:d},R,o),v.F=Da(e[0]);break;case 427:if(!o.cellFormula)break;C[Ia(e[0].s)]=e[1],v=o.dense?g[u][d]:g[Aa(d)+b],v.f=id(e[1],k,{r:l.r,c:d},R,o);break;case 60:if(!o.cellStyles)break;while(e.e>=e.s)D[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},P||(P=!0,Cl(e.w/256)),Rl(D[e.e+1]);break;case 161:g["!autofilter"]={ref:Da(e)};break;case 476:g["!margins"]=e;break;case 147:n.Sheets[t]||(n.Sheets[t]={}),e.name&&(n.Sheets[t].CodeName=e.name),(e.above||e.left)&&(g["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:break;case 64:case 1053:break;case 151:break;case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:E=!0;break;case 36:E=!1;break;case 37:T.push(w),E=!0;break;case 38:T.pop(),E=!1;break;default:if(r.T);else if(!E||o.WTF)throw new Error("Unexpected record 0x"+w.toString(16))}}),o),delete o.supbooks,delete o["!row"],!g["!ref"]&&(k.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(g["!ref"]=Da(c||k)),o.sheetRows&&g["!ref"]){var L=Fa(g["!ref"]);o.sheetRows<=+L.e.r&&(L.e.r=o.sheetRows-1,L.e.r>k.e.r&&(L.e.r=k.e.r),L.e.r<L.s.r&&(L.s.r=L.e.r),L.e.c>k.e.c&&(L.e.c=k.e.c),L.e.c<L.s.c&&(L.s.c=L.e.c),g["!fullref"]=g["!ref"],g["!ref"]=Da(L))}return y.length>0&&(g["!merges"]=y),D.length>0&&(g["!cols"]=D),F.length>0&&(g["!rows"]=F),g}function Tm(e,r,t,a,n,s,i){if(void 0===r.v)return!1;var o="";switch(r.t){case"b":o=r.v?"1":"0";break;case"d":r=wr(r),r.z=r.z||J[14],r.v=cr(vr(r.v)),r.t="n";break;case"n":case"e":o=""+r.v;break;default:o=r.v;break}var c={r:t,c:a};switch(c.s=Dd(n.cellXfs,r,n),r.l&&s["!links"].push([Ia(c),r.l]),r.c&&s["!comments"].push([Ia(c),r.c]),r.t){case"s":case"str":return n.bookSST?(o=Od(n.Strings,r.v,n.revStrings),c.t="s",c.v=o,i?ba(e,18,Up(r,c)):ba(e,7,Lp(r,c))):(c.t="str",i?ba(e,17,qp(r,c)):ba(e,6,Kp(r,c))),!0;case"n":return r.v==(0|r.v)&&r.v>-1e3&&r.v<1e3?i?ba(e,13,Xp(r,c)):ba(e,2,Gp(r,c)):i?ba(e,16,Vp(r,c)):ba(e,5,Wp(r,c)),!0;case"b":return c.t="b",i?ba(e,15,Op(r,c)):ba(e,4,Cp(r,c)),!0;case"e":return c.t="e",i?ba(e,14,Fp(r,c)):ba(e,3,Np(r,c)),!0}return i?ba(e,12,Ap(r,c)):ba(e,1,yp(r,c)),!0}function Em(e,r,t,a){var n,s=Fa(r["!ref"]||"A1"),i="",o=[];ba(e,145);var c=Array.isArray(r),l=s.e.r;r["!rows"]&&(l=Math.max(s.e.r,r["!rows"].length-1));for(var f=s.s.r;f<=l;++f){i=Ea(f),vp(e,r,s,f);var h=!1;if(f<=s.e.r)for(var u=s.s.c;u<=s.e.c;++u){f===s.s.r&&(o[u]=Aa(u)),n=o[u]+i;var d=c?(r[f]||[])[u]:r[n];d?h=Tm(e,d,f,u,a,r,h):h=!1}}ba(e,146)}function Sm(e,r){r&&r["!merges"]&&(ba(e,177,nm(r["!merges"].length)),r["!merges"].forEach((function(r){ba(e,176,am(r))})),ba(e,178))}function ym(e,r){r&&r["!cols"]&&(ba(e,390),r["!cols"].forEach((function(r,t){r&&ba(e,60,fm(t,r))})),ba(e,391))}function _m(e,r){r&&r["!ref"]&&(ba(e,648),ba(e,649,bm(Fa(r["!ref"]))),ba(e,650))}function Am(e,r,t){r["!links"].forEach((function(r){if(r[1].Target){var a=es(t,-1,r[1].Target.replace(/#.*$/,""),Jn.HLINK);ba(e,494,im(r,a))}})),delete r["!links"]}function xm(e,r,t,a){if(r["!comments"].length>0){var n=es(a,-1,"../drawings/vmlDrawing"+(t+1)+".vml",Jn.VML);ba(e,551,on("rId"+n)),r["!legacy"]=n}}function Cm(e,r,t,a){if(r["!autofilter"]){var n=r["!autofilter"],s="string"===typeof n.ref?n.ref:Da(n.ref);t.Workbook||(t.Workbook={Sheets:[]}),t.Workbook.Names||(t.Workbook.Names=[]);var i=t.Workbook.Names,o=Na(s);o.s.r==o.e.r&&(o.e.r=Na(r["!ref"]).e.r,s=Da(o));for(var c=0;c<i.length;++c){var l=i[c];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref="'"+t.SheetNames[a]+"'!"+s;break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+t.SheetNames[a]+"'!"+s}),ba(e,161,dn(Fa(s))),ba(e,162)}}function Rm(e,r,t){ba(e,133),ba(e,137,mm(r,t)),ba(e,138),ba(e,134)}function Om(){}function Im(e,r){r["!protect"]&&ba(e,535,vm(r["!protect"]))}function Nm(e,r,t,a){var n=ma(),s=t.SheetNames[e],i=t.Sheets[s]||{},o=s;try{t&&t.Workbook&&(o=t.Workbook.Sheets[e].CodeName||o)}catch(l){}var c=Fa(i["!ref"]||"A1");if(c.e.c>16383||c.e.r>1048575){if(r.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],ba(n,129),(t.vbaraw||i["!outline"])&&ba(n,147,Ep(o,i["!outline"])),ba(n,148,wp(c)),Rm(n,i,t.Workbook),Om(n,i),ym(n,i,e,r,t),Em(n,i,e,r,t),Im(n,i),Cm(n,i,t,e),Sm(n,i),Am(n,i,a),i["!margins"]&&ba(n,476,dm(i["!margins"])),r&&!r.ignoreEC&&void 0!=r.ignoreEC||_m(n,i),xm(n,i,e,a),ba(n,130),n.end()}function Dm(e){var r,t=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach((function(e){var r=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);r&&(t[+r[1]]=a?+r[2]:r[2])}));var n=Kr((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]);return(e.match(/<c:f>(.*?)<\/c:f>/gm)||[]).forEach((function(e){r=e.replace(/<.*?>/g,"")})),[t,n,r]}function Fm(e,r,t,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach((function(e){var r=Dm(e);f.s.r=f.s.c=0,f.e.c=o,l=Aa(o),r[0].forEach((function(e,t){i[l+Ea(t)]={t:"n",v:e,z:r[1]},c=t})),f.e.r<c&&(f.e.r=c),++o})),o>0&&(i["!ref"]=Da(f)),i}function Pm(e,r,t,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},o=e.match(Gd);return o&&Kd(o[0],i,n,t),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}function Lm(e,r){e.l+=10;var t=Va(e,r-10);return{name:t}}function Mm(e,r,t,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=[],o=!1;return pa(e,(function(e,a,c){switch(c){case 550:s["!rel"]=e;break;case 651:n.Sheets[t]||(n.Sheets[t]={}),e.name&&(n.Sheets[t].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:o=!0;break;case 36:o=!1;break;case 37:i.push(c);break;case 38:i.pop();break;default:if(a.T>0)i.push(c);else if(a.T<0)i.pop();else if(!o||r.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),r),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}var Um=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Bm=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Wm=[],Hm=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Vm(e,r){for(var t=0;t!=e.length;++t)for(var a=e[t],n=0;n!=r.length;++n){var s=r[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=st(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10));break}}}function zm(e,r){for(var t=0;t!=r.length;++t){var a=r[t];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=st(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10));break}}}function Gm(e){zm(e.WBProps,Um),zm(e.CalcPr,Hm),Vm(e.WBView,Bm),Vm(e.Sheets,Wm),Cd.date1904=st(e.WBProps.date1904)}function jm(e){return e.Workbook&&e.Workbook.WBProps&&st(e.Workbook.WBProps.date1904)?"true":"false"}var Xm="][*?/\\".split("");function $m(e,r){if(e.length>31){if(r)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var t=!0;return Xm.forEach((function(a){if(-1!=e.indexOf(a)){if(!r)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");t=!1}})),t}function Ym(e,r,t){e.forEach((function(a,n){$m(a);for(var s=0;s<n;++s)if(a==e[s])throw new Error("Duplicate Sheet Name: "+a);if(t){var i=r&&r[n]&&r[n].CodeName||a;if(95==i.charCodeAt(0)&&i.length>22)throw new Error("Bad Code Name: Worksheet"+i)}}))}function Km(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var r=e.Workbook&&e.Workbook.Sheets||[];Ym(e.SheetNames,r,!!e.vbaraw);for(var t=0;t<e.SheetNames.length;++t)Pd(e.Sheets[e.SheetNames[t]],e.SheetNames[t],t)}var Jm=/<\w+:workbook/;function qm(e,r){if(!e)throw new Error("Could not find file");var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(Vr,(function(o,c){var l=jr(o);switch(Xr(l[0])){case"<?xml":break;case"<workbook":o.match(Jm)&&(n="xmlns"+o.match(/<(\w+):/)[1]),t.xmlns=l[n];break;case"</workbook>":break;case"<fileVersion":delete l[0],t.AppVersion=l;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":break;case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":Um.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":t.WBProps[e[0]]=st(l[e[0]]);break;case"int":t.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:t.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(t.WBProps.CodeName=ft(l.codeName));break;case"</workbookPr>":break;case"<workbookProtection":break;case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete l[0],t.WBView.push(l);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Kr(ft(l.name)),delete l[0],t.Sheets.push(l);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":break;case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":break;case"<externalReference":break;case"<definedNames/>":break;case"<definedNames>":case"<definedNames":a=!0;break;case"</definedNames>":a=!1;break;case"<definedName":s={},s.Name=ft(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),st(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=Kr(ft(e.slice(i,c))),t.Names.push(s);break;case"<definedName/>":break;case"<calcPr":delete l[0],t.CalcPr=l;break;case"<calcPr/>":delete l[0],t.CalcPr=l;break;case"</calcPr>":break;case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":break;case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":break;case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":break;case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":a=!0;break;case"</ext>":a=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</AlternateContent>":a=!1;break;case"<revisionPtr":break;default:if(!a&&r.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o})),-1===xt.indexOf(t.xmlns))throw new Error("Unknown Namespace: "+t.xmlns);return Gm(t),t}function Zm(e){var r=[Ur];r[r.length]=Tt("workbook",null,{xmlns:xt[0],"xmlns:r":At.r});var t=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Um.forEach((function(r){null!=e.Workbook.WBProps[r[0]]&&e.Workbook.WBProps[r[0]]!=r[1]&&(a[r[0]]=e.Workbook.WBProps[r[0]])})),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),r[r.length]=Tt("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(r[r.length]="<bookViews>",s=0;s!=e.SheetNames.length;++s){if(!n[s])break;if(!n[s].Hidden)break}s==e.SheetNames.length&&(s=0),r[r.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',r[r.length]="</bookViews>"}for(r[r.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var i={name:Zr(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden";break}r[r.length]=Tt("sheet",null,i)}return r[r.length]="</sheets>",t&&(r[r.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var t={name:e.Name};e.Comment&&(t.comment=e.Comment),null!=e.Sheet&&(t.localSheetId=""+e.Sheet),e.Hidden&&(t.hidden="1"),e.Ref&&(r[r.length]=Tt("definedName",Zr(e.Ref),t))})),r[r.length]="</definedNames>"),r.length>2&&(r[r.length]="</workbook>",r[1]=r[1].replace("/>",">")),r.join("")}function Qm(e,r){var t={};return t.Hidden=e.read_shift(4),t.iTabID=e.read_shift(4),t.strRelID=sn(e,r-8),t.name=Va(e),t}function eb(e,r){return r||(r=da(127)),r.write_shift(4,e.Hidden),r.write_shift(4,e.iTabID),on(e.strRelID,r),za(e.name.slice(0,31),r),r.length>r.l?r.slice(0,r.l):r}function rb(e,r){var t={},a=e.read_shift(4);t.defaultThemeVersion=e.read_shift(4);var n=r>8?Va(e):"";return n.length>0&&(t.CodeName=n),t.autoCompressPictures=!!(65536&a),t.backupFile=!!(64&a),t.checkCompatibility=!!(4096&a),t.date1904=!!(1&a),t.filterPrivacy=!!(8&a),t.hidePivotFieldList=!!(1024&a),t.promptedSolutions=!!(16&a),t.publishItems=!!(2048&a),t.refreshAllConnections=!!(262144&a),t.saveExternalLinkValues=!!(128&a),t.showBorderUnselectedTables=!!(4&a),t.showInkAnnotation=!!(32&a),t.showObjects=["all","placeholders","none"][a>>13&3],t.showPivotChartFilter=!!(32768&a),t.updateLinks=["userSet","never","always"][a>>8&3],t}function tb(e,r){r||(r=da(72));var t=0;return e&&e.filterPrivacy&&(t|=8),r.write_shift(4,t),r.write_shift(4,0),rn(e&&e.CodeName||"ThisWorkbook",r),r.slice(0,r.l)}function ab(e,r){var t={};return e.read_shift(4),t.ArchID=e.read_shift(4),e.l+=r-8,t}function nb(e,r,t){var a=e.l+r;e.l+=4,e.l+=1;var n=e.read_shift(4),s=nn(e),i=gd(e,0,t),o=tn(e);e.l=a;var c={Name:s,Ptg:i};return n<268435455&&(c.Sheet=n),o&&(c.Comment=o),c}function sb(e,r){var t={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;r||(r={}),r.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],uv[16]={n:"BrtFRTArchID$",f:ab},pa(e,(function(e,o,c){switch(c){case 156:i.SheetNames.push(e.name),t.Sheets.push(e);break;case 153:t.WBProps=e;break;case 39:null!=e.Sheet&&(r.SID=e.Sheet),e.Ref=id(e.Ptg,null,null,i,r),delete r.SID,delete e.Ptg,s.push(e);break;case 1036:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,e]):i[0]=[c,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 361:break;case 2071:case 158:case 143:case 664:case 353:break;case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:a.push(c),n=!0;break;case 36:a.pop(),n=!1;break;case 37:a.push(c),n=!0;break;case 38:a.pop(),n=!1;break;case 16:break;default:if(o.T);else if(!n||r.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}}),r),Gm(t),t.Names=s,t.supbooks=i,t}function ib(e,r){ba(e,143);for(var t=0;t!=r.SheetNames.length;++t){var a=r.Workbook&&r.Workbook.Sheets&&r.Workbook.Sheets[t]&&r.Workbook.Sheets[t].Hidden||0,n={Hidden:a,iTabID:t+1,strRelID:"rId"+(t+1),name:r.SheetNames[t]};ba(e,156,eb(n))}ba(e,144)}function ob(e,r){r||(r=da(127));for(var t=0;4!=t;++t)r.write_shift(4,0);return za("SheetJS",r),za(a.version,r),za(a.version,r),za("7262",r),r.length>r.l?r.slice(0,r.l):r}function cb(e,r){r||(r=da(29)),r.write_shift(-4,0),r.write_shift(-4,460),r.write_shift(4,28800),r.write_shift(4,17600),r.write_shift(4,500),r.write_shift(4,e),r.write_shift(4,e);var t=120;return r.write_shift(1,t),r.length>r.l?r.slice(0,r.l):r}function lb(e,r){if(r.Workbook&&r.Workbook.Sheets){for(var t=r.Workbook.Sheets,a=0,n=-1,s=-1;a<t.length;++a)!t[a]||!t[a].Hidden&&-1==n?n=a:1==t[a].Hidden&&-1==s&&(s=a);s>n||(ba(e,135),ba(e,158,cb(n)),ba(e,136))}}function fb(e,r){var t=ma();return ba(t,131),ba(t,128,ob()),ba(t,153,tb(e.Workbook&&e.Workbook.WBProps||null)),lb(t,e,r),ib(t,e,r),ba(t,132),t.end()}function hb(e,r,t){return".bin"===r.slice(-4)?sb(e,t):qm(e,t)}function ub(e,r,t,a,n,s,i,o){return".bin"===r.slice(-4)?km(e,a,t,n,s,i,o):$d(e,a,t,n,s,i,o)}function db(e,r,t,a,n,s,i,o){return".bin"===r.slice(-4)?Mm(e,a,t,n,s,i,o):Pm(e,a,t,n,s,i,o)}function pb(e,r,t,a,n,s,i,o){return".bin"===r.slice(-4)?Sh(e,a,t,n,s,i,o):yh(e,a,t,n,s,i,o)}function mb(e,r,t,a,n,s,i,o){return".bin"===r.slice(-4)?Th(e,a,t,n,s,i,o):Eh(e,a,t,n,s,i,o)}function bb(e,r,t,a){return".bin"===r.slice(-4)?cf(e,t,a):Gl(e,t,a)}function vb(e,r,t){return Rf(e,t)}function gb(e,r,t){return".bin"===r.slice(-4)?Hc(e,t):Mc(e,t)}function wb(e,r,t){return".bin"===r.slice(-4)?mh(e,t):sh(e,t)}function kb(e,r,t){return".bin"===r.slice(-4)?Zf(e,r,t):Jf(e,r,t)}function Tb(e,r,t,a){return".bin"===t.slice(-4)?eh(e,r,t,a):Qf(e,r,t,a)}function Eb(e,r,t){return".bin"===r.slice(-4)?Xf(e,r,t):Yf(e,r,t)}function Sb(e,r,t){return(".bin"===r.slice(-4)?fb:Zm)(e,t)}function yb(e,r,t,a,n){return(".bin"===r.slice(-4)?Nm:pp)(e,t,a,n)}function _b(e,r,t){return(".bin"===r.slice(-4)?wf:jl)(e,t)}function Ab(e,r,t){return(".bin"===r.slice(-4)?Gc:Bc)(e,t)}function xb(e,r,t){return(".bin"===r.slice(-4)?bh:ih)(e,t)}function Cb(e){return(".bin"===e.slice(-4)?$f:Kf)()}var Rb,Ob=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Ib=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function Nb(e,r){var t=e.split(/\s+/),a=[];if(r||(a[0]=t[0]),1===t.length)return a;var n,s,i,o,c=e.match(Ob);if(c)for(o=0;o!=c.length;++o)n=c[o].match(Ib),-1===(s=n[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):(i="xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1),a[i]=n[2].slice(1,n[2].length-1));return a}function Db(e){var r=e.split(/\s+/),t={};if(1===r.length)return t;var a,n,s,i,o=e.match(Ob);if(o)for(i=0;i!=o.length;++i)a=o[i].match(Ib),-1===(n=a[1].indexOf(":"))?t[a[1]]=a[2].slice(1,a[2].length-1):(s="xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(n+1),t[s]=a[2].slice(1,a[2].length-1));return t}function Fb(e,r){var t=Rb[e]||Kr(e);return"General"===t?fe(r):Ve(t,r)}function Pb(e,r,t,a){var n=a;switch((t[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=st(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=vr(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+t[0])}e[Kr(r)]=n}function Lb(e,r,t){if("z"!==e.t){if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||zn[e.v]:"General"===r?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=le(e.v):e.w=fe(e.v):e.w=Fb(r||"General",e.v)}catch(s){if(t.WTF)throw s}try{var a=Rb[r]||r||"General";if(t.cellNF&&(e.z=a),t.cellDates&&"n"==e.t&&Me(a)){var n=ee(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}catch(s){if(t.WTF)throw s}}}function Mb(e,r,t){if(t.cellStyles&&r.Interior){var a=r.Interior;a.Pattern&&(a.patternType=Fl[a.Pattern]||a.Pattern)}e[r.ID]=r}function Ub(e,r,t,a,n,s,i,o,c,l){var f="General",h=a.StyleID,u={};l=l||{};var d=[],p=0;void 0===h&&o&&(h=o.StyleID),void 0===h&&i&&(h=i.StyleID);while(void 0!==s[h]){if(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),!s[h].Parent)break;h=s[h].Parent}switch(t.Type){case"Boolean":a.t="b",a.v=st(e);break;case"String":a.t="s",a.r=at(Kr(e)),a.v=e.indexOf("<")>-1?Kr(r||e).replace(/<.*?>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=(vr(e)-new Date(Date.UTC(1899,11,30)))/864e5,a.v!==a.v?a.v=Kr(e):a.v<60&&(a.v=a.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=Gn[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==r?a.t="z":(a.t="s",a.v=at(r||e));break}if(Lb(a,f,l),!1!==l.cellFormula)if(a.Formula){var m=Kr(a.Formula);61==m.charCodeAt(0)&&(m=m.slice(1)),a.f=_h(m,n),delete a.Formula,"RC"==a.ArrayRange?a.F=_h("RC:RC",n):a.ArrayRange&&(a.F=_h(a.ArrayRange,n),c.push([Fa(a.F),a.F]))}else for(p=0;p<c.length;++p)n.r>=c[p][0].s.r&&n.r<=c[p][0].e.r&&n.c>=c[p][0].s.c&&n.c<=c[p][0].e.c&&(a.F=c[p][1]);l.cellStyles&&(d.forEach((function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)})),a.s=u),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function Bb(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function Wb(e,r){var t=r||{};je();var a=b(yt(e));"binary"!=t.type&&"array"!=t.type&&"base64"!=t.type||(a="undefined"!==typeof m?m.utils.decode(65001,u(a)):ft(a));var n,s=a.slice(0,1024).toLowerCase(),i=!1;if(s=s.replace(/".*?"/g,""),(1023&s.indexOf(">"))>Math.min(1023&s.indexOf(","),1023&s.indexOf(";"))){var o=wr(t);return o.type="string",yc.to_workbook(a,o)}if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){s.indexOf("<"+e)>=0&&(i=!0)})),i)return Mv(a,t);Rb={"General Number":"General","General Date":J[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":J[15],"Short Date":J[14],"Long Time":J[19],"Medium Time":J[18],"Short Time":J[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:J[2],Standard:J[4],Percent:J[10],Scientific:J[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var c,l=[];null!=w&&null==t.dense&&(t.dense=w);var f,h={},d=[],p=t.dense?[]:{},v="",g={},k={},T=Nb('<Data ss:Type="String">'),E=0,S=0,y=0,_={s:{r:2e6,c:2e6},e:{r:0,c:0}},A={},x={},C="",R=0,O=[],I={},N={},D=0,F=[],P=[],L={},M=[],U=!1,B=[],W=[],H={},V=0,z=0,G={Sheets:[],WBProps:{date1904:!1}},j={};_t.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");var X="";while(n=_t.exec(a))switch(n[3]=(X=n[3]).toLowerCase()){case"data":if("data"==X){if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break}if(l[l.length-1][1])break;"/"===n[1]?Ub(a.slice(E,n.index),C,T,"comment"==l[l.length-1][0]?L:g,{c:S,r:y},A,M[S],k,B,t):(C="",T=Nb(n[0]),E=n.index+n[0].length);break;case"cell":if("/"===n[1])if(P.length>0&&(g.c=P),(!t.sheetRows||t.sheetRows>y)&&void 0!==g.v&&(t.dense?(p[y]||(p[y]=[]),p[y][S]=g):p[Aa(S)+Ea(y)]=g),g.HRef&&(g.l={Target:Kr(g.HRef)},g.HRefScreenTip&&(g.l.Tooltip=g.HRefScreenTip),delete g.HRef,delete g.HRefScreenTip),(g.MergeAcross||g.MergeDown)&&(V=S+(0|parseInt(g.MergeAcross,10)),z=y+(0|parseInt(g.MergeDown,10)),O.push({s:{c:S,r:y},e:{c:V,r:z}})),t.sheetStubs)if(g.MergeAcross||g.MergeDown){for(var $=S;$<=V;++$)for(var Y=y;Y<=z;++Y)($>S||Y>y)&&(t.dense?(p[Y]||(p[Y]=[]),p[Y][$]={t:"z"}):p[Aa($)+Ea(Y)]={t:"z"});S=V+1}else++S;else g.MergeAcross?S=V+1:++S;else g=Db(n[0]),g.Index&&(S=+g.Index-1),S<_.s.c&&(_.s.c=S),S>_.e.c&&(_.e.c=S),"/>"===n[0].slice(-2)&&++S,P=[];break;case"row":"/"===n[1]||"/>"===n[0].slice(-2)?(y<_.s.r&&(_.s.r=y),y>_.e.r&&(_.e.r=y),"/>"===n[0].slice(-2)&&(k=Nb(n[0]),k.Index&&(y=+k.Index-1)),S=0,++y):(k=Nb(n[0]),k.Index&&(y=+k.Index-1),H={},("0"==k.AutoFitHeight||k.Height)&&(H.hpx=parseInt(k.Height,10),H.hpt=Nl(H.hpx),W[y]=H),"1"==k.Hidden&&(H.hidden=!0,W[y]=H));break;case"worksheet":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));d.push(v),_.s.r<=_.e.r&&_.s.c<=_.e.c&&(p["!ref"]=Da(_),t.sheetRows&&t.sheetRows<=_.e.r&&(p["!fullref"]=p["!ref"],_.e.r=t.sheetRows-1,p["!ref"]=Da(_))),O.length&&(p["!merges"]=O),M.length>0&&(p["!cols"]=M),W.length>0&&(p["!rows"]=W),h[v]=p}else _={s:{r:2e6,c:2e6},e:{r:0,c:0}},y=S=0,l.push([n[3],!1]),c=Nb(n[0]),v=Kr(c.Name),p=t.dense?[]:{},O=[],B=[],W=[],j={name:v,Hidden:0},G.Sheets.push(j);break;case"table":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else{if("/>"==n[0].slice(-2))break;l.push([n[3],!1]),M=[],U=!1}break;case"style":"/"===n[1]?Mb(A,x,t):x=Nb(n[0]);break;case"numberformat":x.nf=Kr(Nb(n[0]).Format||"General"),Rb[x.nf]&&(x.nf=Rb[x.nf]);for(var K=0;392!=K;++K)if(J[K]==x.nf)break;if(392==K)for(K=57;392!=K;++K)if(null==J[K]){ze(x.nf,K);break}break;case"column":if("table"!==l[l.length-1][0])break;if(f=Nb(n[0]),f.Hidden&&(f.hidden=!0,delete f.Hidden),f.Width&&(f.wpx=parseInt(f.Width,10)),!U&&f.wpx>10){U=!0,Sl=kl;for(var q=0;q<M.length;++q)M[q]&&Rl(M[q])}U&&Rl(f),M[f.Index-1||M.length]=f;for(var Z=0;Z<+f.Span;++Z)M[M.length]=wr(f);break;case"namedrange":if("/"===n[1])break;G.Names||(G.Names=[]);var Q=jr(n[0]),ee={Name:Q.Name,Ref:_h(Q.RefersTo.slice(1),{r:0,c:0})};G.Sheets.length>0&&(ee.Sheet=G.Sheets.length-1),G.Names.push(ee);break;case"namedcell":break;case"b":break;case"i":break;case"u":break;case"s":break;case"em":break;case"h2":break;case"h3":break;case"sub":break;case"sup":break;case"span":break;case"alignment":break;case"borders":break;case"border":break;case"font":if("/>"===n[0].slice(-2))break;"/"===n[1]?C+=a.slice(R,n.index):R=n.index+n[0].length;break;case"interior":if(!t.cellStyles)break;x.Interior=Nb(n[0]);break;case"protection":break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===n[0].slice(-2))break;"/"===n[1]?Ss(I,X,a.slice(D,n.index)):D=n.index+n[0].length;break;case"paragraphs":break;case"styles":case"workbook":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else l.push([n[3],!1]);break;case"comment":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"));Bb(L),P.push(L)}else l.push([n[3],!1]),c=Nb(n[0]),L={a:c.Author};break;case"autofilter":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else if("/"!==n[0].charAt(n[0].length-2)){var re=Nb(n[0]);p["!autofilter"]={ref:_h(re.Range).replace(/\$/g,"")},l.push([n[3],!0])}break;case"name":break;case"datavalidation":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break;case"pixelsperinch":break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===n[1]){if((c=l.pop())[0]!==n[3])throw new Error("Bad state: "+c.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break;case"null":break;default:if(0==l.length&&"document"==n[3])return Kv(a,t);if(0==l.length&&"uof"==n[3])return Kv(a,t);var te=!0;switch(l[l.length-1][0]){case"officedocumentsettings":switch(n[3]){case"allowpng":break;case"removepersonalinformation":break;case"downloadcomponents":break;case"locationofcomponents":break;case"colors":break;case"color":break;case"index":break;case"rgb":break;case"targetscreensize":break;case"readonlyrecommended":break;default:te=!1}break;case"componentoptions":switch(n[3]){case"toolbar":break;case"hideofficelogo":break;case"spreadsheetautofit":break;case"label":break;case"caption":break;case"maxheight":break;case"maxwidth":break;case"nextsheetnumber":break;default:te=!1}break;case"excelworkbook":switch(n[3]){case"date1904":G.WBProps.date1904=!0;break;case"windowheight":break;case"windowwidth":break;case"windowtopx":break;case"windowtopy":break;case"tabratio":break;case"protectstructure":break;case"protectwindow":break;case"protectwindows":break;case"activesheet":break;case"displayinknotes":break;case"firstvisiblesheet":break;case"supbook":break;case"sheetname":break;case"sheetindex":break;case"sheetindexfirst":break;case"sheetindexlast":break;case"dll":break;case"acceptlabelsinformulas":break;case"donotsavelinkvalues":break;case"iteration":break;case"maxiterations":break;case"maxchange":break;case"path":break;case"xct":break;case"count":break;case"selectedsheets":break;case"calculation":break;case"uncalced":break;case"startupprompt":break;case"crn":break;case"externname":break;case"formula":break;case"colfirst":break;case"collast":break;case"wantadvise":break;case"boolean":break;case"error":break;case"text":break;case"ole":break;case"noautorecover":break;case"publishobjects":break;case"donotcalculatebeforesave":break;case"number":break;case"refmoder1c1":break;case"embedsavesmarttags":break;default:te=!1}break;case"workbookoptions":switch(n[3]){case"owcversion":break;case"height":break;case"width":break;default:te=!1}break;case"worksheetoptions":switch(n[3]){case"visible":if("/>"===n[0].slice(-2));else if("/"===n[1])switch(a.slice(D,n.index)){case"SheetHidden":j.Hidden=1;break;case"SheetVeryHidden":j.Hidden=2;break}else D=n.index+n[0].length;break;case"header":p["!margins"]||Nd(p["!margins"]={},"xlml"),isNaN(+jr(n[0]).Margin)||(p["!margins"].header=+jr(n[0]).Margin);break;case"footer":p["!margins"]||Nd(p["!margins"]={},"xlml"),isNaN(+jr(n[0]).Margin)||(p["!margins"].footer=+jr(n[0]).Margin);break;case"pagemargins":var ae=jr(n[0]);p["!margins"]||Nd(p["!margins"]={},"xlml"),isNaN(+ae.Top)||(p["!margins"].top=+ae.Top),isNaN(+ae.Left)||(p["!margins"].left=+ae.Left),isNaN(+ae.Right)||(p["!margins"].right=+ae.Right),isNaN(+ae.Bottom)||(p["!margins"].bottom=+ae.Bottom);break;case"displayrighttoleft":G.Views||(G.Views=[]),G.Views[0]||(G.Views[0]={}),G.Views[0].RTL=!0;break;case"freezepanes":break;case"frozennosplit":break;case"splithorizontal":case"splitvertical":break;case"donotdisplaygridlines":break;case"activerow":break;case"activecol":break;case"toprowbottompane":break;case"leftcolumnrightpane":break;case"unsynced":break;case"print":break;case"printerrors":break;case"panes":break;case"scale":break;case"pane":break;case"number":break;case"layout":break;case"pagesetup":break;case"selected":break;case"protectobjects":break;case"enableselection":break;case"protectscenarios":break;case"validprinterinfo":break;case"horizontalresolution":break;case"verticalresolution":break;case"numberofcopies":break;case"activepane":break;case"toprowvisible":break;case"leftcolumnvisible":break;case"fittopage":break;case"rangeselection":break;case"papersizeindex":break;case"pagelayoutzoom":break;case"pagebreakzoom":break;case"filteron":break;case"fitwidth":break;case"fitheight":break;case"commentslayout":break;case"zoom":break;case"lefttoright":break;case"gridlines":break;case"allowsort":break;case"allowfilter":break;case"allowinsertrows":break;case"allowdeleterows":break;case"allowinsertcols":break;case"allowdeletecols":break;case"allowinserthyperlinks":break;case"allowformatcells":break;case"allowsizecols":break;case"allowsizerows":break;case"nosummaryrowsbelowdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].above=!0;break;case"tabcolorindex":break;case"donotdisplayheadings":break;case"showpagelayoutzoom":break;case"nosummarycolumnsrightdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].left=!0;break;case"blackandwhite":break;case"donotdisplayzeros":break;case"displaypagebreak":break;case"rowcolheadings":break;case"donotdisplayoutline":break;case"noorientation":break;case"allowusepivottables":break;case"zeroheight":break;case"viewablerange":break;case"selection":break;case"protectcontents":break;default:te=!1}break;case"pivottable":case"pivotcache":switch(n[3]){case"immediateitemsondrop":break;case"showpagemultipleitemlabel":break;case"compactrowindent":break;case"location":break;case"pivotfield":break;case"orientation":break;case"layoutform":break;case"layoutsubtotallocation":break;case"layoutcompactrow":break;case"position":break;case"pivotitem":break;case"datatype":break;case"datafield":break;case"sourcename":break;case"parentfield":break;case"ptlineitems":break;case"ptlineitem":break;case"countofsameitems":break;case"item":break;case"itemtype":break;case"ptsource":break;case"cacheindex":break;case"consolidationreference":break;case"filename":break;case"reference":break;case"nocolumngrand":break;case"norowgrand":break;case"blanklineafteritems":break;case"hidden":break;case"subtotal":break;case"basefield":break;case"mapchilditems":break;case"function":break;case"refreshonfileopen":break;case"printsettitles":break;case"mergelabels":break;case"defaultversion":break;case"refreshname":break;case"refreshdate":break;case"refreshdatecopy":break;case"versionlastrefresh":break;case"versionlastupdate":break;case"versionupdateablemin":break;case"versionrefreshablemin":break;case"calculation":break;default:te=!1}break;case"pagebreaks":switch(n[3]){case"colbreaks":break;case"colbreak":break;case"rowbreaks":break;case"rowbreak":break;case"colstart":break;case"colend":break;case"rowend":break;default:te=!1}break;case"autofilter":switch(n[3]){case"autofiltercolumn":break;case"autofiltercondition":break;case"autofilterand":break;case"autofilteror":break;default:te=!1}break;case"querytable":switch(n[3]){case"id":break;case"autoformatfont":break;case"autoformatpattern":break;case"querysource":break;case"querytype":break;case"enableredirections":break;case"refreshedinxl9":break;case"urlstring":break;case"htmltables":break;case"connection":break;case"commandtext":break;case"refreshinfo":break;case"notitles":break;case"nextid":break;case"columninfo":break;case"overwritecells":break;case"donotpromptforfile":break;case"textwizardsettings":break;case"source":break;case"number":break;case"decimal":break;case"thousandseparator":break;case"trailingminusnumbers":break;case"formatsettings":break;case"fieldtype":break;case"delimiters":break;case"tab":break;case"comma":break;case"autoformatname":break;case"versionlastedit":break;case"versionlastrefresh":break;default:te=!1}break;case"datavalidation":switch(n[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;case"cellrangelist":break;default:te=!1}break;case"sorting":case"conditionalformatting":switch(n[3]){case"range":break;case"type":break;case"min":break;case"max":break;case"sort":break;case"descending":break;case"order":break;case"casesensitive":break;case"value":break;case"errorstyle":break;case"errormessage":break;case"errortitle":break;case"cellrangelist":break;case"inputmessage":break;case"inputtitle":break;case"combohide":break;case"inputhide":break;case"condition":break;case"qualifier":break;case"useblank":break;case"value1":break;case"value2":break;case"format":break;default:te=!1}break;case"mapinfo":case"schema":case"data":switch(n[3]){case"map":break;case"entry":break;case"range":break;case"xpath":break;case"field":break;case"xsdtype":break;case"filteron":break;case"aggregate":break;case"elementtype":break;case"attributetype":break;case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":break;case"row":break;default:te=!1}break;case"smarttags":break;default:te=!1;break}if(te)break;if(n[3].match(/!\[CDATA/))break;if(!l[l.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+l.join("|");if("customdocumentproperties"===l[l.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?Pb(N,X,F,a.slice(D,n.index)):(F=n,D=n.index+n[0].length);break}if(t.WTF)throw"Unrecognized tag: "+n[3]+"|"+l.join("|")}var ne={};return t.bookSheets||t.bookProps||(ne.Sheets=h),ne.SheetNames=d,ne.Workbook=G,ne.SSF=wr(J),ne.Props=I,ne.Custprops=N,ne}function Hb(e,r){switch(Pg(r=r||{}),r.type||"base64"){case"base64":return Wb(S(e),r);case"binary":case"buffer":case"file":return Wb(e,r);case"array":return Wb(O(e),r)}}function Vb(e,r){var t=[];return e.Props&&t.push(ys(e.Props,r)),e.Custprops&&t.push(_s(e.Props,e.Custprops,r)),t.join("")}function zb(){return""}function Gb(e,r){var t=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return r.cellXfs.forEach((function(e,r){var a=[];a.push(Tt("NumberFormat",null,{"ss:Format":Zr(J[e.numFmtId])}));var n={"ss:ID":"s"+(21+r)};t.push(Tt("Style",a.join(""),n))})),Tt("Styles",t.join(""))}function jb(e){return Tt("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+xh(e.Ref,{r:0,c:0})})}function Xb(e){if(!((e||{}).Workbook||{}).Names)return"";for(var r=e.Workbook.Names,t=[],a=0;a<r.length;++a){var n=r[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||t.push(jb(n)))}return Tt("Names",t.join(""))}function $b(e,r,t,a){if(!e)return"";if(!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==t&&(o.Name.match(/^_xlfn\./)||s.push(jb(o)))}return s.join("")}function Yb(e,r,t,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(Tt("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(Tt("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(Tt("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[t])if(a.Workbook.Sheets[t].Hidden)n.push(Tt("Visible",1==a.Workbook.Sheets[t].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<t;++s)if(a.Workbook.Sheets[s]&&!a.Workbook.Sheets[s].Hidden)break;s==t&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(wt("ProtectContents","True")),e["!protect"].objects&&n.push(wt("ProtectObjects","True")),e["!protect"].scenarios&&n.push(wt("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(wt("EnableSelection","UnlockedCells")):n.push(wt("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(r){e["!protect"][r[0]]&&n.push("<"+r[1]+"/>")}))),0==n.length?"":Tt("WorksheetOptions",n.join(""),{xmlns:Ct.x})}function Kb(e){return e.map((function(e){var r=nt(e.t||""),t=Tt("ss:Data",r,{xmlns:"http://www.w3.org/TR/REC-html40"});return Tt("Comment",t,{"ss:Author":e.a})})).join("")}function Jb(e,r,t,a,n,s,i){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+Zr(xh(e.f,i))),e.F&&e.F.slice(0,r.length)==r){var c=Oa(e.F.slice(r.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=Zr(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=Zr(e.l.Tooltip))),t["!merges"])for(var l=t["!merges"],f=0;f!=l.length;++f)l[f].s.c==i.c&&l[f].s.r==i.r&&(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=zn[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||J[14]);break;case"s":h="String",u=tt(e.v||"");break}var d=Dd(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p=null!=e.v?u:"",m="z"==e.t?"":'<Data ss:Type="'+h+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=Kb(e.c)),Tt("Cell",m,o)}function qb(e,r){var t='<Row ss:Index="'+(e+1)+'"';return r&&(r.hpt&&!r.hpx&&(r.hpx=Dl(r.hpt)),r.hpx&&(t+=' ss:AutoFitHeight="0" ss:Height="'+r.hpx+'"'),r.hidden&&(t+=' ss:Hidden="1"')),t+">"}function Zb(e,r,t,a){if(!e["!ref"])return"";var n=Fa(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,r){Rl(e);var t=!!e.width,a=Id(r,e),n={"ss:Index":r+1};t&&(n["ss:Width"]=yl(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push(Tt("Column",null,n))}));for(var c=Array.isArray(e),l=n.s.r;l<=n.e.r;++l){for(var f=[qb(l,(e["!rows"]||[])[l])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h)&&!(s[i].s.r>l)&&!(s[i].e.c<h)&&!(s[i].e.r<l)){s[i].s.c==h&&s[i].s.r==l||(u=!0);break}if(!u){var d={r:l,c:h},p=Ia(d),m=c?(e[l]||[])[h]:e[p];f.push(Jb(m,p,e,r,t,a,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}function Qb(e,r,t){var a=[],n=t.SheetNames[e],s=t.Sheets[n],i=s?$b(s,r,e,t):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),i=s?Zb(s,r,e,t):"",i.length>0&&a.push("<Table>"+i+"</Table>"),a.push(Yb(s,r,e,t)),a.join("")}function ev(e,r){r||(r={}),e.SSF||(e.SSF=wr(J)),e.SSF&&(je(),Ge(e.SSF),r.revssf=sr(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF,r.cellXfs=[],Dd(r.cellXfs,{},{revssf:{General:0}}));var t=[];t.push(Vb(e,r)),t.push(zb(e,r)),t.push(""),t.push("");for(var a=0;a<e.SheetNames.length;++a)t.push(Tt("Worksheet",Qb(a,r,e),{"ss:Name":Zr(e.SheetNames[a])}));return t[2]=Gb(e,r),t[3]=Xb(e,r),Ur+Tt("Workbook",t.join(""),{xmlns:Ct.ss,"xmlns:o":Ct.o,"xmlns:x":Ct.x,"xmlns:ss":Ct.ss,"xmlns:dt":Ct.dt,"xmlns:html":Ct.html})}function rv(e){var r={},t=e.content;if(t.l=28,r.AnsiUserType=t.read_shift(0,"lpstr-ansi"),r.AnsiClipboardFormat=Tn(t),t.length-t.l<=4)return r;var a=t.read_shift(4);return 0==a||a>40?r:(t.l-=4,r.Reserved1=t.read_shift(0,"lpstr-ansi"),t.length-t.l<=4?r:(a=t.read_shift(4),1907505652!==a?r:(r.UnicodeClipboardFormat=En(t),a=t.read_shift(4),0==a||a>40?r:(t.l-=4,void(r.Reserved2=t.read_shift(0,"lpwstr"))))))}var tv=[60,1084,2066,2165,2175];function av(e,r,t,a,n){var s=a,i=[],o=t.slice(t.l,t.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:break;case 133:break;default:n.enc.insitu(o)}i.push(o),t.l+=s;var c=ea(t,t.l),l=dv[c],f=0;while(null!=l&&tv.indexOf(c)>-1)s=ea(t,t.l+2),f=t.l+4,2066==c?f+=4:2165!=c&&2175!=c||(f+=12),o=t.slice(f,t.l+4+s),i.push(o),t.l+=4+s,l=dv[c=ea(t,t.l)];var h=D(i);ha(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return r.f(h,h.length,n)}function nv(e,r,t){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,r.cellNF&&(e.z=J[a])}catch(s){if(r.WTF)throw s}if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||zn[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=le(e.v):e.w=fe(e.v):e.w=Ve(a,e.v,{date1904:!!t,dateNF:r&&r.dateNF})}catch(s){if(r.WTF)throw s}if(r.cellDates&&a&&"n"==e.t&&Me(J[a]||String(a))){var n=ee(e.v);n&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u))}}}function sv(e,r,t){return{v:e,ixfe:r,t:t}}function iv(e,r){var t={opts:{}},a={};null!=w&&null==r.dense&&(r.dense=w);var n,s,i,o,c,l,h,u,d=r.dense?[]:{},p={},m={},b=null,v=[],g="",k={},T="",E={},S=[],y=[],_=[],A={Sheets:[],WBProps:{date1904:!1},Views:[{}]},x={},C=function(e){return e<8?Vn[e]:e<64&&_[e-8]||Vn[e]},R=function(e,r,t){var a,n=r.XF.data;n&&n.patternType&&t&&t.cellStyles&&(r.s={},r.s.patternType=n.patternType,(a=bl(C(n.icvFore)))&&(r.s.fgColor={rgb:a}),(a=bl(C(n.icvBack)))&&(r.s.bgColor={rgb:a}))},O=function(e,r,t){if(!(W>1)&&!(t.sheetRows&&e.r>=t.sheetRows)){if(t.cellStyles&&r.XF&&r.XF.data&&R(e,r,t),delete r.ixfe,delete r.XF,n=e,T=Ia(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),t.cellFormula&&r.f)for(var a=0;a<S.length;++a)if(!(S[a][0].s.c>e.c||S[a][0].s.r>e.r)&&!(S[a][0].e.c<e.c||S[a][0].e.r<e.r)){r.F=Da(S[a][0]),S[a][0].s.c==e.c&&S[a][0].s.r==e.r||delete r.f,r.f&&(r.f=""+id(S[a][1],m,e,M,I));break}t.dense?(d[e.r]||(d[e.r]=[]),d[e.r][e.c]=r):d[T]=r}},I={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:S,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!r&&!!r.cellStyles,WTF:!!r&&!!r.wtf};r.password&&(I.password=r.password);var N=[],D=[],F=[],P=[],L=!1,M=[];M.SheetNames=I.snames,M.sharedf=I.sharedf,M.arrayf=I.arrayf,M.names=[],M.XTI=[];var U,B=0,W=0,H=0,V=[],z=[];I.codepage=1200,f(1200);var G=!1;while(e.l<e.length-1){var j=e.l,X=e.read_shift(2);if(0===X&&10===B)break;var $=e.l===e.length?0:e.read_shift(2),Y=dv[X];if(Y&&Y.f){if(r.bookSheets&&133===B&&133!==X)break;if(B=X,2===Y.r||12==Y.r){var K=e.read_shift(2);if($-=2,!I.enc&&K!==X&&((255&K)<<8|K>>8)!==X)throw new Error("rt mismatch: "+K+"!="+X);12==Y.r&&(e.l+=10,$-=10)}var q={};if(q=10===X?Y.f(e,$,I):av(X,Y,e,$,I),0==W&&-1===[9,521,1033,2057].indexOf(B))continue;switch(X){case 34:t.opts.Date1904=A.WBProps.date1904=q;break;case 134:t.opts.WriteProtect=!0;break;case 47:if(I.enc||(e.l=0),I.enc=q,!r.password)throw new Error("File is password-protected");if(null==q.valid)throw new Error("Encryption scheme unsupported");if(!q.valid)throw new Error("Password is incorrect");break;case 92:I.lastuser=q;break;case 66:var Z=Number(q);switch(Z){case 21010:Z=1200;break;case 32768:Z=1e4;break;case 32769:Z=1252;break}f(I.codepage=Z),G=!0;break;case 317:I.rrtabid=q;break;case 25:I.winlocked=q;break;case 439:t.opts["RefreshAll"]=q;break;case 12:t.opts["CalcCount"]=q;break;case 16:t.opts["CalcDelta"]=q;break;case 17:t.opts["CalcIter"]=q;break;case 13:t.opts["CalcMode"]=q;break;case 14:t.opts["CalcPrecision"]=q;break;case 95:t.opts["CalcSaveRecalc"]=q;break;case 15:I.CalcRefMode=q;break;case 2211:t.opts.FullCalc=q;break;case 129:q.fDialog&&(d["!type"]="dialog"),q.fBelow||((d["!outline"]||(d["!outline"]={})).above=!0),q.fRight||((d["!outline"]||(d["!outline"]={})).left=!0);break;case 224:y.push(q);break;case 430:M.push([q]),M[M.length-1].XTI=[];break;case 35:case 547:M[M.length-1].push(q);break;case 24:case 536:U={Name:q.Name,Ref:id(q.rgce,m,null,M,I)},q.itab>0&&(U.Sheet=q.itab-1),M.names.push(U),M[0]||(M[0]=[],M[0].XTI=[]),M[M.length-1].push(q),"_xlnm._FilterDatabase"==q.Name&&q.itab>0&&q.rgce&&q.rgce[0]&&q.rgce[0][0]&&"PtgArea3d"==q.rgce[0][0][0]&&(z[q.itab-1]={ref:Da(q.rgce[0][0][1][2])});break;case 22:I.ExternCount=q;break;case 23:0==M.length&&(M[0]=[],M[0].XTI=[]),M[M.length-1].XTI=M[M.length-1].XTI.concat(q),M.XTI=M.XTI.concat(q);break;case 2196:if(I.biff<8)break;null!=U&&(U.Comment=q[1]);break;case 18:d["!protect"]=q;break;case 19:0!==q&&I.WTF&&console.error("Password verifier: "+q);break;case 133:p[q.pos]=q,I.snames.push(q.name);break;case 10:if(--W)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,d["!ref"]=Da(m),r.sheetRows&&r.sheetRows<=m.e.r){var Q=m.e.r;m.e.r=r.sheetRows-1,d["!fullref"]=d["!ref"],d["!ref"]=Da(m),m.e.r=Q}m.e.r++,m.e.c++}N.length>0&&(d["!merges"]=N),D.length>0&&(d["!objects"]=D),F.length>0&&(d["!cols"]=F),P.length>0&&(d["!rows"]=P),A.Sheets.push(x)}""===g?k=d:a[g]=d,d=r.dense?[]:{};break;case 9:case 521:case 1033:case 2057:if(8===I.biff&&(I.biff={9:2,521:3,1033:4}[X]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[q.BIFFVer]||8),I.biffguess=0==q.BIFFVer,0==q.BIFFVer&&4096==q.dt&&(I.biff=5,G=!0,f(I.codepage=28591)),8==I.biff&&0==q.BIFFVer&&16==q.dt&&(I.biff=2),W++)break;if(d=r.dense?[]:{},I.biff<8&&!G&&(G=!0,f(I.codepage=r.codepage||1252)),I.biff<5||0==q.BIFFVer&&4096==q.dt){""===g&&(g="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var ee={pos:e.l-$,name:g};p[ee.pos]=ee,I.snames.push(g)}else g=(p[j]||{name:""}).name;32==q.dt&&(d["!type"]="chart"),64==q.dt&&(d["!type"]="macro"),N=[],D=[],I.arrayf=S=[],F=[],P=[],L=!1,x={Hidden:(p[j]||{hs:0}).hs,name:g};break;case 515:case 3:case 2:"chart"==d["!type"]&&(r.dense?(d[q.r]||[])[q.c]:d[Ia({c:q.c,r:q.r})])&&++q.c,l={ixfe:q.ixfe,XF:y[q.ixfe]||{},v:q.val,t:"n"},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r);break;case 5:case 517:l={ixfe:q.ixfe,XF:y[q.ixfe],v:q.val,t:q.t},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r);break;case 638:l={ixfe:q.ixfe,XF:y[q.ixfe],v:q.rknum,t:"n"},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r);break;case 189:for(var re=q.c;re<=q.C;++re){var te=q.rkrec[re-q.c][0];l={ixfe:te,XF:y[te],v:q.rkrec[re-q.c][1],t:"n"},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:re,r:q.r},l,r)}break;case 6:case 518:case 1030:if("String"==q.val){b=q;break}if(l=sv(q.val,q.cell.ixfe,q.tt),l.XF=y[l.ixfe],r.cellFormula){var ae=q.formula;if(ae&&ae[0]&&ae[0][0]&&"PtgExp"==ae[0][0][0]){var ne=ae[0][0][1][0],se=ae[0][0][1][1],ie=Ia({r:ne,c:se});E[ie]?l.f=""+id(q.formula,m,q.cell,M,I):l.F=((r.dense?(d[ne]||[])[se]:d[ie])||{}).F}else l.f=""+id(q.formula,m,q.cell,M,I)}H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O(q.cell,l,r),b=q;break;case 7:case 519:if(!b)throw new Error("String record expects Formula");b.val=q,l=sv(q,b.cell.ixfe,"s"),l.XF=y[l.ixfe],r.cellFormula&&(l.f=""+id(b.formula,m,b.cell,M,I)),H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O(b.cell,l,r),b=null;break;case 33:case 545:S.push(q);var oe=Ia(q[0].s);if(s=r.dense?(d[q[0].s.r]||[])[q[0].s.c]:d[oe],r.cellFormula&&s){if(!b)break;if(!oe||!s)break;s.f=""+id(q[1],m,q[0],M,I),s.F=Da(q[0])}break;case 1212:if(!r.cellFormula)break;if(T){if(!b)break;E[Ia(b.cell)]=q[0],s=r.dense?(d[b.cell.r]||[])[b.cell.c]:d[Ia(b.cell)],(s||{}).f=""+id(q[0],m,n,M,I)}break;case 253:l=sv(v[q.isst].t,q.ixfe,"s"),v[q.isst].h&&(l.h=v[q.isst].h),l.XF=y[l.ixfe],H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r);break;case 513:r.sheetStubs&&(l={ixfe:q.ixfe,XF:y[q.ixfe],t:"z"},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r));break;case 190:if(r.sheetStubs)for(var ce=q.c;ce<=q.C;++ce){var le=q.ixfe[ce-q.c];l={ixfe:le,XF:y[le],t:"z"},H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:ce,r:q.r},l,r)}break;case 214:case 516:case 4:l=sv(q.val,q.ixfe,"s"),l.XF=y[l.ixfe],H>0&&(l.z=V[l.ixfe>>8&63]),nv(l,r,t.opts.Date1904),O({c:q.c,r:q.r},l,r);break;case 0:case 512:1===W&&(m=q);break;case 252:v=q;break;case 1054:if(4==I.biff){V[H++]=q[1];for(var fe=0;fe<H+163;++fe)if(J[fe]==q[1])break;fe>=163&&ze(q[1],H+163)}else ze(q[1],q[0]);break;case 30:V[H++]=q;for(var he=0;he<H+163;++he)if(J[he]==q)break;he>=163&&ze(q,H+163);break;case 229:N=N.concat(q);break;case 93:D[q.cmo[0]]=I.lastobj=q;break;case 438:I.lastobj.TxO=q;break;case 127:I.lastobj.ImData=q;break;case 440:for(c=q[0].s.r;c<=q[0].e.r;++c)for(o=q[0].s.c;o<=q[0].e.c;++o)s=r.dense?(d[c]||[])[o]:d[Ia({c:o,r:c})],s&&(s.l=q[1]);break;case 2048:for(c=q[0].s.r;c<=q[0].e.r;++c)for(o=q[0].s.c;o<=q[0].e.c;++o)s=r.dense?(d[c]||[])[o]:d[Ia({c:o,r:c})],s&&s.l&&(s.l.Tooltip=q[1]);break;case 28:if(I.biff<=5&&I.biff>=2)break;s=r.dense?(d[q[0].r]||[])[q[0].c]:d[Ia(q[0])];var ue=D[q[2]];s||(r.dense?(d[q[0].r]||(d[q[0].r]=[]),s=d[q[0].r][q[0].c]={t:"z"}):s=d[Ia(q[0])]={t:"z"},m.e.r=Math.max(m.e.r,q[0].r),m.s.r=Math.min(m.s.r,q[0].r),m.e.c=Math.max(m.e.c,q[0].c),m.s.c=Math.min(m.s.c,q[0].c)),s.c||(s.c=[]),i={a:q[1],t:ue.TxO.t},s.c.push(i);break;case 2173:Uf(y[q.ixfe],q.ext);break;case 125:if(!I.cellStyles)break;while(q.e>=q.s)F[q.e--]={width:q.w/256,level:q.level||0,hidden:!!(1&q.flags)},L||(L=!0,Cl(q.w/256)),Rl(F[q.e+1]);break;case 520:var de={};null!=q.level&&(P[q.r]=de,de.level=q.level),q.hidden&&(P[q.r]=de,de.hidden=!0),q.hpt&&(P[q.r]=de,de.hpt=q.hpt,de.hpx=Dl(q.hpt));break;case 38:case 39:case 40:case 41:d["!margins"]||Nd(d["!margins"]={}),d["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[X]]=q;break;case 161:d["!margins"]||Nd(d["!margins"]={}),d["!margins"].header=q.header,d["!margins"].footer=q.footer;break;case 574:q.RTL&&(A.Views[0].RTL=!0);break;case 146:_=q;break;case 2198:u=q;break;case 140:h=q;break;case 442:g?x.CodeName=q||x.name:A.WBProps.CodeName=q||"ThisWorkbook";break}}else Y||console.error("Missing Info for XLS Record 0x"+X.toString(16)),e.l+=$}return t.SheetNames=tr(p).sort((function(e,r){return Number(e)-Number(r)})).map((function(e){return p[e].name})),r.bookSheets||(t.Sheets=a),!t.SheetNames.length&&k["!ref"]?(t.SheetNames.push("Sheet1"),t.Sheets&&(t.Sheets["Sheet1"]=k)):t.Preamble=k,t.Sheets&&z.forEach((function(e,r){t.Sheets[t.SheetNames[r]]["!autofilter"]=e})),t.Strings=v,t.SSF=wr(J),I.enc&&(t.Encryption=I.enc),u&&(t.Themes=u),t.Metadata={},void 0!==h&&(t.Metadata.Country=h),M.names.length>0&&(A.Names=M.names),t.Workbook=A,t}var ov={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function cv(e,r,t){var a=qe.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Xs(a,Ln,ov.DSI);for(var s in n)r[s]=n[s]}catch(l){if(t.WTF)throw l}var i=qe.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=Xs(i,Mn,ov.SI);for(var c in o)null==r[c]&&(r[c]=o[c])}catch(l){if(t.WTF)throw l}r.HeadingPairs&&r.TitlesOfParts&&(ms(r.HeadingPairs,r.TitlesOfParts,r,t),delete r.HeadingPairs,delete r.TitlesOfParts)}function lv(e,r){var t,a=[],n=[],s=[],i=0,o=ar(Ln,"n"),c=ar(Mn,"n");if(e.Props)for(t=tr(e.Props),i=0;i<t.length;++i)(Object.prototype.hasOwnProperty.call(o,t[i])?a:Object.prototype.hasOwnProperty.call(c,t[i])?n:s).push([t[i],e.Props[t[i]]]);if(e.Custprops)for(t=tr(e.Custprops),i=0;i<t.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},t[i])||(Object.prototype.hasOwnProperty.call(o,t[i])?a:Object.prototype.hasOwnProperty.call(c,t[i])?n:s).push([t[i],e.Custprops[t[i]]]);var l=[];for(i=0;i<s.length;++i)zs.indexOf(s[i][0])>-1||ps.indexOf(s[i][0])>-1||null!=s[i][1]&&l.push(s[i]);n.length&&qe.utils.cfb_add(r,"/SummaryInformation",$s(n,ov.SI,c,Mn)),(a.length||l.length)&&qe.utils.cfb_add(r,"/DocumentSummaryInformation",$s(a,ov.DSI,o,Ln,l.length?l:null,ov.UDI))}function fv(e,r){var t,a,n,s;if(r||(r={}),Pg(r),h(),r.codepage&&c(r.codepage),e.FullPaths){if(qe.find(e,"/encryption"))throw new Error("File is password-protected");t=qe.find(e,"!CompObj"),a=qe.find(e,"/Workbook")||qe.find(e,"/Book")}else{switch(r.type){case"base64":e=C(S(e));break;case"binary":e=C(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e));break}ha(e,0),a={content:e}}if(t&&rv(t),r.bookProps&&!r.bookSheets)n={};else{var i=y?"buffer":"array";if(a&&a.content)n=iv(a.content,r);else if((s=qe.find(e,"PerfectOffice_MAIN"))&&s.content)n=Ac.to_workbook(s.content,(r.type=i,r));else{if(!(s=qe.find(e,"NativeContent_MAIN"))||!s.content)throw(s=qe.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");n=Ac.to_workbook(s.content,(r.type=i,r))}r.bookVBA&&e.FullPaths&&qe.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=gh(e))}var o={};return e.FullPaths&&cv(e,o,r),n.Props=n.Custprops=o,r.bookFiles&&(n.cfb=e),n}function hv(e,r){var t=r||{},a=qe.utils.cfb_new({root:"R"}),n="/Workbook";switch(t.bookType||"xls"){case"xls":t.bookType="biff8";case"xla":t.bookType||(t.bookType="xla");case"biff8":n="/Workbook",t.biff=8;break;case"biff5":n="/Book",t.biff=5;break;default:throw new Error("invalid type "+t.bookType+" for XLS CFB")}return qe.utils.cfb_add(a,n,Nv(e,t)),8==t.biff&&(e.Props||e.Custprops)&&lv(e,a),8==t.biff&&e.vbaraw&&wh(a,qe.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),a}var uv={0:{f:mp},1:{f:Sp},2:{f:zp},3:{f:Ip},4:{f:xp},5:{f:Bp},6:{f:Yp},7:{f:Pp},8:{f:rm},9:{f:em},10:{f:Zp},11:{f:Qp},12:{f:_p},13:{f:jp},14:{f:Dp},15:{f:Rp},16:{f:Hp},17:{f:Jp},18:{f:Mp},19:{f:Xa},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:nb},40:{},42:{},43:{f:Yl},44:{f:Xl},45:{f:Zl},46:{f:af},47:{f:ef},48:{},49:{f:Wa},50:{},51:{f:Hf},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:tc},62:{f:$p},63:{f:qf},64:{f:gm},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:ua,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:pm},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:Tp},148:{f:gp,p:16},151:{f:om},152:{},153:{f:rb},154:{},155:{},156:{f:Qm},157:{},158:{},159:{T:1,f:Wc},160:{T:-1},161:{T:1,f:un},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:tm},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:Bf},336:{T:-1},337:{f:Gf,T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:sn},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Do},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:cm},427:{f:lm},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:um},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:kp},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:sm},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:sn},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:dh},633:{T:1},634:{T:-1},635:{T:1,f:hh},636:{T:-1},637:{f:Ya},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:Lm},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:wm},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},dv={6:{f:dd},10:{f:Ys},12:{f:Qs},13:{f:Qs},14:{f:qs},15:{f:qs},16:{f:pn},17:{f:qs},18:{f:qs},19:{f:Qs},20:{f:Co},21:{f:Co},23:{f:Do},24:{f:No},25:{f:qs},26:{},27:{},28:{f:Wo},29:{},34:{f:qs},35:{f:Oo},38:{f:pn},39:{f:pn},40:{f:pn},41:{f:pn},42:{f:qs},43:{f:qs},47:{f:dl},49:{f:no},51:{f:Qs},60:{},61:{f:Qi},64:{f:qs},65:{f:ao},66:{f:Qs},77:{},80:{},81:{},82:{},85:{f:Qs},89:{},90:{},91:{},92:{f:Hi},93:{f:zo},94:{},95:{f:qs},96:{},97:{},99:{f:qs},125:{f:tc},128:{f:Eo},129:{f:zi},130:{f:Qs},131:{f:qs},132:{f:qs},133:{f:Gi},134:{},140:{f:qo},141:{f:Qs},144:{},146:{f:ec},151:{},152:{},153:{},154:{},155:{},156:{f:Qs},157:{},158:{},160:{f:cc},161:{f:nc},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:vo},190:{f:go},193:{f:Ys},197:{},198:{},199:{},200:{},201:{},202:{f:qs},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Qs},220:{},221:{f:qs},222:{},224:{f:ko},225:{f:Wi},226:{f:Ys},227:{},229:{f:Ho},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:Xi},253:{f:io},255:{f:Yi},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:ri},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:qs},353:{f:Ys},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:Ro},431:{f:qs},432:{},433:{},434:{},437:{},438:{f:Xo},439:{f:qs},440:{f:$o},441:{},442:{f:ci},443:{},444:{f:Qs},445:{},446:{},448:{f:Ys},449:{f:qi,r:2},450:{f:Ys},512:{f:po},513:{f:oc},515:{f:Ao},516:{f:co},517:{f:yo},519:{f:lc},520:{f:Ki},523:{},545:{f:Mo},549:{f:Zi},566:{},574:{f:ro},638:{f:bo},659:{},1048:{},1054:{f:fo},1084:{},1212:{f:Lo},2048:{f:Ko},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:Ui},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Ys},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:rc,r:12},2173:{f:Mf,r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:qs,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:Po,r:12},2197:{},2198:{f:If,r:12},2199:{},2200:{},2201:{},2202:{f:Uo,r:12},2203:{f:Ys},2204:{},2205:{},2206:{},2207:{},2211:{f:Ji},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Qs},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:sc},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:Qo},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:po},1:{},2:{f:pc},3:{f:uc},4:{f:hc},5:{f:yo},7:{f:bc},8:{},9:{f:Ui},11:{},22:{f:Qs},30:{f:uo},31:{},32:{},33:{f:Mo},36:{},37:{f:Zi},50:{f:vc},62:{},52:{},67:{},68:{f:Qs},69:{},86:{},126:{},127:{f:fc},135:{},136:{},137:{},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:gc},223:{},234:{},354:{},421:{},518:{f:dd},521:{f:Ui},536:{f:No},547:{f:Oo},561:{},579:{},1030:{f:dd},1033:{f:Ui},1091:{},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},29282:{}};function pv(e,r,t,a){var n=r;if(!isNaN(n)){var s=a||(t||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&qt(t)&&e.push(t)}}function mv(e,r,t,a){var n=a||(t||[]).length||0;if(n<=8224)return pv(e,r,t,n);var s=r;if(!isNaN(s)){var i=t.parts||[],o=0,c=0,l=0;while(l+(i[o]||8224)<=8224)l+=i[o]||8224,o++;var f=e.next(4);f.write_shift(2,s),f.write_shift(2,l),e.push(t.slice(c,c+l)),c+=l;while(c<n){f=e.next(4),f.write_shift(2,60),l=0;while(l+(i[o]||8224)<=8224)l+=i[o]||8224,o++;f.write_shift(2,l),e.push(t.slice(c,c+l)),c+=l}}}function bv(e,r,t){return e||(e=da(7)),e.write_shift(2,r),e.write_shift(2,t),e.write_shift(2,0),e.write_shift(1,0),e}function vv(e,r,t,a){var n=da(9);return bv(n,e,r),ai(t,a||"b",n),n}function gv(e,r,t){var a=da(8+2*t.length);return bv(a,e,r),a.write_shift(1,t.length),a.write_shift(t.length,t,"sbcs"),a.l<a.length?a.slice(0,a.l):a}function wv(e,r,t,a){if(null!=r.v)switch(r.t){case"d":case"n":var n="d"==r.t?cr(vr(r.v)):r.v;return void(n==(0|n)&&n>=0&&n<65536?pv(e,2,mc(t,a,n)):pv(e,3,dc(t,a,n)));case"b":case"e":return void pv(e,5,vv(t,a,r.v,r.t));case"s":case"str":return void pv(e,4,gv(t,a,(r.v||"").slice(0,255)))}pv(e,1,bv(null,t,a))}function kv(e,r,t,a){var n,s=Array.isArray(r),i=Fa(r["!ref"]||"A1"),o="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(r["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383),n=Da(i)}for(var l=i.s.r;l<=i.e.r;++l){o=Ea(l);for(var f=i.s.c;f<=i.e.c;++f){l===i.s.r&&(c[f]=Aa(f)),n=c[f]+o;var h=s?(r[l]||[])[f]:r[n];h&&wv(e,h,l,f,a)}}}function Tv(e,r){var t=r||{};null!=w&&null==t.dense&&(t.dense=w);for(var a=ma(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==t.sheet&&(n=s);if(0==n&&t.sheet&&e.SheetNames[0]!=t.sheet)throw new Error("Sheet not found: "+t.sheet);return pv(a,4==t.biff?1033:3==t.biff?521:9,Bi(e,16,t)),kv(a,e.Sheets[e.SheetNames[n]],n,t,e),pv(a,10),a.end()}function Ev(e,r,t){pv(e,49,so({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},t))}function Sv(e,r,t){r&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(a){for(var n=a[0];n<=a[1];++n)null!=r[n]&&pv(e,1054,ho(n,r[n],t))}))}function yv(e,r){var t=da(19);t.write_shift(4,2151),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(2,3),t.write_shift(1,1),t.write_shift(4,0),pv(e,2151,t),t=da(39),t.write_shift(4,2152),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(2,3),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(2,1),t.write_shift(4,4),t.write_shift(2,0),Ri(Fa(r["!ref"]||"A1"),t),t.write_shift(4,4),pv(e,2152,t)}function _v(e,r){for(var t=0;t<16;++t)pv(e,224,To({numFmtId:0,style:!0},0,r));r.cellXfs.forEach((function(t){pv(e,224,To(t,0,r))}))}function Av(e,r){for(var t=0;t<r["!links"].length;++t){var a=r["!links"][t];pv(e,440,Yo(a)),a[1].Tooltip&&pv(e,2048,Jo(a))}delete r["!links"]}function xv(e,r){if(r){var t=0;r.forEach((function(r,a){++t<=256&&r&&pv(e,125,ac(Id(a,r),a))}))}}function Cv(e,r,t,a,n){var s=16+Dd(n.cellXfs,r,n);if(null!=r.v||r.bf)if(r.bf)pv(e,6,pd(r,t,a,n,s));else switch(r.t){case"d":case"n":var i="d"==r.t?cr(vr(r.v)):r.v;pv(e,515,xo(t,a,i,s,n));break;case"b":case"e":pv(e,517,_o(t,a,r.v,s,n,r.t));break;case"s":case"str":if(n.bookSST){var o=Od(n.Strings,r.v,n.revStrings);pv(e,253,oo(t,a,o,s,n))}else pv(e,516,lo(t,a,(r.v||"").slice(0,255),s,n));break;default:pv(e,513,Ei(t,a,s))}else pv(e,513,Ei(t,a,s))}function Rv(e,r,t){var a,n=ma(),s=t.SheetNames[e],i=t.Sheets[s]||{},o=(t||{}).Workbook||{},c=(o.Sheets||[])[e]||{},l=Array.isArray(i),f=8==r.biff,h="",u=[],d=Fa(i["!ref"]||"A1"),p=f?65536:16384;if(d.e.c>255||d.e.r>=p){if(r.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}pv(n,2057,Bi(t,16,r)),pv(n,13,ei(1)),pv(n,12,ei(100)),pv(n,15,Zs(!0)),pv(n,17,Zs(!1)),pv(n,16,mn(.001)),pv(n,95,Zs(!0)),pv(n,42,Zs(!1)),pv(n,43,Zs(!1)),pv(n,130,ei(1)),pv(n,128,So([0,0])),pv(n,131,Zs(!1)),pv(n,132,Zs(!1)),f&&xv(n,i["!cols"]),pv(n,512,mo(d,r)),f&&(i["!links"]=[]);for(var m=d.s.r;m<=d.e.r;++m){h=Ea(m);for(var b=d.s.c;b<=d.e.c;++b){m===d.s.r&&(u[b]=Aa(b)),a=u[b]+h;var v=l?(i[m]||[])[b]:i[a];v&&(Cv(n,v,m,b,r),f&&v.l&&i["!links"].push([a,v.l]))}}var g=c.CodeName||c.name||s;return f&&pv(n,574,to((o.Views||[])[0])),f&&(i["!merges"]||[]).length&&pv(n,229,Vo(i["!merges"])),f&&Av(n,i),pv(n,442,fi(g,r)),f&&yv(n,i),pv(n,10),n.end()}function Ov(e,r,t){var a=ma(),n=(e||{}).Workbook||{},s=n.Sheets||[],i=n.WBProps||{},o=8==t.biff,c=5==t.biff;if(pv(a,2057,Bi(e,5,t)),"xla"==t.bookType&&pv(a,135),pv(a,225,o?ei(1200):null),pv(a,193,Ks(2)),c&&pv(a,191),c&&pv(a,192),pv(a,226),pv(a,92,Vi("SheetJS",t)),pv(a,66,ei(o?1200:1252)),o&&pv(a,353,ei(0)),o&&pv(a,448),pv(a,317,ic(e.SheetNames.length)),o&&e.vbaraw&&pv(a,211),o&&e.vbaraw){var l=i.CodeName||"ThisWorkbook";pv(a,442,fi(l,t))}pv(a,156,ei(17)),pv(a,25,Zs(!1)),pv(a,18,Zs(!1)),pv(a,19,ei(0)),o&&pv(a,431,Zs(!1)),o&&pv(a,444,ei(0)),pv(a,61,eo(t)),pv(a,64,Zs(!1)),pv(a,141,ei(0)),pv(a,34,Zs("true"==jm(e))),pv(a,14,Zs(!0)),o&&pv(a,439,Zs(!1)),pv(a,218,ei(0)),Ev(a,e,t),Sv(a,e.SSF,t),_v(a,t),o&&pv(a,352,Zs(!1));var f=a.end(),h=ma();o&&pv(h,140,Zo()),o&&t.Strings&&mv(h,252,$i(t.Strings,t)),pv(h,10);var u=h.end(),d=ma(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(o?12:11)+(o?2:1)*e.SheetNames[m].length;var b=f.length+p+u.length;for(m=0;m<e.SheetNames.length;++m){var v=s[m]||{};pv(d,133,ji({pos:b,hs:v.Hidden||0,dt:0,name:e.SheetNames[m]},t)),b+=r[m].length}var g=d.end();if(p!=g.length)throw new Error("BS8 "+p+" != "+g.length);var w=[];return f.length&&w.push(f),g.length&&w.push(g),u.length&&w.push(u),D(w)}function Iv(e,r){var t=r||{},a=[];e&&!e.SSF&&(e.SSF=wr(J)),e&&e.SSF&&(je(),Ge(e.SSF),t.revssf=sr(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Lg(t),t.cellXfs=[],Dd(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Rv(n,t,e);return a.unshift(Ov(e,a,t)),D(a)}function Nv(e,r){for(var t=0;t<=e.SheetNames.length;++t){var a=e.Sheets[e.SheetNames[t]];if(a&&a["!ref"]){var n=Na(a["!ref"]);n.e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[t]+"' extends beyond column IV (255).  Data may be lost.")}}var s=r||{};switch(s.biff||2){case 8:case 5:return Iv(e,r);case 4:case 3:case 2:return Tv(e,r)}throw new Error("invalid type "+s.bookType+" for BIFF")}function Dv(e,r){var t=r||{};null!=w&&null==t.dense&&(t.dense=w);var a=t.dense?[]:{};e=e.replace(/<!--.*?-->/g,"");var n=e.match(/<table/i);if(!n)throw new Error("Invalid HTML: could not find <table>");var s=e.match(/<\/table/i),i=n.index,o=s&&s.index||e.length,c=yr(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),l=-1,f=0,h=0,u=0,d={s:{r:1e7,c:1e7},e:{r:0,c:0}},p=[];for(i=0;i<c.length;++i){var m=c[i].trim(),b=m.slice(0,3).toLowerCase();if("<tr"!=b){if("<td"==b||"<th"==b){var v=m.split(/<\/t[dh]>/i);for(o=0;o<v.length;++o){var g=v[o].trim();if(g.match(/<t[dh]/i)){var k=g,T=0;while("<"==k.charAt(0)&&(T=k.indexOf(">"))>-1)k=k.slice(T+1);for(var E=0;E<p.length;++E){var S=p[E];S.s.c==f&&S.s.r<l&&l<=S.e.r&&(f=S.e.c+1,E=-1)}var y=jr(g.slice(0,g.indexOf(">")));u=y.colspan?+y.colspan:1,((h=+y.rowspan)>1||u>1)&&p.push({s:{r:l,c:f},e:{r:l+(h||1)-1,c:f+u-1}});var _=y.t||y["data-t"]||"";if(k.length)if(k=dt(k),d.s.r>l&&(d.s.r=l),d.e.r<l&&(d.e.r=l),d.s.c>f&&(d.s.c=f),d.e.c<f&&(d.e.c=f),k.length){var A={t:"s",v:k};t.raw||!k.trim().length||"s"==_||("TRUE"===k?A={t:"b",v:!0}:"FALSE"===k?A={t:"b",v:!1}:isNaN(Tr(k))?isNaN(Sr(k).getDate())||(A={t:"d",v:vr(k)},t.cellDates||(A={t:"n",v:cr(A.v)}),A.z=t.dateNF||J[14]):A={t:"n",v:Tr(k)}),t.dense?(a[l]||(a[l]=[]),a[l][f]=A):a[Ia({r:l,c:f})]=A,f+=u}else f+=u;else f+=u}}}}else{if(++l,t.sheetRows&&t.sheetRows<=l){--l;break}f=0}}return a["!ref"]=Da(d),p.length&&(a["!merges"]=p),a}function Fv(e,r,t,a){for(var n=e["!merges"]||[],s=[],i=r.s.c;i<=r.e.c;++i){for(var o=0,c=0,l=0;l<n.length;++l)if(!(n[l].s.r>t||n[l].s.c>i)&&!(n[l].e.r<t||n[l].e.c<i)){if(n[l].s.r<t||n[l].s.c<i){o=-1;break}o=n[l].e.r-n[l].s.r+1,c=n[l].e.c-n[l].s.c+1;break}if(!(o<0)){var f=Ia({r:t,c:i}),h=a.dense?(e[t]||[])[i]:e[f],u=h&&null!=h.v&&(h.h||rt(h.w||(La(h),h.w)||""))||"",d={};o>1&&(d.rowspan=o),c>1&&(d.colspan=c),a.editable?u='<span contenteditable="true">'+u+"</span>":h&&(d["data-t"]=h&&h.t||"z",null!=h.v&&(d["data-v"]=h.v),null!=h.z&&(d["data-z"]=h.z),h.l&&"#"!=(h.l.Target||"#").charAt(0)&&(u='<a href="'+h.l.Target+'">'+u+"</a>")),d.id=(a.id||"sjs")+"-"+f,s.push(Tt("td",u,d))}}var p="<tr>";return p+s.join("")+"</tr>"}var Pv='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Lv="</body></html>";function Mv(e,r){var t=e.match(/<table[\s\S]*?>[\s\S]*?<\/table>/gi);if(!t||0==t.length)throw new Error("Invalid HTML: could not find <table>");if(1==t.length)return Ma(Dv(t[0],r),r);var a=kw();return t.forEach((function(e,t){Tw(a,Dv(e,r),"Sheet"+(t+1))})),a}function Uv(e,r,t){var a=[];return a.join("")+"<table"+(t&&t.id?' id="'+t.id+'"':"")+">"}function Bv(e,r){var t=r||{},a=null!=t.header?t.header:Pv,n=null!=t.footer?t.footer:Lv,s=[a],i=Na(e["!ref"]);t.dense=Array.isArray(e),s.push(Uv(e,i,t));for(var o=i.s.r;o<=i.e.r;++o)s.push(Fv(e,i,o,t));return s.push("</table>"+n),s.join("")}function Wv(e,r,t){var a=t||{};null!=w&&(a.dense=w);var n=0,s=0;if(null!=a.origin)if("number"==typeof a.origin)n=a.origin;else{var i="string"==typeof a.origin?Oa(a.origin):a.origin;n=i.r,s=i.c}var o=r.getElementsByTagName("tr"),c=Math.min(a.sheetRows||1e7,o.length),l={s:{r:0,c:0},e:{r:n,c:s}};if(e["!ref"]){var f=Na(e["!ref"]);l.s.r=Math.min(l.s.r,f.s.r),l.s.c=Math.min(l.s.c,f.s.c),l.e.r=Math.max(l.e.r,f.e.r),l.e.c=Math.max(l.e.c,f.e.c),-1==n&&(l.e.r=n=f.e.r+1)}var h=[],u=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,b=0,v=0,g=0,k=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<c;++p){var T=o[p];if(zv(T)){if(a.display)continue;d[m]={hidden:!0}}var E=T.children;for(b=v=0;b<E.length;++b){var S=E[b];if(!a.display||!zv(S)){var y=S.hasAttribute("data-v")?S.getAttribute("data-v"):S.hasAttribute("v")?S.getAttribute("v"):dt(S.innerHTML),_=S.getAttribute("data-z")||S.getAttribute("z");for(u=0;u<h.length;++u){var A=h[u];A.s.c==v+s&&A.s.r<m+n&&m+n<=A.e.r&&(v=A.e.c+1-s,u=-1)}k=+S.getAttribute("colspan")||1,((g=+S.getAttribute("rowspan")||1)>1||k>1)&&h.push({s:{r:m+n,c:v+s},e:{r:m+n+(g||1)-1,c:v+s+(k||1)-1}});var x={t:"s",v:y},C=S.getAttribute("data-t")||S.getAttribute("t")||"";null!=y&&(0==y.length?x.t=C||"z":a.raw||0==y.trim().length||"s"==C||("TRUE"===y?x={t:"b",v:!0}:"FALSE"===y?x={t:"b",v:!1}:isNaN(Tr(y))?isNaN(Sr(y).getDate())||(x={t:"d",v:vr(y)},a.cellDates||(x={t:"n",v:cr(x.v)}),x.z=a.dateNF||J[14]):x={t:"n",v:Tr(y)})),void 0===x.z&&null!=_&&(x.z=_);var R="",O=S.getElementsByTagName("A");if(O&&O.length)for(var I=0;I<O.length;++I)if(O[I].hasAttribute("href")&&(R=O[I].getAttribute("href"),"#"!=R.charAt(0)))break;R&&"#"!=R.charAt(0)&&(x.l={Target:R}),a.dense?(e[m+n]||(e[m+n]=[]),e[m+n][v+s]=x):e[Ia({c:v+s,r:m+n})]=x,l.e.c<v+s&&(l.e.c=v+s),v+=k}}++m}return h.length&&(e["!merges"]=(e["!merges"]||[]).concat(h)),l.e.r=Math.max(l.e.r,m-1+n),e["!ref"]=Da(l),m>=c&&(e["!fullref"]=Da((l.e.r=o.length-p+m-1+n,l))),e}function Hv(e,r){var t=r||{},a=t.dense?[]:{};return Wv(a,e,r)}function Vv(e,r){return Ma(Hv(e,r),r)}function zv(e){var r="",t=Gv(e);return t&&(r=t(e).getPropertyValue("display")),r||(r=e.style&&e.style.display),"none"===r}function Gv(e){return e.ownerDocument.defaultView&&"function"===typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"===typeof getComputedStyle?getComputedStyle:null}function jv(e){var r=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,r){return Array(parseInt(r,10)+1).join(" ")})).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n"),t=Kr(r.replace(/<[^>]*>/g,""));return[t]}var Xv={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']};function $v(e,r){var t=r||{};null!=w&&null==t.dense&&(t.dense=w);var a,n,s,i,o,c,l,f=yt(e),h=[],u={name:""},d="",p=0,m={},b=[],v=t.dense?[]:{},g={value:""},k="",T=0,E=[],S=-1,y=-1,_={s:{r:1e6,c:1e7},e:{r:0,c:0}},A=0,x={},C=[],R={},O=0,I=0,N=[],D=1,F=1,P=[],L={Names:[]},M={},U=["",""],B=[],W={},H="",V=0,z=!1,G=!1,j=0;_t.lastIndex=0,f=f.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");while(o=_t.exec(f))switch(o[3]=o[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===o[1]?(_.e.c>=_.s.c&&_.e.r>=_.s.r?v["!ref"]=Da(_):v["!ref"]="A1:A1",t.sheetRows>0&&t.sheetRows<=_.e.r&&(v["!fullref"]=v["!ref"],_.e.r=t.sheetRows-1,v["!ref"]=Da(_)),C.length&&(v["!merges"]=C),N.length&&(v["!rows"]=N),s.name=s["名称"]||s.name,"undefined"!==typeof JSON&&JSON.stringify(s),b.push(s.name),m[s.name]=v,G=!1):"/"!==o[0].charAt(o[0].length-2)&&(s=jr(o[0],!1),S=y=-1,_.s.r=_.s.c=1e7,_.e.r=_.e.c=0,v=t.dense?[]:{},C=[],N=[],G=!0);break;case"table-row-group":"/"===o[1]?--A:++A;break;case"table-row":case"行":if("/"===o[1]){S+=D,D=1;break}if(i=jr(o[0],!1),i["行号"]?S=i["行号"]-1:-1==S&&(S=0),D=+i["number-rows-repeated"]||1,D<10)for(j=0;j<D;++j)A>0&&(N[S+j]={level:A});y=-1;break;case"covered-table-cell":"/"!==o[1]&&++y,t.sheetStubs&&(t.dense?(v[S]||(v[S]=[]),v[S][y]={t:"z"}):v[Ia({r:S,c:y})]={t:"z"}),k="",E=[];break;case"table-cell":case"数据":if("/"===o[0].charAt(o[0].length-2))++y,g=jr(o[0],!1),F=parseInt(g["number-columns-repeated"]||"1",10),c={t:"z",v:null},g.formula&&0!=t.cellFormula&&(c.f=Sd(Kr(g.formula))),"string"==(g["数据类型"]||g["value-type"])&&(c.t="s",c.v=Kr(g["string-value"]||""),t.dense?(v[S]||(v[S]=[]),v[S][y]=c):v[Ia({r:S,c:y})]=c),y+=F-1;else if("/"!==o[1]){++y,k="",T=0,E=[],F=1;var X=D?S+D-1:S;if(y>_.e.c&&(_.e.c=y),y<_.s.c&&(_.s.c=y),S<_.s.r&&(_.s.r=S),X>_.e.r&&(_.e.r=X),g=jr(o[0],!1),B=[],W={},c={t:g["数据类型"]||g["value-type"],v:null},t.cellFormula)if(g.formula&&(g.formula=Kr(g.formula)),g["number-matrix-columns-spanned"]&&g["number-matrix-rows-spanned"]&&(O=parseInt(g["number-matrix-rows-spanned"],10)||0,I=parseInt(g["number-matrix-columns-spanned"],10)||0,R={s:{r:S,c:y},e:{r:S+O-1,c:y+I-1}},c.F=Da(R),P.push([R,c.F])),g.formula)c.f=Sd(g.formula);else for(j=0;j<P.length;++j)S>=P[j][0].s.r&&S<=P[j][0].e.r&&y>=P[j][0].s.c&&y<=P[j][0].e.c&&(c.F=P[j][1]);switch((g["number-columns-spanned"]||g["number-rows-spanned"])&&(O=parseInt(g["number-rows-spanned"],10)||0,I=parseInt(g["number-columns-spanned"],10)||0,R={s:{r:S,c:y},e:{r:S+O-1,c:y+I-1}},C.push(R)),g["number-columns-repeated"]&&(F=parseInt(g["number-columns-repeated"],10)),c.t){case"boolean":c.t="b",c.v=st(g["boolean-value"]);break;case"float":c.t="n",c.v=parseFloat(g.value);break;case"percentage":c.t="n",c.v=parseFloat(g.value);break;case"currency":c.t="n",c.v=parseFloat(g.value);break;case"date":c.t="d",c.v=vr(g["date-value"]),t.cellDates||(c.t="n",c.v=cr(c.v)),c.z="m/d/yy";break;case"time":c.t="n",c.v=dr(g["time-value"])/86400,t.cellDates&&(c.t="d",c.v=ur(c.v)),c.z="HH:MM:SS";break;case"number":c.t="n",c.v=parseFloat(g["数据数值"]);break;default:if("string"!==c.t&&"text"!==c.t&&c.t)throw new Error("Unsupported value type "+c.t);c.t="s",null!=g["string-value"]&&(k=Kr(g["string-value"]),E=[])}}else{if(z=!1,"s"===c.t&&(c.v=k||"",E.length&&(c.R=E),z=0==T),M.Target&&(c.l=M),B.length>0&&(c.c=B,B=[]),k&&!1!==t.cellText&&(c.w=k),z&&(c.t="z",delete c.v),(!z||t.sheetStubs)&&!(t.sheetRows&&t.sheetRows<=S))for(var $=0;$<D;++$){if(F=parseInt(g["number-columns-repeated"]||"1",10),t.dense){v[S+$]||(v[S+$]=[]),v[S+$][y]=0==$?c:wr(c);while(--F>0)v[S+$][y+F]=wr(c)}else{v[Ia({r:S+$,c:y})]=c;while(--F>0)v[Ia({r:S+$,c:y+F})]=wr(c)}_.e.c<=y&&(_.e.c=y)}F=parseInt(g["number-columns-repeated"]||"1",10),y+=F-1,F=0,c={},k="",E=[]}M={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!0]);break;case"annotation":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a;W.t=k,E.length&&(W.R=E),W.a=H,B.push(W)}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);H="",V=0,k="",T=0,E=[];break;case"creator":"/"===o[1]?H=f.slice(V,o.index):V=o.index+o[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===o[1]){if((a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&h.push([o[3],!1]);k="",T=0,E=[];break;case"scientific-number":break;case"currency-symbol":break;case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===o[1]){if(x[u.name]=d,(a=h.pop())[0]!==o[3])throw"Bad state: "+a}else"/"!==o[0].charAt(o[0].length-2)&&(d="",u=jr(o[0],!1),h.push([o[3],!0]));break;case"script":break;case"libraries":break;case"automatic-styles":break;case"default-style":case"page-layout":break;case"style":break;case"map":break;case"font-face":break;case"paragraph-properties":break;case"table-properties":break;case"table-column-properties":break;case"table-row-properties":break;case"table-cell-properties":break;case"number":switch(h[h.length-1][0]){case"time-style":case"date-style":n=jr(o[0],!1),d+=Xv[o[3]]["long"===n.style?1:0];break}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(h[h.length-1][0]){case"time-style":case"date-style":n=jr(o[0],!1),d+=Xv[o[3]]["long"===n.style?1:0];break}break;case"boolean-style":break;case"boolean":break;case"text-style":break;case"text":if("/>"===o[0].slice(-2))break;if("/"===o[1])switch(h[h.length-1][0]){case"number-style":case"date-style":case"time-style":d+=f.slice(p,o.index);break}else p=o.index+o[0].length;break;case"named-range":n=jr(o[0],!1),U=_d(n["cell-range-address"]);var Y={Name:n.name,Ref:U[0]+"!"+U[1]};G&&(Y.Sheet=b.length),L.Names.push(Y);break;case"text-content":break;case"text-properties":break;case"embedded-text":break;case"body":case"电子表格":break;case"forms":break;case"table-column":break;case"table-header-rows":break;case"table-rows":break;case"table-column-group":break;case"table-header-columns":break;case"table-columns":break;case"null-date":break;case"graphic-properties":break;case"calculation-settings":break;case"named-expressions":break;case"label-range":break;case"label-ranges":break;case"named-expression":break;case"sort":break;case"sort-by":break;case"sort-groups":break;case"tab":break;case"line-break":break;case"span":break;case"p":case"文本串":if(["master-styles"].indexOf(h[h.length-1][0])>-1)break;if("/"!==o[1]||g&&g["string-value"])l=jr(o[0],!1),T=o.index+o[0].length;else{var K=jv(f.slice(T,o.index),l);k=(k.length>0?k+"\n":"")+K[0]}break;case"s":break;case"database-range":if("/"===o[1])break;try{U=_d(jr(o[0])["target-range-address"]),m[U[0]]["!autofilter"]={ref:U[1]}}catch(q){}break;case"date":break;case"object":break;case"title":case"标题":break;case"desc":break;case"binary-data":break;case"table-source":break;case"scenario":break;case"iteration":break;case"content-validations":break;case"content-validation":break;case"help-message":break;case"error-message":break;case"database-ranges":break;case"filter":break;case"filter-and":break;case"filter-or":break;case"filter-condition":break;case"list-level-style-bullet":break;case"list-level-style-number":break;case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":break;case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":break;case"config-item":break;case"page-number":break;case"page-count":break;case"time":break;case"cell-range-source":break;case"detective":break;case"operation":break;case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":break;case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":break;case"properties":break;case"property":break;case"a":if("/"!==o[1]){if(M=jr(o[0],!1),!M.href)break;M.Target=Kr(M.href),delete M.href,"#"==M.Target.charAt(0)&&M.Target.indexOf(".")>-1?(U=_d(M.Target.slice(1)),M.Target="#"+U[0]+"!"+U[1]):M.Target.match(/^\.\.[\\\/]/)&&(M.Target=M.Target.slice(3))}break;case"table-protection":break;case"data-pilot-grand-total":break;case"office-document-common-attrs":break;default:switch(o[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(t.WTF)throw new Error(o)}}var J={Sheets:m,SheetNames:b,Workbook:L};return t.bookSheets&&delete J.Sheets,J}function Yv(e,r){r=r||{},Cr(e,"META-INF/manifest.xml")&&ts(Or(e,"META-INF/manifest.xml"),r);var t=Ir(e,"content.xml");if(!t)throw new Error("Missing content.xml in ODS / UOF file");var a=$v(ft(t),r);return Cr(e,"meta.xml")&&(a.Props=fs(Or(e,"meta.xml"))),a}function Kv(e,r){return $v(e,r)}var Jv=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),r="<office:document-styles "+kt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return Ur+r}}(),qv=function(){var e=function(e){return Zr(e).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},r="          <table:table-cell />\n",t="          <table:covered-table-cell/>\n",a=function(a,n,s){var i=[];i.push('      <table:table table:name="'+Zr(n.SheetNames[s])+'" table:style-name="ta1">\n');var o=0,c=0,l=Na(a["!ref"]||"A1"),f=a["!merges"]||[],h=0,u=Array.isArray(a);if(a["!cols"])for(c=0;c<=l.e.c;++c)i.push("        <table:table-column"+(a["!cols"][c]?' table:style-name="co'+a["!cols"][c].ods+'"':"")+"></table:table-column>\n");var d="",p=a["!rows"]||[];for(o=0;o<l.s.r;++o)d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",i.push("        <table:table-row"+d+"></table:table-row>\n");for(;o<=l.e.r;++o){for(d=p[o]?' table:style-name="ro'+p[o].ods+'"':"",i.push("        <table:table-row"+d+">\n"),c=0;c<l.s.c;++c)i.push(r);for(;c<=l.e.c;++c){var m=!1,b={},v="";for(h=0;h!=f.length;++h)if(!(f[h].s.c>c)&&!(f[h].s.r>o)&&!(f[h].e.c<c)&&!(f[h].e.r<o)){f[h].s.c==c&&f[h].s.r==o||(m=!0),b["table:number-columns-spanned"]=f[h].e.c-f[h].s.c+1,b["table:number-rows-spanned"]=f[h].e.r-f[h].s.r+1;break}if(m)i.push(t);else{var g=Ia({r:o,c:c}),w=u?(a[o]||[])[c]:a[g];if(w&&w.f&&(b["table:formula"]=Zr(yd(w.f)),w.F&&w.F.slice(0,g.length)==g)){var k=Na(w.F);b["table:number-matrix-columns-spanned"]=k.e.c-k.s.c+1,b["table:number-matrix-rows-spanned"]=k.e.r-k.s.r+1}if(w){switch(w.t){case"b":v=w.v?"TRUE":"FALSE",b["office:value-type"]="boolean",b["office:boolean-value"]=w.v?"true":"false";break;case"n":v=w.w||String(w.v||0),b["office:value-type"]="float",b["office:value"]=w.v||0;break;case"s":case"str":v=null==w.v?"":w.v,b["office:value-type"]="string";break;case"d":v=w.w||vr(w.v).toISOString(),b["office:value-type"]="date",b["office:date-value"]=vr(w.v).toISOString(),b["table:style-name"]="ce1";break;default:i.push(r);continue}var T=e(v);if(w.l&&w.l.Target){var E=w.l.Target;E="#"==E.charAt(0)?"#"+Ad(E.slice(1)):E,"#"==E.charAt(0)||E.match(/^\w+:/)||(E="../"+E),T=Tt("text:a",T,{"xlink:href":E.replace(/&/g,"&amp;")})}i.push("          "+Tt("table:table-cell",Tt("text:p",T,{}),b)+"\n")}else i.push(r)}}i.push("        </table:table-row>\n")}return i.push("      </table:table>\n"),i.join("")},n=function(e,r){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var t=0;r.SheetNames.map((function(e){return r.Sheets[e]})).forEach((function(r){if(r&&r["!cols"])for(var a=0;a<r["!cols"].length;++a)if(r["!cols"][a]){var n=r["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;Rl(n),n.ods=t;var s=r["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+t+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++t}}));var a=0;r.SheetNames.map((function(e){return r.Sheets[e]})).forEach((function(r){if(r&&r["!rows"])for(var t=0;t<r["!rows"].length;++t)if(r["!rows"][t]){r["!rows"][t].ods=a;var n=r["!rows"][t].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")};return function(e,r){var t=[Ur],s=kt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=kt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(t.push("<office:document"+s+i+">\n"),t.push(os().replace(/office:document-meta/g,"office:meta"))):t.push("<office:document-content"+s+">\n"),n(t,e),t.push("  <office:body>\n"),t.push("    <office:spreadsheet>\n");for(var o=0;o!=e.SheetNames.length;++o)t.push(a(e.Sheets[e.SheetNames[o]],e,o,r));return t.push("    </office:spreadsheet>\n"),t.push("  </office:body>\n"),"fods"==r.bookType?t.push("</office:document>"):t.push("</office:document-content>"),t.join("")}}();function Zv(e,r){if("fods"==r.bookType)return qv(e,r);var t=Pr(),a="",n=[],s=[];return a="mimetype",Fr(t,a,"application/vnd.oasis.opendocument.spreadsheet"),a="content.xml",Fr(t,a,qv(e,r)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),a="styles.xml",Fr(t,a,Jv(e,r)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),a="meta.xml",Fr(t,a,Ur+os()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),a="manifest.rdf",Fr(t,a,is(s)),n.push([a,"application/rdf+xml"]),a="META-INF/manifest.xml",Fr(t,a,as(n)),t}
/*! sheetjs (C) 2013-present SheetJS -- http://sheetjs.com */function Qv(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function eg(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):ft(O(e))}function rg(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):C(ht(e))}function tg(e,r){e:for(var t=0;t<=e.length-r.length;++t){for(var a=0;a<r.length;++a)if(e[t+a]!=r[a])continue e;return!0}return!1}function ag(e){var r=e.reduce((function(e,r){return e+r.length}),0),t=new Uint8Array(r),a=0;return e.forEach((function(e){t.set(e,a),a+=e.length})),t}function ng(e){return e-=e>>1&1431655765,e=(858993459&e)+(e>>2&858993459),16843009*(e+(e>>4)&252645135)>>>24}function sg(e,r){for(var t=(127&e[r+15])<<7|e[r+14]>>1,a=1&e[r+14],n=r+13;n>=r;--n)a=256*a+e[n];return(128&e[r+15]?-a:a)*Math.pow(10,t-6176)}function ig(e,r,t){var a=Math.floor(0==t?0:Math.LOG10E*Math.log(Math.abs(t)))+6176-20,n=t/Math.pow(10,a-6176);e[r+15]|=a>>7,e[r+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[r+s]=255&n;e[r+15]|=t>=0?0:128}function og(e,r){var t=r?r[0]:0,a=127&e[t];e:if(e[t++]>=128){if(a|=(127&e[t])<<7,e[t++]<128)break e;if(a|=(127&e[t])<<14,e[t++]<128)break e;if(a|=(127&e[t])<<21,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,28),++t,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,35),++t,e[t++]<128)break e;if(a+=(127&e[t])*Math.pow(2,42),++t,e[t++]<128)break e}return r&&(r[0]=t),a}function cg(e){var r=new Uint8Array(7);r[0]=127&e;var t=1;e:if(e>127){if(r[t-1]|=128,r[t]=e>>7&127,++t,e<=16383)break e;if(r[t-1]|=128,r[t]=e>>14&127,++t,e<=2097151)break e;if(r[t-1]|=128,r[t]=e>>21&127,++t,e<=268435455)break e;if(r[t-1]|=128,r[t]=e/256>>>21&127,++t,e<=34359738367)break e;if(r[t-1]|=128,r[t]=e/65536>>>21&127,++t,e<=4398046511103)break e;r[t-1]|=128,r[t]=e/16777216>>>21&127,++t}return r.slice(0,t)}function lg(e){var r=0,t=127&e[r];e:if(e[r++]>=128){if(t|=(127&e[r])<<7,e[r++]<128)break e;if(t|=(127&e[r])<<14,e[r++]<128)break e;if(t|=(127&e[r])<<21,e[r++]<128)break e;t|=(127&e[r])<<28}return t}function fg(e){var r=[],t=[0];while(t[0]<e.length){var a=t[0],n=og(e,t),s=7&n;n=Math.floor(n/8);var i,o=0;if(0==n)break;switch(s){case 0:var c=t[0];while(e[t[0]++]>=128);i=e.slice(c,t[0]);break;case 5:o=4,i=e.slice(t[0],t[0]+o),t[0]+=o;break;case 1:o=8,i=e.slice(t[0],t[0]+o),t[0]+=o;break;case 2:o=og(e,t),i=e.slice(t[0],t[0]+o),t[0]+=o;break;case 3:case 4:default:throw new Error("PB Type ".concat(s," for Field ").concat(n," at offset ").concat(a))}var l={data:i,type:s};null==r[n]?r[n]=[l]:r[n].push(l)}return r}function hg(e){var r=[];return e.forEach((function(e,t){e.forEach((function(e){e.data&&(r.push(cg(8*t+e.type)),2==e.type&&r.push(cg(e.data.length)),r.push(e.data))}))})),ag(r)}function ug(e,r){return(null==e?void 0:e.map((function(e){return r(e.data)})))||[]}function dg(e){var r,t=[],a=[0];while(a[0]<e.length){var n=og(e,a),s=fg(e.slice(a[0],a[0]+n));a[0]+=n;var i={id:lg(s[1][0].data),messages:[]};s[2].forEach((function(r){var t=fg(r.data),n=lg(t[3][0].data);i.messages.push({meta:t,data:e.slice(a[0],a[0]+n)}),a[0]+=n})),(null==(r=s[3])?void 0:r[0])&&(i.merge=lg(s[3][0].data)>>>0>0),t.push(i)}return t}function pg(e){var r=[];return e.forEach((function(e){var t=[];t[1]=[{data:cg(e.id),type:0}],t[2]=[],null!=e.merge&&(t[3]=[{data:cg(+!!e.merge),type:0}]);var a=[];e.messages.forEach((function(e){a.push(e.data),e.meta[3]=[{type:0,data:cg(e.data.length)}],t[2].push({data:hg(e.meta),type:2})}));var n=hg(t);r.push(cg(n.length)),r.push(n),a.forEach((function(e){return r.push(e)}))})),ag(r)}function mg(e,r){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));var t=[0],a=og(r,t),n=[];while(t[0]<r.length){var s=3&r[t[0]];if(0!=s){var i=0,o=0;if(1==s?(o=4+(r[t[0]]>>2&7),i=(224&r[t[0]++])<<3,i|=r[t[0]++]):(o=1+(r[t[0]++]>>2),2==s?(i=r[t[0]]|r[t[0]+1]<<8,t[0]+=2):(i=(r[t[0]]|r[t[0]+1]<<8|r[t[0]+2]<<16|r[t[0]+3]<<24)>>>0,t[0]+=4)),n=[ag(n)],0==i)throw new Error("Invalid offset 0");if(i>n[0].length)throw new Error("Invalid offset beyond length");if(o>=i){n.push(n[0].slice(-i)),o-=i;while(o>=n[n.length-1].length)n.push(n[n.length-1]),o-=n[n.length-1].length}n.push(n[0].slice(-i,-i+o))}else{var c=r[t[0]++]>>2;if(c<60)++c;else{var l=c-59;c=r[t[0]],l>1&&(c|=r[t[0]+1]<<8),l>2&&(c|=r[t[0]+2]<<16),l>3&&(c|=r[t[0]+3]<<24),c>>>=0,c++,t[0]+=l}n.push(r.slice(t[0],t[0]+c)),t[0]+=c}}var f=ag(n);if(f.length!=a)throw new Error("Unexpected length: ".concat(f.length," != ").concat(a));return f}function bg(e){var r=[],t=0;while(t<e.length){var a=e[t++],n=e[t]|e[t+1]<<8|e[t+2]<<16;t+=3,r.push(mg(a,e.slice(t,t+n))),t+=n}if(t!==e.length)throw new Error("data is not a valid framed stream!");return ag(r)}function vg(e){var r=[],t=0;while(t<e.length){var a=Math.min(e.length-t,268435455),n=new Uint8Array(4);r.push(n);var s=cg(a),i=s.length;r.push(s),a<=60?(i++,r.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,r.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,r.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(i+=4,r.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(i+=5,r.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),r.push(e.slice(t,t+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,t+=a}return ag(r)}function gg(e,r,t,a){var n,s=Qv(e),i=s.getUint32(4,!0),o=(a>1?12:8)+4*ng(i&(a>1?3470:398)),c=-1,l=-1,f=NaN,h=new Date(2001,0,1);switch(512&i&&(c=s.getUint32(o,!0),o+=4),o+=4*ng(i&(a>1?12288:4096)),16&i&&(l=s.getUint32(o,!0),o+=4),32&i&&(f=s.getFloat64(o,!0),o+=8),64&i&&(h.setTime(h.getTime()+1e3*s.getFloat64(o,!0)),o+=8),e[2]){case 0:break;case 2:n={t:"n",v:f};break;case 3:n={t:"s",v:r[l]};break;case 5:n={t:"d",v:h};break;case 6:n={t:"b",v:f>0};break;case 7:n={t:"n",v:f/86400};break;case 8:n={t:"e",v:0};break;case 9:if(c>-1)n={t:"s",v:t[c]};else if(l>-1)n={t:"s",v:r[l]};else{if(isNaN(f))throw new Error("Unsupported cell type ".concat(e.slice(0,4)));n={t:"n",v:f}}break;default:throw new Error("Unsupported cell type ".concat(e.slice(0,4)))}return n}function wg(e,r,t){var a,n=Qv(e),s=n.getUint32(8,!0),i=12,o=-1,c=-1,l=NaN,f=NaN,h=new Date(2001,0,1);switch(1&s&&(l=sg(e,i),i+=16),2&s&&(f=n.getFloat64(i,!0),i+=8),4&s&&(h.setTime(h.getTime()+1e3*n.getFloat64(i,!0)),i+=8),8&s&&(c=n.getUint32(i,!0),i+=4),16&s&&(o=n.getUint32(i,!0),i+=4),e[1]){case 0:break;case 2:a={t:"n",v:l};break;case 3:a={t:"s",v:r[c]};break;case 5:a={t:"d",v:h};break;case 6:a={t:"b",v:f>0};break;case 7:a={t:"n",v:f/86400};break;case 8:a={t:"e",v:0};break;case 9:if(!(o>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)));a={t:"s",v:t[o]};break;case 10:a={t:"n",v:l};break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e.slice(0,4)))}return a}function kg(e,r){var t=new Uint8Array(32),a=Qv(t),n=12,s=0;switch(t[0]=5,e.t){case"n":t[1]=2,ig(t,n,e.v),s|=1,n+=16;break;case"b":t[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=2,n+=8;break;case"s":if(-1==r.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));t[1]=3,a.setUint32(n,r.indexOf(e.v),!0),s|=8,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),t.slice(0,n)}function Tg(e,r){var t=new Uint8Array(32),a=Qv(t),n=12,s=0;switch(t[0]=3,e.t){case"n":t[2]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":t[2]=6,a.setFloat64(n,e.v?1:0,!0),s|=32,n+=8;break;case"s":if(-1==r.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));t[2]=3,a.setUint32(n,r.indexOf(e.v),!0),s|=16,n+=4;break;default:throw"unsupported cell type "+e.t}return a.setUint32(4,s,!0),t.slice(0,n)}function Eg(e,r,t){switch(e[0]){case 0:case 1:case 2:case 3:return gg(e,r,t,e[0]);case 5:return wg(e,r,t);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function Sg(e){var r=fg(e);return og(r[1][0].data)}function yg(e,r){var t=fg(r.data),a=lg(t[1][0].data),n=t[3],s=[];return(n||[]).forEach((function(r){var t=fg(r.data),n=lg(t[1][0].data)>>>0;switch(a){case 1:s[n]=eg(t[3][0].data);break;case 8:var i=e[Sg(t[9][0].data)][0],o=fg(i.data),c=e[Sg(o[1][0].data)][0],l=lg(c.meta[1][0].data);if(2001!=l)throw new Error("2000 unexpected reference to ".concat(l));var f=fg(c.data);s[n]=f[3].map((function(e){return eg(e.data)})).join("");break}})),s}function _g(e,r){var t,a,n,s,i,o,c,l,f,h,u,d,p,m,b,v,g=fg(e),w=lg(g[1][0].data)>>>0,k=lg(g[2][0].data)>>>0,T=(null==(a=null==(t=g[8])?void 0:t[0])?void 0:a.data)&&lg(g[8][0].data)>0||!1;if((null==(s=null==(n=g[7])?void 0:n[0])?void 0:s.data)&&0!=r)b=null==(o=null==(i=g[7])?void 0:i[0])?void 0:o.data,v=null==(l=null==(c=g[6])?void 0:c[0])?void 0:l.data;else{if(!(null==(h=null==(f=g[4])?void 0:f[0])?void 0:h.data)||1==r)throw"NUMBERS Tile missing ".concat(r," cell storage");b=null==(d=null==(u=g[4])?void 0:u[0])?void 0:d.data,v=null==(m=null==(p=g[3])?void 0:p[0])?void 0:m.data}for(var E=T?4:1,S=Qv(b),y=[],_=0;_<b.length/2;++_){var A=S.getUint16(2*_,!0);A<65535&&y.push([_,A])}if(y.length!=k)throw"Expected ".concat(k," cells, found ").concat(y.length);var x=[];for(_=0;_<y.length-1;++_)x[y[_][0]]=v.subarray(y[_][1]*E,y[_+1][1]*E);return y.length>=1&&(x[y[y.length-1][0]]=v.subarray(y[y.length-1][1]*E)),{R:w,cells:x}}function Ag(e,r){var t,a=fg(r.data),n=(null==(t=null==a?void 0:a[7])?void 0:t[0])?lg(a[7][0].data)>>>0>0?1:0:-1,s=ug(a[5],(function(e){return _g(e,n)}));return{nrows:lg(a[4][0].data)>>>0,data:s.reduce((function(e,r){return e[r.R]||(e[r.R]=[]),r.cells.forEach((function(t,a){if(e[r.R][a])throw new Error("Duplicate cell r=".concat(r.R," c=").concat(a));e[r.R][a]=t})),e}),[])}}function xg(e,r,t){var a,n=fg(r.data),s={s:{r:0,c:0},e:{r:0,c:0}};if(s.e.r=(lg(n[6][0].data)>>>0)-1,s.e.r<0)throw new Error("Invalid row varint ".concat(n[6][0].data));if(s.e.c=(lg(n[7][0].data)>>>0)-1,s.e.c<0)throw new Error("Invalid col varint ".concat(n[7][0].data));t["!ref"]=Da(s);var i=fg(n[4][0].data),o=yg(e,e[Sg(i[4][0].data)][0]),c=(null==(a=i[17])?void 0:a[0])?yg(e,e[Sg(i[17][0].data)][0]):[],l=fg(i[3][0].data),f=0;l[1].forEach((function(r){var a=fg(r.data),n=e[Sg(a[2][0].data)][0],s=lg(n.meta[1][0].data);if(6002!=s)throw new Error("6001 unexpected reference to ".concat(s));var i=Ag(e,n);i.data.forEach((function(e,r){e.forEach((function(e,a){var n=Ia({r:f+r,c:a}),s=Eg(e,o,c);s&&(t[n]=s)}))})),f+=i.nrows}))}function Cg(e,r){var t=fg(r.data),a={"!ref":"A1"},n=e[Sg(t[2][0].data)],s=lg(n[0].meta[1][0].data);if(6001!=s)throw new Error("6000 unexpected reference to ".concat(s));return xg(e,n[0],a),a}function Rg(e,r){var t,a=fg(r.data),n={name:(null==(t=a[1])?void 0:t[0])?eg(a[1][0].data):"",sheets:[]},s=ug(a[2],Sg);return s.forEach((function(r){e[r].forEach((function(r){var t=lg(r.meta[1][0].data);6e3==t&&n.sheets.push(Cg(e,r))}))})),n}function Og(e,r){var t=kw(),a=fg(r.data),n=ug(a[1],Sg);if(n.forEach((function(r){e[r].forEach((function(r){var a=lg(r.meta[1][0].data);if(2==a){var n=Rg(e,r);n.sheets.forEach((function(e,r){Tw(t,e,0==r?n.name:n.name+"_"+r,!0)}))}}))})),0==t.SheetNames.length)throw new Error("Empty NUMBERS file");return t}function Ig(e){var r,t,a,n,s={},i=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)){var r,t;try{r=bg(e.content)}catch(a){return console.log("?? "+e.content.length+" "+(a.message||a))}try{t=dg(r)}catch(a){return console.log("## "+(a.message||a))}t.forEach((function(e){s[e.id]=e.messages,i.push(e.id)}))}})),!i.length)throw new Error("File has no messages");var o=(null==(n=null==(a=null==(t=null==(r=null==s?void 0:s[1])?void 0:r[0])?void 0:t.meta)?void 0:a[1])?void 0:n[0].data)&&1==lg(s[1][0].meta[1][0].data)&&s[1][0];if(o||i.forEach((function(e){s[e].forEach((function(e){var r=lg(e.meta[1][0].data)>>>0;if(1==r){if(o)throw new Error("Document has multiple roots");o=e}}))})),!o)throw new Error("Cannot find Document root");return Og(s,o)}function Ng(e,r,t){var a,n,s,i;if(!(null==(a=e[6])?void 0:a[0])||!(null==(n=e[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";var o=(null==(i=null==(s=e[8])?void 0:s[0])?void 0:i.data)&&lg(e[8][0].data)>0||!1;if(o)throw"Math only works with normal offsets";for(var c=0,l=Qv(e[7][0].data),f=0,h=[],u=Qv(e[4][0].data),d=0,p=[],m=0;m<r.length;++m)if(null!=r[m]){var b,v;switch(l.setUint16(2*m,f,!0),u.setUint16(2*m,d,!0),typeof r[m]){case"string":b=kg({t:"s",v:r[m]},t),v=Tg({t:"s",v:r[m]},t);break;case"number":b=kg({t:"n",v:r[m]},t),v=Tg({t:"n",v:r[m]},t);break;case"boolean":b=kg({t:"b",v:r[m]},t),v=Tg({t:"b",v:r[m]},t);break;default:throw new Error("Unsupported value "+r[m])}h.push(b),f+=b.length,p.push(v),d+=v.length,++c}else l.setUint16(2*m,65535,!0),u.setUint16(2*m,65535);for(e[2][0].data=cg(c);m<e[7][0].data.length/2;++m)l.setUint16(2*m,65535,!0),u.setUint16(2*m,65535,!0);return e[6][0].data=ag(h),e[3][0].data=ag(p),c}function Dg(e,r){if(!r||!r.numbers)throw new Error("Must pass a `numbers` option -- check the README");var t=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var a=Na(t["!ref"]);a.s.r=a.s.c=0;var n=!1;a.e.c>9&&(n=!0,a.e.c=9),a.e.r>49&&(n=!0,a.e.r=49),n&&console.error("The Numbers writer is currently limited to ".concat(Da(a)));var s=fw(t,{range:a,header:1}),i=["~Sh33tJ5~"];s.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&i.push(e)}))}));var o={},c=[],l=qe.read(r.numbers,{type:"base64"});l.FileIndex.map((function(e,r){return[e,l.FullPaths[r]]})).forEach((function(e){var r=e[0],t=e[1];if(2==r.type&&r.name.match(/\.iwa/)){var a=r.content,n=bg(a),s=dg(n);s.forEach((function(e){c.push(e.id),o[e.id]={deps:[],location:t,type:lg(e.messages[0].meta[1][0].data)}}))}})),c.sort((function(e,r){return e-r}));var f=c.filter((function(e){return e>1})).map((function(e){return[e,cg(e)]}));l.FileIndex.map((function(e,r){return[e,l.FullPaths[r]]})).forEach((function(e){var r=e[0];e[1];if(r.name.match(/\.iwa/)){var t=dg(bg(r.content));t.forEach((function(e){e.messages.forEach((function(r){f.forEach((function(r){e.messages.some((function(e){return 11006!=lg(e.meta[1][0].data)&&tg(e.data,r[1])}))&&o[r[0]].deps.push(e.id)}))}))}))}}));for(var h,u=qe.find(l,o[1].location),d=dg(bg(u.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(h=m)}var b=Sg(fg(h.messages[0].data)[1][0].data);for(u=qe.find(l,o[b].location),d=dg(bg(u.content)),p=0;p<d.length;++p)m=d[p],m.id==b&&(h=m);for(b=Sg(fg(h.messages[0].data)[2][0].data),u=qe.find(l,o[b].location),d=dg(bg(u.content)),p=0;p<d.length;++p)m=d[p],m.id==b&&(h=m);for(b=Sg(fg(h.messages[0].data)[2][0].data),u=qe.find(l,o[b].location),d=dg(bg(u.content)),p=0;p<d.length;++p)m=d[p],m.id==b&&(h=m);var v=fg(h.messages[0].data);v[6][0].data=cg(a.e.r+1),v[7][0].data=cg(a.e.c+1);for(var g=Sg(v[46][0].data),w=qe.find(l,o[g].location),k=dg(bg(w.content)),T=0;T<k.length;++T)if(k[T].id==g)break;if(k[T].id!=g)throw"Bad ColumnRowUIDMapArchive";var E=fg(k[T].messages[0].data);E[1]=[],E[2]=[],E[3]=[];for(var S=0;S<=a.e.c;++S){var y=[];y[1]=y[2]=[{type:0,data:cg(S+420690)}],E[1].push({type:2,data:hg(y)}),E[2].push({type:0,data:cg(S)}),E[3].push({type:0,data:cg(S)})}E[4]=[],E[5]=[],E[6]=[];for(var _=0;_<=a.e.r;++_)y=[],y[1]=y[2]=[{type:0,data:cg(_+726270)}],E[4].push({type:2,data:hg(y)}),E[5].push({type:0,data:cg(_)}),E[6].push({type:0,data:cg(_)});k[T].messages[0].data=hg(E),w.content=vg(pg(k)),w.size=w.content.length,delete v[46];var A=fg(v[4][0].data);A[7][0].data=cg(a.e.r+1);var x=fg(A[1][0].data),C=Sg(x[2][0].data);if(w=qe.find(l,o[C].location),k=dg(bg(w.content)),k[0].id!=C)throw"Bad HeaderStorageBucket";var R=fg(k[0].messages[0].data);for(_=0;_<s.length;++_){var O=fg(R[2][0].data);O[1][0].data=cg(_),O[4][0].data=cg(s[_].length),R[2][_]={type:R[2][0].type,data:hg(O)}}k[0].messages[0].data=hg(R),w.content=vg(pg(k)),w.size=w.content.length;var I=Sg(A[2][0].data);if(w=qe.find(l,o[I].location),k=dg(bg(w.content)),k[0].id!=I)throw"Bad HeaderStorageBucket";for(R=fg(k[0].messages[0].data),S=0;S<=a.e.c;++S)O=fg(R[2][0].data),O[1][0].data=cg(S),O[4][0].data=cg(a.e.r+1),R[2][S]={type:R[2][0].type,data:hg(O)};k[0].messages[0].data=hg(R),w.content=vg(pg(k)),w.size=w.content.length;var N=Sg(A[4][0].data);(function(){for(var e,r=qe.find(l,o[N].location),t=dg(bg(r.content)),a=0;a<t.length;++a){var n=t[a];n.id==N&&(e=n)}var s=fg(e.messages[0].data);s[3]=[];var c=[];i.forEach((function(e,r){c[1]=[{type:0,data:cg(r)}],c[2]=[{type:0,data:cg(1)}],c[3]=[{type:2,data:rg(e)}],s[3].push({type:2,data:hg(c)})})),e.messages[0].data=hg(s);var f=pg(t),h=vg(f);r.content=h,r.size=r.content.length})();var D=fg(A[3][0].data),F=D[1][0];delete D[2];var P=fg(F.data),L=Sg(P[2][0].data);(function(){for(var e,r=qe.find(l,o[L].location),t=dg(bg(r.content)),n=0;n<t.length;++n){var c=t[n];c.id==L&&(e=c)}var f=fg(e.messages[0].data);delete f[6],delete D[7];var h=new Uint8Array(f[5][0].data);f[5]=[];for(var u=0,d=0;d<=a.e.r;++d){var p=fg(h);u+=Ng(p,s[d],i),p[1][0].data=cg(d),f[5].push({data:hg(p),type:2})}f[1]=[{type:0,data:cg(a.e.c+1)}],f[2]=[{type:0,data:cg(a.e.r+1)}],f[3]=[{type:0,data:cg(u)}],f[4]=[{type:0,data:cg(a.e.r+1)}],e.messages[0].data=hg(f);var m=pg(t),b=vg(m);r.content=b,r.size=r.content.length})(),F.data=hg(P),A[3][0].data=hg(D),v[4][0].data=hg(A),h.messages[0].data=hg(v);var M=pg(d),U=vg(M);return u.content=U,u.size=u.content.length,l}function Fg(e){return function(r){for(var t=0;t!=e.length;++t){var a=e[t];void 0===r[a[0]]&&(r[a[0]]=a[1]),"n"===a[2]&&(r[a[0]]=Number(r[a[0]]))}}}function Pg(e){Fg([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Lg(e){Fg([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Mg(e){return Jn.WS.indexOf(e)>-1?"sheet":Jn.CS&&e==Jn.CS?"chart":Jn.DS&&e==Jn.DS?"dialog":Jn.MS&&e==Jn.MS?"macro":e&&e.length?e:"sheet"}function Ug(e,r){if(!e)return 0;try{e=r.map((function(r){return r.id||(r.id=r.strRelID),[r.name,e["!id"][r.id].Target,Mg(e["!id"][r.id].Type)]}))}catch(t){return null}return e&&0!==e.length?e:null}function Bg(e,r,t,a,n,s,i,o,c,l,f,h){try{s[a]=Zn(Ir(e,t,!0),r);var u,d=Or(e,r);switch(o){case"sheet":u=ub(d,r,n,c,s[a],l,f,h);break;case"chart":if(u=db(d,r,n,c,s[a],l,f,h),!u||!u["!drawel"])break;var p=Mr(u["!drawel"].Target,r),m=qn(p),b=rh(Ir(e,p,!0),Zn(Ir(e,m,!0),p)),v=Mr(b,p),g=qn(v);u=Fm(Ir(e,v,!0),v,c,Zn(Ir(e,g,!0),v),l,u);break;case"macro":u=pb(d,r,n,c,s[a],l,f,h);break;case"dialog":u=mb(d,r,n,c,s[a],l,f,h);break;default:throw new Error("Unrecognized sheet type "+o)}i[a]=u;var w=[];s&&s[a]&&tr(s[a]).forEach((function(t){var n="";if(s[a][t].Type==Jn.CMNT){n=Mr(s[a][t].Target,r);var i=wb(Or(e,n,!0),n,c);if(!i||!i.length)return;nh(u,i,!1)}s[a][t].Type==Jn.TCMNT&&(n=Mr(s[a][t].Target,r),w=w.concat(oh(Or(e,n,!0),c)))})),w&&w.length&&nh(u,w,!0,c.people||[])}catch(k){if(c.WTF)throw k}}function Wg(e){return"/"==e.charAt(0)?e.slice(1):e}function Hg(e,r){if(je(),r=r||{},Pg(r),Cr(e,"META-INF/manifest.xml"))return Yv(e,r);if(Cr(e,"objectdata.xml"))return Yv(e,r);if(Cr(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if("undefined"!=typeof Ig){if(e.FileIndex)return Ig(e);var t=qe.utils.cfb_new();return Dr(e).forEach((function(r){Fr(t,r,Nr(e,r))})),Ig(t)}throw new Error("Unsupported NUMBERS file")}if(!Cr(e,"[Content_Types].xml")){if(Cr(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(Cr(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");throw new Error("Unsupported ZIP file")}var a,n,s=Dr(e),i=Yn(Ir(e,"[Content_Types].xml")),o=!1;if(0===i.workbooks.length&&(n="xl/workbook.xml",Or(e,n,!0)&&i.workbooks.push(n)),0===i.workbooks.length){if(n="xl/workbook.bin",!Or(e,n,!0))throw new Error("Could not find workbook");i.workbooks.push(n),o=!0}"bin"==i.workbooks[0].slice(-3)&&(o=!0);var c={},l={};if(!r.bookSheets&&!r.bookProps){if(xd=[],i.sst)try{xd=gb(Or(e,Wg(i.sst)),i.sst,r)}catch(O){if(r.WTF)throw O}r.cellStyles&&i.themes.length&&(c=vb(Ir(e,i.themes[0].replace(/^\//,""),!0)||"",i.themes[0],r)),i.style&&(l=bb(Or(e,Wg(i.style)),i.style,c,r))}i.links.map((function(t){try{var a=Zn(Ir(e,qn(Wg(t))),t);return Tb(Or(e,Wg(t)),a,t,r)}catch(O){}}));var f=hb(Or(e,Wg(i.workbooks[0])),i.workbooks[0],r),h={},u="";i.coreprops.length&&(u=Or(e,Wg(i.coreprops[0]),!0),u&&(h=fs(u)),0!==i.extprops.length&&(u=Or(e,Wg(i.extprops[0]),!0),u&&bs(u,h,r)));var d={};r.bookSheets&&!r.bookProps||0!==i.custprops.length&&(u=Ir(e,Wg(i.custprops[0]),!0),u&&(d=ws(u,r)));var p={};if((r.bookSheets||r.bookProps)&&(f.Sheets?a=f.Sheets.map((function(e){return e.name})):h.Worksheets&&h.SheetNames.length>0&&(a=h.SheetNames),r.bookProps&&(p.Props=h,p.Custprops=d),r.bookSheets&&"undefined"!==typeof a&&(p.SheetNames=a),r.bookSheets?p.SheetNames:r.bookProps))return p;a={};var m={};r.bookDeps&&i.calcchain&&(m=kb(Or(e,Wg(i.calcchain)),i.calcchain,r));var b,v,g=0,w={},k=f.Sheets;h.Worksheets=k.length,h.SheetNames=[];for(var T=0;T!=k.length;++T)h.SheetNames[T]=k[T].name;var E=o?"bin":"xml",S=i.workbooks[0].lastIndexOf("/"),y=(i.workbooks[0].slice(0,S+1)+"_rels/"+i.workbooks[0].slice(S+1)+".rels").replace(/^\//,"");Cr(e,y)||(y="xl/_rels/workbook."+E+".rels");var _=Zn(Ir(e,y,!0),y.replace(/_rels.*/,"s5s"));(i.metadata||[]).length>=1&&(r.xlmeta=Eb(Or(e,Wg(i.metadata[0])),i.metadata[0],r)),(i.people||[]).length>=1&&(r.people=lh(Or(e,Wg(i.people[0])),r)),_&&(_=Ug(_,f.Sheets));var A=Or(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(g=0;g!=h.Worksheets;++g){var x="sheet";if(_&&_[g]?(b="xl/"+_[g][1].replace(/[\/]?xl\//,""),Cr(e,b)||(b=_[g][1]),Cr(e,b)||(b=y.replace(/_rels\/.*$/,"")+_[g][1]),x=_[g][2]):(b="xl/worksheets/sheet"+(g+1-A)+"."+E,b=b.replace(/sheet0\./,"sheet.")),v=b.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),r&&null!=r.sheets)switch(typeof r.sheets){case"number":if(g!=r.sheets)continue e;break;case"string":if(h.SheetNames[g].toLowerCase()!=r.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(r.sheets)){for(var C=!1,R=0;R!=r.sheets.length;++R)"number"==typeof r.sheets[R]&&r.sheets[R]==g&&(C=1),"string"==typeof r.sheets[R]&&r.sheets[R].toLowerCase()==h.SheetNames[g].toLowerCase()&&(C=1);if(!C)continue e}}Bg(e,b,v,h.SheetNames[g],g,w,a,x,r,f,c,l)}return p={Directory:i,Workbook:f,Props:h,Custprops:d,Deps:m,Sheets:a,SheetNames:h.SheetNames,Strings:xd,Styles:l,Themes:c,SSF:wr(J)},r&&r.bookFiles&&(e.files?(p.keys=s,p.files=e.files):(p.keys=[],p.files={},e.FullPaths.forEach((function(r,t){r=r.replace(/^Root Entry[\/]/,""),p.keys.push(r),p.files[r]=e.FileIndex[t]})))),r&&r.bookVBA&&(i.vba.length>0?p.vbaraw=Or(e,Wg(i.vba[0]),!0):i.defaults&&i.defaults.bin===vh&&(p.vbaraw=Or(e,"xl/vbaProject.bin",!0))),p}function Vg(e,r){var t=r||{},a="Workbook",n=qe.find(e,a);try{if(a="/!DataSpaces/Version",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if($c(n.content),a="/!DataSpaces/DataSpaceMap",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var s=Kc(n.content);if(1!==s.length||1!==s[0].comps.length||0!==s[0].comps[0].t||"StrongEncryptionDataSpace"!==s[0].name||"EncryptedPackage"!==s[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var i=Jc(n.content);if(1!=i.length||"StrongEncryptionTransform"!=i[0])throw new Error("ECMA-376 Encrypted file bad "+a);if(a="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);Zc(n.content)}catch(c){}if(a="/EncryptionInfo",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);var o=rl(n.content);if(a="/EncryptedPackage",n=qe.find(e,a),!n||!n.content)throw new Error("ECMA-376 Encrypted file missing "+a);if(4==o[0]&&"undefined"!==typeof decrypt_agile)return decrypt_agile(o[1],n.content,t.password||"",t);if(2==o[0]&&"undefined"!==typeof decrypt_std76)return decrypt_std76(o[1],n.content,t.password||"",t);throw new Error("File is password-protected")}function zg(e,r){return"ods"==r.bookType?Zv(e,r):"numbers"==r.bookType?Dg(e,r):"xlsb"==r.bookType?Gg(e,r):jg(e,r)}function Gg(e,r){th=1024,e&&!e.SSF&&(e.SSF=wr(J)),e&&e.SSF&&(je(),Ge(e.SSF),r.revssf=sr(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.rels={},r.wbrels={},r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Rd?r.revStrings=new Map:(r.revStrings={},r.revStrings.foo=[],delete r.revStrings.foo);var t="xlsb"==r.bookType?"bin":"xml",a=kh.indexOf(r.bookType)>-1,n=$n();Lg(r=r||{});var s=Pr(),i="",o=0;if(r.cellXfs=[],Dd(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),i="docProps/core.xml",Fr(s,i,us(e.Props,r)),n.coreprops.push(i),es(r.rels,2,i,Jn.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,Fr(s,i,vs(e.Props,r)),n.extprops.push(i),es(r.rels,3,i,Jn.EXT_PROPS),e.Custprops!==e.Props&&tr(e.Custprops||{}).length>0&&(i="docProps/custom.xml",Fr(s,i,ks(e.Custprops,r)),n.custprops.push(i),es(r.rels,4,i,Jn.CUST_PROPS)),o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},h=e.Sheets[e.SheetNames[o-1]],u=(h||{})["!type"]||"sheet";switch(u){case"chart":default:i="xl/worksheets/sheet"+o+"."+t,Fr(s,i,yb(o-1,i,r,e,f)),n.sheets.push(i),es(r.wbrels,-1,"worksheets/sheet"+o+"."+t,Jn.WS[0])}if(h){var d=h["!comments"],p=!1,m="";d&&d.length>0&&(m="xl/comments"+o+"."+t,Fr(s,m,xb(d,m,r)),n.comments.push(m),es(f,-1,"../comments"+o+"."+t,Jn.CMNT),p=!0),h["!legacy"]&&p&&Fr(s,"xl/drawings/vmlDrawing"+o+".vml",ah(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}f["!id"].rId1&&Fr(s,qn(i),Qn(f))}return null!=r.Strings&&r.Strings.length>0&&(i="xl/sharedStrings."+t,Fr(s,i,Ab(r.Strings,i,r)),n.strs.push(i),es(r.wbrels,-1,"sharedStrings."+t,Jn.SST)),i="xl/workbook."+t,Fr(s,i,Sb(e,i,r)),n.workbooks.push(i),es(r.rels,1,i,Jn.WB),i="xl/theme/theme1.xml",Fr(s,i,Of(e.Themes,r)),n.themes.push(i),es(r.wbrels,-1,"theme/theme1.xml",Jn.THEME),i="xl/styles."+t,Fr(s,i,_b(e,i,r)),n.styles.push(i),es(r.wbrels,-1,"styles."+t,Jn.STY),e.vbaraw&&a&&(i="xl/vbaProject.bin",Fr(s,i,e.vbaraw),n.vba.push(i),es(r.wbrels,-1,"vbaProject.bin",Jn.VBA)),i="xl/metadata."+t,Fr(s,i,Cb(i)),n.metadata.push(i),es(r.wbrels,-1,"metadata."+t,Jn.XLMETA),Fr(s,"[Content_Types].xml",Kn(n,r)),Fr(s,"_rels/.rels",Qn(r.rels)),Fr(s,"xl/_rels/workbook."+t+".rels",Qn(r.wbrels)),delete r.revssf,delete r.ssf,s}function jg(e,r){th=1024,e&&!e.SSF&&(e.SSF=wr(J)),e&&e.SSF&&(je(),Ge(e.SSF),r.revssf=sr(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.rels={},r.wbrels={},r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Rd?r.revStrings=new Map:(r.revStrings={},r.revStrings.foo=[],delete r.revStrings.foo);var t="xml",a=kh.indexOf(r.bookType)>-1,n=$n();Lg(r=r||{});var s=Pr(),i="",o=0;if(r.cellXfs=[],Dd(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),i="docProps/core.xml",Fr(s,i,us(e.Props,r)),n.coreprops.push(i),es(r.rels,2,i,Jn.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Fr(s,i,vs(e.Props,r)),n.extprops.push(i),es(r.rels,3,i,Jn.EXT_PROPS),e.Custprops!==e.Props&&tr(e.Custprops||{}).length>0&&(i="docProps/custom.xml",Fr(s,i,ks(e.Custprops,r)),n.custprops.push(i),es(r.rels,4,i,Jn.CUST_PROPS));var f=["SheetJ5"];for(r.tcid=0,o=1;o<=e.SheetNames.length;++o){var h={"!id":{}},u=e.Sheets[e.SheetNames[o-1]],d=(u||{})["!type"]||"sheet";switch(d){case"chart":default:i="xl/worksheets/sheet"+o+"."+t,Fr(s,i,pp(o-1,r,e,h)),n.sheets.push(i),es(r.wbrels,-1,"worksheets/sheet"+o+"."+t,Jn.WS[0])}if(u){var p=u["!comments"],m=!1,b="";if(p&&p.length>0){var v=!1;p.forEach((function(e){e[1].forEach((function(e){1==e.T&&(v=!0)}))})),v&&(b="xl/threadedComments/threadedComment"+o+"."+t,Fr(s,b,ch(p,f,r)),n.threadedcomments.push(b),es(h,-1,"../threadedComments/threadedComment"+o+"."+t,Jn.TCMNT)),b="xl/comments"+o+"."+t,Fr(s,b,ih(p,r)),n.comments.push(b),es(h,-1,"../comments"+o+"."+t,Jn.CMNT),m=!0}u["!legacy"]&&m&&Fr(s,"xl/drawings/vmlDrawing"+o+".vml",ah(o,u["!comments"])),delete u["!comments"],delete u["!legacy"]}h["!id"].rId1&&Fr(s,qn(i),Qn(h))}return null!=r.Strings&&r.Strings.length>0&&(i="xl/sharedStrings."+t,Fr(s,i,Bc(r.Strings,r)),n.strs.push(i),es(r.wbrels,-1,"sharedStrings."+t,Jn.SST)),i="xl/workbook."+t,Fr(s,i,Zm(e,r)),n.workbooks.push(i),es(r.rels,1,i,Jn.WB),i="xl/theme/theme1.xml",Fr(s,i,Of(e.Themes,r)),n.themes.push(i),es(r.wbrels,-1,"theme/theme1.xml",Jn.THEME),i="xl/styles."+t,Fr(s,i,jl(e,r)),n.styles.push(i),es(r.wbrels,-1,"styles."+t,Jn.STY),e.vbaraw&&a&&(i="xl/vbaProject.bin",Fr(s,i,e.vbaraw),n.vba.push(i),es(r.wbrels,-1,"vbaProject.bin",Jn.VBA)),i="xl/metadata."+t,Fr(s,i,Kf()),n.metadata.push(i),es(r.wbrels,-1,"metadata."+t,Jn.XLMETA),f.length>1&&(i="xl/persons/person.xml",Fr(s,i,fh(f,r)),n.people.push(i),es(r.wbrels,-1,"persons/person.xml",Jn.PEOPLE)),Fr(s,"[Content_Types].xml",Kn(n,r)),Fr(s,"_rels/.rels",Qn(r.rels)),Fr(s,"xl/_rels/workbook."+t+".rels",Qn(r.wbrels)),delete r.revssf,delete r.ssf,s}function Xg(e,r){var t="";switch((r||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":t=S(e.slice(0,12));break;case"binary":t=e;break;case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];default:throw new Error("Unrecognized type "+(r&&r.type||"undefined"))}return[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3),t.charCodeAt(4),t.charCodeAt(5),t.charCodeAt(6),t.charCodeAt(7)]}function $g(e,r){return qe.find(e,"EncryptedPackage")?Vg(e,r):fv(e,r)}function Yg(e,r){var t,a=e,n=r||{};return n.type||(n.type=y&&Buffer.isBuffer(e)?"buffer":"base64"),t=Lr(a,n),Hg(t,n)}function Kg(e,r){var t=0;e:while(t<e.length)switch(e.charCodeAt(t)){case 10:case 13:case 32:++t;break;case 60:return Hb(e.slice(t),r);default:break e}return yc.to_workbook(e,r)}function Jg(e,r){var t="",a=Xg(e,r);switch(r.type){case"base64":t=S(e);break;case"binary":t=e;break;case"buffer":t=e.toString("binary");break;case"array":t=gr(e);break;default:throw new Error("Unrecognized type "+r.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(t=ft(t)),r.type="binary",Kg(t,r)}function qg(e,r){var t=e;return"base64"==r.type&&(t=S(t)),t=m.utils.decode(1200,t.slice(2),"str"),r.type="binary",Kg(t,r)}function Zg(e){return e.match(/[^\x00-\x7F]/)?ht(e):e}function Qg(e,r,t,a){return a?(t.type="string",yc.to_workbook(e,t)):yc.to_workbook(r,t)}function ew(e,r){h();var t=r||{};if("undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer)return ew(new Uint8Array(e),(t=wr(t),t.type="array",t));"undefined"!==typeof Uint8Array&&e instanceof Uint8Array&&!t.type&&(t.type="undefined"!==typeof Deno?"buffer":"array");var a=e,n=[0,0,0,0],s=!1;if(t.cellStyles&&(t.cellNF=!0,t.sheetStubs=!0),Cd={},t.dateNF&&(Cd.dateNF=t.dateNF),t.type||(t.type=y&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==t.type&&(t.type=y?"buffer":"binary",a=rr(e),"undefined"===typeof Uint8Array||y||(t.type="array")),"string"==t.type&&(s=!0,t.type="binary",t.codepage=65001,a=Zg(e)),"array"==t.type&&"undefined"!==typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!==typeof ArrayBuffer){var i=new ArrayBuffer(3),o=new Uint8Array(i);if(o.foo="bar",!o.foo)return t=wr(t),t.type="array",ew(N(a),t)}switch((n=Xg(a,t))[0]){case 208:if(207===n[1]&&17===n[2]&&224===n[3]&&161===n[4]&&177===n[5]&&26===n[6]&&225===n[7])return $g(qe.read(a,t),t);break;case 9:if(n[1]<=8)return fv(a,t);break;case 60:return Hb(a,t);case 73:if(73===n[1]&&42===n[2]&&0===n[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===n[1])return _c(a,t);break;case 84:if(65===n[1]&&66===n[2]&&76===n[3])return Ec.to_workbook(a,t);break;case 80:return 75===n[1]&&n[2]<9&&n[3]<9?Yg(a,t):Qg(e,a,t,s);case 239:return 60===n[3]?Hb(a,t):Qg(e,a,t,s);case 255:if(254===n[1])return qg(a,t);if(0===n[1]&&2===n[2]&&0===n[3])return Ac.to_workbook(a,t);break;case 0:if(0===n[1]){if(n[2]>=2&&0===n[3])return Ac.to_workbook(a,t);if(0===n[2]&&(8===n[3]||9===n[3]))return Ac.to_workbook(a,t)}break;case 3:case 131:case 139:case 140:return kc.to_workbook(a,t);case 123:if(92===n[1]&&114===n[2]&&116===n[3])return pl.to_workbook(a,t);break;case 10:case 13:case 32:return Jg(a,t);case 137:if(80===n[1]&&78===n[2]&&71===n[3])throw new Error("PNG Image File is not a spreadsheet");break}return wc.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?kc.to_workbook(a,t):Qg(e,a,t,s)}function rw(e,r){switch(r.type){case"base64":case"binary":break;case"buffer":case"array":r.type="";break;case"file":return er(r.file,qe.write(e,{type:y?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");default:throw new Error("Unrecognized type "+r.type)}return qe.write(e,r)}function tw(e,r){var t=wr(r||{}),a=zg(e,t);return aw(a,t)}function aw(e,r){var t={},a=y?"nodebuffer":"undefined"!==typeof Uint8Array?"array":"string";if(r.compression&&(t.compression="DEFLATE"),r.password)t.type=a;else switch(r.type){case"base64":t.type="base64";break;case"binary":t.type="string";break;case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");case"buffer":case"file":t.type=a;break;default:throw new Error("Unrecognized type "+r.type)}var n=e.FullPaths?qe.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[t.type]||t.type,compression:!!r.compression}):e.generate(t);if("undefined"!==typeof Deno&&"string"==typeof n){if("binary"==r.type||"base64"==r.type)return n;n=new Uint8Array(R(n))}return r.password&&"undefined"!==typeof encrypt_agile?rw(encrypt_agile(n,r.password),r):"file"===r.type?er(r.file,n):"string"==r.type?ft(n):n}function nw(e,r){var t=r||{},a=hv(e,t);return rw(a,t)}function sw(e,r,t){t||(t="");var a=t+e;switch(r.type){case"base64":return E(ht(a));case"binary":return ht(a);case"string":return e;case"file":return er(r.file,a,"utf8");case"buffer":return y?_(a,"utf8"):"undefined"!==typeof TextEncoder?(new TextEncoder).encode(a):sw(a,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+r.type)}function iw(e,r){switch(r.type){case"base64":return E(e);case"binary":return e;case"string":return e;case"file":return er(r.file,e,"binary");case"buffer":return y?_(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+r.type)}function ow(e,r){switch(r.type){case"string":case"base64":case"binary":for(var t="",a=0;a<e.length;++a)t+=String.fromCharCode(e[a]);return"base64"==r.type?E(t):"string"==r.type?ft(t):t;case"file":return er(r.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+r.type)}}function cw(e,r){h(),Km(e);var t=wr(r||{});if(t.cellStyles&&(t.cellNF=!0,t.sheetStubs=!0),"array"==t.type){t.type="binary";var a=cw(e,t);return t.type="array",R(a)}var n=0;if(t.sheet&&(n="number"==typeof t.sheet?t.sheet:e.SheetNames.indexOf(t.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+t.sheet+" : "+typeof t.sheet);switch(t.bookType||"xlsb"){case"xml":case"xlml":return sw(ev(e,t),t);case"slk":case"sylk":return sw(Tc.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"htm":case"html":return sw(Bv(e.Sheets[e.SheetNames[n]],t),t);case"txt":return iw(pw(e.Sheets[e.SheetNames[n]],t),t);case"csv":return sw(dw(e.Sheets[e.SheetNames[n]],t),t,"\ufeff");case"dif":return sw(Ec.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"dbf":return ow(kc.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"prn":return sw(yc.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"rtf":return sw(pl.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"eth":return sw(Sc.from_sheet(e.Sheets[e.SheetNames[n]],t),t);case"fods":return sw(Zv(e,t),t);case"wk1":return ow(Ac.sheet_to_wk1(e.Sheets[e.SheetNames[n]],t),t);case"wk3":return ow(Ac.book_to_wk3(e,t),t);case"biff2":t.biff||(t.biff=2);case"biff3":t.biff||(t.biff=3);case"biff4":return t.biff||(t.biff=4),ow(Nv(e,t),t);case"biff5":t.biff||(t.biff=5);case"biff8":case"xla":case"xls":return t.biff||(t.biff=8),nw(e,t);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return tw(e,t);default:throw new Error("Unrecognized bookType |"+t.bookType+"|")}}function lw(e,r,t,a,n,s,i,o){var c=Ea(t),l=o.defval,f=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),h=!0,u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:t,enumerable:!1})}catch(b){u.__rowNum__=t}else u.__rowNum__=t;if(!i||e[t])for(var d=r.s.c;d<=r.e.c;++d){var p=i?e[t][d]:e[a[d]+c];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==l)u[s[d]]=l;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:La(p,m,o);null!=m&&(h=!1)}}else{if(void 0===l)continue;null!=s[d]&&(u[s[d]]=l)}}return{row:u,isempty:h}}function fw(e,r){if(null==e||null==e["!ref"])return[];var t={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=r||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":c=Fa(f);break;case"number":c=Fa(e["!ref"]),c.s.r=f;break;default:c=f}a>0&&(n=0);var h=Ea(c.s.r),u=[],d=[],p=0,m=0,b=Array.isArray(e),v=c.s.r,g=0,w={};b&&!e[v]&&(e[v]=[]);var k=l.skipHidden&&e["!cols"]||[],T=l.skipHidden&&e["!rows"]||[];for(g=c.s.c;g<=c.e.c;++g)if(!(k[g]||{}).hidden)switch(u[g]=Aa(g),t=b?e[v][g]:e[u[g]+h],a){case 1:s[g]=g-c.s.c;break;case 2:s[g]=u[g];break;case 3:s[g]=l.header[g-c.s.c];break;default:if(null==t&&(t={w:"__EMPTY",t:"s"}),o=i=La(t,null,l),m=w[i]||0,m){do{o=i+"_"+m++}while(w[o]);w[i]=m,w[o]=1}else w[i]=1;s[g]=o}for(v=c.s.r+n;v<=c.e.r;++v)if(!(T[v]||{}).hidden){var E=lw(e,c,v,u,a,s,b,l);(!1===E.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=E.row)}return d.length=p,d}var hw=/"/g;function uw(e,r,t,a,n,s,i,o){for(var c=!0,l=[],f="",h=Ea(t),u=r.s.c;u<=r.e.c;++u)if(a[u]){var d=o.dense?(e[t]||[])[u]:e[a[u]+h];if(null==d)f="";else if(null!=d.v){c=!1,f=""+(o.rawNumbers&&"n"==d.t?d.v:La(d,null,o));for(var p=0,m=0;p!==f.length;++p)if((m=f.charCodeAt(p))===n||m===s||34===m||o.forceQuotes){f='"'+f.replace(hw,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(c=!1,f="="+d.f,f.indexOf(",")>=0&&(f='"'+f.replace(hw,'""')+'"'));l.push(f)}return!1===o.blankrows&&c?null:l.join(i)}function dw(e,r){var t=[],a=null==r?{}:r;if(null==e||null==e["!ref"])return"";var n=Fa(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];a.dense=Array.isArray(e);for(var u=a.skipHidden&&e["!cols"]||[],d=a.skipHidden&&e["!rows"]||[],p=n.s.c;p<=n.e.c;++p)(u[p]||{}).hidden||(h[p]=Aa(p));for(var m=0,b=n.s.r;b<=n.e.r;++b)(d[b]||{}).hidden||(f=uw(e,n,b,h,i,c,s,a),null!=f&&(a.strip&&(f=f.replace(l,"")),(f||!1!==a.blankrows)&&t.push((m++?o:"")+f)));return delete a.dense,t.join("")}function pw(e,r){r||(r={}),r.FS="\t",r.RS="\n";var t=dw(e,r);if("undefined"==typeof m||"string"==r.type)return t;var a=m.utils.encode(1200,t,"str");return String.fromCharCode(255)+String.fromCharCode(254)+a}function mw(e){var r,t="",a="";if(null==e||null==e["!ref"])return[];var n,s=Fa(e["!ref"]),i="",o=[],c=[],l=Array.isArray(e);for(n=s.s.c;n<=s.e.c;++n)o[n]=Aa(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=Ea(f),n=s.s.c;n<=s.e.c;++n)if(t=o[n]+i,r=l?(e[f]||[])[n]:e[t],a="",void 0!==r){if(null!=r.F){if(t=r.F,!r.f)continue;a=r.f,-1==t.indexOf(":")&&(t=t+":"+t)}if(null!=r.f)a=r.f;else{if("z"==r.t)continue;if("n"==r.t&&null!=r.v)a=""+r.v;else if("b"==r.t)a=r.v?"TRUE":"FALSE";else if(void 0!==r.w)a="'"+r.w;else{if(void 0===r.v)continue;a="s"==r.t?"'"+r.v:""+r.v}}c[c.length]=t+"="+a}return c}function bw(e,r,t){var a,n=t||{},s=+!n.skipHeader,i=e||{},o=0,c=0;if(i&&null!=n.origin)if("number"==typeof n.origin)o=n.origin;else{var l="string"==typeof n.origin?Oa(n.origin):n.origin;o=l.r,c=l.c}var f={s:{c:0,r:0},e:{c:c,r:o+r.length-1+s}};if(i["!ref"]){var h=Fa(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+r.length-1+s)}else-1==o&&(o=0,f.e.r=r.length-1+s);var u=n.header||[],d=0;r.forEach((function(e,r){tr(e).forEach((function(t){-1==(d=u.indexOf(t))&&(u[d=u.length]=t);var l=e[t],f="z",h="",p=Ia({c:c+d,r:o+r+s});a=gw(i,p),!l||"object"!==typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",n.cellDates||(f="n",l=cr(l)),h=n.dateNF||J[14]):null===l&&n.nullError&&(f="e",l=0),a?(a.t=f,a.v=l,delete a.w,delete a.R,h&&(a.z=h)):i[p]=a={t:f,v:l},h&&(a.z=h)):i[p]=l}))})),f.e.c=Math.max(f.e.c,c+u.length-1);var p=Ea(o);if(s)for(d=0;d<u.length;++d)i[Aa(d+c)+p]={t:"s",v:u[d]};return i["!ref"]=Da(f),i}function vw(e,r){return bw(null,e,r)}function gw(e,r,t){if("string"==typeof r){if(Array.isArray(e)){var a=Oa(r);return e[a.r]||(e[a.r]=[]),e[a.r][a.c]||(e[a.r][a.c]={t:"z"})}return e[r]||(e[r]={t:"z"})}return gw(e,Ia("number"!=typeof r?r:{r:r,c:t||0}))}function ww(e,r){if("number"==typeof r){if(r>=0&&e.SheetNames.length>r)return r;throw new Error("Cannot find sheet # "+r)}if("string"==typeof r){var t=e.SheetNames.indexOf(r);if(t>-1)return t;throw new Error("Cannot find sheet name |"+r+"|")}throw new Error("Cannot find sheet |"+r+"|")}function kw(){return{SheetNames:[],Sheets:{}}}function Tw(e,r,t,a){var n=1;if(!t)for(;n<=65535;++n,t=void 0)if(-1==e.SheetNames.indexOf(t="Sheet"+n))break;if(!t||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(t)>=0){var s=t.match(/(^.*?)(\d+)$/);n=s&&+s[2]||0;var i=s&&s[1]||t;for(++n;n<=65535;++n)if(-1==e.SheetNames.indexOf(t=i+n))break}if($m(t),e.SheetNames.indexOf(t)>=0)throw new Error("Worksheet with name |"+t+"| already exists!");return e.SheetNames.push(t),e.Sheets[t]=r,t}function Ew(e,r,t){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=ww(e,r);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),t){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+t)}e.Workbook.Sheets[a].Hidden=t}function Sw(e,r){return e.z=r,e}function yw(e,r,t){return r?(e.l={Target:r},t&&(e.l.Tooltip=t)):delete e.l,e}function _w(e,r,t){return yw(e,"#"+r,t)}function Aw(e,r,t){e.c||(e.c=[]),e.c.push({t:r,a:t||"SheetJS"})}function xw(e,r,t,a){for(var n="string"!=typeof r?r:Fa(r),s="string"==typeof r?r:Da(r),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=gw(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=t,a&&(c.D=!0))}return e}var Cw={encode_col:Aa,encode_row:Ea,encode_cell:Ia,encode_range:Da,decode_col:_a,decode_row:Ta,split_cell:Ra,decode_cell:Oa,decode_range:Na,format_cell:La,sheet_add_aoa:Ua,sheet_add_json:bw,sheet_add_dom:Wv,aoa_to_sheet:Ba,json_to_sheet:vw,table_to_sheet:Hv,table_to_book:Vv,sheet_to_csv:dw,sheet_to_txt:pw,sheet_to_json:fw,sheet_to_html:Bv,sheet_to_formulae:mw,sheet_to_row_object_array:fw,sheet_get_cell:gw,book_new:kw,book_append_sheet:Tw,book_set_sheet_visibility:Ew,cell_set_number_format:Sw,cell_set_hyperlink:yw,cell_set_internal_link:_w,cell_add_comment:Aw,sheet_set_array_formula:xw,consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};a.version},"4ec9":function(e,r,t){t("6f48")},"6f48":function(e,r,t){"use strict";var a=t("6d61"),n=t("6566");a("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),n)}}]);