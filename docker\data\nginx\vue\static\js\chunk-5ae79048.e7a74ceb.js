(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5ae79048","chunk-05c05b4c"],{"1ab6":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:e.$t("firmware.deviceList.index.222542-0"),visible:e.openDeviceList,width:"810px","append-to-body":""},on:{"update:visible":function(t){e.openDeviceList=t}}},[a("el-form",{ref:"queryForm",attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{prop:"deviceName"}},[a("el-input",{staticStyle:{width:"196px"},attrs:{placeholder:e.$t("firmware.deviceList.index.222542-12"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.query,callback:function(t){e.$set(e.queryParams,"query",t)},expression:"queryParams.query"}})],1),a("el-form-item",{attrs:{prop:"version"}},[a("el-input",{staticClass:"input-with-select",staticStyle:{"vertical-align":"middle",width:"318px"},attrs:{placeholder:e.$t("firmware.deviceList.index.222542-13"),clearable:"",size:"small",disabled:!e.versionType},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.version,callback:function(t){e.$set(e.queryParams,"version",t)},expression:"queryParams.version"}},[a("el-select",{staticStyle:{width:"126px"},attrs:{slot:"prepend",size:"small",placeholder:e.$t("firmware.deviceList.index.222542-14")},slot:"prepend",model:{value:e.versionType,callback:function(t){e.versionType=t},expression:"versionType"}},[a("el-option",{attrs:{label:e.$t("firmware.deviceList.index.222542-15"),value:1}}),a("el-option",{attrs:{label:e.$t("firmware.deviceList.index.222542-16"),value:2}})],1)],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:e.handleQuery}},[e._v(e._s(e.$t("search")))])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"multipleTable",attrs:{data:e.deviceList,size:"small",border:!1},on:{select:e.handleSelectionChange,"select-all":e.handleSelectionAll}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-1"),align:"left",prop:"deviceName","min-width":"160"}}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-5"),align:"left",prop:"serialNumber","min-width":"140"}}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-15"),align:"center",prop:"firmwareVersion",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.firmwareVersion?a("span",[e._v("-")]):e._e(),a("span",[e._v("Version "+e._s(t.row.firmwareVersion))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-16"),align:"center",prop:"wirelessVersion",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[0===t.row.wirelessVersion?a("span",[e._v("-")]):a("span",[e._v("Version "+e._s(t.row.wirelessVersion))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-6"),align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.isOwner?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("firmware.deviceList.index.222542-7")))]):a("el-tag",{attrs:{type:"primary"}},[e._v(e._s(e.$t("firmware.deviceList.index.222542-8")))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-9"),align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_device_status,value:t.row.status}})]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleDeviceSelected}},[e._v(e._s(e.$t("confirm")))]),a("el-button",{on:{click:e.closeSelectDeviceList}},[e._v(e._s(e.$t("cancel")))])],1)],1)},r=[],s=(a("14d9"),a("a434"),a("d3b7"),a("159b"),a("584f")),n=a("ed08"),o={name:"device-list",dicts:["iot_device_status"],props:{upGrade:{type:Object,default:null}},data:function(){return{formUpGrade:{},loading:!0,ids:[],openDeviceList:!1,total:0,deviceList:[],queryParams:{query:"",pageNum:1,pageSize:10},versionType:null}},watch:{upGrade:{handler:function(e,t){e.flag&&(this.formUpGrade=e,this.queryParams.productId=this.formUpGrade.productId,this.queryParams.firmwareVersion=this.formUpGrade.firmwareVersion,this.ids=this.formUpGrade.deviceList)},immediate:!0,deep:!0},openDeviceList:function(e){e&&(this.queryParams.pageNum=1,this.resetQueryParamsHandle(),this.getList())}},created:function(){},methods:{getList:function(){var e=this;this.loading=!0;var t=Object(n["c"])(this.queryParams);t.firmwareVersion=1===this.versionType?this.queryParams.version:null,t.wirelessVersion=2===this.versionType?this.queryParams.version:null,Object(s["o"])(t).then((function(t){e.deviceList=t.rows,e.total=t.total,e.loading=!1,e.deviceList.forEach((function(t){e.$nextTick((function(){e.ids.some((function(e){return e===t.serialNumber}))&&e.$refs.multipleTable.toggleRowSelection(t,!0)}))}))}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},handleSelectionChange:function(e,t){var a=this.ids.indexOf(t.serialNumber),i=e.indexOf(t);-1==a&&-1!=i?this.ids.push(t.serialNumber):-1!=a&&-1==i&&this.ids.splice(a,1)},handleSelectionAll:function(e){for(var t=0;t<this.deviceList.length;t++){var a=this.ids.indexOf(this.deviceList[t].serialNumber),i=e.indexOf(this.deviceList[t]);-1==a&&-1!=i?this.ids.push(this.deviceList[t].serialNumber):-1!=a&&-1==i&&this.ids.splice(a,1)}},closeSelectDeviceList:function(){this.openDeviceList=!1,this.formUpGrade.flag=!1},handleDeviceSelected:function(){this.formUpGrade.deviceList=this.ids,this.formUpGrade.deviceAmount=this.ids.length,this.formUpGrade.flag=!1,this.$modal.msgSuccess(this.$t("firmware.deviceList.index.222542-10")),this.openDeviceList=!1},resetQueryParamsHandle:function(){this.queryParams.firmwareVersion=null,this.queryParams.query=null,this.versionType=null}}},l=o,c=a("2877"),u=Object(c["a"])(l,i,r,!1,null,null,null);t["default"]=u.exports},2065:function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"a",(function(){return s})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"d",(function(){return l})),a.d(t,"f",(function(){return c}));var i=a("b775");function r(e){return Object(i["a"])({url:"/iot/firmware/task/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/task",method:"post",data:e})}function n(e){return Object(i["a"])({url:"/iot/firmware/task/"+e,method:"delete"})}function o(e){return Object(i["a"])({url:"/iot/firmware/task/deviceList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/firmware/task/deviceStatistic",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/iot/firmware/task/upgrade",method:"post",data:e})}},"584f":function(e,t,a){"use strict";a.d(t,"n",(function(){return r})),a.d(t,"t",(function(){return s})),a.d(t,"o",(function(){return n})),a.d(t,"p",(function(){return o})),a.d(t,"m",(function(){return l})),a.d(t,"f",(function(){return c})),a.d(t,"c",(function(){return u})),a.d(t,"g",(function(){return d})),a.d(t,"i",(function(){return m})),a.d(t,"d",(function(){return f})),a.d(t,"u",(function(){return p})),a.d(t,"q",(function(){return v})),a.d(t,"r",(function(){return h})),a.d(t,"h",(function(){return g})),a.d(t,"a",(function(){return w})),a.d(t,"v",(function(){return k})),a.d(t,"b",(function(){return b})),a.d(t,"e",(function(){return y})),a.d(t,"k",(function(){return $})),a.d(t,"l",(function(){return _})),a.d(t,"j",(function(){return S})),a.d(t,"s",(function(){return D}));var i=a("b775");function r(e){return Object(i["a"])({url:"/iot/device/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/device/unAuthlist",method:"get",params:e})}function n(e){return Object(i["a"])({url:"/iot/device/listByGroup",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/iot/device/shortList",method:"get",params:e})}function l(e){return Object(i["a"])({url:"/iot/device/all",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/iot/device/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/iot/device/synchronization/"+e,method:"get"})}function d(e){return Object(i["a"])({url:"/iot/device/getDeviceBySerialNumber/"+e,method:"get"})}function m(){return Object(i["a"])({url:"/iot/device/statistic",method:"get"})}function f(e,t){return Object(i["a"])({url:"/iot/device/assignment?deptId="+e+"&deviceIds="+t,method:"post"})}function p(e,t){return Object(i["a"])({url:"/iot/device/recovery?deviceIds="+e+"&recoveryDeptId="+t,method:"post"})}function v(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function h(e){return Object(i["a"])({url:"/iot/record/list",method:"get",params:e})}function g(e){return Object(i["a"])({url:"/iot/device/runningStatus",method:"get",params:e})}function w(e){return Object(i["a"])({url:"/iot/device",method:"post",data:e})}function k(e){return Object(i["a"])({url:"/iot/device",method:"put",data:e})}function b(e){return Object(i["a"])({url:"/iot/device/"+e,method:"delete"})}function y(e){return Object(i["a"])({url:"/iot/device/generator",method:"get",params:e})}function $(e){return Object(i["a"])({url:"/iot/device/getMqttConnectData",method:"get",params:e})}function _(e){return Object(i["a"])({url:"/sip/sipconfig/auth/"+e,method:"get"})}function S(e){return Object(i["a"])({url:"/iot/device/getHttpAuthData",method:"get",params:e})}function D(e){return Object(i["a"])({url:"/iot/device/listThingsModel",method:"get",params:e})}},"814a":function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"f",(function(){return s})),a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"g",(function(){return c})),a.d(t,"b",(function(){return u}));var i=a("b775");function r(e){return Object(i["a"])({url:"/iot/firmware/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/iot/firmware/upGradeVersionList",method:"get",params:e})}function n(e,t){return Object(i["a"])({url:"/iot/firmware/getLatest?deviceId="+e+"&firmwareType="+t,method:"get"})}function o(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"get"})}function l(e){return Object(i["a"])({url:"/iot/firmware",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/iot/firmware",method:"put",data:e})}function u(e){return Object(i["a"])({url:"/iot/firmware/"+e,method:"delete"})}},be4a:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"firmware-task"},[a("el-card",[a("el-descriptions",{attrs:{title:e.$t("firmware.task.222543-0")}},e._l(e.firmwareInfo,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name,"label-class-name":"feint"}},[e._v(e._s(t.value))])})),1)],1),a("el-card",[a("div",{staticClass:"firmwareDeviceHeader"},[a("h1",[e._v(e._s(e.$t("firmware.task.222543-1")))]),a("i",{staticClass:"el-icon-refresh icon",on:{click:function(t){return e.updateFirmwareDevice()}}})]),a("div",{staticClass:"firmwareDevice"},e._l(e.firmwareDevice,(function(t,i){return a("div",{key:i,staticClass:"box"},[a("span",{staticClass:"title"},[e._v(e._s(t.title))]),a("span",{staticClass:"num"},[e._v(e._s(t.num))])])})),0)]),a("el-card",[a("div",{staticClass:"firmwareDeviceHeader"},[a("h1",[e._v(e._s(e.$t("firmware.task.222543-2")))])]),a("div",{staticClass:"task"},[a("div",{staticClass:"taskHeader"},[a("div",{staticClass:"taskTitle"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:task:add"],expression:"['iot:task:add']"}],attrs:{type:"primary",size:"small",icon:"el-icon-plus",plain:""},on:{click:function(t){return e.otaUpGrade()}}},[e._v(e._s(e.$t("firmware.task.222543-50")))])],1),a("div",{staticClass:"taskInput"},[a("el-input",{staticClass:"searchInput",attrs:{placeholder:e.$t("firmware.task.222543-51"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.taskQuery()}},model:{value:e.taskName,callback:function(t){e.taskName=t},expression:"taskName"}})],1)]),a("div",{staticClass:"taskBody"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.firmwareTaskList,border:!1}},[a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-8"),align:"center",prop:"id",width:"80"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-9"),align:"left",prop:"taskName","min-width":"180"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-10"),align:"center",prop:"upgradeType","min-width":"140"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.upgradeType?a("el-tag",{attrs:{type:"warning"}},[e._v(e._s(e.$t("firmware.task.222543-11")))]):a("el-tag",[e._v(e._s(e.$t("firmware.task.222543-12")))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-13"),align:"center",prop:"deviceAmount","min-width":"80"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-14"),align:"center",prop:"bookTime",width:"160"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-52"),align:"center",prop:"createTime",width:"160"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-15"),align:"left",prop:"taskDesc","min-width":"180"}}),a("el-table-column",{attrs:{label:e.$t("opation"),align:"center",fixed:"right",width:"125"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:task:query"],expression:"['iot:task:query']"}],attrs:{type:"text",size:"small",icon:"el-icon-view"},on:{click:function(a){return e.taskDetailClick(t.row)}}},[e._v(e._s(e.$t("firmware.task.222543-17")))]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:task:remove"],expression:"['iot:task:remove']"}],attrs:{type:"text",size:"small",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(e._s(e.$t("firmware.task.222543-53")))])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.taskTotal>0,expression:"taskTotal > 0"}],attrs:{total:e.taskTotal,page:e.taskParams.pageNum,limit:e.taskParams.pageSize},on:{"update:page":function(t){return e.$set(e.taskParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.taskParams,"pageSize",t)},pagination:e.getTaskList}})],1)])]),a("el-dialog",{attrs:{title:e.$t("firmware.task.222543-31"),width:"960px",visible:e.taskDialogVisible},on:{"update:visible":function(t){e.taskDialogVisible=t}}},[a("div",{staticClass:"dialogBox"},[a("el-card",{staticStyle:{"margin-right":"5px"}},[a("el-descriptions",{attrs:{title:e.$t("firmware.task.222543-32"),column:1}},e._l(e.taskDialogData,(function(t,i){return a("el-descriptions-item",{key:i,attrs:{label:t.name,"label-class-name":"feint"}},[e._v(e._s(t.value))])})),1)],1),a("el-card",{staticStyle:{"margin-left":"5px"}},[a("el-descriptions",{attrs:{title:e.$t("firmware.task.222543-33")}},[a("template",{slot:"extra"},[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-refresh"},on:{click:e.chartRefresh}})],1)],2),a("div",{ref:"taskChart",staticClass:"chart"})],1)],1),a("el-card",[a("div",{staticClass:"firmwareDeviceHeader"},[a("h1",[e._v(e._s(e.$t("firmware.task.222543-32")))])]),a("div",{staticClass:"deviceDialogSearch"},[a("div",{staticClass:"left",attrs:{id:"left"}},[a("span",{class:{active:"all"===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick("all")}}},[e._v(e._s(e.$t("firmware.task.222543-35"))+"（"+e._s(e.deviceDialogSearchStatus[0])+"）")]),a("span",{class:{active:0===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick(0)}}},[e._v(e._s(e.$t("firmware.task.222543-37"))+"（"+e._s(e.deviceDialogSearchStatus[2])+"）")]),a("span",{class:{active:2===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick(2)}}},[e._v(e._s(e.$t("firmware.task.222543-38"))+"（"+e._s(e.deviceDialogSearchStatus[4])+"）")]),a("span",{class:{active:3===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick(3)}}},[e._v(e._s(e.$t("firmware.task.222543-36"))+"（"+e._s(e.deviceDialogSearchStatus[1])+"）")]),a("span",{class:{active:4===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick(4)}}},[e._v(e._s(e.$t("firmware.task.222543-39"))+"（"+e._s(e.deviceDialogSearchStatus[5])+"）")]),a("span",{class:{active:5===e.deviceStatus},on:{click:function(t){return e.deviceStatusClick(5)}}},[e._v(e._s(e.$t("firmware.task.222543-40"))+"（"+e._s(e.deviceDialogSearchStatus[6])+"）")])]),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{height:"32px",width:"32px",padding:"0"},attrs:{size:"small"},on:{click:e.deviceInfoQuery}},[a("i",{staticClass:"el-icon-refresh"})]),a("el-input",{staticClass:"input3",attrs:{placeholder:e.$t("firmware.task.222543-54"),clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.deviceInfoQuery(t)}},model:{value:e.deviceSerialNumber,callback:function(t){e.deviceSerialNumber=t},expression:"deviceSerialNumber"}})],1)]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deviceDialogList,border:!1}},[a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-1"),align:"left",prop:"deviceName","min-width":"150"}}),a("el-table-column",{attrs:{label:e.$t("firmware.deviceList.index.222542-5"),align:"left",prop:"serialNumber","min-width":"140"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-8"),align:"center",prop:"taskId",width:"80"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-9"),align:"center",prop:"taskName","min-width":"150"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-19"),align:"center",prop:"version",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v("Version "+e._s(t.row.version))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-21"),align:"center",prop:"upgradeStatus",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.upgradeStatus?a("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("firmware.task.222543-37")))]):2==t.row.upgradeStatus?a("el-tag",{attrs:{type:""}},[e._v(e._s(e.$t("firmware.task.222543-38")))]):3==t.row.upgradeStatus?a("el-tag",{attrs:{type:"success"}},[e._v(e._s(e.$t("firmware.task.222543-25")))]):4==t.row.upgradeStatus?a("el-tag",{attrs:{type:"danger"}},[e._v(e._s(e.$t("firmware.task.222543-26")))]):5==t.row.upgradeStatus?a("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("firmware.task.222543-27")))]):a("el-tag",{attrs:{type:"info"}},[e._v(e._s(e.$t("firmware.task.222543-28")))])]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-55"),align:"center",prop:"progress",width:"210"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-progress",{attrs:{percentage:parseInt(e.row.progress)}})]}}])}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-29"),align:"center",prop:"detailMsg",width:"100"}}),a("el-table-column",{attrs:{label:e.$t("firmware.task.222543-30"),align:"center",prop:"updateTime",width:"160"}}),a("el-table-column",{attrs:{fixed:"right",label:e.$t("firmware.task.222543-56"),align:"center",width:"95"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:task:upgrade"],expression:"['iot:task:upgrade']"}],attrs:{type:"text",size:"small",disabled:![4,5].includes(t.row.upgradeStatus)},on:{click:function(a){return e.upgradeTaskHandle(t.row)}}},[e._v(" "+e._s(e.$t("firmware.task.222543-57"))+" ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.deviceInfoTotal>0,expression:"deviceInfoTotal > 0"}],attrs:{layout:"prev, pager, next",total:e.deviceInfoTotal,page:e.deviceInfoParams.pageNum,limit:e.deviceInfoParams.pageSize},on:{"update:page":function(t){return e.$set(e.deviceInfoParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.deviceInfoParams,"pageSize",t)},pagination:e.getDeviceInfoList}})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.handleDialogCancel}},[e._v(e._s(e.$t("cancel")))]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleDialogCancel}},[e._v(e._s(e.$t("confirm")))])],1)],1),a("el-dialog",{attrs:{title:e.title,visible:e.openUpGrade,width:"650px","append-to-body":""},on:{"update:visible":function(t){e.openUpGrade=t}}},[a("el-form",{ref:"formUpGrade",attrs:{model:e.formUpGrade,rules:e.rulesUpGrade,"label-width":"135px"}},[a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-0"),prop:"firmwareName"}},[e._v(" "+e._s(e.form.firmwareName)+" ")]),a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-2"),prop:"productId"}},[e._v(" "+e._s(e.form.productName)+" ")]),a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-15"),prop:"version"}},[e._v("Version "+e._s(e.form.version))]),a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-20"),prop:"taskName"}},[a("el-input",{staticStyle:{width:"400px"},attrs:{placeholder:e.$t("firmware.index.222541-22"),maxlength:"20"},model:{value:e.formUpGrade.taskName,callback:function(t){e.$set(e.formUpGrade,"taskName",t)},expression:"formUpGrade.taskName"}})],1),a("el-form-item",{attrs:{label:e.$t("firmware.task.222543-58"),prop:"upgradeType"}},[a("el-select",{attrs:{placeholder:e.$t("firmware.task.222543-59"),clearable:""},on:{change:e.changeUpType},model:{value:e.formUpGrade.upgradeType,callback:function(t){e.$set(e.formUpGrade,"upgradeType",t)},expression:"formUpGrade.upgradeType"}},e._l(e.dict.type.oat_update_limit,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),1!=e.formUpGrade.upgradeType?a("el-form-item",{attrs:{label:e.$t("firmware.task.222543-60")}},[a("el-badge",{staticClass:"item",attrs:{value:e.formUpGrade.deviceAmount}},[a("el-button",{staticClass:"mr5",attrs:{type:"primary",size:"mini",plain:""},on:{click:e.selectDeviceList}},[e._v(e._s(e.$t("firmware.task.222543-61")))])],1),e._l(e.formUpGrade.deviceList,(function(t){return a("el-tag",{key:t,staticStyle:{"margin-left":"10px"},attrs:{closable:"",size:"small","disable-transitions":!1},on:{close:function(a){return e.handleClose(t)}}},[e._v(" "+e._s(t)+" ")])}))],2):e._e(),a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-32"),prop:"bookTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:e.$t("firmware.index.222541-33")},model:{value:e.formUpGrade.bookTime,callback:function(t){e.$set(e.formUpGrade,"bookTime",t)},expression:"formUpGrade.bookTime"}})],1),a("el-form-item",{attrs:{label:e.$t("firmware.index.222541-34"),prop:"taskDesc"}},[a("el-input",{staticStyle:{width:"400px"},attrs:{type:"textarea",placeholder:e.$t("firmware.index.222541-35"),autosize:{minRows:3,maxRows:5}},model:{value:e.formUpGrade.taskDesc,callback:function(t){e.$set(e.formUpGrade,"taskDesc",t)},expression:"formUpGrade.taskDesc"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.istrue?a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-odometer",loading:!0,disabled:""}},[e._v(e._s(e.$t("firmware.task.222543-24")))]):a("el-button",{attrs:{type:"primary"},on:{click:e.submitFormUpGrade}},[e._v(e._s(e.$t("save")))]),a("el-button",{on:{click:e.canceUpGrade}},[e._v(e._s(e.$t("cancel")))])],1)],1),a("selectDeviceList",{ref:"deviceList",attrs:{upGrade:e.formUpGrade}})],1)},r=[],s=a("c7eb"),n=a("1da1"),o=(a("d81d"),a("14d9"),a("b64b"),a("d3b7"),a("25f0"),a("159b"),a("2065")),l=a("cf45"),c=a("313e"),u=a("814a"),d=a("1ab6"),m=a("584f"),f={name:"firmware-task",dicts:["iot_yes_no","oat_update_limit"],components:{selectDeviceList:d["default"]},data:function(){return{firmwareId:"",firmwareInfo:[{name:this.$t("firmware.index.222541-0"),value:""},{name:this.$t("firmware.index.222541-2"),value:""},{name:this.$t("firmware.task.222543-42"),value:""},{name:this.$t("firmware.task.222543-62"),value:""},{name:this.$t("firmware.index.222541-15"),value:""},{name:this.$t("firmware.task.222543-16"),value:""},{name:this.$t("firmware.task.222543-44"),value:""}],firmwareDevice:[{title:this.$t("firmware.task.222543-45"),num:0},{title:this.$t("firmware.task.222543-36"),num:0},{title:this.$t("firmware.task.222543-46"),num:0},{title:this.$t("firmware.task.222543-39"),num:0}],loading:!1,istrue:!1,taskParams:{pageNum:1,pageSize:10,firmwareId:"",id:""},formUpGrade:{taskName:null,firmwareId:0,deviceAmount:0,bookTime:null,upgradeType:null,upType:null,deviceList:[],version:null,flag:!1},taskTotal:0,firmwareTaskList:[],taskName:"",deviceParams:{pageNum:1,pageSize:10,firmwareId:"",taskId:"",serialNumber:"",deviceName:""},deviceTotal:0,firmwareDeviceList:[],deviceId:"",deviceName:"",serialNumber:"",taskDialogVisible:!1,taskDialogData:[{name:this.$t("firmware.task.222543-8"),value:""},{name:this.$t("firmware.task.222543-9"),value:""},{name:this.$t("firmware.task.222543-10"),value:""},{name:this.$t("firmware.task.222543-13"),value:""},{name:this.$t("firmware.task.222543-14"),value:""},{name:this.$t("firmware.task.222543-15"),value:""},{name:this.$t("firmware.task.222543-16"),value:""}],myChart:null,option:null,chartData:[],chartParam:{},deviceDialogList:[],deviceInfoParams:{pageNum:1,pageSize:10,firmwareId:"",taskId:"",upgradeStatus:"",serialNumber:""},title:"",openUpGrade:!1,deviceSerialNumber:"",deviceInfoTotal:0,deviceDialogSearchStatus:[0,0,0,0,0,0,0],deviceStatus:"all",rulesUpGrade:{taskName:[{required:!0,message:this.$t("firmware.index.222541-41"),trigger:"blur"}],upgradeType:[{required:!0,message:this.$t("firmware.task.222543-22"),trigger:"blur"}]},queryDeviceByVersion:{productId:null,firmwareVersion:0},form:{version:1}}},created:function(){var e=JSON.parse(sessionStorage.getItem("firmwareTaskInfo"));this.firmwareId=e.firmwareId,this.taskParams.firmwareId=e.firmwareId,this.deviceParams.firmwareId=e.firmwareId,this.formUpGrade.firmwareId=e.firmwareId,this.deviceInfoParams.firmwareId=e.firmwareId,this.firmwareInfo[0].value=e.firmwareName,this.firmwareInfo[1].value=e.productName,this.firmwareInfo[2].value=1==e.isLatest?this.$t("firmware.task.222543-63"):this.$t("firmware.task.222543-64"),this.firmwareInfo[3].value=1==e.firmwareType?this.$t("firmware.task.222543-65"):"http",this.firmwareInfo[4].value="Version "+e.version,this.firmwareInfo[5].value=e.createTime,this.firmwareInfo[6].value=e.remark,this.getDeviceStatistic(),this.getTaskList(),this.connectMqtt()},destroyed:function(){var e;this.mqttUnSubscribe(null===(e=this.taskDialogData[0])||void 0===e?void 0:e.value)},watch:{chartData:{immediate:!0,deep:!0,handler:function(e,t){var a=this;e.length>0&&this.$nextTick((function(){a.myChart=c["init"](a.$refs.taskChart),a.option={grid:{top:0,bottom:0,containLabel:!0},tooltip:{trigger:"item",formatter:"{b}:{c}"},series:[{name:a.$t("firmware.task.222543-33"),type:"pie",radius:["45%","70%"],label:{show:!0,formatter:"{b}:{c}\n占比:{d}%"},data:e}]},a.myChart.setOption(a.option)}))}}},methods:{getDeviceStatistic:function(){var e=this;Object(o["d"])({firmwareId:this.firmwareId}).then((function(t){if(200==t.code){var a=t.data,i=0,r=0,s=0,n=0;a.map((function(e){n+=e.deviceCount,0==e.upgradeStatus||1==e.upgradeStatus||2==e.upgradeStatus?i+=e.deviceCount:4==e.upgradeStatus||5==e.upgradeStatus?s+=e.deviceCount:r+=e.deviceCount})),e.firmwareDevice[0].num=n,e.firmwareDevice[1].num=r,e.firmwareDevice[2].num=i,e.firmwareDevice[3].num=s}}))},delay:function(){var e=this,t=5,a=setInterval((function(){e.istrue=!0,t--,t<0&&(e.istrue=!1,clearInterval(a))}),2e3)},handleDelete:function(e){var t=this,a=e.id;this.$modal.confirm(this.$t("firmware.task.222543-66",[a])).then((function(){return Object(o["b"])(a)})).then((function(){t.getTaskList(),t.getDeviceStatistic(),t.$modal.msgSuccess(t.$t("firmware.task.222543-67"))})).catch((function(){}))},getProductData:function(e){this.form.productId=e.productId,this.form.productName=e.productName},canceUpGrade:function(){this.openUpGrade=!1,this.reset()},submitFormUpGrade:function(){var e=this;this.$refs["formUpGrade"].validate((function(t){t&&(e.delay(),e.formUpGrade.deviceAmount>0?Object(o["a"])(e.formUpGrade).then((function(t){200==t.code?(e.$modal.msgSuccess(e.$t("addSuccess")),e.openUpGrade=!1,e.getTaskList(),e.getDeviceStatistic()):e.$modal.msgError(res.data.message)})):e.$modal.msgError(e.$t("firmware.index.222541-47")))}))},selectDeviceList:function(){this.formUpGrade.flag=!0,this.$refs.deviceList.openDeviceList=!0},getDeviceList:function(){var e=this;Object(o["c"])(this.deviceParams).then((function(t){e.firmwareDeviceList=t.rows,e.deviceTotal=t.total}))},reset:function(){this.form={firmwareId:0,firmwareName:null,productId:null,productName:null,tenantId:null,tenantName:null,isLatest:0,isSys:null,version:1,filePath:null,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null}},otaUpGrade:function(e){var t=this;this.formUpGrade.deviceList=[],this.formUpGrade.deviceAmount=0,this.resetForm("formUpGrade");var a=this.taskParams.firmwareId||this.ids;Object(u["c"])(a).then((function(e){t.form=e.data,t.openUpGrade=!0,t.title=t.$t("firmware.task.222543-23"),t.formUpGrade.productId=t.form.productId,t.formUpGrade.firmwareId=a}))},updateFirmwareDevice:function(){this.getDeviceStatistic()},getTaskList:function(){var e=this;Object(o["e"])(this.taskParams).then((function(t){e.firmwareTaskList=t.rows,e.taskTotal=t.total}))},taskQuery:function(){this.taskParams.taskName=this.taskName,this.getTaskList()},taskDetailClick:function(e){this.taskDialogData[0].value=e.id,this.taskDialogData[1].value=e.taskName,this.taskDialogData[2].value=1==e.upgradeType?this.$t("firmware.task.222543-68"):this.$t("firmware.task.222543-69"),this.taskDialogData[3].value=e.deviceAmount,this.taskDialogData[4].value=e.bookTime,this.taskDialogData[5].value=e.taskDesc,this.taskDialogData[6].value=e.createTime,this.deviceDialogSearchStatus[0]=e.deviceAmount,this.taskDialogVisible=!0,this.chartParam={taskId:e.id,firmwareId:e.firmwareId},this.deviceInfoParams.taskId=e.id,this.getChartData(),this.getDeviceInfoList(),this.mqttSubscribe(e.id)},getChartData:function(){var e=this;Object(o["d"])(this.chartParam).then((function(t){var a=t.data,i=[],r=[e.deviceDialogSearchStatus[0],0,0,0,0,0,0];a.map((function(t){switch(t.upgradeStatus){case 0:i.push({name:e.$t("firmware.task.222543-48"),value:t.deviceCount}),r[2]=t.deviceCount;break;case 2:i.push({name:e.$t("firmware.task.222543-38"),value:t.deviceCount}),r[4]=t.deviceCount;break;case 3:i.push({name:e.$t("firmware.task.222543-36"),value:t.deviceCount}),r[1]=t.deviceCount;break;case 4:i.push({name:e.$t("firmware.task.222543-39"),value:t.deviceCount}),r[5]=t.deviceCount;break;case 5:i.push({name:e.$t("firmware.task.222543-40"),value:t.deviceCount}),r[6]=t.deviceCount;break}})),e.chartData=i,e.deviceDialogSearchStatus=r}))},chartRefresh:function(){this.getChartData()},getDeviceInfoList:function(){var e=this;Object(o["c"])(this.deviceInfoParams).then((function(t){e.deviceDialogList=t.rows,e.deviceInfoTotal=t.total}))},deviceInfoQuery:function(){this.deviceInfoParams.serialNumber=this.deviceSerialNumber,this.getDeviceInfoList()},deviceStatusClick:function(e){this.deviceStatus=e,this.deviceInfoParams.upgradeStatus="all"===e?"":e,this.getDeviceInfoList()},handleDialogCancel:function(){var e;this.taskDialogVisible=!1,this.deviceStatus="all",this.deviceSerialNumber="",this.deviceInfoParams.upgradeStatus="",this.deviceInfoParams.serialNumber="",this.mqttUnSubscribe(null===(e=this.taskDialogData[0])||void 0===e?void 0:e.value)},connectMqtt:function(){var e=this;return Object(n["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null!=e.$mqttTool.client){t.next=5;break}return t.next=3,e.$mqttTool.connect(e.vuex_token);case 3:t.next=6;break;case 5:e.$mqttTool.client.removeAllListeners("message");case 6:e.mqttCallback();case 7:case"end":return t.stop()}}),t)})))()},mqttCallback:function(){var e=this;this.$mqttTool.client.on("message",(function(t,a,i){a=JSON.parse(a.toString("utf8")),a&&(e.setMqttData(a),console.log(a,"Mqtt message"))}))},mqttSubscribe:function(e){if(e){var t=[],a="/"+e+"/ws/ota/status";t.push(a),this.$mqttTool.subscribe(t)}},mqttUnSubscribe:function(e){if(e){var t=[],a="/"+e+"/ws/ota/status";t.push(a),this.$mqttTool.unsubscribe(t)}},setMqttData:function(e){for(var t=0;t<this.deviceDialogList.length;t++){var a=this.deviceDialogList[t];if(a.serialNumber===e.serialNumber){a.upgradeStatus=e.status,a.updateTime=Object(l["d"])(e.timestamp,"Y-M-D h:m:s"),a.detailMsg=this.getDetailMsg(e.status),e.progress&&(a.progress=e.progress);break}}},getDetailMsg:function(e){var t={0:this.$t("firmware.task.222543-70"),1:this.$t("firmware.task.222543-71"),2:this.$t("firmware.task.222543-72"),3:this.$t("firmware.task.222543-73"),4:this.$t("firmware.task.222543-74"),5:this.$t("firmware.task.222543-75"),6:this.$t("firmware.task.222543-76")};return t[e]||""},changeUpType:function(e){var t=this;this.resetDeviceList(),"1"==e&&(this.queryDeviceByVersion.productId=this.formUpGrade.productId,this.queryDeviceByVersion.firmwareVersion=this.formUpGrade.version,Object(m["o"])(this.queryDeviceByVersion).then((function(e){e.rows.forEach((function(e){t.formUpGrade.deviceList.push(e.serialNumber)})),t.formUpGrade.deviceAmount=e.total})))},changeUpgradeType:function(e){this.resetDeviceList(),2==e&&(this.formUpGrade.version=null)},resetDeviceList:function(){this.formUpGrade.upgradeType=this.formUpGrade.upgradeType,this.formUpGrade.deviceList=[],this.formUpGrade.deviceAmount=0},upgradeTaskHandle:function(e){var t=this,a=e.firmwareId,i=e.taskId,r=e.serialNumber,s={firmwareId:a,taskId:i,devices:[r],upgradeType:2};Object(o["f"])(s).then((function(e){200===e.code?t.$message({message:e.msg||t.$t("firmware.task.222543-77"),type:"success"}):t.$message.error(e.msg||t.$t("firmware.task.222543-78"))}))}}},p=f,v=(a("d891"),a("2877")),h=Object(v["a"])(p,i,r,!1,null,"4ded97a6",null);t["default"]=h.exports},d362:function(e,t,a){},d891:function(e,t,a){"use strict";a("d362")}}]);