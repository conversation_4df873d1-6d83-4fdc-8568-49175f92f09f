(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b47192a2","chunk-09e59946"],{"09a1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"6px"}},[a("el-dialog",{attrs:{title:"编辑参数",visible:e.openEdit,width:"900px","append-to-body":""},on:{"update:visible":function(t){e.openEdit=t}}},[a("div",{staticStyle:{margin:"-30px 0 30px","background-color":"#ddd",height:"1px"}}),a("el-row",[a("el-col",{staticStyle:{border:"1px solid #ddd","border-radius":"5px",padding:"10px","background-color":"#eee"},attrs:{span:12}},[a("el-form",{attrs:{model:e.queryParams,inline:!0,"label-width":"48px",size:"small"}},[a("el-form-item",{attrs:{label:"",prop:"templateName"}},[a("el-input",{staticStyle:{width:"160px"},attrs:{placeholder:"请输入物模型名称",clearable:"",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.templateName,callback:function(t){e.$set(e.queryParams,"templateName",t)},expression:"queryParams.templateName"}})],1),a("el-form-item",[a("el-button",{staticStyle:{padding:"5px"},attrs:{type:"info",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")])],1),a("el-form-item",[a("el-link",{staticStyle:{"margin-left":"20px"},attrs:{underline:!1,icon:"el-icon-info",type:"primary"}},[e._v("单击应用模板")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.templateList,size:"mini","highlight-current-row":"",border:!1,"show-header":!1,"row-style":{backgroundColor:"#eee"}},on:{"row-click":e.rowClick}},[a("el-table-column",{attrs:{label:"选择",width:"30",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("input",{attrs:{type:"radio",disabled:"array"==e.row.datatype||"object"==e.row.datatype,name:"template"},domProps:{checked:e.row.isSelect}})]}}])}),a("el-table-column",{attrs:{label:"名称",align:"left",prop:"templateName"}}),a("el-table-column",{attrs:{label:"标识符",align:"left",prop:"identifier"}}),a("el-table-column",{attrs:{label:"数据类型",align:"center",prop:"datatype",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_data_type,value:t.row.datatype}})]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticStyle:{margin:"0 0 10px","background-color":"#eee"},attrs:{small:"",layout:"prev, pager, next",total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"参数名称",prop:"name"}},[a("el-input",{staticStyle:{width:"270px"},attrs:{placeholder:"例如：温度",size:"small"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"参数标识",prop:"id"}},[a("el-input",{staticStyle:{width:"270px"},attrs:{placeholder:"例如：temperature",size:"small"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1),a("el-form-item",{attrs:{label:"参数排序",prop:"order"}},[a("el-input-number",{staticStyle:{width:"270px"},attrs:{"controls-position":"right",placeholder:"请输入排序",type:"number",size:"small"},model:{value:e.form.order,callback:function(t){e.$set(e.form,"order",t)},expression:"form.order"}})],1),a("el-form-item",{attrs:{label:"参数特性",prop:"property"}},[a("el-checkbox",{attrs:{name:"isChart",label:"图表展示","true-label":1,"false-label":0},on:{change:e.isChartChange},model:{value:e.form.isChart,callback:function(t){e.$set(e.form,"isChart",t)},expression:"form.isChart"}}),a("el-checkbox",{attrs:{name:"isMonitor",label:"实时监测","true-label":1,"false-label":0},on:{change:e.isMonitorChange},model:{value:e.form.isMonitor,callback:function(t){e.$set(e.form,"isMonitor",t)},expression:"form.isMonitor"}}),a("el-checkbox",{attrs:{name:"isReadonly",label:"只读数据","true-label":1,"false-label":0},on:{change:e.isReadonlyChange},model:{value:e.form.isReadonly,callback:function(t){e.$set(e.form,"isReadonly",t)},expression:"form.isReadonly"}}),a("el-checkbox",{attrs:{name:"isHistory",label:"历史存储","true-label":1,"false-label":0},model:{value:e.form.isHistory,callback:function(t){e.$set(e.form,"isHistory",t)},expression:"form.isHistory"}}),a("el-checkbox",{attrs:{name:"isSharePerm",label:"分享权限","true-label":1,"false-label":0},model:{value:e.form.isSharePerm,callback:function(t){e.$set(e.form,"isSharePerm",t)},expression:"form.isSharePerm"}})],1),a("div",{staticStyle:{"margin-bottom":"20px","background-color":"#ddd",height:"1px"}}),a("el-form-item",{attrs:{label:"数据类型",prop:"datatype"}},[a("el-select",{staticStyle:{width:"125px"},attrs:{placeholder:"请选择数据类型",size:"small"},model:{value:e.form.datatype,callback:function(t){e.$set(e.form,"datatype",t)},expression:"form.datatype"}},[a("el-option",{key:"integer",attrs:{label:"整数",value:"integer"}}),a("el-option",{key:"decimal",attrs:{label:"小数",value:"decimal"}}),a("el-option",{key:"bool",attrs:{label:"布尔",value:"bool",disabled:1==e.form.isChart}}),a("el-option",{key:"enum",attrs:{label:"枚举",value:"enum",disabled:1==e.form.isChart}}),a("el-option",{key:"string",attrs:{label:"字符串",value:"string",disabled:1==e.form.isChart}})],1)],1),"integer"==e.form.datatype||"decimal"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"取值范围"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"最小值",type:"number",size:"small"},model:{value:e.form.specs.min,callback:function(t){e.$set(e.form.specs,"min",t)},expression:"form.specs.min"}})],1),a("el-col",{attrs:{span:4,align:"center"}},[e._v("到")]),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"最大值",type:"number",size:"small"},model:{value:e.form.specs.max,callback:function(t){e.$set(e.form.specs,"max",t)},expression:"form.specs.max"}})],1)],1)],1),a("el-form-item",{attrs:{label:"单位"}},[a("el-input",{staticStyle:{width:"308px"},attrs:{placeholder:"例如：℃",size:"small"},model:{value:e.form.specs.unit,callback:function(t){e.$set(e.form.specs,"unit",t)},expression:"form.specs.unit"}})],1),a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{staticStyle:{width:"308px"},attrs:{"controls-position":"right",placeholder:"例如：1",type:"number",size:"small"},model:{value:e.form.specs.step,callback:function(t){e.$set(e.form.specs,"step",t)},expression:"form.specs.step"}})],1)],1):e._e(),"bool"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"布尔值",prop:""}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：关闭",size:"small"},model:{value:e.form.specs.falseText,callback:function(t){e.$set(e.form.specs,"falseText",t)},expression:"form.specs.falseText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（0 值对应文本）")])],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：打开",size:"small"},model:{value:e.form.specs.trueText,callback:function(t){e.$set(e.form.specs,"trueText",t)},expression:"form.specs.trueText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（1 值对应文本）")])],1)],1)],1):e._e(),"enum"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"展示方式"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择展示方式"},model:{value:e.form.specs.showWay,callback:function(t){e.$set(e.form.specs,"showWay",t)},expression:"form.specs.showWay"}},[a("el-option",{key:"select",attrs:{label:"下拉框",value:"select"}}),a("el-option",{key:"button",attrs:{label:"按钮",value:"button"}})],1)],1),a("el-form-item",{attrs:{label:"枚举项",prop:""}},[e._l(e.form.specs.enumList,(function(t,r){return a("el-row",{key:"enum"+r,staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"例如：0",size:"small"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-input",{attrs:{placeholder:"例如：中速挡位",size:"small"},model:{value:t.text,callback:function(a){e.$set(t,"text",a)},expression:"item.text"}})],1),0!=r?a("el-col",{attrs:{span:3,offset:1}},[a("a",{staticStyle:{color:"#f56c6c"},on:{click:function(t){return e.removeEnumItem(r)}}},[e._v("删除")])]):e._e()],1)})),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addEnumItem()}}},[e._v("添加枚举项")])])],2)],1):e._e(),"string"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"最大长度",prop:""}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"例如：1024",type:"number",size:"small"},model:{value:e.form.specs.maxLength,callback:function(t){e.$set(e.form.specs,"maxLength",t)},expression:"form.specs.maxLength"}})],1)],1)],1)],1):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],o=(a("14d9"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("cec4")),i={name:"things_parameter",dicts:["iot_things_type","iot_data_type","iot_yes_no"],props:{data:{type:Object,default:null}},watch:{data:function(e,t){this.index=e.index,e&&e.parameter.name&&""!=e.parameter.name&&(this.form.name=e.parameter.name,this.form.id=e.parameter.id,this.form.order=e.parameter.order,this.form.isChart=e.parameter.isChart?e.parameter.isChart:0,this.form.isHistory=e.parameter.isHistory?e.parameter.isHistory:1,this.form.isSharePerm=e.parameter.isSharePerm?e.parameter.isSharePerm:0,this.form.isMonitor=e.parameter.isMonitor?e.parameter.isMonitor:0,this.form.isReadonly=e.parameter.isReadonly?e.parameter.isReadonly:0,this.form.specs=e.parameter.datatype,this.form.datatype=this.form.specs.type,this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer")),this.openEdit=!0,this.getList()}},data:function(){return{loading:!0,total:0,templateList:[],openEdit:!1,queryParams:{pageNum:1,pageSize:10,name:null,type:null},index:-1,form:{},rules:{name:[{required:!0,message:"参数名称不能为空",trigger:"blur"}],id:[{required:!0,message:"参数标识符不能为空",trigger:"blur"}],order:[{required:!0,message:"模型排序不能为空",trigger:"blur"}],datatype:[{required:!0,message:"数据类型不能为空",trigger:"change"}]}}},created:function(){this.getList(),this.reset()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){for(var a=0;a<t.rows.length;a++)t.rows[a].isSelect=!1;e.templateList=t.rows,e.total=t.total,e.setRadioSelected(e.productId),e.loading=!1}))},rowClick:function(e){null!=e&&"array"!=e.datatype&&"object"!=e.datatype&&(this.form.name=e.templateName,this.form.id=e.identifier,this.form.order=e.modelOrder,this.form.isChart=e.isChart?e.isChart:0,this.form.isHistory=e.isHistory?e.isHistory:1,this.form.isSharePerm=e.isSharePerm?e.isSharePerm:0,this.form.isReadonly=e.isReadonly?e.isReadonly:0,this.form.isMonitor=e.isMonitor?e.isMonitor:0,this.form.datatype=e.datatype,this.form.specs=JSON.parse(e.specs),this.form.specs.enumList||(this.form.specs.enumList=[{value:"",text:""}]),this.form.specs.arrayType||(this.form.specs.arrayType="integer"),this.setRadioSelected(e.templateId))},setRadioSelected:function(e){for(var t=0;t<this.templateList.length;t++)this.templateList[t].templateId==e?this.templateList[t].isSelect=!0:this.templateList[t].isSelect=!1},cancel:function(){this.openEdit=!1,this.reset()},reset:function(){this.index=-1,this.form={name:null,id:null,order:0,datatype:"integer",isChart:0,isHistory:1,isSharePerm:0,isMonitor:0,isReadonly:0,specs:{enumList:[{value:"",text:""}],showWay:"select"}},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){e.form.datatype=e.formatThingsSpecs(),delete e.form.specs,e.openEdit=!1;var a={parameter:JSON.parse(JSON.stringify(e.form)),index:e.index};console.log("data",a),e.$emit("dataEvent",a),e.reset()}}))},isChartChange:function(){1==this.form.isChart?this.form.isReadonly=1:this.form.isMonitor=0},isMonitorChange:function(){1==this.form.isMonitor&&(this.form.isReadonly=1,this.form.isChart=1)},isReadonlyChange:function(){0==this.form.isReadonly&&(this.form.isMonitor=0,this.form.isChart=0)},formatThingsSpecs:function(){var e={};return e.type=this.form.datatype,"integer"==this.form.datatype||"decimal"==this.form.datatype?(e.min=Number(this.form.specs.min?this.form.specs.min:0),e.max=Number(this.form.specs.max?this.form.specs.max:100),e.unit=this.form.specs.unit?this.form.specs.unit:"",e.step=Number(this.form.specs.step?this.form.specs.step:1)):"string"==this.form.datatype?e.maxLength=Number(this.form.specs.maxLength?this.form.specs.maxLength:1024):"bool"==this.form.datatype?(e.falseText=this.form.specs.falseText?this.form.specs.falseText:"关闭",e.trueText=this.form.specs.trueText?this.form.specs.trueText:"打开"):"array"==this.form.datatype?e.arrayType=this.form.specs.arrayType:"enum"==this.form.datatype&&(e.showWay=this.form.specs.showWay,this.form.specs.enumList&&""!=this.form.specs.enumList[0].text?e.enumList=this.form.specs.enumList:(e.showWay="select",e.enumList=[{value:"0",text:"低"},{value:"1",text:"高"}])),e},addEnumItem:function(){this.form.specs.enumList.push({value:"",text:""})},removeEnumItem:function(e){this.form.specs.enumList.splice(e,1)}}},l=i,n=(a("4df4f"),a("2877")),m=Object(n["a"])(l,r,s,!1,null,null,null);t["default"]=m.exports},"4df4f":function(e,t,a){"use strict";a("c23a")},"4e6e":function(e,t,a){},c23a:function(e,t,a){},ca90:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{padding:"6px"}},[a("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],staticStyle:{"margin-bottom":"5px"}},[a("el-form",{ref:"queryForm",staticStyle:{"margin-bottom":"-20px"},attrs:{model:e.queryParams,inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"名称",prop:"templateName"}},[a("el-input",{attrs:{placeholder:"请输入物模型名称",clearable:"",size:"small"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.templateName,callback:function(t){e.$set(e.queryParams,"templateName",t)},expression:"queryParams.templateName"}})],1),a("el-form-item",{attrs:{label:"类别",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择模型类别",clearable:"",size:"small"},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}},e._l(e.dict.type.iot_things_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1),a("el-form-item",{staticStyle:{float:"right"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:template:add"],expression:"['iot:template:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1)],1)],1),a("el-card",{staticStyle:{"padding-bottom":"100px"}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.templateList,border:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{label:"名称",align:"center",prop:"templateName"}}),a("el-table-column",{attrs:{label:"标识符",align:"center",prop:"identifier"}}),a("el-table-column",{attrs:{label:"图表展示",align:"center",prop:"isMonitor",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isChart}})]}}])}),a("el-table-column",{attrs:{label:"实时监测",align:"center",prop:"",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isMonitor}})]}}])}),a("el-table-column",{attrs:{label:"只读",align:"center",prop:"",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isReadonly}})]}}])}),a("el-table-column",{attrs:{label:"历史存储",align:"center",prop:"",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isHistory}})]}}])}),a("el-table-column",{attrs:{label:"系统定义",align:"center",prop:"isSys",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_yes_no,value:t.row.isSys}})]}}])}),a("el-table-column",{attrs:{label:"物模型类别",align:"center",prop:"type",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_things_type,value:t.row.type}})]}}])}),a("el-table-column",{attrs:{label:"数据类型",align:"center",prop:"datatype",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.iot_data_type,value:t.row.datatype}})]}}])}),a("el-table-column",{attrs:{label:"数据定义",align:"left","header-align":"center",prop:"specs","min-width":"150","class-name":"specsColor"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{domProps:{innerHTML:e._s(e.formatSpecsDisplay(t.row.specs))}})]}}])}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"modelOrder",width:"80"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"!=t.row.isSys&&e.isTenant?e._e():a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:template:query"],expression:"['iot:template:query']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"primary",icon:"el-icon-view"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v(" 查看 ")]),"0"!=t.row.isSys&&e.isTenant?e._e():a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:template:remove"],expression:"['iot:template:remove']"}],staticStyle:{padding:"5px"},attrs:{size:"small",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(" 删除 ")]),"1"==t.row.isSys&&e.isTenant?a("span",{staticStyle:{"font-size":"10px",color:"#999"}},[e._v("系统定义，不能修改")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"模型名称",prop:"templateName"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入物模型名称，例如：温度"},model:{value:e.form.templateName,callback:function(t){e.$set(e.form,"templateName",t)},expression:"form.templateName"}})],1),a("el-form-item",{attrs:{label:"模型标识",prop:"identifier"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入标识符，例如：temperature"},model:{value:e.form.identifier,callback:function(t){e.$set(e.form,"identifier",t)},expression:"form.identifier"}})],1),a("el-form-item",{attrs:{label:"模型排序",prop:"modelOrder"}},[a("el-input-number",{staticStyle:{width:"385px"},attrs:{"controls-position":"right",placeholder:"请输入排序",type:"number"},model:{value:e.form.modelOrder,callback:function(t){e.$set(e.form,"modelOrder",t)},expression:"form.modelOrder"}})],1),a("el-form-item",{attrs:{label:"模型类别",prop:"type"}},[a("el-radio-group",{on:{change:function(t){return e.typeChange(e.form.type)}},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("属性")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("功能")]),a("el-radio-button",{attrs:{label:"3"}},[e._v("事件")])],1)],1),a("el-form-item",{attrs:{label:"模型特性",prop:"property"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-tooltip",{attrs:{effect:"dark",content:"设备详情中以图表方式展示",placement:"top"}},[a("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:1==e.form.type,expression:"form.type == 1"}],attrs:{name:"isChart",label:"图表展示","true-label":1,"false-label":0},on:{change:e.isChartChange},model:{value:e.form.isChart,callback:function(t){e.$set(e.form,"isChart",t)},expression:"form.isChart"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-tooltip",{attrs:{effect:"dark",content:"实时显示监测数据，但是不会存储到数据库",placement:"top"}},[a("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:1==e.form.type,expression:"form.type == 1"}],attrs:{name:"isMonitor",label:"实时监测","true-label":1,"false-label":0},on:{change:e.isMonitorChange},model:{value:e.form.isMonitor,callback:function(t){e.$set(e.form,"isMonitor",t)},expression:"form.isMonitor"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-tooltip",{attrs:{effect:"dark",content:"设备上报数据，但是平台不能下发指令",placement:"top"}},[a("el-checkbox",{attrs:{name:"isReadonly",label:"只读数据",disabled:3==e.form.type,"true-label":1,"false-label":0},on:{change:e.isReadonlyChange},model:{value:e.form.isReadonly,callback:function(t){e.$set(e.form,"isReadonly",t)},expression:"form.isReadonly"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-tooltip",{attrs:{effect:"dark",content:"设备上报的数据会存储到数据库作为历史数据",placement:"top"}},[a("el-checkbox",{attrs:{name:"isHistory",label:"历史存储","true-label":1,"false-label":0},model:{value:e.form.isHistory,callback:function(t){e.$set(e.form,"isHistory",t)},expression:"form.isHistory"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-tooltip",{attrs:{effect:"dark",content:"设备分享时需要指定是否拥有该权限",placement:"top"}},[a("el-checkbox",{attrs:{name:"isSharePerm",label:"分享权限","true-label":1,"false-label":0},model:{value:e.form.isSharePerm,callback:function(t){e.$set(e.form,"isSharePerm",t)},expression:"form.isSharePerm"}})],1)],1)],1)],1),a("el-divider"),a("el-form-item",{attrs:{label:"数据类型",prop:"datatype"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择数据类型"},on:{change:e.dataTypeChange},model:{value:e.form.datatype,callback:function(t){e.$set(e.form,"datatype",t)},expression:"form.datatype"}},[a("el-option",{key:"integer",attrs:{label:"整数",value:"integer"}}),a("el-option",{key:"decimal",attrs:{label:"小数",value:"decimal"}}),a("el-option",{key:"bool",attrs:{label:"布尔",value:"bool",disabled:1==e.form.isChart}}),a("el-option",{key:"enum",attrs:{label:"枚举",value:"enum",disabled:1==e.form.isChart}}),a("el-option",{key:"string",attrs:{label:"字符串",value:"string",disabled:1==e.form.isChart}}),a("el-option",{key:"array",attrs:{label:"数组",value:"array",disabled:1==e.form.isChart}}),a("el-option",{key:"object",attrs:{label:"对象",value:"object",disabled:1==e.form.isChart}})],1)],1),"integer"==e.form.datatype||"decimal"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"取值范围"}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"最小值",type:"number"},model:{value:e.form.specs.min,callback:function(t){e.$set(e.form.specs,"min",t)},expression:"form.specs.min"}})],1),a("el-col",{attrs:{span:2,align:"center"}},[e._v("到")]),a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"最大值",type:"number"},model:{value:e.form.specs.max,callback:function(t){e.$set(e.form.specs,"max",t)},expression:"form.specs.max"}})],1)],1)],1),a("el-form-item",{attrs:{label:"单位"}},[a("el-input",{staticStyle:{width:"385px"},attrs:{placeholder:"请输入单位，例如：℃"},model:{value:e.form.specs.unit,callback:function(t){e.$set(e.form.specs,"unit",t)},expression:"form.specs.unit"}})],1),a("el-form-item",{attrs:{label:"步长"}},[a("el-input-number",{staticStyle:{width:"385px"},attrs:{"controls-position":"right",placeholder:"请输入步长，例如：1",type:"number"},model:{value:e.form.specs.step,callback:function(t){e.$set(e.form.specs,"step",t)},expression:"form.specs.step"}})],1)],1):e._e(),"bool"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"布尔值",prop:""}},[a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：关闭"},model:{value:e.form.specs.falseText,callback:function(t){e.$set(e.form.specs,"falseText",t)},expression:"form.specs.falseText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（0 值对应文本）")])],1),a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：打开"},model:{value:e.form.specs.trueText,callback:function(t){e.$set(e.form.specs,"trueText",t)},expression:"form.specs.trueText"}})],1),a("el-col",{attrs:{span:10,offset:1}},[e._v("（1 值对应文本）")])],1)],1)],1):e._e(),"enum"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"展示方式"}},[a("el-select",{staticStyle:{width:"175px"},attrs:{placeholder:"请选择展示方式"},model:{value:e.form.specs.showWay,callback:function(t){e.$set(e.form.specs,"showWay",t)},expression:"form.specs.showWay"}},[a("el-option",{key:"select",attrs:{label:"下拉框",value:"select"}}),a("el-option",{key:"button",attrs:{label:"按钮",value:"button"}})],1)],1),a("el-form-item",{attrs:{label:"枚举项",prop:""}},[e._l(e.form.specs.enumList,(function(t,r){return a("el-row",{key:"enum"+r,staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"参数值，例如：0"},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"item.value"}})],1),a("el-col",{attrs:{span:11,offset:1}},[a("el-input",{attrs:{placeholder:"参数描述，例如：中速档位"},model:{value:t.text,callback:function(a){e.$set(t,"text",a)},expression:"item.text"}})],1),0!=r?a("el-col",{attrs:{span:2,offset:1}},[a("a",{staticStyle:{color:"#f56c6c"},on:{click:function(t){return e.removeEnumItem(r)}}},[e._v("删除")])]):e._e()],1)})),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addEnumItem()}}},[e._v("添加枚举项")])])],2)],1):e._e(),"string"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"最大长度",prop:""}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：1024",type:"number"},model:{value:e.form.specs.maxLength,callback:function(t){e.$set(e.form.specs,"maxLength",t)},expression:"form.specs.maxLength"}})],1),a("el-col",{attrs:{span:14,offset:1}},[e._v("（字符串的最大长度）")])],1)],1)],1):e._e(),"array"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"元素个数",prop:""}},[a("el-row",[a("el-col",{attrs:{span:9}},[a("el-input",{attrs:{placeholder:"例如：5",type:"number"},model:{value:e.form.specs.arrayCount,callback:function(t){e.$set(e.form.specs,"arrayCount",t)},expression:"form.specs.arrayCount"}})],1)],1)],1),a("el-form-item",{attrs:{label:"数组类型",prop:""}},[a("el-radio-group",{model:{value:e.form.specs.arrayType,callback:function(t){e.$set(e.form.specs,"arrayType",t)},expression:"form.specs.arrayType"}},[a("el-radio",{attrs:{label:"integer"}},[e._v("整数")]),a("el-radio",{attrs:{label:"decimal"}},[e._v("小数")]),a("el-radio",{attrs:{label:"string"}},[e._v("字符串")]),a("el-radio",{attrs:{label:"object"}},[e._v("对象")])],1)],1),"object"==e.form.specs.arrayType?a("el-form-item",{attrs:{label:"对象参数"}},[a("div",{staticStyle:{"background-color":"#f8f8f8","border-radius":"5px"}},e._l(e.form.specs.params,(function(t,r){return a("el-row",{key:r,staticStyle:{padding:"0 10px 5px"}},[0==r?a("div",{staticStyle:{"margin-top":"5px"}}):e._e(),a("el-col",{attrs:{span:18}},[a("el-input",{staticStyle:{"margin-top":"3px"},attrs:{readonly:"",size:"mini",placeholder:"请选择设备"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}},[a("template",{slot:"prepend"},[a("el-tag",{staticStyle:{"margin-left":"-21px",height:"26px","line-height":"26px"},attrs:{size:"mini",effect:"dark"}},[e._v(e._s(t.order))]),e._v(" "+e._s(e.form.identifier+"_"+t.id)+" ")],1),a("el-button",{attrs:{slot:"append",size:"small"},on:{click:function(a){return e.editParameter(t,r)}},slot:"append"},[e._v("编辑")])],2)],1),a("el-col",{attrs:{span:2,offset:2}},[a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",plain:"",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.removeParameter(r)}}},[e._v("删除")])],1)],1)})),1),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addParameter()}}},[e._v("添加参数")])])]):e._e()],1):e._e(),"object"==e.form.datatype?a("div",[a("el-form-item",{attrs:{label:"对象参数",prop:""}},[a("div",{staticStyle:{"background-color":"#f8f8f8","border-radius":"5px"}},e._l(e.form.specs.params,(function(t,r){return a("el-row",{key:r,staticStyle:{padding:"0 10px 5px"}},[0==r?a("div",{staticStyle:{"margin-top":"5px"}}):e._e(),a("el-col",{attrs:{span:18}},[a("el-input",{staticStyle:{"margin-top":"3px"},attrs:{readonly:"",size:"mini",placeholder:"请选择设备"},model:{value:t.name,callback:function(a){e.$set(t,"name",a)},expression:"item.name"}},[a("template",{slot:"prepend"},[a("el-tag",{staticStyle:{"margin-left":"-21px",height:"26px","line-height":"26px"},attrs:{size:"mini",effect:"dark"}},[e._v(e._s(t.order))]),e._v(" "+e._s(e.form.identifier+"_"+t.id)+" ")],1),a("el-button",{attrs:{slot:"append"},on:{click:function(a){return e.editParameter(t,r)}},slot:"append"},[e._v("编辑")])],2)],1),a("el-col",{attrs:{span:2,offset:2}},[a("el-button",{staticStyle:{padding:"5px"},attrs:{size:"small",plain:"",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.removeParameter(r)}}},[e._v("删除")])],1)],1)})),1),a("div",[e._v(" + "),a("a",{staticStyle:{color:"#409eff"},on:{click:function(t){return e.addParameter()}}},[e._v("添加参数")])])])],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:template:edit"],expression:"['iot:template:edit']"},{name:"show",rawName:"v-show",value:e.form.templateId,expression:"form.templateId"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("修 改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["iot:template:add"],expression:"['iot:template:add']"},{name:"show",rawName:"v-show",value:!e.form.templateId,expression:"!form.templateId"}],attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("新 增")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("things-parameter",{attrs:{data:e.paramData},on:{dataEvent:function(t){return e.getParamData(t)}}})],1)],1)},s=[],o=a("5530"),i=(a("d81d"),a("14d9"),a("4e82"),a("a434"),a("b0c0"),a("e9c4"),a("a9e3"),a("b64b"),a("ac1f"),a("00b4"),a("cec4")),l=a("09a1"),n={name:"Template",dicts:["iot_things_type","iot_data_type","iot_yes_no"],components:{thingsParameter:l["default"]},data:function(){return{isTenant:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,templateList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,templateName:null,type:null},form:{},paramData:{index:-1,parameter:{}},rules:{templateName:[{required:!0,message:"物模型名称不能为空",trigger:"blur"}],identifier:[{required:!0,message:"标识符，产品下唯一不能为空",trigger:"blur"}],modelOrder:[{required:!0,message:"模型排序不能为空",trigger:"blur"}],type:[{required:!0,message:"模型类别不能为空",trigger:"change"}],datatype:[{required:!0,message:"数据类型不能为空",trigger:"change"}]}}},created:function(){this.getList(),this.init()},methods:{init:function(){-1!==this.$store.state.user.roles.indexOf("tenant")&&(this.isTenant=!0)},getList:function(){var e=this;this.loading=!0,Object(i["e"])(this.queryParams).then((function(t){e.templateList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={templateId:null,templateName:null,userId:null,userName:null,tenantId:null,tenantName:null,identifier:null,modelOrder:0,type:1,datatype:"integer",isSys:null,isChart:1,isHistory:1,isMonitor:1,isReadonly:1,isSharePerm:1,delFlag:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null,specs:{enumList:[{value:"",text:""}],arrayType:"integer",arrayCount:5,showWay:"select",params:[]}},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.templateId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加通用物模型"},handleUpdate:function(e){var t=this;this.reset();var a=e.templateId||this.ids;Object(i["d"])(a).then((function(e){var a=e.data;if(t.open=!0,t.title="修改通用物模型",a.specs=JSON.parse(a.specs),a.specs.enumList||(a.specs.showWay="select",a.specs.enumList=[{value:"",text:""}]),a.specs.arrayType||(a.specs.arrayType="integer"),a.specs.arrayCount||(a.specs.arrayCount=5),a.specs.params||(a.specs.params=[]),"array"==a.specs.type&&"object"==a.specs.arrayType||"object"==a.specs.type)for(var r=0;r<a.specs.params.length;r++)a.specs.params[r].id=String(a.specs.params[r].id).substring(String(a.identifier).length+1);t.form=a}))},containsUnderscore:function(e){return/_/.test(e)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){if("object"==e.form.datatype||"array"==e.form.datatype&&"object"==e.form.specs.arrayType){if(!e.form.specs.params||0==e.form.specs.params)return void e.$modal.msgError("对象的参数不能为空");if(e.containsUnderscore(e.form.identifier))return void e.$modal.msgError("对象类型模型标识输入不能包含下划线，请重新填写模型标识！")}if(e.form.specs.params&&e.form.specs.params.length>0)for(var a=e.form.specs.params.map((function(e){return e.id})).sort(),r=0;r<a.length;r++)if(a[r]==a[r+1])return void e.$modal.msgError("参数标识 "+a[r]+" 重复");if(1==e.form.isChart&&"integer"!=e.form.datatype&&1==e.form.isChart&&"decimal"!=e.form.datatype)e.$modal.msgError("请重新选择数据类型！");else if(null!=e.form.templateId){var s=JSON.parse(JSON.stringify(e.form));s.specs=e.formatThingsSpecs(),(2==e.form.type||3==e.form.type)&&(s.isMonitor=0,s.isChart=0),s.updateBy=e.$store.state.user.name,Object(i["f"])(s).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()}))}else{var o=JSON.parse(JSON.stringify(e.form));o.specs=e.formatThingsSpecs(),2==e.form.type?o.isMonitor=0:3==e.form.type&&(o.isMonitor=0,o.isChart=0),o.createBy=e.$store.state.user.name,Object(i["a"])(o).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()}))}}}))},handleDelete:function(e){var t=this,a=e.templateId||this.ids;this.$modal.confirm('是否确认删除通用物模型编号为"'+a+'"的数据项？').then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("iot/template/export",Object(o["a"])({},this.queryParams),"template_".concat((new Date).getTime(),".xlsx"))},typeChange:function(e){console.log(e),1==e?(this.form.isChart=1,this.form.isHistory=1,this.form.isMonitor=1,this.form.isReadonly=1,this.form.isSharePerm=1,this.form.datatype="integer"):2==e?(this.form.isChart=0,this.form.isHistory=1,this.form.isSharePerm=1,this.form.isMonitor=0,this.form.isReadonly=0):3==e&&(this.form.isChart=0,this.form.isHistory=1,this.form.isMonitor=0,this.form.isReadonly=1,this.form.isSharePerm=0)},isChartChange:function(){1==this.form.isChart?this.form.isReadonly=1:this.form.isMonitor=0},isMonitorChange:function(){1==this.form.isMonitor&&(this.form.isReadonly=1,this.form.isChart=1)},isReadonlyChange:function(){0==this.form.isReadonly&&(this.form.isMonitor=0,this.form.isChart=0)},formatThingsSpecs:function(){var e={};if(e.type=this.form.datatype,"integer"==this.form.datatype||"decimal"==this.form.datatype)e.min=Number(this.form.specs.min?this.form.specs.min:0),e.max=Number(this.form.specs.max?this.form.specs.max:100),e.unit=this.form.specs.unit?this.form.specs.unit:"",e.step=Number(this.form.specs.step?this.form.specs.step:1);else if("string"==this.form.datatype)e.maxLength=Number(this.form.specs.maxLength?this.form.specs.maxLength:1024);else if("bool"==this.form.datatype)e.falseText=this.form.specs.falseText?this.form.specs.falseText:"关闭",e.trueText=this.form.specs.trueText?this.form.specs.trueText:"打开";else if("enum"==this.form.datatype)e.showWay=this.form.specs.showWay,this.form.specs.enumList&&""!=this.form.specs.enumList[0].text?e.enumList=this.form.specs.enumList:(e.showWay="select",e.enumList=[{value:"0",text:"低"},{value:"1",text:"高"}]);else if("array"==this.form.datatype){if(e.arrayType=this.form.specs.arrayType,e.arrayCount=this.form.specs.arrayCount?this.form.specs.arrayCount:5,"object"==e.arrayType){e.params=this.form.specs.params;for(var t=0;t<e.params.length;t++)e.params[t].id=this.form.identifier+"_"+e.params[t].id}}else if("object"==this.form.datatype){e.params=this.form.specs.params;for(var a=0;a<e.params.length;a++)e.params[a].id=this.form.identifier+"_"+e.params[a].id}return JSON.stringify(e)},dataTypeChange:function(e){},addEnumItem:function(){this.form.specs.enumList.push({value:"",text:""})},removeEnumItem:function(e){this.form.specs.enumList.splice(e,1)},formatSpecsDisplay:function(e){if(null!=e&&void 0!=e){var t=JSON.parse(e);if("integer"===t.type||"decimal"===t.type)return"<span style='width:50%;display:inline-block;'>最大值：<span style=\"color:#F56C6C\">"+t.max+'</span></span>最小值：<span style="color:#F56C6C">'+t.min+"</span><br /><span style='width:50%;display:inline-block;'>步长：<span style=\"color:#F56C6C\">"+t.step+'</span></span>单位：<span style="color:#F56C6C">'+t.unit;if("string"===t.type)return'最大长度：<span style="color:#F56C6C">'+t.maxLength+"</span>";if("array"===t.type)return"<span style='width:50%;display:inline-block;'>数组类型：<span style=\"color:#F56C6C\">"+t.arrayType+'</span></span>元素个数：<span style="color:#F56C6C">'+t.arrayCount;if("enum"===t.type){for(var a="",r=0;r<t.enumList.length;r++)a=a+"<span style='width:50%;display:inline-block;'>"+t.enumList[r].value+"：<span style='color:#F56C6C'>"+t.enumList[r].text+"</span></span>",r>0&&r%2!=0&&(a+="<br />");return a}if("bool"===t.type)return"<span style='width:50%;display:inline-block;'>0：<span style=\"color:#F56C6C\">"+t.falseText+'</span></span>1：<span style="color:#F56C6C">'+t.trueText;if("object"===t.type){for(var s="",o=0;o<t.params.length;o++)s=s+"<span style='width:50%;display:inline-block;'>"+t.params[o].name+"：<span style='color:#F56C6C'>"+t.params[o].datatype.type+"</span></span>",o>0&&o%2!=0&&(s+="<br />");return s}}},addParameter:function(){this.paramData={index:-1,parameter:{}}},editParameter:function(e,t){this.paramData=null,this.paramData={index:t,parameter:e}},removeParameter:function(e){this.form.specs.params.splice(e,1)},getParamData:function(e){-1==e.index?this.form.specs.params.push(e.parameter):(this.form.specs.params[e.index]=e.parameter,this.$set(this.form.specs.params,e.index,this.form.specs.params[e.index]))}}},m=n,p=(a("fbf3"),a("2877")),c=Object(p["a"])(m,r,s,!1,null,null,null);t["default"]=c.exports},cec4:function(e,t,a){"use strict";a.d(t,"e",(function(){return s})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return l})),a.d(t,"b",(function(){return n})),a.d(t,"c",(function(){return m}));var r=a("b775");function s(e){return Object(r["a"])({url:"/iot/template/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/iot/template/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/iot/template",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/iot/template",method:"put",data:e})}function n(e){return Object(r["a"])({url:"/iot/template/"+e,method:"delete"})}function m(e){return Object(r["a"])({url:"/iot/template/getPoints",method:"get",params:e})}},fbf3:function(e,t,a){"use strict";a("4e6e")}}]);