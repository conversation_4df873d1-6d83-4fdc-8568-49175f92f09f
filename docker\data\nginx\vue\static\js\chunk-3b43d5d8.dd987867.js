(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3b43d5d8"],{"3c73":function(e,t,a){"use strict";a("af52")},"6abb":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{attrs:{id:"easyplayer"}})},n=[],s={name:"player",data:function(){return{easyPlayer:null}},props:["videoUrl","error","hasaudio","height"],mounted:function(){var e=this,t=decodeURIComponent(this.$route.params.url);this.$nextTick((function(){"undefined"==typeof e.videoUrl&&(e.videoUrl=t),console.log("初始化时的地址为: "+e.videoUrl),e.play(e.videoUrl)}))},watch:{videoUrl:function(e,t){this.play(e)},immediate:!0},methods:{play:function(e){console.log(this.height),null!=this.easyPlayer&&this.easyPlayer.destroy(),"undefined"==typeof this.height&&(this.height=!1),this.easyPlayer=new WasmPlayer(null,"easyplayer",this.eventcallbacK,{Height:this.height}),this.easyPlayer.play(e,1)},pause:function(){this.easyPlayer.destroy(),this.easyPlayer=null},eventcallbacK:function(e,t){}},destroyed:function(){this.easyPlayer.destroy()}},l=s,o=(a("3c73"),a("2877")),r=Object(o["a"])(l,i,n,!1,null,null,null);t["default"]=r.exports},af52:function(e,t,a){}}]);