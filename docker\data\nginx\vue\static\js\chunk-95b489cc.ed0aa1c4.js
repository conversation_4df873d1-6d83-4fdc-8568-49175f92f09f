(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-95b489cc"],{"20f7":function(e,t,a){"use strict";a.r(t);var c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"device-scada-wrap"},[e.isScada?a("div",{staticClass:"scada",style:{height:e.contentHeight+"px"}},[a(e.scadaComp,{ref:"deviceScada",tag:"component",attrs:{fullScreemTip:!1,isContextmenu:!1}})],1):a("div",[a("el-empty",{attrs:{description:e.$t("device.scada.789543-0")}})],1)])},i=[],n=a("5530"),s=(a("14d9"),{name:"<PERSON><PERSON><PERSON>ca<PERSON>",props:{device:{type:Object,default:null}},watch:{device:{deep:!0,handler:function(e,t){e.guid?(this.getScadaComp(),this.isScada=!0):this.isScada=!1}}},data:function(){return{isScada:!1,contentHeight:window.innerHeight,scadaComp:null}},mounted:function(){var e=this.device.guid;e?(this.getScadaComp(),this.isScada=!0):this.isScada=!1},methods:{calculateContentHeight:function(){var e=document.getElementById("deviceDetailTab").offsetHeight;this.contentHeight=parseFloat(e)},getScadaComp:function(){var e=this.device,t=e.guid,a=e.serialNumber;this.$router.push({query:Object(n["a"])(Object(n["a"])({},this.$route.query),{},{guid:t,serialNumber:a,type:1})})}}}),d=s,u=(a("46c2"),a("2877")),o=Object(u["a"])(d,c,i,!1,null,"0c1beae2",null);t["default"]=o.exports},"46c2":function(e,t,a){"use strict";a("cca2")},cca2:function(e,t,a){}}]);