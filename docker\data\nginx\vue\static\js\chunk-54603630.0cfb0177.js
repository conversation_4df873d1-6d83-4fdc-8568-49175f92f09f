(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54603630","chunk-722c5e57"],{"3aec":function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("el-dialog",{attrs:{title:t.upload.title,visible:t.upload.importDeviceDialog,width:"550px","append-to-body":""},on:{"update:visible":function(e){return t.$set(t.upload,"importDeviceDialog",e)}}},[o("el-form",{ref:"importForm",attrs:{"label-width":"100px",model:t.importForm,rules:t.importRules}},[o("el-form-item",{attrs:{label:"",prop:"productName"}},[o("template",{slot:"label"},[t._v(" "+t._s(t.$t("device.device-edit.148398-4"))+" ")]),o("el-input",{staticStyle:{width:"360px"},attrs:{readonly:"",placeholder:t.$t("device.device-edit.148398-5")},model:{value:t.importForm.productName,callback:function(e){t.$set(t.importForm,"productName",e)},expression:"importForm.productName"}},[o("el-button",{attrs:{slot:"append"},on:{click:function(e){return t.selectProduct()}},slot:"append"},[t._v(t._s(t.$t("device.device-edit.148398-6")))])],1)],2),o("el-form-item",{attrs:{label:t.$t("uploadFile"),prop:"fileList"}},[o("el-upload",{ref:"upload",attrs:{limit:1,accept:".xlsx, .xls",headers:t.upload.headers,action:t.upload.url+"?productId="+t.importForm.productId,disabled:t.upload.isUploading,"on-progress":t.handleFileUploadProgress,"on-success":t.handleFileSuccess,"auto-upload":!1,"on-change":t.handleChange,"on-remove":t.handleRemove,drag:""},model:{value:t.importForm.fileList,callback:function(e){t.$set(t.importForm,"fileList",e)},expression:"importForm.fileList"}},[o("i",{staticClass:"el-icon-upload"}),o("div",{staticClass:"el-upload__text"},[t._v(" "+t._s(t.$t("dragFileTips"))+" "),o("em",[t._v(t._s(t.$t("clickFileTips")))])]),o("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[o("div",{staticStyle:{"margin-top":"10px"}},[o("span",[t._v(t._s(t.$t("device.batch-import-dialog.850870-5")))])])])]),o("el-link",{staticStyle:{"font-size":"14px","vertical-align":"baseline"},attrs:{type:"primary",underline:!1},on:{click:t.importTemplate}},[o("i",{staticClass:"el-icon-download"}),t._v(" "+t._s(t.$t("device.batch-import-dialog.850870-6"))+" ")])],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitFileForm}},[t._v(t._s(t.$t("confirm")))]),o("el-button",{on:{click:function(e){t.upload.importDeviceDialog=!1}}},[t._v(t._s(t.$t("cancel")))])],1)],1),o("product-list",{ref:"productList",attrs:{productId:t.importForm.productId},on:{productEvent:function(e){return t.getProductData(e)}}})],1)},r=[],l=o("5f87"),a=o("e51f"),n={name:"batchImport",components:{productList:a["default"]},props:{device:{type:Object,default:null}},data:function(){return{type:1,importForm:{productId:null,fileList:[],productName:""},productList:[],file:null,upload:{importDeviceDialog:!1,title:this.$t("batchImport"),isUploading:!1,headers:{Authorization:"Bearer "+Object(l["a"])()},url:"/prod-api/iot/device/importData"},importRules:{productName:[{required:!0,message:this.$t("device.allot-import-dialog.060657-14"),trigger:"blur"}],fileList:[{required:!0,message:this.$t("plzUploadFile"),trigger:"change"}]}}},created:function(){},methods:{importTemplate:function(){this.download("/iot/device/uploadTemplate?type="+this.type,{},"device_template_".concat((new Date).getTime(),".xlsx"))},handleChange:function(t,e){this.importForm.fileList=e,this.importForm.fileList&&this.$refs.importForm.clearValidate("fileList")},handleRemove:function(t,e){this.importForm.fileList=e,this.$refs.importForm.validateField("fileList")},handleFileUploadProgress:function(t,e,o){this.upload.isUploading=!0},handleFileSuccess:function(t,e,o){this.upload.importDeviceDialog=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+t.msg+"</div>",this.$t("device.allot-import-dialog.060657-17"),{dangerouslyUseHTMLString:!0}),this.$parent.getList()},selectProduct:function(){this.$refs.productList.open=!0,this.$refs.productList.getList()},getProductData:function(t){this.importForm.productId=t.productId,this.importForm.productName=t.productName},submitFileForm:function(){var t=this;this.$refs["importForm"].validate((function(e){e&&(t.$refs.upload.submit(),t.upload.importDeviceDialog=!1)}))}}},s=n,c=o("2877"),u=Object(c["a"])(s,i,r,!1,null,null,null);e["default"]=u.exports},"9b9c":function(t,e,o){"use strict";o.d(e,"g",(function(){return r})),o.d(e,"h",(function(){return l})),o.d(e,"f",(function(){return a})),o.d(e,"a",(function(){return n})),o.d(e,"i",(function(){return s})),o.d(e,"e",(function(){return c})),o.d(e,"b",(function(){return u})),o.d(e,"d",(function(){return d})),o.d(e,"c",(function(){return p}));var i=o("b775");function r(t){return Object(i["a"])({url:"/iot/product/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/iot/product/shortList",method:"get",params:t})}function a(t){return Object(i["a"])({url:"/iot/product/"+t,method:"get"})}function n(t){return Object(i["a"])({url:"/iot/product",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/iot/product",method:"put",data:t})}function c(t){return Object(i["a"])({url:"/iot/product/deviceCount/"+t,method:"get"})}function u(t){return Object(i["a"])({url:"/iot/product/status",method:"put",data:t})}function d(t){return Object(i["a"])({url:"/iot/product/"+t,method:"delete"})}function p(t){return Object(i["a"])({url:"/iot/product/copy?productId="+t,method:"post"})}},e51f:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-dialog",{attrs:{title:t.$t("device.product-list.058448-0"),visible:t.open,width:"910px"},on:{"update:visible":function(e){t.open=e}}},[o("el-form",{ref:"queryForm",attrs:{model:t.queryParams,inline:!0,"label-width":"68px"},nativeOn:{submit:function(t){t.preventDefault()}}},[o("el-form-item",{attrs:{prop:"productName"}},[o("el-input",{attrs:{placeholder:t.$t("device.product-list.058448-2"),clearable:"",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.productName,callback:function(e){t.$set(t.queryParams,"productName",e)},expression:"queryParams.productName"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.handleQuery}},[t._v(t._s(t.$t("device.product-list.058448-3")))]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"small"},on:{click:t.resetQuery}},[t._v(t._s(t.$t("device.product-list.058448-4")))])],1)],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"singleTable",attrs:{data:t.productList,"highlight-current-row":"",size:"small",border:!1},on:{"row-click":t.rowClick}},[o("el-table-column",{attrs:{label:t.$t("device.device-edit.148398-6"),width:"50",align:"center"},scopedSlots:t._u([{key:"default",fn:function(t){return[o("input",{attrs:{type:"radio",name:"product"},domProps:{checked:t.row.isSelect}})]}}])}),o("el-table-column",{attrs:{label:t.$t("device.allot-record.155854-2"),align:"left",prop:"productName","min-width":"180"}}),o("el-table-column",{attrs:{label:t.$t("device.product-list.058448-6"),align:"left",prop:"categoryName","min-width":"150"}}),o("el-table-column",{attrs:{label:t.$t("device.product-list.058448-7"),align:"left",prop:"tenantName","min-width":"100"}}),o("el-table-column",{attrs:{label:t.$t("device.product-list.058448-8"),align:"center",prop:"status",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.isAuthorize?o("el-tag",{attrs:{type:"success"}},[t._v(t._s(t.$t("device.product-list.058448-9")))]):t._e(),0==e.row.isAuthorize?o("el-tag",{attrs:{type:"info"}},[t._v(t._s(t.$t("device.product-list.058448-10")))]):t._e()]}}])}),o("el-table-column",{attrs:{label:t.$t("device.product-list.058448-11"),align:"center",prop:"status","min-width":"130"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.iot_vertificate_method,value:e.row.vertificateMethod}})]}}])}),o("el-table-column",{attrs:{label:t.$t("device.product-list.058448-12"),align:"center",prop:"networkMethod","min-width":"150"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.iot_network_method,value:e.row.networkMethod}})]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{layout:"prev, pager, next",total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}}),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.confirmSelectProduct}},[t._v(t._s(t.$t("device.product-list.058448-14")))]),o("el-button",{attrs:{type:"info"},on:{click:t.closeDialog}},[t._v(t._s(t.$t("device.product-list.058448-15")))])],1)],1)},r=[],l=(o("a9e3"),o("9b9c")),a={name:"ProductList",dicts:["iot_vertificate_method","iot_network_method"],props:{productId:{type:Number,default:0},showSenior:{type:Boolean,default:!0}},data:function(){return{loading:!0,total:0,open:!1,productList:[],product:{},queryParams:{pageNum:1,pageSize:10,productName:null,categoryId:null,categoryName:null,tenantId:null,tenantName:null,isSys:null,status:2,deviceType:null,networkMethod:null}}},created:function(){},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.showSenior=this.showSenior,Object(l["g"])(this.queryParams).then((function(e){for(var o=0;o<e.rows.length;o++)e.rows[o].isSelect=!1;t.productList=e.rows,t.total=e.total,0!=t.productId&&t.setRadioSelected(t.productId),t.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},rowClick:function(t){null!=t&&(this.setRadioSelected(t.productId),this.product=t)},setRadioSelected:function(t){for(var e=0;e<this.productList.length;e++)this.productList[e].productId==t?this.productList[e].isSelect=!0:this.productList[e].isSelect=!1},confirmSelectProduct:function(){this.$emit("productEvent",this.product),this.open=!1},closeDialog:function(){this.open=!1}}},n=a,s=o("2877"),c=Object(s["a"])(n,i,r,!1,null,null,null);e["default"]=c.exports}}]);